import { jsPDF } from 'jspdf';

/**
 * Helper function to add a table of contents to the PDF report
 * @param doc The PDF document
 * @param sections Array of section names and their page numbers
 */
export const addTableOfContents = (doc: jsPDF, sections: Array<{ title: string; page: number }>): void => {
  // Add a new page for the table of contents
  doc.addPage();

  const pageWidth = doc.internal.pageSize.width;

  // Add title
  doc.setFontSize(20);
  doc.setTextColor(41, 128, 185);
  doc.text('Table of Contents', 14, 22);

  // Add decorative line
  doc.setDrawColor(41, 128, 185);
  doc.setLineWidth(0.5);
  doc.line(14, 26, pageWidth - 14, 26);

  // Add sections
  let startY = 40;
  doc.setFontSize(12);
  doc.setTextColor(70);

  sections.forEach((section, index) => {
    // Add section number and title
    doc.setFont(undefined, 'normal');
    doc.text(`${index + 1}. ${section.title}`, 14, startY);

    // Add page number
    doc.setFont(undefined, 'bold');
    doc.text(`Page ${section.page}`, pageWidth - 30, startY, { align: 'right' });

    // Add dotted line between title and page number
    doc.setLineDashPattern([1, 1], 0);
    doc.line(14 + doc.getTextWidth(`${index + 1}. ${section.title}`) + 5, startY, pageWidth - 35, startY);

    startY += 12;
  });

  // Reset line dash pattern
  doc.setLineDashPattern([], 0);
};
