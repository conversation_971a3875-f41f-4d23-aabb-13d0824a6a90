import React, { useState, useEffect } from 'react';
import {
  Info,
  AlertCircle,
  CheckCircle,
  X,
  Bell
} from 'lucide-react';
import { Notification } from '@/contexts/NotificationContext';
import { AnimatePresence, motion } from 'framer-motion';

interface InAppNotificationProps {
  notification: Notification;
  onClose: () => void;
  autoClose?: boolean;
  duration?: number;
}

const InAppNotification: React.FC<InAppNotificationProps> = ({
  notification,
  onClose,
  autoClose = true,
  duration = 5000
}) => {
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (autoClose) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        setTimeout(onClose, 300); // Wait for exit animation to complete
      }, duration);

      return () => clearTimeout(timer);
    }
  }, [autoClose, duration, onClose]);

  // Get icon based on notification type
  const getNotificationIcon = () => {
    switch (notification.type) {
      case 'info':
        return <Info className="h-5 w-5 text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-amber-500" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Bell className="h-5 w-5 text-blue-500" />;
    }
  };

  // Get background color based on notification type
  const getBackgroundColor = () => {
    switch (notification.type) {
      case 'info':
        return 'bg-blue-500/10 border-blue-500/30';
      case 'success':
        return 'bg-green-500/10 border-green-500/30';
      case 'warning':
        return 'bg-amber-500/10 border-amber-500/30';
      case 'error':
        return 'bg-red-500/10 border-red-500/30';
      default:
        return 'bg-blue-500/10 border-blue-500/30';
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3 }}
          className={`p-4 rounded-lg border ${getBackgroundColor()} shadow-lg max-w-md w-full`}
        >
          <div className="flex gap-3">
            <div className="flex-shrink-0 mt-0.5">
              {getNotificationIcon()}
            </div>
            <div className="flex-1 min-w-0">
              <h4 className="font-medium text-gray-800">{notification.title}</h4>
              <p className="text-gray-600 text-sm mt-1">{notification.message}</p>
            </div>
            <button
              className="flex-shrink-0 text-gray-500 hover:text-gray-700"
              onClick={() => {
                setIsVisible(false);
                setTimeout(onClose, 300); // Wait for exit animation to complete
              }}
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default InAppNotification;
