import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import * as pdfjsLib from 'pdfjs-dist';

// Set up PDF.js worker
// Use the correct URL format for the worker file (.mjs extension)
pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.min.mjs`;

interface PDFViewerProps {
  url: string;
  fileName: string;
  onPageCountChange?: (pageCount: number) => void;
}

const PDFViewer: React.FC<PDFViewerProps> = ({ url, fileName, onPageCountChange }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pageCount, setPageCount] = useState(0);

  // Effect to fix PDF embed height
  useEffect(() => {
    const style = document.createElement('style');
    style.innerHTML = `
      embed[type="application/pdf"] {
        height: 45em !important;
        min-height: 45em !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  // Effect to load PDF and get page count
  useEffect(() => {
    // Log the URL for debugging
    console.log('PDFViewer received URL:', url);

    // Check if the URL is valid
    if (!url || url === '/mock-cv-preview.pdf' || url.includes('fallback-storage.example.com')) {
      setError('No valid PDF URL available');
      setIsLoading(false);
      return;
    }

    // Load the PDF and get page count
    const loadPDF = async () => {
      try {
        // First check if the URL is accessible
        const response = await fetch(url, { method: 'HEAD' });
        if (!response.ok) {
          setError(`Unable to access PDF (${response.status}: ${response.statusText})`);
          setIsLoading(false);
          return;
        }

        // Load the PDF document to get page count
        const loadingTask = pdfjsLib.getDocument(url);
        const pdf = await loadingTask.promise;

        // Get the page count
        const count = pdf.numPages;
        setPageCount(count);

        // Notify parent component of page count
        if (onPageCountChange) {
          onPageCountChange(count);
        }

        setIsLoading(false);
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError(`Error loading PDF: ${err instanceof Error ? err.message : String(err)}`);
        setIsLoading(false);
      }
    };

    loadPDF();
  }, [url, onPageCountChange]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p>Loading PDF...</p>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 text-center">
        <p className="text-red-500 mb-2">{error}</p>
        <p className="mb-4">The PDF might be inaccessible due to CORS restrictions or storage permissions.</p>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Open PDF in New Tab
        </a>
      </div>
    );
  }

  // Render PDF using an embed tag which is less likely to auto-download
  return (
    <div className="h-full w-full" style={{ minHeight: '45em' }}>
      <embed
        src={`${url}#toolbar=0&navpanes=0&scrollbar=1`}
        type="application/pdf"
        width="100%"
        height="45em"
        className="border-0"
        style={{ minHeight: '45em' }}
      />
    </div>
  );
};

export default PDFViewer;
