import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  Search,
  Filter,
  MoreHorizontal,
  FileText,
  Mail,
  Phone,
  MapPin,
  Star,
  Download,
  Eye,
  MessageSquare,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Upload,
  FileUp,
  BarChart3,
  Users,
  Trash2,
  Briefcase,
  FileSearch
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { getCandidates, updateCandidate } from '@/services/supabase/candidates';
import { useParseCandidateCV } from '@/hooks/use-candidates';
import { useToast } from '@/hooks/use-toast';
import { useReportExport } from '@/hooks/use-reports';
import { getCandidateReportData } from '@/utils/reportUtils';
import SubscriptionGuard from '@/components/guards/SubscriptionGuard';
import EvaluateModal from '@/components/cv/EvaluateModal';
import AddNoteModal from '@/components/candidate/AddNoteModal';
import ScheduleInterviewModal from '@/components/interviews/ScheduleInterviewModal';
import AssignToJobModal from '@/components/candidate/AssignToJobModal';
import AssignmentInfo from '@/components/candidate/AssignmentInfo';
import { getStatusColor } from '@/utils/candidateUtils';
import StatusChangeModal from '@/components/candidate/StatusChangeModal';
import { useMultipleCandidateAssignments, useUpdateAssignmentStatus } from '@/hooks/use-assignments';

// Define the candidate interface to match our data structure
interface Candidate {
  id: string;
  name: string;
  email: string;
  phone?: string;
  cv_url: string;
  status: string;
  job_id: string;
  user_id: string;
  evaluation_score?: number;
  evaluation_summary?: string;
  notes?: string;
  created_at: string;
  source?: string;
  // Additional fields for UI
  location?: string;
  position?: string;
  experience?: string;
  skills?: string[];
  education?: string;
  matchScore?: number;
  appliedDate?: string;
  lastActivity?: string;
  avatar?: string;
}

// Mock data for candidates as fallback
const mockCandidates = [
  {
    id: 1,
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'New York, NY',
    position: 'Frontend Developer',
    experience: '5 years',
    skills: ['React', 'TypeScript', 'CSS', 'Node.js'],
    education: 'Bachelor in Computer Science',
    status: 'Screening',
    matchScore: 85,
    appliedDate: '2023-09-15',
    lastActivity: '2023-09-20',
    avatar: 'https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png?ssl=1',
  },
  {
    id: 2,
    name: 'Jane Smith',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'San Francisco, CA',
    position: 'UX Designer',
    experience: '3 years',
    skills: ['Figma', 'UI Design', 'User Research', 'Prototyping'],
    education: 'Master in Design',
    status: 'Interview',
    matchScore: 92,
    appliedDate: '2023-09-18',
    lastActivity: '2023-09-25',
    avatar: 'https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png?ssl=1',
  },
  {
    id: 3,
    name: 'Michael Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'Remote',
    position: 'DevOps Engineer',
    experience: '7 years',
    skills: ['AWS', 'Docker', 'Kubernetes', 'CI/CD', 'Terraform'],
    education: 'Bachelor in Information Technology',
    status: 'Offer',
    matchScore: 78,
    appliedDate: '2023-09-10',
    lastActivity: '2023-09-22',
    avatar: 'https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png?ssl=1',
  },
  {
    id: 4,
    name: 'Emily Davis',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'Chicago, IL',
    position: 'Product Manager',
    experience: '4 years',
    skills: ['Product Strategy', 'Agile', 'User Stories', 'Roadmapping'],
    education: 'MBA',
    status: 'Rejected',
    matchScore: 65,
    appliedDate: '2023-09-05',
    lastActivity: '2023-09-15',
    avatar: 'https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png?ssl=1',
  },
  {
    id: 5,
    name: 'David Wilson',
    email: '<EMAIL>',
    phone: '+****************',
    location: 'Austin, TX',
    position: 'Frontend Developer',
    experience: '2 years',
    skills: ['JavaScript', 'React', 'HTML', 'CSS'],
    education: 'Bachelor in Computer Science',
    status: 'New',
    matchScore: 88,
    appliedDate: '2023-09-22',
    lastActivity: '2023-09-22',
    avatar: 'https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png?ssl=1',
  },
];

// We're now importing getStatusColor from candidateUtils

// Match score color mapping
const getMatchScoreColor = (score: number) => {
  if (score >= 90) return 'text-green-500';
  if (score >= 75) return 'text-blue-500';
  if (score >= 60) return 'text-amber-500';
  return 'text-red-500';
};

const CandidateManagement = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { exportReportData } = useReportExport();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [positionFilter, setPositionFilter] = useState('all');
  const [activeTab, setActiveTab] = useState('all');
  const [candidates, setCandidates] = useState<Candidate[]>([]);
  const [loading, setLoading] = useState(true);
  const [evaluateModalOpen, setEvaluateModalOpen] = useState(false);
  const [isAddNoteModalOpen, setIsAddNoteModalOpen] = useState(false);
  const [scheduleInterviewModalOpen, setScheduleInterviewModalOpen] = useState(false);
  const [assignToJobModalOpen, setAssignToJobModalOpen] = useState(false);
  const [selectedCandidate, setSelectedCandidate] = useState<Candidate | null>(null);
  const [statusModalOpen, setStatusModalOpen] = useState(false);

  // Multi-select state
  const [selectedCandidates, setSelectedCandidates] = useState<Set<string>>(new Set());
  const [isBulkEvaluationMode, setIsBulkEvaluationMode] = useState(false);

  // Assignment hooks
  const updateAssignmentStatus = useUpdateAssignmentStatus();

  // Parse CV hook
  const parseCVMutation = useParseCandidateCV();

  // Get assignments for all candidates
  const candidateIds = candidates.map(c => c.id);
  const { data: candidateAssignments = {} } = useMultipleCandidateAssignments(candidateIds);

  // Fetch candidates from Supabase
  useEffect(() => {
    const fetchCandidates = async () => {
      try {
        setLoading(true);
        const candidatesData = await getCandidates();

        // Process the candidates data to match our UI needs
        const enhancedCandidates = candidatesData.map(candidate => {
          // Parse evaluation summary if available
          let skills: string[] = [];
          let position = '';
          let education = '';
          let experience = '';
          let location = '';
          let parsedCV = null;
          let extractedSkills = null;

          if (candidate.evaluation_summary) {
            try {
              const summary = JSON.parse(candidate.evaluation_summary);
              console.log('Parsed evaluation summary:', summary);

              // Check if the summary contains parsedCV and extractedSkills
              if (summary.parsedCV) {
                parsedCV = summary.parsedCV;

                // Extract position from work experience if available
                if (parsedCV.workExperience && parsedCV.workExperience.length > 0) {
                  position = parsedCV.workExperience[0].title || '';
                }

                // Extract education
                if (parsedCV.education && parsedCV.education.length > 0) {
                  const edu = parsedCV.education[0];
                  education = `${edu.degree || ''} ${edu.field ? 'in ' + edu.field : ''} ${edu.institution ? 'from ' + edu.institution : ''}`.trim();
                }

                // Extract experience duration
                if (parsedCV.workExperience && parsedCV.workExperience.length > 0) {
                  const workExp = parsedCV.workExperience;
                  const totalYears = workExp.reduce((total, job) => {
                    const start = job.startDate ? new Date(job.startDate).getFullYear() : 0;
                    const end = job.endDate ? new Date(job.endDate).getFullYear() : new Date().getFullYear();
                    return total + (end - start);
                  }, 0);
                  experience = totalYears > 0 ? `${totalYears} years` : '';
                }

                // Extract location
                if (parsedCV.personalDetails && parsedCV.personalDetails.location) {
                  location = parsedCV.personalDetails.location;
                }
              }

              // Extract skills from extractedSkills if available
              if (summary.extractedSkills) {
                extractedSkills = summary.extractedSkills;

                // Combine technical, domain, and soft skills
                const technicalSkills = extractedSkills.technical ?
                  extractedSkills.technical.map(skill => typeof skill === 'string' ? skill : skill.name) : [];
                const domainSkills = extractedSkills.domain ?
                  extractedSkills.domain.map(skill => typeof skill === 'string' ? skill : skill.name) : [];
                const softSkills = extractedSkills.soft ?
                  extractedSkills.soft.map(skill => typeof skill === 'string' ? skill : skill.name) : [];

                skills = [...technicalSkills, ...domainSkills, ...softSkills];
              }

              // Fallback to direct properties if nested structure not found
              if (!parsedCV && !extractedSkills) {
                skills = summary.skills || [];
                position = summary.position || '';
                education = summary.education || '';
                experience = summary.experience || '';
                location = summary.location || '';
              }
            } catch (e) {
              console.error('Error parsing evaluation summary:', e);
              console.error('Raw evaluation summary:', candidate.evaluation_summary);
            }
          }

          return {
            ...candidate,
            skills: Array.isArray(skills) ? skills : typeof skills === 'string' ? [skills] : [],
            position: position || (candidate.job_title || 'Position Not Specified'),
            education: education || 'Education Not Specified',
            experience: experience || 'Experience Not Specified',
            location: location || (candidate.location || 'Location Not Specified'),
            matchScore: candidate.evaluation_score || null, // Only show real evaluation scores
            appliedDate: candidate.created_at,
            lastActivity: candidate.created_at,
            avatar: 'https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png?ssl=1',
          };
        });

        setCandidates(enhancedCandidates);
      } catch (error) {
        console.error('Error fetching candidates:', error);
        toast({
          title: 'Error',
          description: 'Failed to load candidates. Using mock data instead.',
          variant: 'destructive'
        });

        // Fallback to mock data
        setCandidates(mockCandidates.map(c => ({
          ...c,
          id: c.id.toString(),
          cv_url: '',
          job_id: '',
          user_id: '',
          created_at: c.appliedDate,
          status: c.status.toLowerCase()
        })));
      } finally {
        setLoading(false);
      }
    };

    fetchCandidates();
  }, [toast]);

  // Filter candidates based on search query, filters, and active tab
  const filteredCandidates = candidates.filter(candidate => {
    const matchesSearch =
      candidate.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      candidate.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (candidate.position?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
      (candidate.skills?.some(skill => typeof skill === 'string' && skill.toLowerCase().includes(searchQuery.toLowerCase())) || false);

    const matchesStatus = statusFilter === 'all' || candidate.status.toLowerCase() === statusFilter.toLowerCase();
    const matchesPosition = positionFilter === 'all' || (candidate.position?.toLowerCase() || '').includes(positionFilter.toLowerCase());
    const matchesTab = activeTab === 'all' ||
                      (activeTab === 'new' && candidate.status.toLowerCase() === 'new') ||
                      (activeTab === 'screening' && candidate.status.toLowerCase() === 'screening') ||
                      (activeTab === 'interview' && candidate.status.toLowerCase() === 'interview') ||
                      (activeTab === 'offer' && candidate.status.toLowerCase() === 'offer');

    return matchesSearch && matchesStatus && matchesPosition && matchesTab;
  });

  // Get unique positions for filter
  // Function to handle opening the evaluation modal
  const handleOpenEvaluateModal = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setEvaluateModalOpen(true);
  };

  // Function to handle successful evaluation
  const handleEvaluationSuccess = (score: number) => {
    // Update the candidate's match score in the local state
    if (selectedCandidate) {
      setCandidates(prevCandidates =>
        prevCandidates.map(c =>
          c.id === selectedCandidate.id
            ? { ...c, matchScore: score }
            : c
        )
      );
    }
  };

  // Multi-select handlers
  const handleSelectCandidate = (candidateId: string, checked: boolean) => {
    const newSelected = new Set(selectedCandidates);
    if (checked) {
      newSelected.add(candidateId);
    } else {
      newSelected.delete(candidateId);
    }
    setSelectedCandidates(newSelected);
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(filteredCandidates.map(c => c.id));
      setSelectedCandidates(allIds);
    } else {
      setSelectedCandidates(new Set());
    }
  };

  const handleBulkEvaluate = () => {
    if (selectedCandidates.size === 0) {
      toast({
        title: 'No Candidates Selected',
        description: 'Please select at least one candidate to evaluate.',
        variant: 'destructive',
      });
      return;
    }

    // Check if any selected candidates have unprocessed CVs
    const selectedCandidatesList = candidates.filter(c => selectedCandidates.has(c.id));
    const unprocessedCandidates = selectedCandidatesList.filter(c => !isCVParsed(c));

    if (unprocessedCandidates.length > 0) {
      toast({
        title: 'Cannot Evaluate Unprocessed CVs',
        description: `${unprocessedCandidates.length} selected candidate(s) have unprocessed CVs. Please process their CVs first before evaluation.`,
        variant: 'destructive',
      });
      return;
    }

    setIsBulkEvaluationMode(true);
    setEvaluateModalOpen(true);
  };

  const handleClearSelection = () => {
    setSelectedCandidates(new Set());
  };

  // Handle download CV
  const handleDownloadCV = (candidate: Candidate) => {
    if (candidate.cv_url) {
      window.open(candidate.cv_url, '_blank');
    } else {
      toast({
        title: 'Error',
        description: 'No CV file available for download.',
        variant: 'destructive'
      });
    }
  };

  // Handle schedule interview
  const handleScheduleInterview = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setScheduleInterviewModalOpen(true);
  };

  // Handle assign to job (single candidate)
  const handleAssignToJob = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setAssignToJobModalOpen(true);
  };

  // Handle bulk assign to job
  const handleBulkAssignToJob = () => {
    if (selectedCandidates.size === 0) {
      toast({
        title: 'No Candidates Selected',
        description: 'Please select at least one candidate to assign to a job.',
        variant: 'destructive',
      });
      return;
    }
    setAssignToJobModalOpen(true);
  };

  // Handle change status
  const handleChangeStatus = async (candidate: Candidate, newStatus: string) => {
    console.log('Changing status for candidate:', candidate.id, 'to:', newStatus);
    try {
      await updateCandidate(candidate.id, { status: newStatus });

      // Update the candidate in the local state
      setCandidates(prev =>
        prev.map(c => c.id === candidate.id ? { ...c, status: newStatus } : c)
      );

      toast({
        title: 'Status updated',
        description: `Candidate status has been changed to ${newStatus}.`,
      });
    } catch (error) {
      console.error('Error updating candidate status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update candidate status. Please try again.',
        variant: 'destructive'
      });
    }
  };

  // Handle add note
  const handleOpenAddNoteModal = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setIsAddNoteModalOpen(true);
  };

  // Check if a candidate's CV is parsed
  const isCVParsed = (candidate: Candidate) => {
    return candidate.evaluation_summary && candidate.evaluation_summary.trim() !== '';
  };

  // Handle CV processing
  const handleProcessCV = async (candidate: Candidate) => {
    if (!candidate.cv_url) {
      toast({
        title: 'Error',
        description: 'No CV file found for this candidate.',
        variant: 'destructive'
      });
      return;
    }

    try {
      await parseCVMutation.mutateAsync({
        candidateId: candidate.id,
        cvUrl: candidate.cv_url
      });

      // Refresh candidates data
      const candidatesData = await getCandidates();
      const enhancedCandidates = candidatesData.map(candidate => {
        // Same processing as in the useEffect
        let skills: string[] = [];
        let position = '';
        let education = '';
        let experience = '';

        if (candidate.evaluation_summary) {
          try {
            const parsed = JSON.parse(candidate.evaluation_summary);
            if (parsed.parsedCV) {
              skills = parsed.parsedCV.skills?.technical || [];
              position = parsed.parsedCV.workExperience?.[0]?.title || '';
              education = parsed.parsedCV.education?.[0]?.degree || '';
              experience = parsed.parsedCV.workExperience?.length ? `${parsed.parsedCV.workExperience.length} positions` : '';
            }
          } catch (e) {
            console.warn('Failed to parse evaluation_summary:', e);
          }
        }

        return {
          ...candidate,
          skills,
          position,
          education,
          experience,
          location: candidate.phone || 'Not specified',
          appliedDate: new Date(candidate.created_at).toLocaleDateString(),
          lastActivity: new Date(candidate.created_at).toLocaleDateString(),
          avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(candidate.name)}&background=3b82f6&color=fff&size=150`
        };
      });

      setCandidates(enhancedCandidates);
    } catch (error) {
      console.error('Error parsing CV:', error);
    }
  };

  // Handle export to PDF
  const handleExportToPDF = (candidate: Candidate) => {
    const reportData = getCandidateReportData(candidate);
    exportReportData(reportData, 'candidate_pipeline', 'pdf');
  };

  // Handle email candidate
  const handleEmailCandidate = (candidate: Candidate) => {
    if (candidate.email) {
      window.location.href = `mailto:${candidate.email}`;
    } else {
      toast({
        title: 'Error',
        description: 'No email address available for this candidate.',
        variant: 'destructive'
      });
    }
  };

  const handleOpenStatusModal = (candidate: Candidate) => {
    setSelectedCandidate(candidate);
    setStatusModalOpen(true);
  };

  // Handle assignment status update
  const handleAssignmentStatusUpdate = async (assignmentId: string, newStatus: string) => {
    try {
      await updateAssignmentStatus.mutateAsync({
        assignmentId,
        status: newStatus as any
      });
    } catch (error) {
      console.error('Error updating assignment status:', error);
    }
  };

  const positions = ['all', ...new Set(candidates
    .filter(candidate => candidate.position)
    .map(candidate => candidate.position?.toLowerCase() || 'unknown'))];

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold text-gray-800">Candidate Management</h1>
        <SubscriptionGuard limitType="cv_uploads" useToast={true}>
          <Button
            className="bg-primary-gradient text-white hover:shadow-md transition-all hover:text-white"
            onClick={() => navigate('/dashboard/cvs/evaluation')}
          >
            <Upload className="mr-2 h-4 w-4" /> Upload CVs
          </Button>
        </SubscriptionGuard>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="bg-white border-b border-gray-200 w-full justify-start">
          <TabsTrigger value="all" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            All Candidates
          </TabsTrigger>
          <TabsTrigger value="new" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            New
          </TabsTrigger>
          <TabsTrigger value="screening" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Screening
          </TabsTrigger>
          <TabsTrigger value="interview" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Interview
          </TabsTrigger>
          <TabsTrigger value="offer" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Offer
          </TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="mt-6">
          {/* Filters and search */}
          <Card className="bg-white border-gray-200 shadow-sm mb-6">
            <CardContent className="p-4">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
                  <Input
                    type="text"
                    placeholder="Search candidates..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 bg-white border-gray-200 text-gray-800 w-full"
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="w-full sm:w-40">
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border-gray-200 text-gray-800">
                        <SelectItem value="all">All Status</SelectItem>
                        <SelectItem value="new">New</SelectItem>
                        <SelectItem value="screening">Screening</SelectItem>
                        <SelectItem value="interview">Interview</SelectItem>
                        <SelectItem value="offer">Offer</SelectItem>
                        <SelectItem value="rejected">Rejected</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="w-full sm:w-40">
                    <Select value={positionFilter} onValueChange={setPositionFilter}>
                      <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                        <SelectValue placeholder="Position" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border-gray-200 text-gray-800">
                        <SelectItem value="all">All Positions</SelectItem>
                        {positions.filter(p => p !== 'all').map((position) => (
                          <SelectItem key={position} value={position}>
                            {position.charAt(0).toUpperCase() + position.slice(1)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <Button variant="outline" className="border-gray-200 text-gray-700 hover:bg-gray-100 hover:text-gray-700">
                    <Filter className="mr-2 h-4 w-4" /> More Filters
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Bulk Actions Bar */}
          {selectedCandidates.size > 0 && (
            <Card className="bg-blue-50 border-blue-200">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Users className="h-5 w-5 text-blue-600" />
                      <span className="font-medium text-blue-800">
                        {selectedCandidates.size} candidate{selectedCandidates.size !== 1 ? 's' : ''} selected
                      </span>
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleClearSelection}
                      className="text-blue-700 border-blue-300 hover:bg-blue-100"
                    >
                      Clear Selection
                    </Button>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      onClick={handleBulkAssignToJob}
                      variant="outline"
                      className="border-green-300 text-green-700 hover:bg-green-50"
                    >
                      <Briefcase className="mr-2 h-4 w-4" />
                      Assign to Job
                    </Button>
                    <Button
                      onClick={handleBulkEvaluate}
                      className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white"
                    >
                      <BarChart3 className="mr-2 h-4 w-4" />
                      Evaluate Selected Candidates
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Select All Checkbox */}
          {!loading && filteredCandidates.length > 0 && (
            <Card className="bg-gray-50 border-gray-200">
              <CardContent className="p-3">
                <div className="flex items-center gap-2">
                  <Checkbox
                    id="select-all"
                    checked={selectedCandidates.size === filteredCandidates.length && filteredCandidates.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <label htmlFor="select-all" className="text-sm font-medium text-gray-700 cursor-pointer">
                    Select all {filteredCandidates.length} candidate{filteredCandidates.length !== 1 ? 's' : ''}
                  </label>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Candidate listings */}
          <div className="space-y-4">
            {loading ? (
              <Card className="bg-white border-gray-200 p-8 text-center">
                <p className="text-gray-500">Loading candidates...</p>
              </Card>
            ) : filteredCandidates.length === 0 ? (
              <Card className="bg-white border-gray-200 p-8 text-center">
                <p className="text-gray-500">No candidates found matching your criteria.</p>
              </Card>
            ) : (
              filteredCandidates.map((candidate) => (
                <Card key={candidate.id} className={`bg-white border-gray-200 hover:shadow-md transition-all duration-200 bg-card-hover ${selectedCandidates.has(candidate.id) ? 'ring-2 ring-blue-500 border-blue-300' : ''}`}>
                  <CardContent className="p-6">
                    <div className="flex flex-col md:flex-row gap-6">
                      {/* Selection checkbox */}
                      <div className="flex items-start">
                        <Checkbox
                          id={`select-${candidate.id}`}
                          checked={selectedCandidates.has(candidate.id)}
                          onCheckedChange={(checked) => handleSelectCandidate(candidate.id, checked as boolean)}
                          className="mt-1"
                        />
                      </div>

                      {/* Candidate info */}
                      <div className="flex items-start gap-4">
                        <Avatar className="h-12 w-12">
                          <AvatarImage src={candidate.avatar} />
                          <AvatarFallback>{candidate.name.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                            <h3 className="text-lg font-medium text-gray-800">{candidate.name}</h3>
                            <div className="flex gap-2">
                              <Badge className={getStatusColor(candidate.status)}>
                                {candidate.status}
                              </Badge>
                              <Badge
                                className={isCVParsed(candidate)
                                  ? "bg-green-100 text-green-800 border-green-200"
                                  : "bg-orange-100 text-orange-800 border-orange-200"
                                }
                              >
                                {isCVParsed(candidate) ? 'CV Processed' : 'Needs Processing'}
                              </Badge>
                            </div>
                          </div>
                          <p className="text-gray-600 mt-1">{candidate.position}</p>
                          <div className="flex flex-wrap gap-4 mt-2">
                            <div className="flex items-center text-sm text-gray-600">
                              <Mail className="mr-1 h-4 w-4" />
                              <span>{candidate.email}</span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <Phone className="mr-1 h-4 w-4" />
                              <span>{candidate.phone}</span>
                            </div>
                            <div className="flex items-center text-sm text-gray-600">
                              <MapPin className="mr-1 h-4 w-4" />
                              <span>{candidate.location}</span>
                            </div>
                            {candidate.source && (
                              <div className="flex items-center text-sm text-gray-600">
                                <Users className="mr-1 h-4 w-4" />
                                <span>{candidate.source}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* Match score and actions - Only show for processed CVs */}
                      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 ml-auto">
                        {isCVParsed(candidate) && (
                          <div className="flex flex-col items-center">
                            {candidate.matchScore ? (
                              <>
                                <div className="flex items-center gap-1">
                                  <span className={`text-2xl font-bold ${getMatchScoreColor(candidate.matchScore)}`}>
                                    {candidate.matchScore}%
                                  </span>
                                  <Star className={`h-5 w-5 ${getMatchScoreColor(candidate.matchScore)}`} fill="currentColor" />
                                </div>
                                <span className="text-xs text-gray-500">Match Score</span>
                                <Progress
                                  value={candidate.matchScore}
                                  className="h-1.5 w-24 mt-1"
                                  indicatorClassName={`${
                                    candidate.matchScore >= 90 ? 'bg-green-500' :
                                    candidate.matchScore >= 75 ? 'bg-blue-500' :
                                    candidate.matchScore >= 60 ? 'bg-amber-500' :
                                    'bg-red-500'
                                  }`}
                                />
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-2 text-xs border-gray-200 text-gray-700 hover:bg-gray-100 hover:text-gray-700"
                                  onClick={() => handleOpenEvaluateModal(candidate)}
                                >
                                  Re-evaluate
                                </Button>
                              </>
                            ) : (
                              <div className="flex flex-col items-center">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="mt-1 text-xs border-gray-200 text-gray-700 hover:bg-gray-100 hover:text-gray-700"
                                  onClick={() => handleOpenEvaluateModal(candidate)}
                                >
                                  Evaluate
                                </Button>
                              </div>
                            )}
                          </div>
                        )}

                        <div className="flex items-center gap-2">
                          {!isCVParsed(candidate) ? (
                            // Show only Process CV button for unparsed CVs
                            <Button
                              className="bg-blue-600 hover:bg-blue-700 text-white"
                              size="sm"
                              onClick={() => handleProcessCV(candidate)}
                              disabled={parseCVMutation.isPending}
                            >
                              <FileSearch className="mr-2 h-4 w-4" />
                              {parseCVMutation.isPending ? 'Processing...' : 'Process CV'}
                            </Button>
                          ) : (
                            // Show full actions for parsed CVs
                            <>
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-200 text-gray-700 hover:bg-gray-100 hover:text-gray-700"
                                onClick={() => navigate(`/dashboard/cvs/${candidate.id}`)}
                              >
                                <Eye className="mr-1 h-4 w-4" /> View Details
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="outline" size="icon" className="border-gray-200 text-gray-700 hover:bg-gray-100 hover:text-gray-700">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end" className="bg-white border-gray-200 text-gray-800 shadow-lg rounded-lg animate-scale-in">
                                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                  <DropdownMenuSeparator className="bg-gray-200" />
                                  <DropdownMenuItem
                                    className="hover:bg-gray-100 cursor-pointer"
                                    onClick={() => navigate(`/dashboard/cvs/${candidate.id}?tab=cv`)}
                                  >
                                    <FileText className="mr-2 h-4 w-4" /> View CV
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="hover:bg-gray-100 cursor-pointer"
                                    onClick={() => handleDownloadCV(candidate)}
                                  >
                                    <Download className="mr-2 h-4 w-4" /> Download CV
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="hover:bg-gray-100 cursor-pointer"
                                    onClick={() => handleOpenEvaluateModal(candidate)}
                                  >
                                    <Star className="mr-2 h-4 w-4" /> {candidate.matchScore ? 'Re-evaluate' : 'Evaluate'} Candidate
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="hover:bg-gray-100 cursor-pointer"
                                    onClick={() => handleAssignToJob(candidate)}
                                  >
                                    <Briefcase className="mr-2 h-4 w-4" /> Assign to Job
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="hover:bg-gray-100 cursor-pointer"
                                    onClick={() => handleOpenStatusModal(candidate)}
                                  >
                                    <CheckCircle className="mr-2 h-4 w-4" /> Change Status
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="hover:bg-gray-100 cursor-pointer"
                                    onClick={() => handleScheduleInterview(candidate)}
                                  >
                                    <Calendar className="mr-2 h-4 w-4" /> Schedule Interview - Pro
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    className="hover:bg-gray-100 cursor-pointer"
                                    onClick={() => handleEmailCandidate(candidate)}
                                  >
                                    <Mail className="mr-2 h-4 w-4" /> Email Candidate
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Skills */}
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      <div className="flex flex-wrap gap-2">
                        {candidate.skills.map((skill, index) => (
                          <Badge key={index} variant="outline" className="bg-gray-100 text-gray-700 border-gray-200 hover:bg-gray-200 transition-colors">
                            {typeof skill === 'string' ? skill : JSON.stringify(skill)}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Assignment Information */}
                    {candidateAssignments[candidate.id] && candidateAssignments[candidate.id].length > 0 && (
                      <div className="mt-4 pt-4 border-t border-gray-200">
                        <AssignmentInfo
                          assignments={candidateAssignments[candidate.id]}
                          onUpdateStatus={handleAssignmentStatusUpdate}
                          compact={true}
                        />
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Evaluation Modal */}
      {(selectedCandidate || isBulkEvaluationMode) && (
        <EvaluateModal
          isOpen={evaluateModalOpen}
          onClose={() => {
            setEvaluateModalOpen(false);
            setIsBulkEvaluationMode(false);
            setSelectedCandidate(null);
          }}
          candidateId={selectedCandidate?.id}
          candidateIds={isBulkEvaluationMode ? Array.from(selectedCandidates) : undefined}
          candidateName={isBulkEvaluationMode
            ? `${selectedCandidates.size} selected candidates`
            : selectedCandidate?.name || ''
          }
          isBulkMode={isBulkEvaluationMode}
          onSuccess={handleEvaluationSuccess}
        />
      )}

      {/* Add Note Modal */}
      {selectedCandidate && (
        <AddNoteModal
          isOpen={isAddNoteModalOpen}
          onClose={() => setIsAddNoteModalOpen(false)}
          candidateId={selectedCandidate.id}
          candidateName={selectedCandidate.name}
          onSuccess={() => {
            // Refresh candidates after adding a note
            const fetchCandidates = async () => {
              try {
                setLoading(true);
                const candidatesData = await getCandidates();

                // Process the candidates data to match our UI needs
                const enhancedCandidates = candidatesData.map(candidate => {
                  // Same processing as in the useEffect
                  // ... (processing code omitted for brevity)
                  return {
                    ...candidate,
                    // ... (same properties as in the useEffect)
                  };
                });

                setCandidates(enhancedCandidates);
              } catch (error) {
                console.error('Error fetching candidates:', error);
                toast({
                  title: 'Error',
                  description: 'Failed to refresh candidates.',
                  variant: 'destructive'
                });
              } finally {
                setLoading(false);
              }
            };

            fetchCandidates();
          }}
        />
      )}

      {/* Schedule Interview Modal */}
      {selectedCandidate && (
        <ScheduleInterviewModal
          isOpen={scheduleInterviewModalOpen}
          onClose={() => {
            setScheduleInterviewModalOpen(false);
            setSelectedCandidate(null);
          }}
          preselectedCandidateId={selectedCandidate.id}
          candidateName={selectedCandidate.name}
        />
      )}

      {/* Assign to Job Modal */}
      <AssignToJobModal
        isOpen={assignToJobModalOpen}
        onClose={() => {
          setAssignToJobModalOpen(false);
          setSelectedCandidate(null);
        }}
        candidateIds={selectedCandidate ? [selectedCandidate.id] : Array.from(selectedCandidates)}
        candidateNames={selectedCandidate ? [selectedCandidate.name] :
          filteredCandidates
            .filter(c => selectedCandidates.has(c.id))
            .map(c => c.name)
        }
      />

      {/* Status Change Modal */}
      <StatusChangeModal
        isOpen={statusModalOpen}
        onClose={() => setStatusModalOpen(false)}
        candidate={selectedCandidate}
        currentStatus={selectedCandidate?.status || ''}
        onStatusChange={(newStatus) => selectedCandidate && handleChangeStatus(selectedCandidate, newStatus)}
        getStatusColor={getStatusColor}
      />
    </div>
  );
};

// Wrap the CandidateManagement component with the DashboardLayout
const CandidateManagementWithLayout = () => (
  <DashboardLayout>
    <CandidateManagement />
  </DashboardLayout>
);

export default CandidateManagementWithLayout;



