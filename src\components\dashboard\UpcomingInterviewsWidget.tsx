import React from 'react';
import { format } from 'date-fns';
import { getTimezoneByValue } from '@/lib/timezones';
import { CalendarClock, ChevronRight } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useUpcomingInterviews } from '@/hooks/use-interviews';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';

const UpcomingInterviewsWidget = () => {
  const navigate = useNavigate();
  const { data: interviews, isLoading } = useUpcomingInterviews(3);

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-md font-medium">Upcoming Interviews</CardTitle>
        <CalendarClock className="h-4 w-4 text-gray-500" />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex justify-between items-start">
                <div className="space-y-1">
                  <Skeleton className="h-4 w-[150px]" />
                  <Skeleton className="h-3 w-[100px]" />
                </div>
                <Skeleton className="h-5 w-[60px]" />
              </div>
            ))}
          </div>
        ) : interviews && interviews.length > 0 ? (
          <div className="space-y-3">
            {interviews.map((interview) => (
              <div key={interview.id} className="flex justify-between items-start">
                <div className="space-y-1">
                  <p className="font-medium">{interview.candidate_name}</p>
                  <p className="text-xs text-gray-500">
                    {format(new Date(interview.scheduled_at), 'MMM d, h:mm a')}
                    {interview.timezone && (
                      <span className="ml-1">
                        ({getTimezoneByValue(interview.timezone)?.label.split(' ')[0] || interview.timezone})
                      </span>
                    )} • {interview.job_title}
                  </p>
                </div>
                <Badge
                  variant={
                    interview.interview_type === 'phone' ? 'outline' :
                    interview.interview_type === 'video' ? 'secondary' : 'default'
                  }
                  className="capitalize text-xs"
                >
                  {interview.interview_type}
                </Badge>
              </div>
            ))}
          </div>
        ) : (
          <div className="py-3 text-center text-sm text-gray-500">
            No upcoming interviews scheduled
          </div>
        )}
      </CardContent>
      <CardFooter>
        <Button 
          variant="ghost" 
          className="w-full justify-between" 
          onClick={() => navigate('/dashboard/interviews')}
        >
          <span>View all interviews</span>
          <ChevronRight className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  );
};

export default UpcomingInterviewsWidget;
