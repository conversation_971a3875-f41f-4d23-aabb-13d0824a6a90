import { supabase } from '@/lib/supabase';
import { candidateNotificationOrchestrator } from './candidateNotificationOrchestrator';

/**
 * Notification event interface
 */
interface NotificationEvent {
  id: string;
  event_type: string;
  candidate_id: string;
  application_id?: string;
  interview_id?: string;
  job_id?: string;
  old_status?: string;
  new_status?: string;
  metadata: Record<string, any>;
  processed: boolean;
  created_at: string;
}

/**
 * Notification Processor
 * Processes notification events from the database and sends appropriate emails
 */
export class NotificationProcessor {
  private isProcessing = false;
  private processingInterval: NodeJS.Timeout | null = null;

  /**
   * Start the notification processor
   */
  start(intervalMs: number = 30000): void {
    if (this.processingInterval) {
      console.log('Notification processor already running');
      return;
    }

    console.log(`Starting notification processor with ${intervalMs}ms interval`);
    this.processingInterval = setInterval(() => {
      this.processNotifications();
    }, intervalMs);

    // Process immediately on start
    this.processNotifications();
  }

  /**
   * Stop the notification processor
   */
  stop(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = null;
      console.log('Notification processor stopped');
    }
  }

  /**
   * Process pending notification events
   */
  async processNotifications(): Promise<void> {
    if (this.isProcessing) {
      console.log('Notification processing already in progress, skipping...');
      return;
    }

    this.isProcessing = true;

    try {
      // Get unprocessed notification events
      const { data: events, error } = await supabase
        .from('notification_events')
        .select('*')
        .eq('processed', false)
        .order('created_at', { ascending: true })
        .limit(50); // Process in batches

      if (error) {
        console.error('Failed to fetch notification events:', error);
        return;
      }

      if (!events || events.length === 0) {
        return; // No events to process
      }

      console.log(`Processing ${events.length} notification events`);

      // Process each event
      for (const event of events) {
        await this.processEvent(event);
      }

    } catch (error) {
      console.error('Error processing notifications:', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * Process a single notification event
   */
  private async processEvent(event: NotificationEvent): Promise<void> {
    try {
      console.log(`Processing ${event.event_type} event for candidate ${event.candidate_id}`);

      switch (event.event_type) {
        case 'application_received':
          await this.handleApplicationReceived(event);
          break;

        case 'candidate_status_changed':
          await this.handleStatusChanged(event);
          break;

        case 'interview_scheduled':
          await this.handleInterviewScheduled(event);
          break;

        default:
          console.warn(`Unknown event type: ${event.event_type}`);
          break;
      }

      // Mark event as processed
      await this.markEventProcessed(event.id);

    } catch (error) {
      console.error(`Failed to process event ${event.id}:`, error);
      
      // Mark as processed to avoid infinite retries
      // In production, you might want to implement retry logic with exponential backoff
      await this.markEventProcessed(event.id, false);
    }
  }

  /**
   * Handle application received event
   */
  private async handleApplicationReceived(event: NotificationEvent): Promise<void> {
    if (!event.application_id || !event.job_id) {
      console.error('Missing application_id or job_id for application_received event');
      return;
    }

    await candidateNotificationOrchestrator.handleApplicationReceived(
      event.candidate_id,
      event.job_id,
      event.application_id
    );
  }

  /**
   * Handle status changed event
   */
  private async handleStatusChanged(event: NotificationEvent): Promise<void> {
    if (!event.old_status || !event.new_status) {
      console.error('Missing status information for candidate_status_changed event');
      return;
    }

    await candidateNotificationOrchestrator.handleStatusChange(
      event.candidate_id,
      event.old_status,
      event.new_status,
      {
        jobId: event.job_id,
        message: event.metadata?.message,
        nextSteps: event.metadata?.nextSteps
      }
    );
  }

  /**
   * Handle interview scheduled event
   */
  private async handleInterviewScheduled(event: NotificationEvent): Promise<void> {
    if (!event.interview_id) {
      console.error('Missing interview_id for interview_scheduled event');
      return;
    }

    await candidateNotificationOrchestrator.handleInterviewScheduled(
      event.candidate_id,
      event.interview_id
    );
  }

  /**
   * Mark event as processed
   */
  private async markEventProcessed(eventId: string, success: boolean = true): Promise<void> {
    const { error } = await supabase
      .from('notification_events')
      .update({
        processed: true,
        processed_at: new Date().toISOString(),
        metadata: success ? {} : { error: 'Processing failed' }
      })
      .eq('id', eventId);

    if (error) {
      console.error(`Failed to mark event ${eventId} as processed:`, error);
    }
  }

  /**
   * Get processing statistics
   */
  async getStats(): Promise<{
    total: number;
    processed: number;
    pending: number;
    failed: number;
  }> {
    const { data: stats } = await supabase
      .from('notification_events')
      .select('processed, metadata')
      .then(result => {
        if (result.error || !result.data) {
          return { data: [] };
        }

        const total = result.data.length;
        const processed = result.data.filter(e => e.processed && !e.metadata?.error).length;
        const failed = result.data.filter(e => e.processed && e.metadata?.error).length;
        const pending = result.data.filter(e => !e.processed).length;

        return {
          data: {
            total,
            processed,
            pending,
            failed
          }
        };
      });

    return stats || { total: 0, processed: 0, pending: 0, failed: 0 };
  }

  /**
   * Manually trigger processing (for testing)
   */
  async triggerProcessing(): Promise<void> {
    await this.processNotifications();
  }
}

// Export singleton instance
export const notificationProcessor = new NotificationProcessor();
