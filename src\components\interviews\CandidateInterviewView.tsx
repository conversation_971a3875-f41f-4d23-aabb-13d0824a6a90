import React, { useState } from 'react';
import { format } from 'date-fns';
import {
  Calendar<PERSON>lock,
  Clock,
  MapPin,
  Video,
  User,
  Briefcase,
  Building,
  FileText,
  MessageSquare,
  CheckCircle,
  AlertCircle,
  Mic,
  MicOff,
  Camera,
  CameraOff,
  ScreenShare,
  PhoneOff,
  ThumbsUp,
  ThumbsDown,
  Send
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';

interface CandidateInterviewViewProps {
  // This component doesn't need any props as it's a presentation UI
}

const CandidateInterviewView: React.FC<CandidateInterviewViewProps> = () => {
  const [activeTab, setActiveTab] = useState('interview');
  const [micEnabled, setMicEnabled] = useState(true);
  const [cameraEnabled, setCameraEnabled] = useState(true);
  const [screenShareEnabled, setScreenShareEnabled] = useState(false);
  const [chatMessage, setChatMessage] = useState('');
  const [feedbackRating, setFeedbackRating] = useState<'positive' | 'negative' | null>(null);
  const [feedbackText, setFeedbackText] = useState('');

  // Interview data for Jayesh Choolun
  const interviewData = {
    candidate: {
      name: 'Jayesh Choolun',
      email: '<EMAIL>',
      avatar: 'https://i.pravatar.cc/150?u=jayesh'
    },
    job: {
      title: 'Senior Frontend Developer',
      company: 'TechCorp Solutions',
      location: 'Remote (London preferred)'
    },
    interview: {
      date: new Date(),
      duration: 60,
      type: 'video',
      status: 'in-progress',
      interviewers: [
        { name: 'Sarah Johnson', role: 'HR Manager' },
        { name: 'Michael Chen', role: 'Tech Lead' }
      ]
    }
  };

  // Preparation questions
  const preparationQuestions = [
    'Tell us about your experience with React and TypeScript',
    'Describe a challenging project you worked on and how you overcame obstacles',
    'How do you approach testing in your frontend applications?',
    'What is your experience with state management libraries?',
    'How do you stay updated with the latest frontend technologies?'
  ];

  // Chat messages
  const chatMessages = [
    { sender: 'Sarah Johnson', message: 'Hello Jayesh, welcome to the interview!', time: '10:02 AM' },
    { sender: 'Michael Chen', message: 'Hi Jayesh, glad you could join us today.', time: '10:03 AM' },
    { sender: 'Jayesh Choolun', message: 'Thank you for having me. I\'m excited to discuss the position.', time: '10:03 AM' },
    { sender: 'Sarah Johnson', message: 'Let\'s start with a brief introduction about yourself.', time: '10:04 AM' }
  ];

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      // In a real app, this would send the message
      setChatMessage('');
    }
  };

  const handleSubmitFeedback = () => {
    // In a real app, this would submit the feedback
    alert('Feedback submitted successfully!');
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-start justify-between">
          <div>
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={interviewData.candidate.avatar} alt={interviewData.candidate.name} />
                <AvatarFallback>{interviewData.candidate.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-xl">{interviewData.candidate.name}</CardTitle>
                <div className="flex items-center mt-1 text-sm text-gray-500">
                  <User className="h-4 w-4 mr-1" />
                  <span>{interviewData.candidate.email}</span>
                </div>
              </div>
            </div>
          </div>
          <Badge
            variant="default"
            className="capitalize bg-green-500 hover:bg-green-600"
          >
            Live Interview
          </Badge>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start">
              <Briefcase className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Position</p>
                <p className="text-gray-600">{interviewData.job.title}</p>
              </div>
            </div>
            <div className="flex items-start">
              <Building className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Company</p>
                <p className="text-gray-600">{interviewData.job.company}</p>
              </div>
            </div>
            <div className="flex items-start">
              <MapPin className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Location</p>
                <p className="text-gray-600">{interviewData.job.location}</p>
              </div>
            </div>
            <div className="flex items-start">
              <CalendarClock className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Date</p>
                <p className="text-gray-600">
                  {format(interviewData.interview.date, 'EEEE, MMMM d, yyyy')}
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <Clock className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Time</p>
                <p className="text-gray-600">
                  {format(interviewData.interview.date, 'h:mm a')} ({interviewData.interview.duration} minutes)
                </p>
              </div>
            </div>
            <div className="flex items-start">
              <Video className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Type</p>
                <p className="text-gray-600 capitalize">{interviewData.interview.type} Interview</p>
              </div>
            </div>
          </div>

          <Separator />

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mt-6">
            {/* Main Interview Area - Left Side (3/4 width on large screens) */}
            <div className="lg:col-span-3 space-y-6">
              <div className="aspect-video bg-gray-900 rounded-lg flex items-center justify-center relative overflow-hidden">
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-white text-center">
                    <Video className="h-16 w-16 mx-auto mb-4 opacity-20" />
                    <p className="text-lg opacity-50">Video call in progress</p>
                  </div>
                </div>
                <div className="absolute bottom-4 right-4 flex gap-2">
                  <Avatar className="h-24 w-24 border-2 border-white">
                    <AvatarImage src={interviewData.candidate.avatar} alt={interviewData.candidate.name} />
                    <AvatarFallback>{interviewData.candidate.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                </div>
                <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
                  <Button
                    variant={micEnabled ? "default" : "secondary"}
                    size="icon"
                    onClick={() => setMicEnabled(!micEnabled)}
                  >
                    {micEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant={cameraEnabled ? "default" : "secondary"}
                    size="icon"
                    onClick={() => setCameraEnabled(!cameraEnabled)}
                  >
                    {cameraEnabled ? <Camera className="h-4 w-4" /> : <CameraOff className="h-4 w-4" />}
                  </Button>
                  <Button
                    variant={screenShareEnabled ? "default" : "secondary"}
                    size="icon"
                    onClick={() => setScreenShareEnabled(!screenShareEnabled)}
                  >
                    <ScreenShare className="h-4 w-4" />
                  </Button>
                  <Button variant="destructive" size="icon">
                    <PhoneOff className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="md:col-span-3">
                  <Card>
                    <CardHeader className="py-3">
                      <CardTitle className="text-base">Chat</CardTitle>
                    </CardHeader>
                    <CardContent className="h-48 overflow-y-auto space-y-3 pt-0">
                      {chatMessages.map((msg, index) => (
                        <div key={index} className={`flex ${msg.sender === 'Jayesh Choolun' ? 'justify-end' : 'justify-start'}`}>
                          <div className={`max-w-[80%] ${msg.sender === 'Jayesh Choolun' ? 'bg-primary text-primary-foreground' : 'bg-muted'} rounded-lg p-2`}>
                            <p className="text-xs font-medium">{msg.sender}</p>
                            <p>{msg.message}</p>
                            <p className="text-xs opacity-70 text-right">{msg.time}</p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                    <CardFooter>
                      <div className="flex w-full gap-2">
                        <Input
                          placeholder="Type a message..."
                          value={chatMessage}
                          onChange={(e) => setChatMessage(e.target.value)}
                          className="flex-1"
                        />
                        <Button onClick={handleSendMessage}>
                          <Send className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardFooter>
                  </Card>
                </div>
                <div>
                  <Card>
                    <CardHeader className="py-3">
                      <CardTitle className="text-base">Interviewers</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3 pt-0">
                      {interviewData.interview.interviewers.map((interviewer, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <Avatar className="h-8 w-8">
                            <AvatarFallback>{interviewer.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="text-sm font-medium">{interviewer.name}</p>
                            <p className="text-xs text-gray-500">{interviewer.role}</p>
                          </div>
                        </div>
                      ))}
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>

            {/* Right Sidebar (1/4 width on large screens) */}
            <div className="lg:col-span-1">
              <Tabs defaultValue="preparation" className="w-full">
                <TabsList className="grid grid-cols-2 mb-4">
                  <TabsTrigger value="preparation">Preparation</TabsTrigger>
                  <TabsTrigger value="feedback">Feedback</TabsTrigger>
                </TabsList>

                <TabsContent value="preparation" className="space-y-4">
                  <Card>
                    <CardHeader className="py-3">
                      <CardTitle className="text-sm">Questions to Prepare</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0 space-y-2">
                      <ul className="space-y-2 text-sm">
                        {preparationQuestions.map((question, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <FileText className="h-4 w-4 text-gray-500 mt-0.5 flex-shrink-0" />
                            <span>{question}</span>
                          </li>
                        ))}
                      </ul>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader className="py-3">
                      <CardTitle className="text-sm">Interview Tips</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0 space-y-2">
                      <ul className="space-y-2 text-sm">
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>Test your camera and microphone</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>Find a quiet place with good lighting</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                          <span>Have your resume ready</span>
                        </li>
                        <li className="flex items-start gap-2">
                          <AlertCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                          <span>Turn off notifications</span>
                        </li>
                      </ul>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="feedback" className="space-y-4">
                  <Card>
                    <CardHeader className="py-3">
                      <CardTitle className="text-sm">Interview Feedback</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0 space-y-4">
                      <p className="text-xs text-gray-600">
                        Please provide your feedback about the interview experience.
                      </p>

                      <div>
                        <h3 className="text-sm font-medium mb-2">Rate your experience:</h3>
                        <div className="flex gap-2">
                          <Button
                            variant={feedbackRating === 'positive' ? 'default' : 'outline'}
                            className="flex-1 h-8 text-xs"
                            onClick={() => setFeedbackRating('positive')}
                          >
                            <ThumbsUp className="h-3 w-3 mr-1" />
                            Positive
                          </Button>
                          <Button
                            variant={feedbackRating === 'negative' ? 'default' : 'outline'}
                            className="flex-1 h-8 text-xs"
                            onClick={() => setFeedbackRating('negative')}
                          >
                            <ThumbsDown className="h-3 w-3 mr-1" />
                            Needs Improvement
                          </Button>
                        </div>
                      </div>

                      <div>
                        <h3 className="text-sm font-medium mb-2">Comments:</h3>
                        <Textarea
                          placeholder="Share your thoughts..."
                          value={feedbackText}
                          onChange={(e) => setFeedbackText(e.target.value)}
                          rows={3}
                          className="text-sm"
                        />
                      </div>

                      <Button onClick={handleSubmitFeedback} className="w-full text-xs">
                        Submit Feedback
                      </Button>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default CandidateInterviewView;
