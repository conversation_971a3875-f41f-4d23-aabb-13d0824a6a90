
import { NavBar } from "@/components/NavBar";
import { useEffect } from "react";

// Import our new components
import { EnhancedSimpleHero } from "@/components/EnhancedSimpleHero";
import { ProcessFlowAnimation } from "@/components/ProcessFlowAnimation";
import { ParallaxFeatureSection } from "@/components/ParallaxFeatureSection";
import { AnimatedPricingSection } from "@/components/AnimatedPricingSection";

const Index = () => {
  useEffect(() => {
    document.body.className = "bg-recruiter-navy";
  }, []);

  return (
    <div className="min-h-screen bg-hero-gradient overflow-hidden relative">
      {/* NavBar is now positioned absolutely by the component itself */}
      <NavBar />

      {/* Enhanced Hero Section with Blurred Gradient and Floating Elements */}
      <EnhancedSimpleHero />

      {/* Process Flow Animation */}
      <ProcessFlowAnimation />

      {/* Parallax Feature Section */}
      <ParallaxFeatureSection />

      {/* Enhanced Animated Pricing Section - Now with dynamic database features! */}
      <AnimatedPricingSection />

      {/* Alternative Dynamic Pricing Section - Kept as backup */}
      {/* <DynamicPricingSection /> */}

    </div>
  );
};

export default Index;
