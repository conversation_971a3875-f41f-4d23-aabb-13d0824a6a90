import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Building,
  Briefcase,
  Calendar,
  Save,
  Camera,
  X,
  Loader2
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import * as profileService from '@/services/supabase/profiles';
import * as authService from '@/services/supabase/auth';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';

// Define user profile interface
interface UserProfileData {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  company: string;
  position: string;
  avatar: string;
  joinDate: string;
  bio: string;
  socialLinks: {
    linkedin: string;
    twitter: string;
    website: string;
  };
  preferences: {
    emailNotifications: boolean;
    newCandidateAlerts: boolean;
    jobStatusUpdates: boolean;
    marketingCommunications: boolean;
  };
}

const UserProfile = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user: authUser, updateProfile } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [userData, setUserData] = useState<UserProfileData | null>(null);

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    location: '',
    company: '',
    position: '',
    bio: '',
    linkedin: '',
    twitter: '',
    website: ''
  });

  const [preferences, setPreferences] = useState({
    emailNotifications: true,
    newCandidateAlerts: true,
    jobStatusUpdates: true,
    marketingCommunications: false
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPasswordSubmitting, setIsPasswordSubmitting] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);
  const [avatarPreview, setAvatarPreview] = useState<string | null>(null);

  // Password change state
  const [passwordData, setPasswordData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordError, setPasswordError] = useState('');

  // Fetch user profile data
  useEffect(() => {
    const fetchUserProfile = async () => {
      if (!authUser) return;

      try {
        setIsLoading(true);
        const profile = await profileService.getProfile(authUser.id);

        if (profile) {
          // Parse metadata from profile
          const metadata = profile.metadata ? JSON.parse(profile.metadata as string) : {};
          const socialLinks = metadata.socialLinks || { linkedin: '', twitter: '', website: '' };
          const preferences = metadata.preferences || {
            emailNotifications: true,
            newCandidateAlerts: true,
            jobStatusUpdates: true,
            marketingCommunications: false
          };

          // Create user data object
          const userData: UserProfileData = {
            id: authUser.id,
            name: profile.full_name || authUser.name,
            email: authUser.email,
            phone: metadata.phone || '',
            location: metadata.location || '',
            company: metadata.company || '',
            position: profile.job_title || '',
            avatar: profile.avatar_url || '',
            joinDate: profile.created_at || new Date().toISOString(),
            bio: metadata.bio || '',
            socialLinks,
            preferences
          };

          setUserData(userData);
          setFormData({
            name: userData.name,
            email: userData.email,
            phone: userData.phone,
            location: userData.location,
            company: userData.company,
            position: userData.position,
            bio: userData.bio,
            linkedin: userData.socialLinks.linkedin,
            twitter: userData.socialLinks.twitter,
            website: userData.socialLinks.website
          });

          setPreferences(userData.preferences);
        }
      } catch (error) {
        console.error('Error fetching user profile:', error);
        toast({
          title: 'Error',
          description: 'Failed to load profile data. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchUserProfile();
  }, [authUser, toast]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle avatar change
  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setAvatarFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setAvatarPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove avatar preview
  const removeAvatarPreview = () => {
    setAvatarFile(null);
    setAvatarPreview(null);
  };

  // Remove profile avatar
  const [isRemovingAvatar, setIsRemovingAvatar] = useState(false);

  const handleRemoveAvatar = async () => {
    if (!authUser || !userData) return;

    setIsRemovingAvatar(true);

    try {
      // Remove avatar from profile
      await profileService.removeAvatar(authUser.id);

      // Update auth context
      await updateProfile({
        fullName: userData.name,
        avatarUrl: ''
      });

      // Update local state
      setUserData({
        ...userData,
        avatar: ''
      });

      // Clear any preview
      setAvatarFile(null);
      setAvatarPreview(null);

      toast({
        title: 'Avatar removed',
        description: 'Your profile picture has been removed successfully.',
      });
    } catch (error) {
      console.error('Error removing avatar:', error);

      toast({
        title: 'Error',
        description: 'Failed to remove profile picture. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsRemovingAvatar(false);
    }
  };

  // Toggle preference
  const togglePreference = (key: keyof typeof preferences) => {
    setPreferences(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Handle password input change
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user types
    if (passwordError) {
      setPasswordError('');
    }
  };

  // Handle password update
  const handlePasswordUpdate = async () => {
    // Reset error
    setPasswordError('');

    // Validate passwords
    if (!passwordData.currentPassword) {
      setPasswordError('Current password is required');
      return;
    }

    if (!passwordData.newPassword) {
      setPasswordError('New password is required');
      return;
    }

    if (passwordData.newPassword.length < 6) {
      setPasswordError('New password must be at least 6 characters');
      return;
    }

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      setPasswordError('New passwords do not match');
      return;
    }

    setIsPasswordSubmitting(true);

    try {
      // First verify the current password by attempting to sign in
      try {
        await authService.signIn({
          email: userData?.email || '',
          password: passwordData.currentPassword
        });
      } catch (error) {
        console.error('Password verification error:', error);

        // Handle specific sign-in errors
        if (error instanceof Error) {
          if (error.message.includes('Invalid login credentials')) {
            setPasswordError('Current password is incorrect');
          } else if (error.message.includes('Failed to fetch') ||
                    error.message.includes('ERR_CONNECTION_CLOSED') ||
                    error.message.includes('network')) {
            setPasswordError('Connection to the server was lost. Please check your internet connection and try again.');
          } else {
            setPasswordError(error.message);
          }
        } else {
          setPasswordError('An error occurred while verifying your password');
        }

        return; // Exit early if password verification fails
      }

      // If sign in successful, update the password with retry logic
      const maxRetries = 3;
      let retryCount = 0;
      let success = false;

      while (retryCount < maxRetries && !success) {
        try {
          // Update password
          await authService.updatePassword({
            password: passwordData.newPassword
          });

          // If we get here, the update was successful
          success = true;
        } catch (error) {
          retryCount++;
          console.error(`Password update attempt ${retryCount} failed:`, error);

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          } else {
            // We've exhausted our retries, rethrow the error
            throw error;
          }
        }
      }

      // Clear form
      setPasswordData({
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      });

      toast({
        title: 'Password updated',
        description: 'Your password has been updated successfully.',
      });
    } catch (error) {
      console.error('Password update error:', error);

      // Handle specific errors
      if (error instanceof Error) {
        if (error.message.includes('Invalid login credentials')) {
          setPasswordError('Current password is incorrect');
        } else if (error.message.includes('Failed to fetch') ||
                  error.message.includes('ERR_CONNECTION_CLOSED') ||
                  error.message.includes('network')) {
          setPasswordError('Connection to the server was lost. Please check your internet connection and try again.');
        } else {
          setPasswordError(error.message);
        }
      } else {
        setPasswordError('An error occurred while updating your password');
      }
    } finally {
      setIsPasswordSubmitting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!authUser) return;

    setIsSubmitting(true);

    try {
      // Prepare metadata
      const metadata = {
        phone: formData.phone,
        location: formData.location,
        company: formData.company,
        bio: formData.bio,
        socialLinks: {
          linkedin: formData.linkedin,
          twitter: formData.twitter,
          website: formData.website
        },
        preferences
      };

      // Upload avatar if changed
      let avatarUrl = userData?.avatar || '';
      if (avatarFile) {
        try {
          avatarUrl = await profileService.uploadAvatar(authUser.id, avatarFile);
        } catch (error) {
          console.error('Error uploading avatar:', error);
          toast({
            title: 'Avatar upload failed',
            description: 'Your profile will be updated without the new avatar.',
            variant: 'destructive',
          });
        }
      }

      // Update local user data first (optimistic update)
      if (userData) {
        setUserData({
          ...userData,
          name: formData.name,
          phone: formData.phone,
          location: formData.location,
          company: formData.company,
          position: formData.position,
          bio: formData.bio,
          avatar: avatarUrl,
          socialLinks: {
            linkedin: formData.linkedin,
            twitter: formData.twitter,
            website: formData.website
          },
          preferences
        });
      }

      // Try to update profile with retry logic
      const maxRetries = 3;
      let retryCount = 0;
      let success = false;

      while (retryCount < maxRetries && !success) {
        try {
          // Update profile
          await profileService.updateProfile({
            user_id: authUser.id,
            full_name: formData.name,
            job_title: formData.position,
            avatar_url: avatarUrl,
            metadata: JSON.stringify(metadata)
          });

          // If we get here, the update was successful
          success = true;
        } catch (error) {
          retryCount++;
          console.error(`Profile update attempt ${retryCount} failed:`, error);

          if (retryCount < maxRetries) {
            // Wait before retrying (exponential backoff)
            await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
          } else {
            // We've exhausted our retries, rethrow the error
            throw error;
          }
        }
      }

      // Update auth context user data
      await updateProfile({
        fullName: formData.name,
        avatarUrl
      });

      // Clear avatar file after successful upload
      setAvatarFile(null);
      setAvatarPreview(null);

      toast({
        title: 'Profile updated',
        description: 'Your profile has been updated successfully.',
      });
    } catch (error) {
      console.error('Error updating profile:', error);

      // Provide more specific error messages based on the error type
      let errorMessage = 'There was an error updating your profile. Please try again.';

      if (error instanceof Error) {
        if (error.message.includes('Failed to fetch') ||
            error.message.includes('ERR_CONNECTION_CLOSED') ||
            error.message.includes('network')) {
          errorMessage = 'Connection to the server was lost. Please check your internet connection and try again.';
        }
      }

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-[60vh]">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-lg text-gray-600">Loading profile...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  // Show error state if user data couldn't be loaded
  if (!userData) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-[60vh]">
          <div className="flex flex-col items-center space-y-4 max-w-md text-center">
            <div className="p-4 rounded-full bg-red-100">
              <X className="h-12 w-12 text-red-500" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">Failed to load profile</h2>
            <p className="text-gray-600">We couldn't load your profile information. Please try refreshing the page or contact support if the problem persists.</p>
            <Button onClick={() => window.location.reload()}>Refresh Page</Button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-2xl font-bold text-gray-800">My Profile</h1>
        </div>

        <div className="mt-6">
            <form onSubmit={handleSubmit}>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Profile Overview */}
                <div className="lg:col-span-1">
                  <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardHeader>
                      <CardTitle className="text-gray-800 text-lg">Profile Picture</CardTitle>
                    </CardHeader>
                    <CardContent className="flex flex-col items-center">
                      <div className="flex flex-col items-center gap-4 mb-4">
                        <div className="relative inline-block">
                          <Avatar className="h-32 w-32 border-2 border-gray-200 shadow-md">
                            <AvatarImage
                              src={avatarPreview || userData.avatar}
                              alt={userData.name}
                              className="object-cover"
                            />
                            <AvatarFallback className="text-3xl bg-primary text-white">
                              {userData.name.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="absolute -bottom-2 -right-2 z-10">
                            <label
                              htmlFor="avatar-upload"
                              className="flex items-center justify-center bg-primary hover:bg-primary/90 text-white w-8 h-8 rounded-full cursor-pointer shadow-md"
                              title="Upload new picture"
                            >
                              <Camera className="h-4 w-4" />
                            </label>
                            <input
                              id="avatar-upload"
                              type="file"
                              accept="image/*"
                              className="hidden"
                              onChange={handleAvatarChange}
                            />
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          {avatarPreview ? (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="border-gray-200 text-gray-700 hover:bg-gray-100"
                              onClick={removeAvatarPreview}
                            >
                              <X className="mr-1 h-4 w-4" /> Cancel
                            </Button>
                          ) : (
                            userData.avatar && (
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                className="border-red-100 text-red-600 hover:bg-red-50"
                                onClick={handleRemoveAvatar}
                                disabled={isRemovingAvatar}
                              >
                                {isRemovingAvatar ? (
                                  <>
                                    <div className="animate-spin mr-1 h-3 w-3 border-t-2 border-b-2 border-red-600 rounded-full"></div>
                                    Removing...
                                  </>
                                ) : (
                                  <>
                                    <X className="mr-1 h-4 w-4" /> Remove Picture
                                  </>
                                )}
                              </Button>
                            )
                          )}
                        </div>
                      </div>

                      <div className="text-center">
                        <h2 className="text-xl font-bold text-gray-800">{userData.name}</h2>
                        <p className="text-gray-500">{userData.position} {userData.company ? `at ${userData.company}` : ''}</p>
                        <p className="text-gray-500 text-sm mt-1">
                          Member since {new Date(userData.joinDate).toLocaleDateString()}
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </div>

                {/* Profile Details */}
                <div className="lg:col-span-2">
                  <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardHeader>
                      <CardTitle className="text-gray-800 text-lg">Personal Information</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="name" className="text-gray-700">Full Name</Label>
                          <Input
                            id="name"
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="email" className="text-gray-700">Email</Label>
                          <Input
                            id="email"
                            name="email"
                            type="email"
                            value={formData.email}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="phone" className="text-gray-700">Phone</Label>
                          <Input
                            id="phone"
                            name="phone"
                            value={formData.phone}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="location" className="text-gray-700">Location</Label>
                          <Input
                            id="location"
                            name="location"
                            value={formData.location}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="company" className="text-gray-700">Company</Label>
                          <Input
                            id="company"
                            name="company"
                            value={formData.company}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="position" className="text-gray-700">Position</Label>
                          <Input
                            id="position"
                            name="position"
                            value={formData.position}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="bio" className="text-gray-700">Bio</Label>
                        <Textarea
                          id="bio"
                          name="bio"
                          value={formData.bio}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800 min-h-[100px]"
                          placeholder="Tell us about yourself"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 mt-6">
                    <CardHeader>
                      <CardTitle className="text-gray-800 text-lg">Social Links</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="linkedin" className="text-gray-700">LinkedIn</Label>
                        <Input
                          id="linkedin"
                          name="linkedin"
                          value={formData.linkedin}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                          placeholder="https://linkedin.com/in/username"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="twitter" className="text-gray-700">Twitter</Label>
                        <Input
                          id="twitter"
                          name="twitter"
                          value={formData.twitter}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                          placeholder="https://twitter.com/username"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="website" className="text-gray-700">Website</Label>
                        <Input
                          id="website"
                          name="website"
                          value={formData.website}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                          placeholder="https://example.com"
                        />
                      </div>
                    </CardContent>
                  </Card>

                  <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 mt-6">
                    <CardHeader>
                      <CardTitle className="text-gray-800 text-lg">Account Settings</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      {passwordError && (
                        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
                          {passwordError}
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label htmlFor="currentPassword" className="text-gray-700">Current Password</Label>
                        <Input
                          id="currentPassword"
                          name="currentPassword"
                          type="password"
                          value={passwordData.currentPassword}
                          onChange={handlePasswordChange}
                          className="bg-white border-gray-200 text-gray-800"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="newPassword" className="text-gray-700">New Password</Label>
                          <Input
                            id="newPassword"
                            name="newPassword"
                            type="password"
                            value={passwordData.newPassword}
                            onChange={handlePasswordChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="confirmPassword" className="text-gray-700">Confirm New Password</Label>
                          <Input
                            id="confirmPassword"
                            name="confirmPassword"
                            type="password"
                            value={passwordData.confirmPassword}
                            onChange={handlePasswordChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                      </div>

                      <div className="flex justify-between mt-4">
                        <Button
                          type="button"
                          className="bg-primary hover:bg-primary/90"
                          onClick={handlePasswordUpdate}
                          disabled={isPasswordSubmitting}
                        >
                          {isPasswordSubmitting ? (
                            <>
                              <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                              Updating Password...
                            </>
                          ) : (
                            <>
                              <Save className="mr-2 h-4 w-4" /> Update Password
                            </>
                          )}
                        </Button>

                        <Button
                          type="submit"
                          className="bg-primary hover:bg-primary/90"
                          disabled={isSubmitting}
                        >
                          {isSubmitting ? (
                            <>
                              <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                              Saving Profile...
                            </>
                          ) : (
                            <>
                              <Save className="mr-2 h-4 w-4" /> Save All Changes
                            </>
                          )}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </form>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default UserProfile;
