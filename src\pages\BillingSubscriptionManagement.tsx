import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { 
  Loader2, 
  FileText, 
  Building, 
  CreditCard, 
  AlertCircle, 
  CheckCircle, 
  Clock,
  RefreshCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

// Components
import BillingInfoForm from '@/components/billing/BillingInfoForm';
import InvoicesList from '@/components/billing/InvoicesList';

// Services
import * as billingService from '@/services/supabase/billing';
import * as invoiceService from '@/services/supabase/invoices';
import * as subscriptionService from '@/services/supabase/subscriptions';
import * as paymentService from '@/services/supabase/payments';

// Types
import { BillingInfo } from '@/services/supabase/billing';
import { Invoice } from '@/services/supabase/invoices';
import { PRICING } from '@/config/paypal';
import { testPaymentRecording, getPaymentHistory } from '@/utils/testPaymentRecording';

// Subscription data type
interface Subscription {
  id: string;
  subscription_id: string;
  plan_id: string;
  tier: 'starter' | 'growth' | 'pro';
  status: 'active' | 'cancelled' | 'suspended' | 'expired' | 'pending';
  start_date: string;
  end_date: string | null;
  payment_method: string;
}

// Payment data type
interface Payment {
  id: string;
  payment_id: string;
  amount: number;
  currency: string;
  status: string;
  payment_method: string;
  payment_type: 'subscription' | 'one-time';
  created_at: string;
}

const BillingSubscriptionManagement = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState(() => {
    // Check URL params for initial tab, default to subscription
    return searchParams.get('tab') || 'subscription';
  });
  const [isLoading, setIsLoading] = useState(true);
  
  // Subscription state
  const [subscription, setSubscription] = useState<Subscription | null>(null);
  const [isCancelling, setIsCancelling] = useState(false);
  
  // Billing state
  const [billingInfo, setBillingInfo] = useState<BillingInfo | null>(null);
  
  // Payment/Invoice state
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [companyInfo, setCompanyInfo] = useState<any>(null);

  // Load all data
  useEffect(() => {
    const loadAllData = async () => {
      if (!user) return;

      setIsLoading(true);
      try {
        // Load subscription data
        const activeSubscription = await subscriptionService.getActiveSubscription(user.id);
        setSubscription(activeSubscription);

        // Load billing info
        const info = await billingService.getBillingInfo(user.id);
        setBillingInfo(info);

        // Load invoices
        const userInvoices = await invoiceService.getUserInvoices(user.id);
        setInvoices(userInvoices);

        // Load payment history
        const paymentHistory = await paymentService.getPaymentHistory(user.id);
        setPayments(paymentHistory);

        // Get company info if available
        if (user.companyId) {
          // This would be replaced with your actual company service call
          // const company = await companyService.getCompany(user.companyId);
          // setCompanyInfo(company);
        }
      } catch (error) {
        console.error('Error loading data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load billing and subscription information. Please try again.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadAllData();
  }, [user, toast]);

  // Handle subscription cancellation
  const handleCancelSubscription = async () => {
    if (!user || !subscription) return;

    setIsCancelling(true);
    try {
      await subscriptionService.cancelSubscription(subscription.subscription_id, user.id);

      toast({
        title: 'Subscription cancelled',
        description: 'Your subscription has been cancelled successfully.',
      });

      // Update local state
      setSubscription({
        ...subscription,
        status: 'cancelled',
        end_date: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to cancel subscription. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsCancelling(false);
    }
  };

  // Handle tab change with URL update
  const handleTabChange = (tab: string) => {
    setActiveTab(tab);
    setSearchParams({ tab });
  };

  // Handle billing info update
  const handleBillingInfoUpdate = (info: BillingInfo) => {
    setBillingInfo(info);
  };

  // Test payment recording (for debugging)
  const handleTestPayment = async () => {
    if (!user) return;

    console.log('Testing payment recording...');
    const result = await testPaymentRecording(user.id);

    if (result.success) {
      toast({
        title: 'Test Payment Recorded',
        description: 'Test payment was recorded successfully. Check the Payment History tab.',
      });

      // Refresh payment data
      const paymentHistory = await getPaymentHistory(user.id);
      if (paymentHistory.success) {
        setPayments(paymentHistory.data || []);
      }
    } else {
      toast({
        title: 'Test Failed',
        description: 'Failed to record test payment. Check console for details.',
        variant: 'destructive',
      });
    }
  };

  // Get subscription details based on tier
  const getSubscriptionDetails = (tier: 'starter' | 'growth' | 'pro') => {
    const tierKey = tier.toUpperCase() as keyof typeof PRICING;
    return PRICING[tierKey];
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMMM d, yyyy');
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500">Active</Badge>;
      case 'cancelled':
        return <Badge className="bg-yellow-500">Cancelled</Badge>;
      case 'suspended':
        return <Badge className="bg-red-500">Suspended</Badge>;
      case 'expired':
        return <Badge className="bg-gray-500">Expired</Badge>;
      case 'pending':
        return <Badge className="bg-blue-500">Pending</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-[60vh]">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-lg text-gray-600">Loading billing and subscription information...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Billing & Subscription</h1>
            <p className="text-gray-600 mt-1">
              Manage your subscription, billing information, and payment history
            </p>
          </div>
          <div className="flex gap-2">
            <Button onClick={() => navigate('/dashboard/pricing')} variant="outline">
              View Plans
            </Button>
            <Button onClick={() => window.location.reload()} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            {process.env.NODE_ENV === 'development' && (
              <Button onClick={handleTestPayment} variant="outline" size="sm">
                Test Payment
              </Button>
            )}
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="bg-white border-b border-gray-200 w-full justify-start">
            <TabsTrigger value="subscription" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              <CreditCard className="mr-2 h-4 w-4" /> Subscription
            </TabsTrigger>
            <TabsTrigger value="billing-info" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              <Building className="mr-2 h-4 w-4" /> Billing Information
            </TabsTrigger>
            <TabsTrigger value="payment-history" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              <FileText className="mr-2 h-4 w-4" /> Payment History
            </TabsTrigger>
          </TabsList>

          {/* Subscription Tab */}
          <TabsContent value="subscription" className="mt-6">
            {subscription ? (
              <Card>
                <CardHeader>
                  <div className="flex justify-between items-center">
                    <div>
                      <CardTitle className="text-xl capitalize">{subscription.tier} Plan</CardTitle>
                      <CardDescription>
                        {getStatusBadge(subscription.status)}
                        <span className="ml-2">
                          {subscription.status === 'active'
                            ? 'Your subscription is active'
                            : subscription.status === 'cancelled'
                            ? 'Your subscription has been cancelled'
                            : `Subscription status: ${subscription.status}`}
                        </span>
                      </CardDescription>
                    </div>
                    {subscription.status === 'active' && (
                      <Button
                        variant="destructive"
                        onClick={handleCancelSubscription}
                        disabled={isCancelling}
                      >
                        {isCancelling ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Cancelling...
                          </>
                        ) : (
                          'Cancel Subscription'
                        )}
                      </Button>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Subscription Details</h3>
                        <div className="mt-2 space-y-2">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Plan</span>
                            <span className="font-medium capitalize">{subscription.tier}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Status</span>
                            <span>{getStatusBadge(subscription.status)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Start Date</span>
                            <span>{formatDate(subscription.start_date)}</span>
                          </div>
                          {subscription.end_date && (
                            <div className="flex justify-between">
                              <span className="text-gray-600">End Date</span>
                              <span>{formatDate(subscription.end_date)}</span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span className="text-gray-600">Payment Method</span>
                            <span className="capitalize">{subscription.payment_method}</span>
                          </div>
                        </div>
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-gray-500">Plan Features</h3>
                        <div className="mt-2 space-y-2">
                          {getSubscriptionDetails(subscription.tier).features.map((feature, index) => (
                            <div key={index} className="flex items-start">
                              <CheckCircle className="h-5 w-5 text-green-500 mr-2 flex-shrink-0" />
                              <span className="text-gray-600">{feature}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    {subscription.status === 'active' && (
                      <Alert>
                        <Clock className="h-4 w-4" />
                        <AlertTitle>Billing Information</AlertTitle>
                        <AlertDescription>
                          Your subscription will automatically renew on{' '}
                          {formatDate(
                            new Date(
                              new Date(subscription.start_date).setMonth(
                                new Date(subscription.start_date).getMonth() + 1
                              )
                            ).toISOString()
                          )}
                          . You can cancel anytime before this date.
                        </AlertDescription>
                      </Alert>
                    )}

                    {subscription.status === 'cancelled' && (
                      <Alert>
                        <AlertCircle className="h-4 w-4" />
                        <AlertTitle>Subscription Cancelled</AlertTitle>
                        <AlertDescription>
                          Your subscription has been cancelled. You will continue to have access until{' '}
                          {subscription.end_date ? formatDate(subscription.end_date) : 'the end of your billing period'}.
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button variant="outline" onClick={() => navigate('/dashboard/pricing')}>
                    Change Plan
                  </Button>
                  <Button variant="outline" onClick={() => navigate('/dashboard')}>
                    Back to Dashboard
                  </Button>
                </CardFooter>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>No Active Subscription</CardTitle>
                  <CardDescription>
                    You don't have an active subscription. Choose a plan to get started.
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertTitle>Limited Access</AlertTitle>
                    <AlertDescription>
                      Without an active subscription, you have limited access to features. Subscribe to a plan to unlock all features.
                    </AlertDescription>
                  </Alert>
                </CardContent>
                <CardFooter>
                  <Button onClick={() => navigate('/dashboard/pricing')} className="bg-primary-gradient text-white">
                    Choose a Plan
                  </Button>
                </CardFooter>
              </Card>
            )}
          </TabsContent>

          {/* Billing Information Tab */}
          <TabsContent value="billing-info" className="mt-6">
            {user && (
              <BillingInfoForm
                userId={user.id}
                initialData={billingInfo}
                onSaved={handleBillingInfoUpdate}
              />
            )}
          </TabsContent>

          {/* Payment History Tab */}
          <TabsContent value="payment-history" className="mt-6">
            <InvoicesList
              invoices={invoices}
              companyInfo={companyInfo}
              userInfo={user}
            />
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default BillingSubscriptionManagement;
