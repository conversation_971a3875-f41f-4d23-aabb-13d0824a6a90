import { Inbox, Plus } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "./button";

interface EmptyStateProps {
  /**
   * Title of the empty state message
   */
  title?: string;
  
  /**
   * Message to display
   */
  message?: string;
  
  /**
   * Function to call when action button is clicked
   */
  onAction?: () => void;
  
  /**
   * Text for the action button
   */
  actionText?: string;
  
  /**
   * Icon to display (defaults to Inbox)
   */
  icon?: React.ReactNode;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Whether to center the empty state
   */
  center?: boolean;
}

/**
 * A reusable empty state component
 */
export function EmptyState({
  title = "No data found",
  message = "There are no items to display.",
  onAction,
  actionText = "Create New",
  icon,
  className,
  center = true,
}: EmptyStateProps) {
  const containerClasses = cn(
    "flex flex-col items-center space-y-4 p-6 text-center",
    center && "justify-center",
    className
  );
  
  return (
    <div className={containerClasses}>
      <div className="p-4 rounded-full bg-muted">
        {icon || <Inbox className="h-8 w-8 text-muted-foreground" />}
      </div>
      <h3 className="text-lg font-semibold">{title}</h3>
      <p className="text-sm text-muted-foreground max-w-md">{message}</p>
      {onAction && (
        <Button onClick={onAction} variant="outline" className="mt-2">
          <Plus className="h-4 w-4 mr-2" />
          {actionText}
        </Button>
      )}
    </div>
  );
}
