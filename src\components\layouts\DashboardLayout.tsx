import React, { useState, useEffect } from 'react';
import { Outlet } from 'react-router-dom';
import { DashboardSidebar } from './DashboardSidebar';
import { DashboardHeader } from './DashboardHeader';
import { cn } from '@/lib/utils';

export const DashboardLayout: React.FC<{ children?: React.ReactNode }> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // Add effect to fix iframe heights
  useEffect(() => {
    // Function to set iframe heights
    const fixIframeHeights = () => {
      // Target the msdoc-iframe
      const msdocIframe = document.getElementById('msdoc-iframe');
      if (msdocIframe) {
        msdocIframe.style.height = '45em';
        msdocIframe.style.minHeight = '45em';
      }

      // Target all PDF embeds
      const pdfEmbeds = document.querySelectorAll('embed[type="application/pdf"]');
      pdfEmbeds.forEach(embed => {
        (embed as HTMLElement).style.height = '45em';
        (embed as HTMLElement).style.minHeight = '45em';
      });

      // Target PDF.js elements
      const pdfDocs = document.querySelectorAll('.react-pdf__Document, .react-pdf__Page');
      pdfDocs.forEach(doc => {
        (doc as HTMLElement).style.height = '45em';
        (doc as HTMLElement).style.minHeight = '45em';
      });
    };

    // Run immediately
    fixIframeHeights();

    // Set up a MutationObserver to watch for DOM changes
    const observer = new MutationObserver((mutations) => {
      fixIframeHeights();
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });

    // Clean up
    return () => {
      observer.disconnect();
    };
  }, []);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col w-full overflow-x-hidden dashboard-zoom" style={{ height: '133.33vh' }}>
      <DashboardHeader toggleSidebar={toggleSidebar} />

      <div className="flex flex-1 overflow-hidden w-full h-full">
        <DashboardSidebar open={sidebarOpen} />

        <main
          className={cn(
            "flex-1 overflow-y-auto transition-all duration-300 ease-in-out h-full bg-gray-50",
            sidebarOpen ? "md:ml-64" : "md:ml-20"
          )}
        >
          <div className="w-full px-4 sm:px-6 py-8 animate-fade-in min-h-full bg-gray-50">
            {children || <Outlet />}
          </div>
        </main>
      </div>
    </div>
  );
};
