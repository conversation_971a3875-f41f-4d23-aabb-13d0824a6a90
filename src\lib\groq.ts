import OpenAI from 'openai';
import { env } from '@/lib/env';
import { getUserFriendlyErrorMessage } from '@/lib/apiRetry';
import {
  parseCV as parseCVUnified,
  matchCandidateToJob as matchCandidateToJobUnified,
  extractSkills as extractSkillsUnified,
  rankCandidatesForJob as rankCandidatesForJobUnified
} from '@/lib/ai/operations';

// Note: createAIClient function moved to centralized operations

/**
 * Legacy GROQ client for backward compatibility
 * Uses environment variables validated by the env utility
 */
export const groq = new OpenAI({
  apiKey: env.GROQ_API_KEY,
  baseURL: 'https://api.groq.com/openai/v1', // GROQ's OpenAI-compatible endpoint
  dangerouslyAllowBrowser: true, // Enable browser usage (for development only)
});

// Default model to use from environment or fallback
export const DEFAULT_MODEL = env.GROQ_MODEL || 'llama-3.3-70b-versatile';

// Legacy exports for backward compatibility - now using centralized system
export { AI_PROMPTS } from '@/lib/ai/prompts';
export { AI_SCHEMAS } from '@/lib/ai/schemas';
import { AI_PROMPTS } from '@/lib/ai/prompts';

// Backward compatibility: Export individual prompts
export const CV_PARSING_SYSTEM_PROMPT = AI_PROMPTS.CV_PARSING;

export const JOB_CANDIDATE_MATCHING_SYSTEM_PROMPT = AI_PROMPTS.JOB_MATCHING;

export const SKILL_EXTRACTION_SYSTEM_PROMPT = AI_PROMPTS.SKILL_EXTRACTION;

export const SCORING_RANKING_SYSTEM_PROMPT = AI_PROMPTS.CANDIDATE_RANKING;

// Function to parse a CV - now uses centralized operations
export async function parseCV(cvText: string) {
  return parseCVUnified(cvText);
}

// Function to match a candidate to a job - now uses centralized operations
export async function matchCandidateToJob(candidateProfile: any, jobDescription: string, coverLetterContent?: string) {
  try {
    return await matchCandidateToJobUnified(candidateProfile, jobDescription, coverLetterContent);
  } catch (error) {
    console.error('Error in matchCandidateToJob:', error);

    // Get user-friendly error message
    const userMessage = getUserFriendlyErrorMessage(error);

    // Return a fallback object for backward compatibility
    return {
      overallScore: 0,
      skillsMatch: { score: 0, analysis: userMessage, skills: [] },
      experienceMatch: { score: 0, analysis: userMessage, details: [] },
      educationMatch: { score: 0, analysis: userMessage, details: [] },
      locationMatch: { score: 0, analysis: userMessage },
      strengths: ['Unable to determine due to API error'],
      gaps: ['Unable to determine due to API error'],
      recommendation: `Unable to provide recommendation: ${userMessage}`,
      error: error.message || 'Unknown API error',
      userMessage: userMessage,
      isRateLimitError: error.status === 429 || (error.message && error.message.toLowerCase().includes('rate limit'))
    };
  }
}

// Function to extract skills from text - now uses centralized operations
export async function extractSkills(text: string) {
  return extractSkillsUnified(text);
}

// Function to score and rank candidates - now uses centralized operations
export async function scoreCandidates(jobDescription: string, candidates: any[]) {
  return rankCandidatesForJobUnified(candidates, jobDescription);
}



