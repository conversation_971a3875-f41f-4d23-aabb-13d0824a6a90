import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

export type Notification = Database['public']['Tables']['notifications']['Row'];
export type NotificationInsert = Database['public']['Tables']['notifications']['Insert'];
export type NotificationUpdate = Database['public']['Tables']['notifications']['Update'];

/**
 * Get all notifications for the current user
 */
export const getNotifications = async (): Promise<Notification[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      return data || [];
    },
    'Failed to fetch notifications',
    [] // Return empty array as fallback
  );
};

/**
 * Get unread notifications count
 */
export const getUnreadNotificationsCount = async (): Promise<number> => {
  return safeDbOperation(
    async () => {
      const { count, error } = await supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('read', false);

      if (error) throw error;

      return count || 0;
    },
    'Failed to fetch unread notifications count',
    0 // Return 0 as fallback
  );
};

/**
 * Create a new notification
 */
export const createNotification = async (notification: NotificationInsert): Promise<Notification> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('notifications')
        .insert(notification)
        .select()
        .single();

      if (error) throw error;

      return data;
    },
    'Failed to create notification'
  );
};

/**
 * Mark a notification as read
 */
export const markNotificationAsRead = async (id: string): Promise<Notification> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return data;
    },
    `Failed to mark notification ${id} as read`
  );
};

/**
 * Mark all notifications as read
 */
export const markAllNotificationsAsRead = async (userId: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userId)
        .eq('read', false);

      if (error) throw error;

      return true;
    },
    `Failed to mark all notifications as read for user ${userId}`,
    false // Return false as fallback
  );
};

/**
 * Delete a notification
 */
export const deleteNotification = async (id: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return true;
    },
    `Failed to delete notification ${id}`,
    false // Return false as fallback
  );
};

/**
 * Delete all notifications
 */
export const deleteAllNotifications = async (userId: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('notifications')
        .delete()
        .eq('user_id', userId);

      if (error) throw error;

      return true;
    },
    `Failed to delete all notifications for user ${userId}`,
    false // Return false as fallback
  );
};

/**
 * Subscribe to real-time notifications
 */
export const subscribeToNotifications = (userId: string, callback: (notification: Notification) => void) => {
  return supabase
    .channel('public:notifications')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'notifications',
        filter: `user_id=eq.${userId}`
      },
      (payload) => {
        callback(payload.new as Notification);
      }
    )
    .subscribe();
};
