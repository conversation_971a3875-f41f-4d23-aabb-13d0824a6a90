import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import { corsHeaders } from '../_shared/cors.ts'

// Helper function to get Stripe client
function getStripeClient() {
  const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY')
  
  if (!stripeSecretKey) {
    throw new Error('Stripe secret key is not configured')
  }
  
  return {
    secretKey: stripeSecretKey,
    baseUrl: 'https://api.stripe.com/v1',
    headers: {
      'Authorization': `Bearer ${stripeSecretKey}`,
      'Content-Type': 'application/x-www-form-urlencoded',
      'Stripe-Version': '2023-10-16'
    }
  }
}

// Helper function to make Stripe API requests
async function makeStripeRequest(endpoint: string, body: URLSearchParams) {
  const stripe = getStripeClient()
  const url = `${stripe.baseUrl}${endpoint}`
  
  const response = await fetch(url, {
    method: 'POST',
    headers: stripe.headers,
    body: body
  })
  
  const data = await response.json()
  
  if (!response.ok) {
    throw new Error(data.error?.message || 'Stripe API request failed')
  }
  
  return data
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    })
  }

  try {
    // Create a Supabase client with service role for customer account operations
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    const { priceId, userId, tier, successUrl, cancelUrl } = await req.json()

    if (!priceId || !userId || !tier || !successUrl || !cancelUrl) {
      throw new Error('Missing required parameters')
    }

    // Get or create Stripe customer using scalable customer_accounts table
    let customerId: string

    // Check if user already has a Stripe customer account
    const { data: existingCustomer, error: customerError } = await supabaseAdmin
      .from('customer_accounts')
      .select('customer_id, customer_email, customer_name')
      .eq('user_id', userId)
      .eq('provider', 'stripe')
      .eq('is_active', true)
      .single()

    if (existingCustomer && !customerError) {
      customerId = existingCustomer.customer_id
    } else {
      // Get user profile for customer creation
      const { data: profile, error: profileError } = await supabaseAdmin
        .from('profiles')
        .select('email, full_name')
        .eq('user_id', userId)
        .single()

      if (profileError) {
        throw new Error('Failed to fetch user profile')
      }

      // Create new Stripe customer
      const customerData = new URLSearchParams()
      customerData.append('email', profile.email || '')
      customerData.append('name', profile.full_name || '')
      customerData.append('metadata[user_id]', userId)

      const customer = await makeStripeRequest('/customers', customerData)
      customerId = customer.id

      // Store Stripe customer ID in customer_accounts table
      const { error: updateError } = await supabaseAdmin
        .from('customer_accounts')
        .insert({
          user_id: userId,
          provider: 'stripe',
          customer_id: customerId,
          customer_email: profile.email,
          customer_name: profile.full_name,
          is_active: true,
          metadata: {
            created_via: 'stripe-checkout',
            stripe_customer_object: customer
          }
        })

      if (updateError) {
        console.error('Failed to store Stripe customer ID:', updateError)
        console.error('User ID:', userId)
        console.error('Customer ID:', customerId)
        console.error('Profile data:', profile)
        // Don't throw here, continue with the checkout process
      } else {
        console.log('Successfully stored Stripe customer ID:', customerId, 'for user:', userId)
      }
    }

    // Create checkout session
    const sessionData = new URLSearchParams()
    sessionData.append('customer', customerId)
    sessionData.append('mode', 'subscription')
    sessionData.append('line_items[0][price]', priceId)
    sessionData.append('line_items[0][quantity]', '1')
    sessionData.append('success_url', successUrl)
    sessionData.append('cancel_url', cancelUrl)
    sessionData.append('metadata[user_id]', userId)
    sessionData.append('metadata[tier]', tier)
    sessionData.append('subscription_data[metadata][user_id]', userId)
    sessionData.append('subscription_data[metadata][tier]', tier)

    const session = await makeStripeRequest('/checkout/sessions', sessionData)

    return new Response(JSON.stringify({ 
      status: 'success',
      sessionId: session.id,
      url: session.url 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })

  } catch (error) {
    console.error('Error creating Stripe checkout session:', error)
    
    return new Response(JSON.stringify({ 
      status: 'error',
      error: error.message 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})
