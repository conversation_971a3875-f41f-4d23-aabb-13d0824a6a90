import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle } from 'lucide-react';

interface StatusChangeModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidate: any;
  currentStatus: string;
  onStatusChange: (status: string) => void;
  getStatusColor: (status: string) => string;
}

const StatusChangeModal: React.FC<StatusChangeModalProps> = ({
  isOpen,
  onClose,
  candidate,
  currentStatus,
  onStatusChange,
  getStatusColor
}) => {
  const statuses = [
    { value: 'new', label: 'New', color: 'text-blue-500' },
    { value: 'screening', label: 'Screening', color: 'text-amber-500' },
    { value: 'interview', label: 'Interview', color: 'text-purple-500' },
    { value: 'offer', label: 'Offer', color: 'text-green-500' },
    { value: 'hired', label: 'Hired', color: 'text-green-700' },
    { value: 'rejected', label: 'Rejected', color: 'text-red-500' }
  ];

  const handleStatusSelect = (status: string) => {
    onStatusChange(status);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Change Status for {candidate?.name}</DialogTitle>
        </DialogHeader>
        <div className="space-y-3">
          <div className="text-sm text-gray-600 mb-4">
            Current status: <Badge className={getStatusColor(currentStatus)}>{currentStatus}</Badge>
          </div>
          <div className="grid grid-cols-2 gap-2">
            {statuses.map((status) => (
              <Button
                key={status.value}
                variant="outline"
                className={`justify-start h-auto p-3 ${
                  currentStatus === status.value 
                    ? `${getStatusColor(status.value)} border-current` 
                    : 'hover:bg-gray-50'
                }`}
                onClick={() => handleStatusSelect(status.value)}
                disabled={currentStatus === status.value}
              >
                <CheckCircle className={`mr-2 h-4 w-4 ${status.color}`} />
                <Badge className={getStatusColor(status.value)}>{status.label}</Badge>
              </Button>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default StatusChangeModal;
