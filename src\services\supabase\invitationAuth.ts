import { supabase } from '@/lib/supabase';

export interface InvitationSignupData {
  email: string;
  fullName: string;
  invitationToken: string;
}

/**
 * Create account for invited user without email verification
 */
export const signUpFromInvitation = async ({ email, fullName, invitationToken }: InvitationSignupData) => {
  // Generate a temporary password that user will change later
  const tempPassword = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  
  const { data, error } = await supabase.auth.signUp({
    email,
    password: tempPassword,
    options: {
      data: {
        full_name: fullName,
        invitation_token: invitationToken,
        needs_password_setup: true,
      },
      emailRedirectTo: undefined, // Skip email verification
    },
  });

  if (error) {
    throw error;
  }

  // For invited users, we need to manually confirm their email via edge function
  if (data.user) {
    try {
      // Call edge function to confirm email for invited users
      const { data: { session } } = await supabase.auth.getSession();
      
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/confirm-invited-user`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: data.user.id,
          email: email
        }),
      });

      if (!response.ok) {
        console.warn('Failed to auto-confirm email via edge function');
      }

      // Now try to sign in the user
      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email,
        password: tempPassword,
      });

      if (signInError) {
        console.error('Auto sign-in failed:', signInError);
        // If still failing, we'll return the user data and handle it differently
        return { 
          ...data, 
          tempPassword,
          needsManualConfirmation: true
        };
      }

      return { 
        ...data, 
        tempPassword,
        session: signInData?.session 
      };
    } catch (signInError) {
      console.error('Auto sign-in error:', signInError);
      return { 
        ...data, 
        tempPassword,
        needsManualConfirmation: true
      };
    }
  }

  return { ...data, tempPassword };
};

/**
 * Get invitation details by token
 */
export const getInvitationDetails = async (token: string) => {
  try {
    // First get the team member record
    const { data: teamMember, error: teamMemberError } = await supabase
      .from('team_members')
      .select('*')
      .eq('invitation_token', token)
      .eq('status', 'pending')
      .single();

    if (teamMemberError) {
      console.error('Team member query error:', teamMemberError);
      throw teamMemberError;
    }

    if (!teamMember) {
      throw new Error('Invitation not found');
    }

    // Get company details
    let companyData = null;
    if (teamMember.company_id) {
      const { data: company, error: companyError } = await supabase
        .from('companies')
        .select('name')
        .eq('id', teamMember.company_id)
        .single();

      if (!companyError && company) {
        companyData = company;
      }
    }

    // Get inviter details
    let inviterData = null;
    if (teamMember.invited_by) {
      const { data: inviter, error: inviterError } = await supabase
        .from('profiles')
        .select('full_name')
        .eq('user_id', teamMember.invited_by)
        .single();

      if (!inviterError && inviter) {
        inviterData = inviter;
      }
    }

    // Combine the data
    return {
      ...teamMember,
      companies: companyData,
      profiles: inviterData
    };

  } catch (error) {
    console.error('Failed to get invitation details:', error);
    throw error;
  }
};




