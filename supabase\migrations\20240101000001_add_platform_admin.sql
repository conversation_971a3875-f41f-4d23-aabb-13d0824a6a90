-- Add platform_admin field to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS platform_admin BOOLEAN DEFAULT FALSE;

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_profiles_platform_admin ON public.profiles(platform_admin);

-- Add comment for documentation
COMMENT ON COLUMN public.profiles.platform_admin IS 'Indicates if the user is a platform administrator with special privileges';

-- Update existing admin account
UPDATE public.profiles
SET platform_admin = TRUE
WHERE user_id IN (
  SELECT id FROM auth.users WHERE email = '<EMAIL>'
);

-- Create policy to prevent users from setting themselves as platform admins
CREATE OR REPLACE FUNCTION check_platform_admin_update()
RETURNS TRIGGER AS $$
BEGIN
  -- Allow if user is a platform admin
  IF EXISTS (SELECT 1 FROM public.profiles WHERE user_id = auth.uid() AND platform_admin = TRUE) THEN
    RETURN NEW;
  END IF;

  -- Allow if not changing platform_admin field
  IF OLD.platform_admin = NEW.platform_admin THEN
    RETURN NEW;
  END IF;

  -- Otherwise deny
  RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER check_platform_admin_update_trigger
BEFORE UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION check_platform_admin_update();

-- Create policy to prevent non-admin users from updating platform_admin field
CREATE POLICY "Only platform admins can update profiles"
ON public.profiles
FOR UPDATE
USING (
  auth.uid() IN (SELECT user_id FROM public.profiles WHERE platform_admin = TRUE) OR
  auth.uid() = user_id
);
