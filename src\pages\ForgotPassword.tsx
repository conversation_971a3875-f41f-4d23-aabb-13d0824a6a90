import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { ArrowRight, Mail, ArrowLeft } from 'lucide-react';

// Define form schema with Zod
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

type FormValues = z.infer<typeof formSchema>;

const ForgotPassword = () => {
  const { forgotPassword } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  // Initialize form with react-hook-form and zod validation
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);

    try {
      await forgotPassword(data.email);
      setIsSubmitted(true);
      toast({
        title: 'Reset link sent',
        description: 'Check your email for a link to reset your password',
      });
    } catch (error) {
      toast({
        title: 'Request failed',
        description: error instanceof Error ? error.message : 'Please check your email and try again',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-hero-gradient flex flex-col">
      {/* Header with logo */}
      <header className="container mx-auto px-6 py-5">
        <Link to="/" className="flex items-center">
          <img src="/logo.png" alt="RecruitAI Logo" className="h-10" />
        </Link>
      </header>

      {/* Main content */}
      <div className="flex-1 flex items-center justify-center px-6 py-12">
        <div className="w-full max-w-md space-y-8 bg-card-gradient p-8 rounded-xl border border-gray-800 shadow-xl">
          {!isSubmitted ? (
            <>
              <div className="text-center">
                <h1 className="text-3xl font-bold text-white">Reset your password</h1>
                <p className="mt-2 text-gray-400">
                  Enter your email address and we'll send you a link to reset your password
                </p>
              </div>

              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-white">Email</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                            <Input
                              placeholder="<EMAIL>"
                              className="pl-10 bg-[#1a2035] border-gray-700 text-white"
                              {...field}
                            />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="submit"
                    className="w-full bg-recruiter-lightblue hover:bg-blue-500"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <div className="flex items-center">
                        <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                        Sending reset link...
                      </div>
                    ) : (
                      <div className="flex items-center">
                        Send Reset Link <ArrowRight className="ml-2 h-4 w-4" />
                      </div>
                    )}
                  </Button>
                </form>
              </Form>
            </>
          ) : (
            <div className="text-center space-y-6">
              <div className="mx-auto w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center">
                <Mail className="h-8 w-8 text-green-500" />
              </div>
              <h2 className="text-2xl font-bold text-white">Check your email</h2>
              <p className="text-gray-400">
                We've sent a password reset link to your email address. Please check your inbox.
              </p>
              <Button
                variant="outline"
                className="mt-4 border-gray-700 text-white hover:bg-[#2a2f3d]"
                onClick={() => setIsSubmitted(false)}
              >
                <ArrowLeft className="mr-2 h-4 w-4" /> Try another email
              </Button>
            </div>
          )}

          <div className="mt-6 text-center">
            <p className="text-gray-400">
              Remember your password?{' '}
              <Link to="/login" className="text-recruiter-blue hover:text-blue-400 transition">
                Back to login
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
