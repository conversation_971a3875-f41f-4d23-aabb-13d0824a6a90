import React, { useState, useEffect } from 'react';
import { Loader2, FileText, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import PDFViewer from './PDFViewer';
import DocxViewer from './DocxViewer';

interface DocumentViewerProps {
  url: string;
  fileName: string;
  onPageCountChange?: (pageCount: number) => void;
}

const DocumentViewer: React.FC<DocumentViewerProps> = ({ url, fileName, onPageCountChange }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fileType, setFileType] = useState<'pdf' | 'docx' | 'doc' | 'unknown'>('unknown');

  useEffect(() => {
    // Log the URL for debugging
    console.log('DocumentViewer received URL:', url);

    // Check if the URL is valid
    if (!url || url === '/mock-cv-preview.pdf' || url.includes('fallback-storage.example.com')) {
      setError('No valid document URL available');
      setIsLoading(false);
      return;
    }

    // Determine file type based on extension
    const extension = url.split('.').pop()?.toLowerCase();
    if (extension === 'pdf') {
      setFileType('pdf');
    } else if (extension === 'docx') {
      setFileType('docx');
    } else if (extension === 'doc') {
      setFileType('doc');
    } else {
      setFileType('unknown');
    }

    // Create a test fetch to check if the URL is accessible
    const testFetch = async () => {
      try {
        const response = await fetch(url, { method: 'HEAD' });
        if (!response.ok) {
          setError(`Unable to access document (${response.status}: ${response.statusText})`);
        }
      } catch (err) {
        setError(`Error accessing document: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setIsLoading(false);
      }
    };

    testFetch();
  }, [url]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p>Loading document...</p>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-2" />
        <p className="text-red-500 mb-2">{error}</p>
        <p className="mb-4">The document might be inaccessible due to CORS restrictions or storage permissions.</p>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Open Document in New Tab
        </a>
      </div>
    );
  }

  // For PDF files, use PDFViewer
  if (fileType === 'pdf') {
    return (
      <div className="h-full w-full">
        <PDFViewer url={url} fileName={fileName} onPageCountChange={onPageCountChange} />
      </div>
    );
  }

  // For DOCX files, use DocxViewer
  if (fileType === 'docx') {
    return (
      <div className="h-full w-full">
        <DocxViewer url={url} fileName={fileName} />
      </div>
    );
  }

  // For DOC files, use DocxViewer (it can handle both DOCX and DOC)
  if (fileType === 'doc') {
    return (
      <div className="h-full w-full">
        <DocxViewer url={url} fileName={fileName} />
      </div>
    );
  }

  // For unknown file types, show a preview message with options to download or open
  return (
    <div className="flex flex-col items-center justify-center h-full p-4 text-center">
      <FileText className="h-16 w-16 text-blue-500 mb-4" />
      <h3 className="text-lg font-medium mb-2">
        Unknown Document Type
      </h3>
      <p className="mb-6 text-gray-600">
        {fileName || 'Document'} cannot be previewed directly in the browser.
      </p>
      <div className="flex gap-4">
        <a
          href={url}
          download={fileName}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Download Document
        </a>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-100"
        >
          Open in New Tab
        </a>
      </div>
      <p className="mt-4 text-sm text-gray-500">
        Tip: You can use Microsoft Office Online, Google Docs, or other online document viewers to view this file.
      </p>
    </div>
  );
};

export default DocumentViewer;
