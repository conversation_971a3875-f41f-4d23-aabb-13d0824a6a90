import React from 'react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from '@/components/ui/select';
import { TIMEZONES, getTimezonesByRegion, getUserTimezone } from '@/lib/timezones';

interface TimezoneSelectProps {
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  className?: string;
}

export const TimezoneSelect: React.FC<TimezoneSelectProps> = ({
  value,
  onValueChange,
  placeholder = "Select timezone",
  disabled = false,
  className
}) => {
  const timezonesByRegion = getTimezonesByRegion();
  const userTimezone = getUserTimezone();

  // If no value is set, try to use the user's detected timezone
  const currentValue = value || (TIMEZONES.find(tz => tz.value === userTimezone)?.value);

  return (
    <Select
      value={currentValue}
      onValueChange={onValueChange}
      disabled={disabled}
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent className="max-h-[300px]">
        {/* Show user's detected timezone first if it's not already selected */}
        {userTimezone && !value && (
          <>
            <SelectGroup>
              <SelectLabel>Detected Timezone</SelectLabel>
              {TIMEZONES.filter(tz => tz.value === userTimezone).map((timezone) => (
                <SelectItem key={`detected-${timezone.value}`} value={timezone.value}>
                  <div className="flex flex-col">
                    <span className="font-medium">{timezone.label}</span>
                    <span className="text-xs text-muted-foreground">{timezone.offset}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectGroup>
          </>
        )}
        
        {/* Group timezones by region */}
        {Object.entries(timezonesByRegion).map(([region, timezones]) => (
          <SelectGroup key={region}>
            <SelectLabel>{region}</SelectLabel>
            {timezones.map((timezone) => (
              <SelectItem key={timezone.value} value={timezone.value}>
                <div className="flex flex-col">
                  <span className="font-medium">{timezone.label}</span>
                  <span className="text-xs text-muted-foreground">{timezone.offset}</span>
                </div>
              </SelectItem>
            ))}
          </SelectGroup>
        ))}
      </SelectContent>
    </Select>
  );
};

export default TimezoneSelect;
