import React from 'react';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface ColorfulIconProps {
  icon: LucideIcon;
  color: 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'danger' | 'purple';
  size?: number;
  className?: string;
}

export const ColorfulIcon: React.FC<ColorfulIconProps> = ({
  icon: Icon,
  color,
  size = 20,
  className,
}) => {
  const gradientMap = {
    primary: 'bg-primary-gradient',
    secondary: 'bg-secondary-gradient',
    success: 'bg-success-gradient',
    info: 'bg-info-gradient',
    warning: 'bg-warning-gradient',
    danger: 'bg-danger-gradient',
    purple: 'bg-purple-gradient',
  };

  return (
    <div
      className={cn(
        'flex items-center justify-center rounded-md p-1.5',
        gradientMap[color],
        className
      )}
    >
      <Icon size={size} className="text-white" />
    </div>
  );
};
