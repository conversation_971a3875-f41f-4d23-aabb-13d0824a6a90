import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useJobs } from '@/hooks/use-jobs';
import { useCompanyContext } from '@/contexts/CompanyContext';
import { useCreateApplication, useCreateBulkApplications } from '@/hooks/use-applications';
import { Loader2, Users, Briefcase } from 'lucide-react';

interface AssignToJobModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidateIds: string[];
  candidateNames: string[];
}

const AssignToJobModal: React.FC<AssignToJobModalProps> = ({
  isOpen,
  onClose,
  candidateIds,
  candidateNames
}) => {
  const [selectedJobId, setSelectedJobId] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const { activeCompanyId } = useCompanyContext();
  
  // Fetch jobs for the active company
  const { data: jobs = [], isLoading: jobsLoading } = useJobs(activeCompanyId);
  
  // Mutations for creating applications
  const createApplication = useCreateApplication();
  const createBulkApplications = useCreateBulkApplications();
  
  const isSingleCandidate = candidateIds.length === 1;
  const isLoading = createApplication.isPending || createBulkApplications.isPending;

  const handleSubmit = async () => {
    if (!selectedJobId) {
      return;
    }

    try {
      if (isSingleCandidate) {
        await createApplication.mutateAsync({
          candidate_id: candidateIds[0],
          job_id: selectedJobId,
          notes: notes.trim() || undefined
        });
      } else {
        await createBulkApplications.mutateAsync({
          candidate_ids: candidateIds,
          job_id: selectedJobId,
          notes: notes.trim() || undefined
        });
      }
      
      // Reset form and close modal
      setSelectedJobId('');
      setNotes('');
      onClose();
    } catch (error) {
      // Error handling is done in the mutation hooks
      console.error('Error assigning candidates to job:', error);
    }
  };

  const handleClose = () => {
    setSelectedJobId('');
    setNotes('');
    onClose();
  };

  const selectedJob = jobs.find(job => job.id === selectedJobId);

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Briefcase className="h-5 w-5" />
            Assign to Job
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Candidate Info */}
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="flex items-center gap-2 text-sm text-gray-600 mb-1">
              <Users className="h-4 w-4" />
              {isSingleCandidate ? 'Candidate' : 'Candidates'}
            </div>
            <div className="text-sm font-medium">
              {isSingleCandidate 
                ? candidateNames[0] 
                : `${candidateIds.length} candidates selected`
              }
            </div>
            {!isSingleCandidate && candidateNames.length <= 3 && (
              <div className="text-xs text-gray-500 mt-1">
                {candidateNames.join(', ')}
              </div>
            )}
          </div>

          {/* Job Selection */}
          <div className="space-y-2">
            <Label htmlFor="job-select">Select Job</Label>
            {jobsLoading ? (
              <div className="flex items-center justify-center p-4">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span className="ml-2 text-sm text-gray-500">Loading jobs...</span>
              </div>
            ) : jobs.length === 0 ? (
              <div className="text-sm text-gray-500 p-4 text-center">
                No jobs available for the selected company
              </div>
            ) : (
              <Select value={selectedJobId} onValueChange={setSelectedJobId}>
                <SelectTrigger>
                  <SelectValue placeholder="Choose a job position" />
                </SelectTrigger>
                <SelectContent>
                  {jobs.map((job) => (
                    <SelectItem key={job.id} value={job.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{job.title}</span>
                        <span className="text-xs text-gray-500">
                          {job.companies?.name} • {job.location}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
          </div>

          {/* Selected Job Info */}
          {selectedJob && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-blue-900">
                {selectedJob.title}
              </div>
              <div className="text-xs text-blue-700 mt-1">
                {selectedJob.companies?.name} • {selectedJob.location} • {selectedJob.job_type}
              </div>
            </div>
          )}

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              placeholder="Add any notes about this assignment..."
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit} 
            disabled={!selectedJobId || isLoading}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isSingleCandidate ? 'Assign Candidate' : `Assign ${candidateIds.length} Candidates`}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AssignToJobModal;
