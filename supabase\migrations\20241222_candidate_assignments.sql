-- Create candidate_assignments table for tracking candidate-job assignments
-- This table manages the relationship between candidates and jobs after evaluation

-- Drop existing table if it exists (for development)
DROP TABLE IF EXISTS public.candidate_assignments;

-- Create candidate_assignments table
CREATE TABLE public.candidate_assignments (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  candidate_id uuid NOT NULL REFERENCES public.candidates(id) ON DELETE CASCADE,
  job_id uuid NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
  company_id uuid NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  match_score numeric NOT NULL DEFAULT 0 CHECK (match_score >= 0 AND match_score <= 100),
  status text NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'screening', 'interview', 'offer', 'hired', 'rejected')),
  assigned_by uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  notes text,
  PRIMARY KEY (id),
  UNIQUE (candidate_id, job_id) -- Prevent duplicate assignments
);

-- Create indexes for better performance
CREATE INDEX idx_candidate_assignments_candidate_id ON public.candidate_assignments(candidate_id);
CREATE INDEX idx_candidate_assignments_job_id ON public.candidate_assignments(job_id);
CREATE INDEX idx_candidate_assignments_company_id ON public.candidate_assignments(company_id);
CREATE INDEX idx_candidate_assignments_status ON public.candidate_assignments(status);
CREATE INDEX idx_candidate_assignments_match_score ON public.candidate_assignments(match_score DESC);
CREATE INDEX idx_candidate_assignments_created_at ON public.candidate_assignments(created_at DESC);

-- Enable Row Level Security
ALTER TABLE public.candidate_assignments ENABLE ROW LEVEL SECURITY;

-- RLS Policies for candidate_assignments

-- Users can view assignments for their own companies or candidates they uploaded
CREATE POLICY "Users can view their company assignments"
  ON public.candidate_assignments FOR SELECT
  USING (
    -- User can see assignments for their company
    company_id IN (
      SELECT id FROM public.companies WHERE user_id = auth.uid()
    )
    OR
    -- User can see assignments for candidates they uploaded
    candidate_id IN (
      SELECT id FROM public.candidates WHERE user_id = auth.uid()
    )
    OR
    -- User assigned the candidate
    assigned_by = auth.uid()
  );

-- Users can create assignments for their companies and candidates
CREATE POLICY "Users can create assignments for their companies"
  ON public.candidate_assignments FOR INSERT
  WITH CHECK (
    -- User owns the company
    company_id IN (
      SELECT id FROM public.companies WHERE user_id = auth.uid()
    )
    AND
    -- User can assign candidates they uploaded or have access to
    (
      candidate_id IN (
        SELECT id FROM public.candidates WHERE user_id = auth.uid()
      )
      OR
      assigned_by = auth.uid()
    )
  );

-- Users can update assignments for their companies
CREATE POLICY "Users can update their company assignments"
  ON public.candidate_assignments FOR UPDATE
  USING (
    company_id IN (
      SELECT id FROM public.companies WHERE user_id = auth.uid()
    )
    OR
    assigned_by = auth.uid()
  );

-- Users can delete assignments for their companies
CREATE POLICY "Users can delete their company assignments"
  ON public.candidate_assignments FOR DELETE
  USING (
    company_id IN (
      SELECT id FROM public.companies WHERE user_id = auth.uid()
    )
    OR
    assigned_by = auth.uid()
  );

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_candidate_assignments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER trigger_candidate_assignments_updated_at
  BEFORE UPDATE ON public.candidate_assignments
  FOR EACH ROW
  EXECUTE FUNCTION update_candidate_assignments_updated_at();

-- Create function to automatically update candidate status when assigned
CREATE OR REPLACE FUNCTION update_candidate_status_on_assignment()
RETURNS TRIGGER AS $$
BEGIN
  -- When a new assignment is created, update candidate status if it's 'new'
  IF TG_OP = 'INSERT' THEN
    UPDATE public.candidates 
    SET status = 'screening'
    WHERE id = NEW.candidate_id 
    AND status = 'new';
  END IF;
  
  RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic candidate status update
CREATE TRIGGER trigger_update_candidate_status_on_assignment
  AFTER INSERT ON public.candidate_assignments
  FOR EACH ROW
  EXECUTE FUNCTION update_candidate_status_on_assignment();

-- Create function to get assignment statistics
CREATE OR REPLACE FUNCTION get_assignment_stats(company_uuid uuid)
RETURNS TABLE (
  total_assignments bigint,
  new_assignments bigint,
  screening_assignments bigint,
  interview_assignments bigint,
  offer_assignments bigint,
  hired_assignments bigint,
  rejected_assignments bigint,
  avg_match_score numeric
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total_assignments,
    COUNT(*) FILTER (WHERE status = 'new') as new_assignments,
    COUNT(*) FILTER (WHERE status = 'screening') as screening_assignments,
    COUNT(*) FILTER (WHERE status = 'interview') as interview_assignments,
    COUNT(*) FILTER (WHERE status = 'offer') as offer_assignments,
    COUNT(*) FILTER (WHERE status = 'hired') as hired_assignments,
    COUNT(*) FILTER (WHERE status = 'rejected') as rejected_assignments,
    ROUND(AVG(match_score), 2) as avg_match_score
  FROM public.candidate_assignments
  WHERE company_id = company_uuid;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON public.candidate_assignments TO authenticated;
GRANT EXECUTE ON FUNCTION get_assignment_stats(uuid) TO authenticated;

-- Create status_transitions table for audit trail
CREATE TABLE public.status_transitions (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  candidate_id uuid NOT NULL REFERENCES public.candidates(id) ON DELETE CASCADE,
  assignment_id uuid REFERENCES public.candidate_assignments(id) ON DELETE SET NULL,
  from_status text NOT NULL,
  to_status text NOT NULL,
  reason text,
  notes text,
  changed_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
  changed_at timestamp with time zone NOT NULL DEFAULT now(),
  PRIMARY KEY (id)
);

-- Create indexes for status_transitions
CREATE INDEX idx_status_transitions_candidate_id ON public.status_transitions(candidate_id);
CREATE INDEX idx_status_transitions_changed_at ON public.status_transitions(changed_at DESC);

-- Enable RLS for status_transitions
ALTER TABLE public.status_transitions ENABLE ROW LEVEL SECURITY;

-- RLS policy for status_transitions
CREATE POLICY "Users can view status transitions for their candidates"
  ON public.status_transitions FOR SELECT
  USING (
    candidate_id IN (
      SELECT id FROM public.candidates WHERE user_id = auth.uid()
    )
    OR
    candidate_id IN (
      SELECT candidate_id FROM public.candidate_assignments
      WHERE company_id IN (
        SELECT id FROM public.companies WHERE user_id = auth.uid()
      )
    )
    OR
    changed_by = auth.uid()
  );

-- Grant permissions for status_transitions
GRANT ALL ON public.status_transitions TO authenticated;

-- Add comments for documentation
COMMENT ON TABLE public.candidate_assignments IS 'Tracks assignments of candidates to specific jobs after evaluation';
COMMENT ON COLUMN public.candidate_assignments.match_score IS 'AI-generated match score between candidate and job (0-100)';
COMMENT ON COLUMN public.candidate_assignments.status IS 'Current status of the candidate in the hiring pipeline';
COMMENT ON COLUMN public.candidate_assignments.assigned_by IS 'User who assigned the candidate to this job';
COMMENT ON TABLE public.status_transitions IS 'Audit trail for candidate status changes';
COMMENT ON FUNCTION get_assignment_stats(uuid) IS 'Returns assignment statistics for a specific company';
