/**
 * React Query hooks for dashboard data
 */

import { useAuth } from '@/contexts/AuthContext';
import { useCompanyContext } from '@/contexts/CompanyContext';
import { useEnhancedQuery, queryKeys } from '@/lib/queryUtils';
import * as dashboardService from '@/services/supabase/dashboard';
import {
  DashboardStats,
  RecentActivity,
  RecentEvaluation,
  ChartData,
  EvaluationFunnelData,
  EvaluationTimelineData,
  CompanyJobEvaluation,
  TimeToHireData,
  SourceEffectivenessData,
  RecruitmentFunnelData,
  TimeBreakdownData,
  SourceMetricsData
} from '@/services/supabase/dashboard';

/**
 * Hook to fetch dashboard statistics
 */
export function useDashboardStats() {
  const { user } = useAuth();
  const { activeCompanyId } = useCompanyContext();

  return useEnhancedQuery<DashboardStats>(
    ['dashboardStats', activeCompanyId],
    () => dashboardService.getDashboardStats(activeCompanyId || undefined),
    {
      enabled: !!user?.id,
      fallbackData: {
        totalCVs: 0,
        newCVsThisMonth: 0,
        totalCompanies: 0,
        newCompaniesThisMonth: 0,
        avgMatchScore: 0,
        matchScoreChange: 0,
        isMatchScorePositive: true
      },
      errorMessage: 'Failed to load dashboard statistics',
    }
  );
}

/**
 * Hook to fetch recent activities
 */
export function useRecentActivities(limit: number = 5) {
  const { user } = useAuth();
  const { activeCompanyId } = useCompanyContext();

  return useEnhancedQuery<RecentActivity[]>(
    ['recentActivities', activeCompanyId, limit],
    () => dashboardService.getRecentActivities(activeCompanyId || undefined, limit),
    {
      enabled: !!user?.id,
      fallbackData: [],
      errorMessage: 'Failed to load recent activities',
    }
  );
}

/**
 * Hook to fetch recent evaluations
 */
export function useRecentEvaluations(limit: number = 4) {
  const { user } = useAuth();
  const { activeCompanyId } = useCompanyContext();

  return useEnhancedQuery<RecentEvaluation[]>(
    ['recentEvaluations', activeCompanyId, limit],
    () => dashboardService.getRecentEvaluations(activeCompanyId || undefined, limit),
    {
      enabled: !!user?.id,
      fallbackData: [],
      errorMessage: 'Failed to load recent evaluations',
    }
  );
}

/**
 * Hook to fetch top skills from candidates
 */
export function useTopSkills(limit: number = 5) {
  const { user } = useAuth();
  const { activeCompanyId } = useCompanyContext();

  return useEnhancedQuery<ChartData[]>(
    ['topSkills', activeCompanyId, limit],
    () => dashboardService.getTopSkills(limit, activeCompanyId || undefined),
    {
      enabled: !!user?.id,
      fallbackData: [],
      errorMessage: 'Failed to load top skills',
    }
  );
}

/**
 * Hook to fetch match quality distribution
 */
export function useMatchQualityDistribution() {
  const { user } = useAuth();
  const { activeCompanyId } = useCompanyContext();

  return useEnhancedQuery<ChartData[]>(
    ['matchQualityDistribution', activeCompanyId],
    () => dashboardService.getMatchQualityDistribution(activeCompanyId || undefined),
    {
      enabled: !!user?.id,
      fallbackData: [
        { label: 'Excellent (90%+)', value: 0, color: 'bg-green-500' },
        { label: 'Good (70-89%)', value: 0, color: 'bg-blue-500' },
        { label: 'Fair (50-69%)', value: 0, color: 'bg-amber-500' },
        { label: 'Poor (<50%)', value: 0, color: 'bg-red-500' }
      ],
      errorMessage: 'Failed to load match quality distribution',
    }
  );
}

/**
 * Hook to fetch evaluation funnel data
 */
export function useEvaluationFunnelData() {
  const { user } = useAuth();
  const { activeCompanyId } = useCompanyContext();

  return useEnhancedQuery<EvaluationFunnelData>(
    ['evaluationFunnelData', activeCompanyId],
    () => dashboardService.getEvaluationFunnelData(activeCompanyId || undefined),
    {
      enabled: !!user?.id,
      fallbackData: {
        stages: [
          { label: 'CVs Uploaded', value: 0, color: 'bg-blue-500' },
          { label: 'Evaluated', value: 0, color: 'bg-purple-500' },
          { label: 'Good Match (70%+)', value: 0, color: 'bg-green-500' },
          { label: 'Hired', value: 0, color: 'bg-recruiter-blue' },
        ],
        totalCandidates: 0,
        conversionRate: 0
      },
      errorMessage: 'Failed to load evaluation funnel data',
    }
  );
}

/**
 * Hook to fetch evaluation timeline data
 */
export function useEvaluationTimelineData(period: 'week' | 'month' | 'year' = 'month') {
  const { user } = useAuth();

  return useEnhancedQuery<EvaluationTimelineData>(
    ['evaluationTimelineData', period],
    () => dashboardService.getEvaluationTimelineData(period),
    {
      enabled: !!user?.id,
      fallbackData: { evaluations: [], candidates: [] },
      errorMessage: `Failed to load evaluation timeline data for period ${period}`,
    }
  );
}

/**
 * Hook to fetch evaluation breakdown by company and job
 */
export function useEvaluationByCompanyJob() {
  const { user } = useAuth();

  return useEnhancedQuery<CompanyJobEvaluation[]>(
    ['evaluationByCompanyJob'],
    () => dashboardService.getEvaluationByCompanyJob(),
    {
      enabled: !!user?.id,
      fallbackData: [],
      errorMessage: 'Failed to load evaluation breakdown by company and job',
    }
  );
}

/**
 * Hook to fetch time to hire data
 */
export function useTimeToHireData() {
  const { user } = useAuth();

  return useEnhancedQuery<TimeToHireData[]>(
    ['timeToHireData'],
    () => dashboardService.getTimeToHireData(),
    {
      enabled: !!user?.id,
      fallbackData: [],
      errorMessage: 'Failed to load time to hire data',
    }
  );
}

/**
 * Hook to fetch source effectiveness data
 */
export function useSourceEffectivenessData() {
  const { user } = useAuth();

  return useEnhancedQuery<SourceEffectivenessData[]>(
    ['sourceEffectivenessData'],
    () => dashboardService.getSourceEffectivenessData(),
    {
      enabled: !!user?.id,
      fallbackData: [],
      errorMessage: 'Failed to load source effectiveness data',
    }
  );
}

/**
 * Hook to fetch recruitment funnel data
 */
export function useRecruitmentFunnelData() {
  const { user } = useAuth();

  return useEnhancedQuery<RecruitmentFunnelData>(
    ['recruitmentFunnelData'],
    () => dashboardService.getRecruitmentFunnelData(),
    {
      enabled: !!user?.id,
      fallbackData: {
        stages: [
          { stage: 'Applications', count: 0, color: 'bg-blue-500' },
          { stage: 'Screening', count: 0, color: 'bg-amber-500' },
          { stage: 'Interview', count: 0, color: 'bg-purple-500' },
          { stage: 'Offer', count: 0, color: 'bg-green-500' },
          { stage: 'Hired', count: 0, color: 'bg-recruiter-blue' }
        ],
        conversionRates: [],
        overallConversion: 0
      },
      errorMessage: 'Failed to load recruitment funnel data',
    }
  );
}

/**
 * Hook to fetch time breakdown data
 */
export function useTimeBreakdownData() {
  const { user } = useAuth();

  return useEnhancedQuery<TimeBreakdownData>(
    ['timeBreakdownData'],
    () => dashboardService.getTimeBreakdownData(),
    {
      enabled: !!user?.id,
      fallbackData: {
        stages: [
          { stage: 'Application Review', days: 3, color: 'bg-blue-500' },
          { stage: 'Screening', days: 4, color: 'bg-amber-500' },
          { stage: 'First Interview', days: 3, color: 'bg-purple-500' },
          { stage: 'Technical Assessment', days: 3, color: 'bg-green-500' },
          { stage: 'Final Interview', days: 3, color: 'bg-pink-500' },
          { stage: 'Offer & Negotiation', days: 4, color: 'bg-recruiter-blue' }
        ],
        totalDays: 20
      },
      errorMessage: 'Failed to load time breakdown data',
    }
  );
}

/**
 * Hook to fetch source metrics data
 */
export function useSourceMetricsData() {
  const { user } = useAuth();

  return useEnhancedQuery<SourceMetricsData>(
    ['sourceMetricsData'],
    () => dashboardService.getSourceMetricsData(),
    {
      enabled: !!user?.id,
      fallbackData: {
        costPerHire: [],
        timeToHire: []
      },
      errorMessage: 'Failed to load source metrics data',
    }
  );
}
