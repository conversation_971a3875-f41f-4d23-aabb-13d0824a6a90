import React, { useState } from 'react';
import {
  Clipboard<PERSON>heck,
  Code,
  User,
  Briefcase,
  Building,
  Clock,
  CheckCircle,
  AlertCircle,
  ChevronRight,
  ChevronLeft,
  BarChart,
  FileText,
  Download,
  Brain,
  Lightbulb,
  CheckSquare,
  XSquare,
  Timer
} from 'lucide-react';

import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface CandidateAssessmentViewProps {
  // This component doesn't need any props as it's a presentation UI
}

const CandidateAssessmentView: React.FC<CandidateAssessmentViewProps> = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [currentTechnicalQuestion, setCurrentTechnicalQuestion] = useState(0);
  const [currentPersonalityQuestion, setCurrentPersonalityQuestion] = useState(0);
  const [technicalAnswers, setTechnicalAnswers] = useState<Record<number, string>>({});
  const [personalityAnswers, setPersonalityAnswers] = useState<Record<number, number>>({});
  const [codingAnswer, setCodingAnswer] = useState('');
  const [assessmentStarted, setAssessmentStarted] = useState(false);
  const [assessmentCompleted, setAssessmentCompleted] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState(60); // minutes

  // Assessment data for Jayesh Choolun
  const assessmentData = {
    candidate: {
      name: 'Jayesh Choolun',
      email: '<EMAIL>',
      avatar: 'https://i.pravatar.cc/150?u=jayesh',
      position: 'Senior Frontend Developer',
      company: 'TechCorp Solutions'
    },
    assessment: {
      title: 'Frontend Developer Skills Assessment',
      timeLimit: 60, // minutes
      sections: ['Technical Skills', 'Coding Challenge', 'Personality Assessment'],
      totalQuestions: 25,
      progress: assessmentCompleted ? 100 : assessmentStarted ? 45 : 0
    },
    technicalQuestions: [
      {
        id: 1,
        question: 'Which of the following is NOT a React hook?',
        options: [
          'useState',
          'useEffect',
          'useHistory',
          'useReactState'
        ],
        correctAnswer: 'useReactState'
      },
      {
        id: 2,
        question: 'What is the correct way to create a state variable in a functional component?',
        options: [
          'const [state, setState] = React.useState(initialValue)',
          'this.state = { value: initialValue }',
          'const state = new State(initialValue)',
          'React.createState(initialValue)'
        ],
        correctAnswer: 'const [state, setState] = React.useState(initialValue)'
      },
      {
        id: 3,
        question: 'Which CSS property is used to create a flexible box layout?',
        options: [
          'flex',
          'display: block',
          'position: relative',
          'float: left'
        ],
        correctAnswer: 'flex'
      },
      {
        id: 4,
        question: 'What does the "async" keyword do in JavaScript?',
        options: [
          'Makes a function return a Promise',
          'Makes a function run in a separate thread',
          'Makes a function execute immediately',
          'Makes a function wait for user input'
        ],
        correctAnswer: 'Makes a function return a Promise'
      },
      {
        id: 5,
        question: 'Which of the following is a valid way to conditionally render in React?',
        options: [
          '{condition && <Component />}',
          '<Component if={condition} />',
          '<If condition={condition}><Component /></If>',
          'condition ? Component : null'
        ],
        correctAnswer: '{condition && <Component />}'
      }
    ],
    codingChallenge: {
      title: 'Create a Counter Component',
      description: 'Implement a React counter component that increments and decrements a value.',
      instructions: [
        'Create a functional component that uses useState',
        'Implement increment and decrement buttons',
        'Display the current count',
        'Add a reset button that sets the count back to 0',
        'Style the component using CSS'
      ],
      starterCode: `import React, { useState } from 'react';

function Counter() {
  // Implement your counter here

  return (
    <div>
      {/* Your UI here */}
    </div>
  );
}

export default Counter;`,
      testCases: [
        'Counter should display initial value of 0',
        'Clicking increment should increase the count by 1',
        'Clicking decrement should decrease the count by 1',
        'Clicking reset should set the count back to 0',
        'Count should not go below 0'
      ]
    },
    personalityQuestions: [
      {
        id: 1,
        question: 'I prefer working in a team rather than independently.',
        type: 'likert'
      },
      {
        id: 2,
        question: 'I enjoy solving complex problems.',
        type: 'likert'
      },
      {
        id: 3,
        question: 'I am comfortable with ambiguity and uncertainty.',
        type: 'likert'
      },
      {
        id: 4,
        question: 'I prefer to plan everything in detail before starting work.',
        type: 'likert'
      },
      {
        id: 5,
        question: 'I am open to receiving feedback and criticism.',
        type: 'likert'
      }
    ],
    results: {
      overallScore: 82,
      technicalScore: 85,
      codingScore: 78,
      personalityScore: 90,
      strengths: [
        'Strong understanding of React fundamentals',
        'Excellent problem-solving skills',
        'Good coding practices and style',
        'Team-oriented mindset'
      ],
      areasForImprovement: [
        'Could improve on advanced CSS concepts',
        'More experience with state management libraries needed',
        'Consider more edge cases in code implementation'
      ],
      jobMatch: 87
    }
  };

  // Handle starting the assessment
  const handleStartAssessment = () => {
    setAssessmentStarted(true);
    setActiveTab('technical');
  };

  // Handle completing the assessment
  const handleCompleteAssessment = () => {
    setAssessmentCompleted(true);
    setActiveTab('results');
  };

  // Handle technical question navigation
  const handleNextTechnicalQuestion = () => {
    if (currentTechnicalQuestion < assessmentData.technicalQuestions.length - 1) {
      setCurrentTechnicalQuestion(currentTechnicalQuestion + 1);
    } else {
      setActiveTab('coding');
    }
  };

  const handlePrevTechnicalQuestion = () => {
    if (currentTechnicalQuestion > 0) {
      setCurrentTechnicalQuestion(currentTechnicalQuestion - 1);
    }
  };

  // Handle personality question navigation
  const handleNextPersonalityQuestion = () => {
    if (currentPersonalityQuestion < assessmentData.personalityQuestions.length - 1) {
      setCurrentPersonalityQuestion(currentPersonalityQuestion + 1);
    } else {
      handleCompleteAssessment();
    }
  };

  const handlePrevPersonalityQuestion = () => {
    if (currentPersonalityQuestion > 0) {
      setCurrentPersonalityQuestion(currentPersonalityQuestion - 1);
    }
  };

  // Handle technical answer selection
  const handleTechnicalAnswerSelect = (questionId: number, answer: string) => {
    setTechnicalAnswers({
      ...technicalAnswers,
      [questionId]: answer
    });
  };

  // Handle personality answer selection
  const handlePersonalityAnswerSelect = (questionId: number, value: number) => {
    setPersonalityAnswers({
      ...personalityAnswers,
      [questionId]: value
    });
  };

  // Calculate progress percentage
  const calculateProgress = () => {
    if (!assessmentStarted) return 0;
    if (assessmentCompleted) return 100;

    const totalSteps =
      assessmentData.technicalQuestions.length +
      1 + // coding challenge
      assessmentData.personalityQuestions.length;

    const completedSteps =
      (activeTab === 'technical' ? currentTechnicalQuestion :
       activeTab === 'coding' ? assessmentData.technicalQuestions.length :
       activeTab === 'personality' ? assessmentData.technicalQuestions.length + 1 + currentPersonalityQuestion :
       0);

    return Math.round((completedSteps / totalSteps) * 100);
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-start justify-between">
          <div>
            <div className="flex items-center gap-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={assessmentData.candidate.avatar} alt={assessmentData.candidate.name} />
                <AvatarFallback>{assessmentData.candidate.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
              </Avatar>
              <div>
                <CardTitle className="text-xl">{assessmentData.candidate.name}</CardTitle>
                <div className="flex items-center mt-1 text-sm text-gray-500">
                  <User className="h-4 w-4 mr-1" />
                  <span>{assessmentData.candidate.email}</span>
                </div>
              </div>
            </div>
          </div>
          <Badge
            variant={assessmentCompleted ? "success" : assessmentStarted ? "default" : "outline"}
            className={assessmentCompleted ? "bg-green-500 hover:bg-green-600" : ""}
          >
            {assessmentCompleted ? "Completed" : assessmentStarted ? "In Progress" : "Not Started"}
          </Badge>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-start">
              <Briefcase className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Position</p>
                <p className="text-gray-600">{assessmentData.candidate.position}</p>
              </div>
            </div>
            <div className="flex items-start">
              <Building className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Company</p>
                <p className="text-gray-600">{assessmentData.candidate.company}</p>
              </div>
            </div>
            <div className="flex items-start">
              <Clock className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
              <div>
                <p className="font-medium">Time Remaining</p>
                <p className="text-gray-600">{timeRemaining} minutes</p>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <p className="text-sm font-medium">Assessment Progress</p>
              <p className="text-sm text-gray-500">{calculateProgress()}%</p>
            </div>
            <Progress value={calculateProgress()} className="h-2" />
          </div>

          <Separator />

          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="technical" disabled={!assessmentStarted}>Technical</TabsTrigger>
              <TabsTrigger value="coding" disabled={!assessmentStarted}>Coding</TabsTrigger>
              <TabsTrigger value="personality" disabled={!assessmentStarted}>Personality</TabsTrigger>
              <TabsTrigger value="results" disabled={!assessmentCompleted}>Results</TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-4 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>{assessmentData.assessment.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <h3 className="font-medium">Assessment Details</h3>
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <Clock className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                        <span>Time Limit: {assessmentData.assessment.timeLimit} minutes</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <ClipboardCheck className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                        <span>Total Questions: {assessmentData.assessment.totalQuestions}</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <FileText className="h-5 w-5 text-gray-500 mt-0.5 flex-shrink-0" />
                        <span>Sections: {assessmentData.assessment.sections.join(', ')}</span>
                      </li>
                    </ul>
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <h3 className="font-medium">Instructions</h3>
                    <ul className="space-y-2">
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>Complete all sections of the assessment</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>You can navigate between questions within each section</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <CheckCircle className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                        <span>The coding challenge requires you to write and test code</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                        <span>Do not refresh the page during the assessment</span>
                      </li>
                      <li className="flex items-start gap-2">
                        <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                        <span>The assessment will automatically submit when the time limit is reached</span>
                      </li>
                    </ul>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    onClick={handleStartAssessment}
                    className="w-full"
                    disabled={assessmentStarted}
                  >
                    {assessmentStarted ? 'Assessment In Progress' : 'Start Assessment'}
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Technical Skills Tab */}
            <TabsContent value="technical" className="space-y-4 pt-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Technical Skills Assessment</CardTitle>
                  <Badge variant="outline" className="ml-2">
                    Question {currentTechnicalQuestion + 1} of {assessmentData.technicalQuestions.length}
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Timer className="h-5 w-5 text-amber-500" />
                      <span className="text-sm text-amber-700">Time remaining: {timeRemaining} minutes</span>
                    </div>

                    <div className="p-4 border rounded-md">
                      <h3 className="font-medium text-lg mb-4">
                        {assessmentData.technicalQuestions[currentTechnicalQuestion].question}
                      </h3>

                      <RadioGroup
                        value={technicalAnswers[assessmentData.technicalQuestions[currentTechnicalQuestion].id] || ''}
                        onValueChange={(value) => handleTechnicalAnswerSelect(
                          assessmentData.technicalQuestions[currentTechnicalQuestion].id,
                          value
                        )}
                        className="space-y-3"
                      >
                        {assessmentData.technicalQuestions[currentTechnicalQuestion].options.map((option, index) => (
                          <div key={index} className="flex items-center space-x-2">
                            <RadioGroupItem
                              value={option}
                              id={`option-${index}`}
                            />
                            <Label htmlFor={`option-${index}`}>{option}</Label>
                          </div>
                        ))}
                      </RadioGroup>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={handlePrevTechnicalQuestion}
                    disabled={currentTechnicalQuestion === 0}
                  >
                    <ChevronLeft className="h-4 w-4 mr-2" />
                    Previous
                  </Button>
                  <Button
                    onClick={handleNextTechnicalQuestion}
                    disabled={!technicalAnswers[assessmentData.technicalQuestions[currentTechnicalQuestion].id]}
                  >
                    {currentTechnicalQuestion === assessmentData.technicalQuestions.length - 1 ? 'Next Section' : 'Next Question'}
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Coding Challenge Tab */}
            <TabsContent value="coding" className="space-y-4 pt-4">
              <Card>
                <CardHeader>
                  <CardTitle>{assessmentData.codingChallenge.title}</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Timer className="h-5 w-5 text-amber-500" />
                      <span className="text-sm text-amber-700">Time remaining: {timeRemaining} minutes</span>
                    </div>

                    <div className="space-y-4">
                      <div className="p-4 border rounded-md bg-gray-50">
                        <h3 className="font-medium mb-2">Challenge Description</h3>
                        <p>{assessmentData.codingChallenge.description}</p>

                        <h3 className="font-medium mt-4 mb-2">Instructions</h3>
                        <ul className="list-disc pl-5 space-y-1">
                          {assessmentData.codingChallenge.instructions.map((instruction, index) => (
                            <li key={index}>{instruction}</li>
                          ))}
                        </ul>
                      </div>

                      <div className="p-4 border rounded-md bg-gray-900 text-white font-mono text-sm">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-400">Code Editor</span>
                          <Badge variant="outline" className="text-gray-400 border-gray-700">React</Badge>
                        </div>
                        <Textarea
                          value={codingAnswer || assessmentData.codingChallenge.starterCode}
                          onChange={(e) => setCodingAnswer(e.target.value)}
                          className="bg-gray-800 border-gray-700 font-mono h-64 text-white"
                        />
                      </div>

                      <div className="p-4 border rounded-md">
                        <h3 className="font-medium mb-2">Test Cases</h3>
                        <ul className="space-y-2">
                          {assessmentData.codingChallenge.testCases.map((testCase, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <div className="mt-0.5">
                                {index < 3 ?
                                  <CheckCircle className="h-4 w-4 text-green-500" /> :
                                  <AlertCircle className="h-4 w-4 text-gray-400" />
                                }
                              </div>
                              <span className={index < 3 ? "text-green-700" : "text-gray-600"}>
                                {testCase}
                              </span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={() => setActiveTab('technical')}
                  >
                    <ChevronLeft className="h-4 w-4 mr-2" />
                    Previous Section
                  </Button>
                  <div className="flex gap-2">
                    <Button variant="outline">
                      Run Tests
                    </Button>
                    <Button
                      onClick={() => setActiveTab('personality')}
                    >
                      Next Section
                      <ChevronRight className="h-4 w-4 ml-2" />
                    </Button>
                  </div>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Personality Assessment Tab */}
            <TabsContent value="personality" className="space-y-4 pt-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Personality Assessment</CardTitle>
                  <Badge variant="outline" className="ml-2">
                    Question {currentPersonalityQuestion + 1} of {assessmentData.personalityQuestions.length}
                  </Badge>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex items-center gap-2">
                      <Timer className="h-5 w-5 text-amber-500" />
                      <span className="text-sm text-amber-700">Time remaining: {timeRemaining} minutes</span>
                    </div>

                    <div className="p-4 border rounded-md">
                      <h3 className="font-medium text-lg mb-6">
                        {assessmentData.personalityQuestions[currentPersonalityQuestion].question}
                      </h3>

                      <div className="space-y-4">
                        <div className="flex justify-between text-sm text-gray-500 px-6">
                          <span>Strongly Disagree</span>
                          <span>Neutral</span>
                          <span>Strongly Agree</span>
                        </div>
                        <div className="flex justify-between gap-2">
                          {[1, 2, 3, 4, 5].map((value) => (
                            <Button
                              key={value}
                              variant={personalityAnswers[assessmentData.personalityQuestions[currentPersonalityQuestion].id] === value ? "default" : "outline"}
                              className="flex-1"
                              onClick={() => handlePersonalityAnswerSelect(
                                assessmentData.personalityQuestions[currentPersonalityQuestion].id,
                                value
                              )}
                            >
                              {value}
                            </Button>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-between">
                  <Button
                    variant="outline"
                    onClick={handlePrevPersonalityQuestion}
                    disabled={currentPersonalityQuestion === 0}
                  >
                    {currentPersonalityQuestion === 0 ? (
                      <>
                        <ChevronLeft className="h-4 w-4 mr-2" />
                        Previous Section
                      </>
                    ) : (
                      <>
                        <ChevronLeft className="h-4 w-4 mr-2" />
                        Previous
                      </>
                    )}
                  </Button>
                  <Button
                    onClick={handleNextPersonalityQuestion}
                    disabled={!personalityAnswers[assessmentData.personalityQuestions[currentPersonalityQuestion].id]}
                  >
                    {currentPersonalityQuestion === assessmentData.personalityQuestions.length - 1 ? 'Complete Assessment' : 'Next Question'}
                    <ChevronRight className="h-4 w-4 ml-2" />
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>

            {/* Results Tab */}
            <TabsContent value="results" className="space-y-4 pt-4">
              <Card>
                <CardHeader className="text-center">
                  <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 mb-4">
                    <BarChart className="h-10 w-10 text-primary" />
                  </div>
                  <CardTitle className="text-2xl">Assessment Results</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="text-center">
                    <div className="inline-flex items-center justify-center rounded-full bg-gray-100 p-6">
                      <div className="text-4xl font-bold text-primary">{assessmentData.results.overallScore}%</div>
                    </div>
                    <h3 className="mt-2 font-medium">Overall Score</h3>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <Card>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm flex items-center">
                          <Code className="h-4 w-4 mr-2 text-blue-500" />
                          Technical Skills
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="py-2">
                        <div className="text-center">
                          <span className="text-2xl font-bold">{assessmentData.results.technicalScore}%</span>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm flex items-center">
                          <FileText className="h-4 w-4 mr-2 text-green-500" />
                          Coding Challenge
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="py-2">
                        <div className="text-center">
                          <span className="text-2xl font-bold">{assessmentData.results.codingScore}%</span>
                        </div>
                      </CardContent>
                    </Card>
                    <Card>
                      <CardHeader className="py-3">
                        <CardTitle className="text-sm flex items-center">
                          <Brain className="h-4 w-4 mr-2 text-purple-500" />
                          Personality Fit
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="py-2">
                        <div className="text-center">
                          <span className="text-2xl font-bold">{assessmentData.results.personalityScore}%</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-medium mb-3 flex items-center">
                        <Lightbulb className="h-5 w-5 mr-2 text-amber-500" />
                        Strengths
                      </h3>
                      <ul className="space-y-2">
                        {assessmentData.results.strengths.map((strength, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <CheckSquare className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
                            <span>{strength}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <h3 className="font-medium mb-3 flex items-center">
                        <AlertCircle className="h-5 w-5 mr-2 text-red-500" />
                        Areas for Improvement
                      </h3>
                      <ul className="space-y-2">
                        {assessmentData.results.areasForImprovement.map((area, index) => (
                          <li key={index} className="flex items-start gap-2">
                            <XSquare className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                            <span>{area}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <Card className="bg-gray-50">
                    <CardHeader className="py-3">
                      <CardTitle className="text-base flex items-center">
                        <Briefcase className="h-5 w-5 mr-2 text-primary" />
                        Job Match: Senior Frontend Developer
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="py-2">
                      <div className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">Match Percentage</span>
                          <span className="text-sm font-medium">{assessmentData.results.jobMatch}%</span>
                        </div>
                        <Progress value={assessmentData.results.jobMatch} className="h-2" />
                        <p className="text-sm text-gray-600 mt-2">
                          This candidate is a strong match for the Senior Frontend Developer position, with excellent technical skills and a good cultural fit.
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                </CardContent>
                <CardFooter className="flex justify-center">
                  <Button className="gap-2">
                    <Download className="h-4 w-4" />
                    Download Full Report
                  </Button>
                </CardFooter>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
};

export default CandidateAssessmentView;
