import React from 'react';
import { Check, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

interface PricingFeature {
  text: string;
  highlighted?: boolean;
}

interface EnhancedPricingCardProps {
  emoji: string;
  title: string;
  price: string;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
  delay?: number;
}

export const EnhancedPricingCard: React.FC<EnhancedPricingCardProps> = ({
  emoji,
  title,
  price,
  description,
  features,
  isPopular = false,
  delay = 0
}) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: delay * 0.2 }}
      className={`relative bg-card-gradient rounded-xl p-6 flex flex-col h-full overflow-hidden ${
        isPopular ? 'border-2 border-indigo-500' : 'border border-gray-800'
      }`}
    >
      {/* Popular badge */}
      {isPopular && (
        <div className="absolute -right-12 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs font-bold py-1 px-12 transform rotate-45 shadow-lg">
          MOST POPULAR
        </div>
      )}
      
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <div className="text-3xl mr-3">{emoji}</div>
            <h3 className="text-white text-xl font-bold">{title}</h3>
          </div>
        </div>
        
        <div className="mb-3">
          <div className="flex items-baseline">
            <span className="text-white text-4xl font-bold">{price}</span>
            <span className="text-gray-400 ml-2">/month</span>
          </div>
          <p className="text-gray-300 mt-2">{description}</p>
        </div>
      </div>
      
      <div className="mb-6 flex-grow">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <motion.li 
              key={index} 
              className="flex items-start"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
            >
              <div className="mt-1 mr-3 flex-shrink-0 w-5 h-5 rounded-full bg-indigo-500/20 flex items-center justify-center">
                <Check size={12} className="text-indigo-400" />
              </div>
              <span className={`text-gray-300 ${feature.highlighted ? 'font-semibold' : ''}`}>
                {feature.text}
                {feature.highlighted && (
                  <Sparkles size={12} className="inline-block ml-1 text-yellow-400" />
                )}
              </span>
            </motion.li>
          ))}
        </ul>
      </div>
      
      <motion.div
        whileHover={{ scale: 1.03 }}
        whileTap={{ scale: 0.98 }}
      >
        <Link 
          to="/signup" 
          className={`w-full py-3 rounded-lg text-center font-medium transition-all flex items-center justify-center
            ${isPopular 
              ? 'bg-gradient-to-r from-indigo-500 to-purple-600 hover:from-indigo-600 hover:to-purple-700 text-white shadow-lg shadow-indigo-500/20' 
              : 'bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white'}`}
        >
          Get Started
        </Link>
      </motion.div>
    </motion.div>
  );
};
