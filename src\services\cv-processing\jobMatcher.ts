import { matchCandidateToJob, rankCandidatesForJob as rankCandidatesWithAI } from '@/lib/ai/operations';
import { supabase } from '@/lib/supabase';
import { ParsedCV } from './cvParser';
import { getCompanies } from '@/services/supabase/companies';
import { getJobs } from '@/services/supabase/jobs';
import { calculateTotalExperience, extractMinimumExperience } from '@/utils/experienceUtils';

// Interface for job-specific match result
export interface JobSpecificMatch {
  jobId: string;
  jobTitle: string;
  companyId?: string;
  companyName?: string;
  overallScore: number;
  skillsScore: number;
  experienceScore: number;
  educationScore: number;
  locationScore: number;
  coverLetterScore: number;
  strengths: string[];
  gaps: string[];
  recommendation: string;
}

// Interface for job-candidate match result
export interface JobCandidateMatch {
  overallScore: number;
  skillsMatch: {
    score: number;
    analysis: string;
    skills?: Array<{
      name: string;
      match: number;
      required: boolean;
    }>;
  };
  experienceMatch: {
    score: number;
    analysis: string;
    details?: Array<{
      title: string;
      company: string;
      duration: string;
      relevance: number;
    }>;
  };
  educationMatch: {
    score: number;
    analysis: string;
    details?: Array<{
      degree: string;
      institution: string;
      year: string;
      relevance: number;
    }>;
  };
  locationMatch: {
    score: number;
    analysis: string;
  };
  strengths: string[];
  gaps: string[];
  recommendation: string;
  raw?: string;

  // Enhanced company evaluation features
  jobSpecificMatches?: JobSpecificMatch[];
  topMatchingJobs?: JobSpecificMatch[];
  isCompanyEvaluation?: boolean;
}

// Interface for candidate ranking
export interface CandidateRanking {
  candidateId: string;
  name: string;
  overallScore: number;
  skillsScore: number;
  experienceScore: number;
  educationScore: number;
  culturalFitScore: number;
  strengths: string[];
  areasForImprovement: string[];
  recommendation: 'Hire' | 'Consider' | 'Reject';
}

// Interface for bulk evaluation result
export interface BulkEvaluationResult {
  jobId: string;
  jobTitle: string;
  totalCandidates: number;
  evaluatedCandidates: number;
  rankings: CandidateRanking[];
  topCandidate?: CandidateRanking;
  evaluationSummary: {
    averageScore: number;
    hireRecommendations: number;
    considerRecommendations: number;
    rejectRecommendations: number;
  };
  errors?: Array<{
    candidateId: string;
    candidateName: string;
    error: string;
  }>;
}

/**
 * Match a candidate to a job with optional cover letter content
 */
export async function matchCandidate(
  candidateId: string,
  jobId: string,
  coverLetterContent?: string | null
): Promise<JobCandidateMatch> {
  try {
    // Get candidate data
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('*')
      .eq('id', candidateId)
      .single();

    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw candidateError;
    }

    // Get complete job data with all fields
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select(`
        *,
        companies!inner(name)
      `)
      .eq('id', jobId)
      .single();

    if (jobError) {
      console.error('Error fetching job:', jobError);
      throw jobError;
    }

    // Parse the evaluation summary to get the parsed CV
    let parsedCV: ParsedCV;
    try {
      const evaluationSummary = JSON.parse(candidate.evaluation_summary || '{}');
      parsedCV = evaluationSummary.parsedCV;
    } catch (error) {
      console.error('Error parsing evaluation summary:', error);
      throw new Error('Invalid evaluation summary format');
    }

    if (!parsedCV) {
      throw new Error('No parsed CV data found. Please re-upload the CV.');
    }

    // Use the AI service to match candidate to job with complete job details
    const matchResult = await matchCandidateToJob(
      parsedCV, 
      job.description || '', 
      {
        title: job.title,
        department: job.department,
        location: job.location,
        employment_type: job.employment_type,
        experience_level: job.experience_level,
        salary_range: job.salary_range,
        application_deadline: job.application_deadline,
        requirements: job.requirements,
        responsibilities: job.responsibilities,
        benefits: job.benefits,
        company_name: job.companies?.name
      },
      coverLetterContent || undefined
    );

    // Get the current user's ID
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id;

    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Insert a new record in the evaluations table
    const { error: insertError } = await supabase
      .from('evaluations')
      .insert({
        candidate_id: candidateId,
        job_id: jobId,
        company_id: job.company_id,
        evaluation_mode: 'specific-job',
        evaluation_score: matchResult.overallScore,
        evaluation_summary: JSON.stringify({
          parsedCV,
          matchResult
        }),
        user_id: userId
      });

    if (insertError) {
      console.error('Error inserting evaluation:', insertError);
      throw insertError;
    }

    // Also update the candidate record with the latest evaluation score and summary
    // This maintains backward compatibility with existing code
    const { error: updateError } = await supabase
      .from('candidates')
      .update({
        evaluation_score: matchResult.overallScore,
        evaluation_summary: JSON.stringify({
          parsedCV,
          matchResult
        })
      })
      .eq('id', candidateId);

    if (updateError) {
      console.error('Error updating candidate with match result:', updateError);
      throw updateError;
    }

    return matchResult as JobCandidateMatch;
  } catch (error) {
    console.error('Error matching candidate to job:', error);
    throw error;
  }
}

/**
 * Match a candidate to all jobs in a specific company with enhanced evaluation
 */
export async function matchCandidateToCompany(
  candidateId: string,
  companyId: string,
  coverLetterContent?: string | null
): Promise<JobCandidateMatch> {
  try {
    // Get candidate data
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('*')
      .eq('id', candidateId)
      .single();

    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw candidateError;
    }

    // Parse the evaluation summary to get the parsed CV
    let parsedCV: ParsedCV;
    try {
      const evaluationSummary = JSON.parse(candidate.evaluation_summary || '{}');
      parsedCV = evaluationSummary.parsedCV;
    } catch (error) {
      console.error('Error parsing evaluation summary:', error);
      throw new Error('Invalid evaluation summary format');
    }

    // Get all jobs for the company with complete details
    const { data: companyJobs, error: jobsError } = await supabase
      .from('jobs')
      .select(`
        *,
        companies!inner(name)
      `)
      .eq('company_id', companyId);

    if (jobsError) {
      console.error('Error fetching company jobs:', jobsError);
      throw jobsError;
    }

    if (companyJobs.length === 0) {
      throw new Error('No jobs found for this company');
    }

    const company = companyJobs[0]?.companies;
    const matchResults: any[] = [];
    const jobSpecificMatches: JobSpecificMatch[] = [];

    // Process each job with complete details
    for (const job of companyJobs) {
      try {
        const matchResult = await matchCandidateToJob(
          parsedCV, 
          job.description || '', 
          {
            title: job.title,
            department: job.department,
            location: job.location,
            employment_type: job.employment_type,
            experience_level: job.experience_level,
            salary_range: job.salary_range,
            application_deadline: job.application_deadline,
            requirements: job.requirements,
            responsibilities: job.responsibilities,
            benefits: job.benefits,
            company_name: company?.name
          },
          coverLetterContent || undefined
        );

        matchResults.push(matchResult);

        // Create job-specific match data
        jobSpecificMatches.push({
          jobId: job.id,
          jobTitle: job.title,
          companyId: companyId,
          companyName: company?.name || 'Unknown Company',
          overallScore: matchResult.overallScore,
          skillsScore: matchResult.skillsMatch.score,
          experienceScore: matchResult.experienceMatch.score,
          educationScore: matchResult.educationMatch.score,
          locationScore: matchResult.locationMatch.score,
          coverLetterScore: (matchResult as any).coverLetterMatch?.score || 0,
          strengths: matchResult.strengths.slice(0, 3),
          gaps: matchResult.gaps.slice(0, 3),
          recommendation: matchResult.recommendation
        });
      } catch (error) {
        console.error(`Error matching candidate to job ${job.id}:`, error);
        // Continue with other jobs even if one fails
      }
    }

    if (matchResults.length === 0) {
      throw new Error('Failed to match candidate to any jobs in this company');
    }

    // Calculate average scores with weighted evaluation
    // Give more weight to higher scores to prioritize better matches
    const weightedScores = matchResults.map(result => ({
      score: result.overallScore,
      weight: result.overallScore >= 80 ? 2 : 1 // Higher weight for better matches
    }));

    const totalWeightedScore = weightedScores.reduce((sum, item) => sum + (item.score * item.weight), 0);
    const totalWeight = weightedScores.reduce((sum, item) => sum + item.weight, 0);
    const averageScore = Math.round(totalWeightedScore / totalWeight);

    // Collect all strengths and gaps
    const allStrengths = new Set<string>();
    const allGaps = new Set<string>();

    matchResults.forEach(result => {
      result.strengths.forEach(strength => allStrengths.add(strength));
      result.gaps.forEach(gap => allGaps.add(gap));
    });

    // Sort job-specific matches by overall score (descending)
    const sortedJobMatches = [...jobSpecificMatches].sort((a, b) => b.overallScore - a.overallScore);

    // Get top 3 matching jobs
    const topMatchingJobs = sortedJobMatches.slice(0, 3);

    // Create a combined match result with enhanced features
    const combinedResult: JobCandidateMatch = {
      overallScore: averageScore,
      skillsMatch: {
        score: Math.round(matchResults.reduce((sum, r) => sum + r.skillsMatch.score, 0) / matchResults.length),
        analysis: `Evaluated against ${matchResults.length} jobs from the selected company.`
      },
      experienceMatch: {
        score: Math.round(matchResults.reduce((sum, r) => sum + r.experienceMatch.score, 0) / matchResults.length),
        analysis: `Experience evaluated across multiple positions.`
      },
      educationMatch: {
        score: Math.round(matchResults.reduce((sum, r) => sum + r.educationMatch.score, 0) / matchResults.length),
        analysis: `Education requirements evaluated across multiple positions.`
      },
      locationMatch: {
        score: Math.round(matchResults.reduce((sum, r) => sum + r.locationMatch.score, 0) / matchResults.length),
        analysis: `Location preferences evaluated across multiple positions.`
      },
      strengths: Array.from(allStrengths).slice(0, 10), // Limit to top 10 strengths
      gaps: Array.from(allGaps).slice(0, 10), // Limit to top 10 gaps
      recommendation: averageScore >= 75
        ? `Strong candidate for this company. Best match: ${topMatchingJobs[0]?.jobTitle} (${topMatchingJobs[0]?.overallScore}%)`
        : 'May not be a good fit for this company',

      // Enhanced features
      jobSpecificMatches: jobSpecificMatches,
      topMatchingJobs: topMatchingJobs,
      isCompanyEvaluation: true
    };

    // Get the current user's ID
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id;

    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Insert a new record in the evaluations table
    const { error: insertError } = await supabase
      .from('evaluations')
      .insert({
        candidate_id: candidateId,
        company_id: companyId,
        evaluation_mode: 'all-company-jobs',
        evaluation_score: combinedResult.overallScore,
        evaluation_summary: JSON.stringify({
          parsedCV,
          matchResult: combinedResult
        }),
        user_id: userId
      });

    if (insertError) {
      console.error('Error inserting evaluation:', insertError);
      throw insertError;
    }

    // Also update the candidate record with the latest evaluation score and summary
    // This maintains backward compatibility with existing code
    const { error: updateError } = await supabase
      .from('candidates')
      .update({
        evaluation_score: combinedResult.overallScore,
        evaluation_summary: JSON.stringify({
          parsedCV,
          matchResult: combinedResult
        })
      })
      .eq('id', candidateId);

    if (updateError) {
      console.error('Error updating candidate with match result:', updateError);
      throw updateError;
    }

    return combinedResult;
  } catch (error) {
    console.error('Error matching candidate to company:', error);
    throw error;
  }
}

/**
 * Match a candidate to all jobs across all companies
 */
export async function matchCandidateToAllCompanies(
  candidateId: string,
  coverLetterContent?: string | null
): Promise<JobCandidateMatch> {
  try {
    // Get candidate data
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('*')
      .eq('id', candidateId)
      .single();

    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw candidateError;
    }

    // Parse the evaluation summary to get the parsed CV
    let parsedCV: ParsedCV;
    try {
      const evaluationSummary = JSON.parse(candidate.evaluation_summary || '{}');
      parsedCV = evaluationSummary.parsedCV;
    } catch (error) {
      console.error('Error parsing evaluation summary:', error);
      throw new Error('Invalid evaluation summary format');
    }

    // Get all jobs with complete details and company information
    const { data: allJobs, error: jobsError } = await supabase
      .from('jobs')
      .select(`
        *,
        companies!inner(name)
      `);

    if (jobsError) {
      console.error('Error fetching jobs:', jobsError);
      throw jobsError;
    }

    if (allJobs.length === 0) {
      throw new Error('No jobs found in the system');
    }

    const matchResults: any[] = [];
    const jobSpecificMatches: JobSpecificMatch[] = [];

    // Process each job with complete details
    for (const job of allJobs) {
      try {
        const matchResult = await matchCandidateToJob(
          parsedCV, 
          job.description || '', 
          {
            title: job.title,
            department: job.department,
            location: job.location,
            employment_type: job.employment_type,
            experience_level: job.experience_level,
            salary_range: job.salary_range,
            application_deadline: job.application_deadline,
            requirements: job.requirements,
            responsibilities: job.responsibilities,
            benefits: job.benefits,
            company_name: job.companies?.name
          },
          coverLetterContent || undefined
        );

        matchResults.push(matchResult);

        // Create job-specific match data with company information
        jobSpecificMatches.push({
          jobId: job.id,
          jobTitle: job.title,
          companyId: job.company_id,
          companyName: job.companies?.name || 'Unknown Company',
          overallScore: matchResult.overallScore,
          skillsScore: matchResult.skillsMatch.score,
          experienceScore: matchResult.experienceMatch.score,
          educationScore: matchResult.educationMatch.score,
          locationScore: matchResult.locationMatch.score,
          coverLetterScore: (matchResult as any).coverLetterMatch?.score || 0,
          strengths: matchResult.strengths.slice(0, 3),
          gaps: matchResult.gaps.slice(0, 3),
          recommendation: matchResult.recommendation
        });
      } catch (error) {
        console.error(`Error matching candidate to job ${job.id}:`, error);
        // Continue with other jobs even if one fails
      }
    }

    // Calculate the average score across all jobs
    const totalScore = matchResults.reduce((sum, result) => sum + result.overallScore, 0);
    const averageScore = Math.round(totalScore / matchResults.length);

    // Find the best matching jobs (top 3)
    const bestMatches = [...matchResults]
      .sort((a, b) => b.overallScore - a.overallScore)
      .slice(0, 3);

    // Create top matching jobs from job-specific matches
    const topMatchingJobs = [...jobSpecificMatches]
      .sort((a, b) => b.overallScore - a.overallScore)
      .slice(0, 5); // Top 5 jobs

    // Combine strengths and gaps from all evaluations
    const allStrengths = new Set<string>();
    const allGaps = new Set<string>();

    matchResults.forEach(result => {
      result.strengths.forEach(strength => allStrengths.add(strength));
      result.gaps.forEach(gap => allGaps.add(gap));
    });

    // Create a combined match result
    const combinedResult: JobCandidateMatch = {
      overallScore: averageScore,
      skillsMatch: {
        score: Math.round(matchResults.reduce((sum, r) => sum + r.skillsMatch.score, 0) / matchResults.length),
        analysis: `Evaluated against ${matchResults.length} jobs across ${allJobs.length} companies.`
      },
      experienceMatch: {
        score: Math.round(matchResults.reduce((sum, r) => sum + r.experienceMatch.score, 0) / matchResults.length),
        analysis: `Experience evaluated across multiple positions and companies.`
      },
      educationMatch: {
        score: Math.round(matchResults.reduce((sum, r) => sum + r.educationMatch.score, 0) / matchResults.length),
        analysis: `Education requirements evaluated across multiple positions.`
      },
      locationMatch: {
        score: Math.round(matchResults.reduce((sum, r) => sum + r.locationMatch.score, 0) / matchResults.length),
        analysis: `Location preferences evaluated across multiple positions.`
      },
      strengths: Array.from(allStrengths).slice(0, 10), // Limit to top 10 strengths
      gaps: Array.from(allGaps).slice(0, 10), // Limit to top 10 gaps
      recommendation: `Best matches found in ${bestMatches.length} positions with scores ranging from ${bestMatches[bestMatches.length - 1]?.overallScore || 0}% to ${bestMatches[0]?.overallScore || 0}%.`,

      // Enhanced features for all companies evaluation
      jobSpecificMatches: jobSpecificMatches,
      topMatchingJobs: topMatchingJobs,
      isCompanyEvaluation: true
    };

    // Get the current user's ID
    const { data: { user } } = await supabase.auth.getUser();
    const userId = user?.id;

    if (!userId) {
      throw new Error('User not authenticated');
    }

    // Insert a new record in the evaluations table
    const evaluationData = {
      candidate_id: candidateId,
      evaluation_mode: 'all-companies',
      evaluation_score: combinedResult.overallScore,
      evaluation_summary: JSON.stringify({
        parsedCV,
        matchResult: combinedResult
      }),
      user_id: userId
    };

    const { error: insertError } = await supabase
      .from('evaluations')
      .insert(evaluationData);

    if (insertError) {
      console.error('Error inserting evaluation:', insertError);
      throw insertError;
    }

    // Also update the candidate record with the latest evaluation score and summary
    // This maintains backward compatibility with existing code
    const { error: updateError } = await supabase
      .from('candidates')
      .update({
        evaluation_score: combinedResult.overallScore,
        evaluation_summary: JSON.stringify({
          parsedCV,
          matchResult: combinedResult
        })
      })
      .eq('id', candidateId);

    if (updateError) {
      console.error('Error updating candidate with match result:', updateError);
      throw updateError;
    }

    return combinedResult;
  } catch (error) {
    console.error('Error matching candidate to all companies:', error);
    throw error;
  }
}

/**
 * Rank candidates for a job
 */
export async function rankCandidatesForJob(jobId: string): Promise<CandidateRanking[]> {
  try {
    // Get the job data
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('*')
      .eq('id', jobId)
      .single();

    if (jobError) {
      console.error('Error fetching job:', jobError);
      throw jobError;
    }

    // Get all candidates for the job
    const { data: candidates, error: candidatesError } = await supabase
      .from('candidates')
      .select('*')
      .eq('job_id', jobId);

    if (candidatesError) {
      console.error('Error fetching candidates:', candidatesError);
      throw candidatesError;
    }

    // If there are no candidates, return an empty array
    if (!candidates || candidates.length === 0) {
      return [];
    }

    // Prepare candidate data for scoring
    const candidateProfiles = candidates.map(candidate => {
      try {
        const evaluationSummary = JSON.parse(candidate.evaluation_summary || '{}');
        return {
          id: candidate.id,
          name: candidate.name,
          parsedCV: evaluationSummary.parsedCV
        };
      } catch (error) {
        console.error(`Error parsing evaluation summary for candidate ${candidate.id}:`, error);
        return null;
      }
    }).filter(Boolean);

    // Score and rank the candidates
    const rankingResult = await rankCandidatesWithAI(candidateProfiles, job.description);

    // Map the ranking result to our interface
    const candidateRankings: CandidateRanking[] = rankingResult.rankings.map((ranking: any) => ({
      candidateId: ranking.candidateId,
      name: ranking.name,
      overallScore: ranking.overallScore,
      skillsScore: ranking.scores.skills,
      experienceScore: ranking.scores.experience,
      educationScore: ranking.scores.education,
      culturalFitScore: ranking.scores.culturalFit,
      strengths: ranking.strengths,
      areasForImprovement: ranking.areasForImprovement,
      recommendation: ranking.recommendation
    }));

    // Sort by overall score in descending order
    candidateRankings.sort((a, b) => b.overallScore - a.overallScore);

    return candidateRankings;
  } catch (error) {
    console.error('Error ranking candidates for job:', error);
    throw error;
  }
}

/**
 * Enhanced bulk evaluation function for all candidates of a specific job
 * Provides detailed evaluation results with error handling and progress tracking
 */
export async function bulkEvaluateCandidatesForJob(
  jobId: string,
  onProgress?: (progress: { current: number; total: number; candidateName: string }) => void
): Promise<BulkEvaluationResult> {
  try {
    // Get complete job data with all fields
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select(`
        *,
        companies!inner(name)
      `)
      .eq('id', jobId)
      .single();

    if (jobError) {
      console.error('Error fetching job:', jobError);
      throw new Error(`Failed to fetch job: ${jobError.message}`);
    }

    // Create comprehensive job description for AI ranking
    const comprehensiveJobDescription = `Job Title: ${job.title || 'Not specified'}
Department: ${job.department || 'Not specified'}
Location: ${job.location || 'Not specified'}
Employment Type: ${job.employment_type || 'Not specified'}
Experience Level: ${job.experience_level || 'Not specified'}
Salary Range: ${job.salary_range || 'Not specified'}
Company: ${job.companies?.name || 'Not specified'}

Requirements:
${job.requirements || 'Not specified'}

Responsibilities:
${job.responsibilities || job.description || 'Not specified'}

Benefits:
${job.benefits || 'Not specified'}

Additional Description:
${job.description || ''}`;

    // Get all candidates for the job
    const { data: candidates, error: candidatesError } = await supabase
      .from('candidates')
      .select('*')
      .eq('job_id', jobId);

    if (candidatesError) {
      console.error('Error fetching candidates:', candidatesError);
      throw new Error(`Failed to fetch candidates: ${candidatesError.message}`);
    }

    // If there are no candidates, return empty result
    if (!candidates || candidates.length === 0) {
      return {
        jobId,
        jobTitle: job.title,
        totalCandidates: 0,
        evaluatedCandidates: 0,
        rankings: [],
        evaluationSummary: {
          averageScore: 0,
          hireRecommendations: 0,
          considerRecommendations: 0,
          rejectRecommendations: 0,
        },
      };
    }

    const totalCandidates = candidates.length;
    const errors: Array<{ candidateId: string; candidateName: string; error: string }> = [];
    const validCandidates: any[] = [];

    // Process candidates and collect valid ones with error handling
    for (let i = 0; i < candidates.length; i++) {
      const candidate = candidates[i];

      // Report progress
      if (onProgress) {
        onProgress({
          current: i + 1,
          total: totalCandidates,
          candidateName: candidate.name || 'Unknown'
        });
      }

      try {
        // Check if candidate has evaluation summary (parsed CV)
        if (!candidate.evaluation_summary) {
          errors.push({
            candidateId: candidate.id,
            candidateName: candidate.name || 'Unknown',
            error: 'No CV data available - candidate needs to be processed first'
          });
          continue;
        }

        const evaluationSummary = JSON.parse(candidate.evaluation_summary);
        if (!evaluationSummary.parsedCV) {
          errors.push({
            candidateId: candidate.id,
            candidateName: candidate.name || 'Unknown',
            error: 'Invalid CV data - candidate needs to be re-processed'
          });
          continue;
        }

        validCandidates.push({
          id: candidate.id,
          name: candidate.name,
          parsedCV: evaluationSummary.parsedCV
        });
      } catch (parseError) {
        console.error(`Error processing candidate ${candidate.id}:`, parseError);
        errors.push({
          candidateId: candidate.id,
          candidateName: candidate.name || 'Unknown',
          error: 'Failed to parse candidate data'
        });
      }
    }

    // If no valid candidates, return result with errors
    if (validCandidates.length === 0) {
      return {
        jobId,
        jobTitle: job.title,
        totalCandidates,
        evaluatedCandidates: 0,
        rankings: [],
        evaluationSummary: {
          averageScore: 0,
          hireRecommendations: 0,
          considerRecommendations: 0,
          rejectRecommendations: 0,
        },
        errors,
      };
    }

    // Use the centralized AI API to score and rank candidates
    const rankingResult = await rankCandidatesWithAI(validCandidates, comprehensiveJobDescription);

    // Map the ranking result to our interface
    const candidateRankings: CandidateRanking[] = rankingResult.rankings.map((ranking: any) => ({
      candidateId: ranking.candidateId,
      name: ranking.name,
      overallScore: ranking.overallScore,
      skillsScore: ranking.skillsScore,
      experienceScore: ranking.experienceScore,
      educationScore: ranking.educationScore,
      culturalFitScore: ranking.culturalFitScore,
      strengths: ranking.strengths,
      areasForImprovement: ranking.areasForImprovement,
      recommendation: ranking.recommendation
    }));

    // Sort by overall score in descending order
    candidateRankings.sort((a, b) => b.overallScore - a.overallScore);

    // Calculate evaluation summary
    const averageScore = candidateRankings.reduce((sum, candidate) => sum + candidate.overallScore, 0) / candidateRankings.length;
    const hireRecommendations = candidateRankings.filter(c => c.recommendation === 'Hire').length;
    const considerRecommendations = candidateRankings.filter(c => c.recommendation === 'Consider').length;
    const rejectRecommendations = candidateRankings.filter(c => c.recommendation === 'Reject').length;

    const result: BulkEvaluationResult = {
      jobId,
      jobTitle: job.title,
      totalCandidates,
      evaluatedCandidates: candidateRankings.length,
      rankings: candidateRankings,
      topCandidate: candidateRankings.length > 0 ? candidateRankings[0] : undefined,
      evaluationSummary: {
        averageScore: Math.round(averageScore * 100) / 100,
        hireRecommendations,
        considerRecommendations,
        rejectRecommendations,
      },
    };

    // Add errors if any
    if (errors.length > 0) {
      result.errors = errors;
    }

    return result;
  } catch (error) {
    console.error('Error in bulk evaluation:', error);
    throw new Error(`Bulk evaluation failed: ${error.message}`);
  }
}

/**
 * Bulk evaluate selected candidates against a specific job, company, or all companies
 * This is for the candidate management page where users select multiple candidates
 */
export async function bulkEvaluateSelectedCandidates(
  candidateIds: string[],
  evaluationMode: 'specific-job' | 'all-company-jobs' | 'all-companies',
  targetId?: string // jobId for specific-job, companyId for all-company-jobs
): Promise<BulkEvaluationResult[]> {
  try {
    const results: BulkEvaluationResult[] = [];

    // Process each candidate
    for (const candidateId of candidateIds) {
      try {
        let matchResult: JobCandidateMatch;
        let jobTitle = 'Multiple Jobs';

        switch (evaluationMode) {
          case 'specific-job':
            if (!targetId) throw new Error('Job ID is required for specific job evaluation');
            matchResult = await matchCandidate(candidateId, targetId);

            // Get job title
            const { data: job } = await supabase
              .from('jobs')
              .select('title')
              .eq('id', targetId)
              .single();
            jobTitle = job?.title || 'Unknown Job';
            break;

          case 'all-company-jobs':
            if (!targetId) throw new Error('Company ID is required for company evaluation');
            matchResult = await matchCandidateToCompany(candidateId, targetId);

            // Get company name
            const { data: company } = await supabase
              .from('companies')
              .select('name')
              .eq('id', targetId)
              .single();
            jobTitle = `All jobs at ${company?.name || 'Unknown Company'}`;
            break;

          case 'all-companies':
            matchResult = await matchCandidateToAllCompanies(candidateId);
            jobTitle = 'All Companies';
            break;

          default:
            throw new Error('Invalid evaluation mode');
        }

        // Get candidate info
        const { data: candidate } = await supabase
          .from('candidates')
          .select('name')
          .eq('id', candidateId)
          .single();

        // Create a result for this candidate
        const candidateResult: BulkEvaluationResult = {
          jobId: targetId || 'multiple',
          jobTitle,
          totalCandidates: 1,
          evaluatedCandidates: 1,
          rankings: [{
            candidateId,
            name: candidate?.name || 'Unknown',
            overallScore: matchResult.overallScore,
            skillsScore: matchResult.skillsMatch?.score || 0,
            experienceScore: matchResult.experienceMatch?.score || 0,
            educationScore: matchResult.educationMatch?.score || 0,
            culturalFitScore: 0, // Not available in single evaluation
            strengths: matchResult.strengths,
            areasForImprovement: matchResult.gaps,
            recommendation: matchResult.overallScore >= 80 ? 'Hire' :
                           matchResult.overallScore >= 60 ? 'Consider' : 'Reject'
          }],
          topCandidate: {
            candidateId,
            name: candidate?.name || 'Unknown',
            overallScore: matchResult.overallScore,
            skillsScore: matchResult.skillsMatch?.score || 0,
            experienceScore: matchResult.experienceMatch?.score || 0,
            educationScore: matchResult.educationMatch?.score || 0,
            culturalFitScore: 0,
            strengths: matchResult.strengths,
            areasForImprovement: matchResult.gaps,
            recommendation: matchResult.overallScore >= 80 ? 'Hire' :
                           matchResult.overallScore >= 60 ? 'Consider' : 'Reject'
          },
          evaluationSummary: {
            averageScore: matchResult.overallScore,
            hireRecommendations: matchResult.overallScore >= 80 ? 1 : 0,
            considerRecommendations: matchResult.overallScore >= 60 && matchResult.overallScore < 80 ? 1 : 0,
            rejectRecommendations: matchResult.overallScore < 60 ? 1 : 0,
          }
        };

        results.push(candidateResult);
      } catch (error) {
        console.error(`Error evaluating candidate ${candidateId}:`, error);

        // Get candidate info for error reporting
        const { data: candidate } = await supabase
          .from('candidates')
          .select('name')
          .eq('id', candidateId)
          .single();

        // Create an error result
        const errorResult: BulkEvaluationResult = {
          jobId: targetId || 'multiple',
          jobTitle: 'Evaluation Failed',
          totalCandidates: 1,
          evaluatedCandidates: 0,
          rankings: [],
          evaluationSummary: {
            averageScore: 0,
            hireRecommendations: 0,
            considerRecommendations: 0,
            rejectRecommendations: 0,
          },
          errors: [{
            candidateId,
            candidateName: candidate?.name || 'Unknown',
            error: error.message || 'Evaluation failed'
          }]
        };

        results.push(errorResult);
      }
    }

    return results;
  } catch (error) {
    console.error('Error in bulk evaluation of selected candidates:', error);
    throw new Error(`Bulk evaluation failed: ${error.message}`);
  }
}


