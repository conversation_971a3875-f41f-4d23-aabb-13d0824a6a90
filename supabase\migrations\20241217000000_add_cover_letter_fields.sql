-- Add cover letter fields to candidates table
-- This migration adds optional cover letter storage capabilities

-- Add cover letter fields to the candidates table
ALTER TABLE public.candidates 
ADD COLUMN cover_letter_content TEXT,
ADD COLUMN cover_letter_url TEXT;

-- Add comments to document the new fields
COMMENT ON COLUMN public.candidates.cover_letter_content IS 'Optional cover letter text content (for copy/paste input)';
COMMENT ON COLUMN public.candidates.cover_letter_url IS 'Optional cover letter file URL (for uploaded files)';

-- Update the RLS policies to include the new fields (they inherit the existing policies)
-- No additional RLS changes needed as the new fields are part of the candidates table
