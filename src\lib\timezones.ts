/**
 * Timezone utilities and constants for interview scheduling
 */

export interface Timezone {
  value: string;
  label: string;
  offset: string;
  region: string;
}

export const TIMEZONES: Timezone[] = [
  // North America
  { value: 'America/New_York', label: 'Eastern Time (ET)', offset: 'UTC-5/-4', region: 'North America' },
  { value: 'America/Chicago', label: 'Central Time (CT)', offset: 'UTC-6/-5', region: 'North America' },
  { value: 'America/Denver', label: 'Mountain Time (MT)', offset: 'UTC-7/-6', region: 'North America' },
  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)', offset: 'UTC-8/-7', region: 'North America' },
  { value: 'America/Anchorage', label: 'Alaska Time (AKT)', offset: 'UTC-9/-8', region: 'North America' },
  { value: 'Pacific/Honolulu', label: 'Hawaii Time (HST)', offset: 'UTC-10', region: 'North America' },
  { value: 'America/Toronto', label: 'Toronto', offset: 'UTC-5/-4', region: 'North America' },
  { value: 'America/Vancouver', label: 'Vancouver', offset: 'UTC-8/-7', region: 'North America' },
  
  // Europe
  { value: 'Europe/London', label: 'London (GMT/BST)', offset: 'UTC+0/+1', region: 'Europe' },
  { value: 'Europe/Paris', label: 'Paris (CET/CEST)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Berlin', label: 'Berlin (CET/CEST)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Rome', label: 'Rome (CET/CEST)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Madrid', label: 'Madrid (CET/CEST)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Amsterdam', label: 'Amsterdam (CET/CEST)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Zurich', label: 'Zurich (CET/CEST)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Stockholm', label: 'Stockholm (CET/CEST)', offset: 'UTC+1/+2', region: 'Europe' },
  { value: 'Europe/Helsinki', label: 'Helsinki (EET/EEST)', offset: 'UTC+2/+3', region: 'Europe' },
  { value: 'Europe/Moscow', label: 'Moscow (MSK)', offset: 'UTC+3', region: 'Europe' },
  
  // Asia Pacific
  { value: 'Asia/Tokyo', label: 'Tokyo (JST)', offset: 'UTC+9', region: 'Asia Pacific' },
  { value: 'Asia/Shanghai', label: 'Shanghai (CST)', offset: 'UTC+8', region: 'Asia Pacific' },
  { value: 'Asia/Hong_Kong', label: 'Hong Kong (HKT)', offset: 'UTC+8', region: 'Asia Pacific' },
  { value: 'Asia/Singapore', label: 'Singapore (SGT)', offset: 'UTC+8', region: 'Asia Pacific' },
  { value: 'Asia/Seoul', label: 'Seoul (KST)', offset: 'UTC+9', region: 'Asia Pacific' },
  { value: 'Asia/Kolkata', label: 'Mumbai/Delhi (IST)', offset: 'UTC+5:30', region: 'Asia Pacific' },
  { value: 'Asia/Dubai', label: 'Dubai (GST)', offset: 'UTC+4', region: 'Asia Pacific' },
  { value: 'Asia/Bangkok', label: 'Bangkok (ICT)', offset: 'UTC+7', region: 'Asia Pacific' },
  { value: 'Asia/Jakarta', label: 'Jakarta (WIB)', offset: 'UTC+7', region: 'Asia Pacific' },
  { value: 'Australia/Sydney', label: 'Sydney (AEST/AEDT)', offset: 'UTC+10/+11', region: 'Asia Pacific' },
  { value: 'Australia/Melbourne', label: 'Melbourne (AEST/AEDT)', offset: 'UTC+10/+11', region: 'Asia Pacific' },
  { value: 'Australia/Perth', label: 'Perth (AWST)', offset: 'UTC+8', region: 'Asia Pacific' },
  { value: 'Pacific/Auckland', label: 'Auckland (NZST/NZDT)', offset: 'UTC+12/+13', region: 'Asia Pacific' },
  
  // South America
  { value: 'America/Sao_Paulo', label: 'São Paulo (BRT/BRST)', offset: 'UTC-3/-2', region: 'South America' },
  { value: 'America/Argentina/Buenos_Aires', label: 'Buenos Aires (ART)', offset: 'UTC-3', region: 'South America' },
  { value: 'America/Lima', label: 'Lima (PET)', offset: 'UTC-5', region: 'South America' },
  { value: 'America/Bogota', label: 'Bogotá (COT)', offset: 'UTC-5', region: 'South America' },
  
  // Africa
  { value: 'Africa/Cairo', label: 'Cairo (EET/EEST)', offset: 'UTC+2/+3', region: 'Africa' },
  { value: 'Africa/Johannesburg', label: 'Johannesburg (SAST)', offset: 'UTC+2', region: 'Africa' },
  { value: 'Africa/Lagos', label: 'Lagos (WAT)', offset: 'UTC+1', region: 'Africa' },
  { value: 'Africa/Nairobi', label: 'Nairobi (EAT)', offset: 'UTC+3', region: 'Africa' },
  
  // UTC
  { value: 'UTC', label: 'Coordinated Universal Time (UTC)', offset: 'UTC+0', region: 'UTC' },
];

/**
 * Get timezone by value
 */
export const getTimezoneByValue = (value: string): Timezone | undefined => {
  return TIMEZONES.find(tz => tz.value === value);
};

/**
 * Get timezones grouped by region
 */
export const getTimezonesByRegion = (): Record<string, Timezone[]> => {
  return TIMEZONES.reduce((acc, timezone) => {
    if (!acc[timezone.region]) {
      acc[timezone.region] = [];
    }
    acc[timezone.region].push(timezone);
    return acc;
  }, {} as Record<string, Timezone[]>);
};

/**
 * Get user's detected timezone
 */
export const getUserTimezone = (): string => {
  try {
    return Intl.DateTimeFormat().resolvedOptions().timeZone;
  } catch (error) {
    console.warn('Could not detect user timezone, falling back to UTC');
    return 'UTC';
  }
};

/**
 * Format time in a specific timezone
 */
export const formatTimeInTimezone = (date: Date, timezone: string, format: string = 'PPpp'): string => {
  try {
    return new Intl.DateTimeFormat('en-US', {
      timeZone: timezone,
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    }).format(date);
  } catch (error) {
    console.warn(`Could not format time in timezone ${timezone}, falling back to local time`);
    return date.toLocaleString();
  }
};

/**
 * Convert time from one timezone to another
 */
export const convertTimezone = (date: Date, fromTimezone: string, toTimezone: string): Date => {
  try {
    // Create a date string in the source timezone
    const sourceTime = new Intl.DateTimeFormat('en-CA', {
      timeZone: fromTimezone,
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);

    // Parse the date and create a new Date object
    const parsedDate = new Date(sourceTime);

    // Get the offset difference
    const sourceOffset = getTimezoneOffset(fromTimezone, date);
    const targetOffset = getTimezoneOffset(toTimezone, date);
    const offsetDiff = targetOffset - sourceOffset;

    // Apply the offset difference
    return new Date(parsedDate.getTime() + offsetDiff);
  } catch (error) {
    console.warn(`Could not convert timezone from ${fromTimezone} to ${toTimezone}`);
    return date;
  }
};

/**
 * Get timezone offset in milliseconds
 */
export const getTimezoneOffset = (timezone: string, date: Date): number => {
  try {
    const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }));
    const tzDate = new Date(date.toLocaleString('en-US', { timeZone: timezone }));
    return utcDate.getTime() - tzDate.getTime();
  } catch (error) {
    console.warn(`Could not get timezone offset for ${timezone}`);
    return 0;
  }
};
