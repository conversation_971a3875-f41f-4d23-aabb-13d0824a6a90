/**
 * PayPal Configuration
 *
 * This file contains configuration settings for PayPal integration.
 * Different settings are used for development and production environments.
 */

// Environment detection
const paypalEnv = import.meta.env.VITE_PAYPAL_ENVIRONMENT || 'sandbox';
const isProduction = paypalEnv === 'production';

// PayPal Client IDs - Hardcoded to avoid environment variable issues
// For production, use the live client ID from your PayPal developer account
// For development, use the sandbox client ID
export const PAYPAL_CLIENT_ID = isProduction
  ? 'Aff-ccbKrEamBT-wqkGposzmazAuJ4jFNOEI5Xs2a3mQpipXDlwM6ZXWzX4u8L9lnNo8vOAhp59dbJPi' // Live client ID
  : 'AZDxjDScFpQtjWTOUtWKbyN_bDt4OgqaF4eYXlewfBP4-8aqX3PiV8e1GWU6liB2CUXlkA59kJXE7M6R'; // Sandbox client ID

// PayPal API URLs
export const PAYPAL_API_URL = isProduction
  ? 'https://api.paypal.com' // Live API URL
  : 'https://api.sandbox.paypal.com'; // Sandbox API URL

// PayPal SDK Script URL
export const PAYPAL_SDK_URL = `https://www.paypal.com/sdk/js?client-id=${PAYPAL_CLIENT_ID}&currency=USD&intent=subscription`;

// Subscription plan IDs - Hardcoded to avoid environment variable issues
// These should match the plan IDs created in your PayPal dashboard
export const SUBSCRIPTION_PLANS = {
  STARTER: isProduction
    ? 'P-36S468193L538630ENAJAR6I' // Live Starter Plan ID
    : 'P-5ML4271244454362WXNWU5NQ', // Sandbox Starter Plan ID
  GROWTH: isProduction
    ? 'P-8XR40709L5499133VNAJASDA' // Live Growth Plan ID
    : 'P-3RX095426A3469222XNWU5VY', // Sandbox Growth Plan ID
  PRO: isProduction
    ? 'P-4NX64869RB646951HNAJASFQ' // Live Pro Plan ID
    : 'P-8XB43134SS218891MXNWU6MA', // Sandbox Pro Plan ID
};

// Define available features
export const FEATURES = {
  CV_UPLOAD: 'cv_upload',
  JOB_POSTING: 'job_posting',
  COMPANY_MANAGEMENT: 'company_management',
  CV_EVALUATION: 'cv_evaluation',
  REPORTS: 'reports',
  INTERVIEW_SCHEDULING: 'interview_scheduling',
  TEAM_MANAGEMENT: 'team_management',
  CUSTOM_SCORING: 'custom_scoring',
  ADVANCED_ANALYTICS: 'advanced_analytics',
  API_ACCESS: 'api_access',
  WHITE_LABEL: 'white_label',
  PRIORITY_SUPPORT: 'priority_support'
} as const;

// Feature access configuration by plan
export const PLAN_FEATURES = {
  STARTER: {
    [FEATURES.CV_UPLOAD]: true,
    [FEATURES.JOB_POSTING]: true,
    [FEATURES.COMPANY_MANAGEMENT]: true,
    [FEATURES.CV_EVALUATION]: true,
    [FEATURES.REPORTS]: true,
    [FEATURES.INTERVIEW_SCHEDULING]: false, // Disabled for basic plan
    [FEATURES.TEAM_MANAGEMENT]: false,
    [FEATURES.CUSTOM_SCORING]: false,
    [FEATURES.ADVANCED_ANALYTICS]: false,
    [FEATURES.API_ACCESS]: false,
    [FEATURES.WHITE_LABEL]: false,
    [FEATURES.PRIORITY_SUPPORT]: false
  },
  GROWTH: {
    [FEATURES.CV_UPLOAD]: true,
    [FEATURES.JOB_POSTING]: true,
    [FEATURES.COMPANY_MANAGEMENT]: true,
    [FEATURES.CV_EVALUATION]: true,
    [FEATURES.REPORTS]: true,
    [FEATURES.INTERVIEW_SCHEDULING]: true, // Enabled for growth plan
    [FEATURES.TEAM_MANAGEMENT]: true,
    [FEATURES.CUSTOM_SCORING]: false,
    [FEATURES.ADVANCED_ANALYTICS]: true,
    [FEATURES.API_ACCESS]: false,
    [FEATURES.WHITE_LABEL]: false,
    [FEATURES.PRIORITY_SUPPORT]: false
  },
  PRO: {
    [FEATURES.CV_UPLOAD]: true,
    [FEATURES.JOB_POSTING]: true,
    [FEATURES.COMPANY_MANAGEMENT]: true,
    [FEATURES.CV_EVALUATION]: true,
    [FEATURES.REPORTS]: true,
    [FEATURES.INTERVIEW_SCHEDULING]: true,
    [FEATURES.TEAM_MANAGEMENT]: true,
    [FEATURES.CUSTOM_SCORING]: true,
    [FEATURES.ADVANCED_ANALYTICS]: true,
    [FEATURES.API_ACCESS]: true,
    [FEATURES.WHITE_LABEL]: true,
    [FEATURES.PRIORITY_SUPPORT]: true
  }
} as const;

// Pricing information (for display purposes)
export const PRICING = {
  STARTER: {
    price: 49,
    currency: 'USD',
    period: 'month',
    features: [
      'Upload up to 50 CVs per month',
      'Post 1 job at a time',
      '1 company profile',
      'Automatic CV evaluation',
      'Simple hiring reports',
    ],
    enabledFeatures: PLAN_FEATURES.STARTER,
  },
  GROWTH: {
    price: 99,
    currency: 'USD',
    period: 'month',
    features: [
      'Upload up to 500 CVs per month',
      'Post up to 5 jobs at a time',
      'Manage up to 5 company profiles',
      'Smart CV matching with job roles',
      'Download reports (PDF or Excel)',
      'Insights to improve your hiring',
    ],
    enabledFeatures: PLAN_FEATURES.GROWTH,
  },
  PRO: {
    price: 199,
    currency: 'USD',
    period: 'month',
    features: [
      'Unlimited CV uploads',
      'Unlimited job postings',
      'Unlimited company profiles',
      'Custom rules for candidate scoring',
      'Add team members and collaborate',
      'Detailed hiring reports and insights',
    ],
    enabledFeatures: PLAN_FEATURES.PRO,
  },
};

// Webhook secret for verifying PayPal webhook events
// This should be set in your environment variables for security
export const WEBHOOK_SECRET = import.meta.env.VITE_PAYPAL_WEBHOOK_SECRET || '';
