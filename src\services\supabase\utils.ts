/**
 * Safely execute a database operation with error handling
 * @param operation The database operation to execute
 * @param errorMessage The error message to display if the operation fails
 * @param defaultValue The default value to return if the operation fails
 * @returns The result of the operation or the default value if it fails
 */
export async function safeDbOperation<T>(
  operation: () => Promise<T>,
  errorMessage: string,
  defaultValue: T
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    console.error(`${errorMessage}:`, error);
    return defaultValue;
  }
}
