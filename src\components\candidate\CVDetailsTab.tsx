import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { FileText, Download } from 'lucide-react';
import CVPreview from '@/components/cv/CVPreview.jsx';

interface CVDetailsTabProps {
  candidate: any;
  setIsEvaluateModalOpen: (isOpen: boolean) => void;
}

const CVDetailsTab: React.FC<CVDetailsTabProps> = ({
  candidate,
  setIsEvaluateModalOpen
}) => {
  return (
    <div className="grid grid-cols-1 gap-6">
      <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="text-gray-800 text-lg">CV Document</CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              className="text-gray-700"
              onClick={() => window.open(candidate.cv_url, '_blank')}
            >
              <Download className="mr-2 h-4 w-4" /> Download
            </Button>
            <Button
              className="bg-recruiter-lightblue hover:bg-blue-500"
              onClick={() => setIsEvaluateModalOpen(true)}
            >
              Evaluate Now
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <CVPreview
            url={candidate.cv_url}
            fileName={candidate.name ? `${candidate.name.replace(/\s+/g, '_')}.pdf` : 'resume.pdf'}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default CVDetailsTab;
