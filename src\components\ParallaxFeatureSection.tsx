import React, { useRef } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import {
  FileText, Building, CheckCircle, BarChart2, Users, Award, TrendingUp,
  Zap, Sparkles, CalendarClock, Settings, Database
} from 'lucide-react';
import { useDynamicPricing } from '@/hooks/use-dynamic-pricing';

// Feature data with images and detailed information
const featureData = [
  {
    id: 'cv-upload',
    icon: <FileText className="h-6 w-6" />,
    title: "CV Upload & Processing",
    description: "Upload CVs in multiple formats and our AI automatically extracts key information.",
    color: "from-blue-500 to-blue-600",
    image: "https://images.unsplash.com/photo-1586281380349-632531db7ed4?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    stats: "Process bulk CVs per day",
    detailedDescription: "Our AI parses CVs in seconds, extracting skills, experience, and qualifications with 95% accuracy."
  },
  {
    id: 'company-mgmt',
    icon: <Building className="h-6 w-6" />,
    title: "Company Management",
    description: "Create and manage multiple company profiles with detailed requirements.",
    color: "from-purple-500 to-purple-600",
    image: "https://images.unsplash.com/photo-1568992687947-868a62a9f521?ixlib=rb-4.0.3&auto=format&fit=crop&w=1932&q=80",
    stats: "Manage unlimited companies",
    detailedDescription: "Organize job requirements by company, department, and position for precise candidate matching."
  },
  {
    id: 'ai-evaluation',
    icon: <CheckCircle className="h-6 w-6" />,
    title: "AI-Powered Evaluation",
    description: "Our advanced AI evaluates candidates against company requirements.",
    color: "from-green-500 to-green-600",
    image: "https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    stats: "95% matching accuracy",
    detailedDescription: "Get percentage-based matching scores and detailed skill analysis for each candidate."
  },
  {
    id: 'reports',
    icon: <BarChart2 className="h-6 w-6" />,
    title: "Comprehensive Reports",
    description: "Generate detailed reports on candidate evaluations and match quality.",
    color: "from-amber-500 to-amber-600",
    image: "https://images.unsplash.com/photo-*************-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=2015&q=80",
    stats: "Export in PDF, Excel, CSV",
    detailedDescription: "Share professional reports with clients showing candidate rankings and skill distribution."
  },
  {
    id: 'team',
    icon: <Users className="h-6 w-6" />,
    title: "Team Collaboration",
    description: "Add team members to your agency account and collaborate on evaluations.",
    color: "from-pink-500 to-pink-600",
    image: "https://images.unsplash.com/photo-*************-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    stats: "Unlimited team members",
    detailedDescription: "Work together seamlessly with role-based permissions and shared candidate pools."
  },
  {
    id: 'analytics',
    icon: <TrendingUp className="h-6 w-6" />,
    title: "Performance Analytics",
    description: "Track your agency's performance with detailed analytics.",
    color: "from-indigo-500 to-indigo-600",
    image: "https://images.unsplash.com/photo-**********-bebda4e38f71?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
    stats: "Real-time dashboards",
    detailedDescription: "Monitor placement rates, evaluation accuracy, and client satisfaction metrics."
  }
];

// Add dynamic features based on database - only show features available on at least one plan
const getDynamicFeatures = (pricingTiers: any[]) => {
  const dynamicFeatures = [];

  // Add Interview Scheduling if available in ANY plan (hide if disabled on all plans)
  const hasInterviewScheduling = pricingTiers.some(tier =>
    tier.enabledFeatures?.interview_scheduling
  );

  if (hasInterviewScheduling) {
    dynamicFeatures.push({
      id: 'interview-scheduling',
      icon: <CalendarClock className="h-6 w-6" />,
      title: "Interview Scheduling",
      description: "Schedule and manage candidate interviews seamlessly.",
      color: "from-emerald-500 to-emerald-600",
      image: "https://images.unsplash.com/photo-1506784983877-45594efa4cbe?ixlib=rb-4.0.3&auto=format&fit=crop&w=2068&q=80",
      stats: "Automated scheduling",
      detailedDescription: "Coordinate interviews with candidates and hiring managers with automated reminders.",
      planAvailability: pricingTiers.filter(tier => tier.enabledFeatures?.interview_scheduling).map(tier => tier.planKey)
    });
  }

  // Add Custom Scoring if available in ANY plan (hide if disabled on all plans)
  const hasCustomScoring = pricingTiers.some(tier =>
    tier.enabledFeatures?.custom_scoring
  );

  if (hasCustomScoring) {
    dynamicFeatures.push({
      id: 'custom-scoring',
      icon: <Settings className="h-6 w-6" />,
      title: "Custom Scoring Rules",
      description: "Create custom evaluation criteria for different roles.",
      color: "from-violet-500 to-violet-600",
      image: "https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80",
      stats: "Tailored evaluations",
      detailedDescription: "Define specific scoring criteria that match your unique hiring requirements.",
      planAvailability: pricingTiers.filter(tier => tier.enabledFeatures?.custom_scoring).map(tier => tier.planKey)
    });
  }

  return dynamicFeatures;
};

// Feature Card Component with hover effects and animations
const FeatureCard: React.FC<{
  feature: typeof featureData[0] & { planAvailability?: string[] };
  index: number;
}> = ({ feature, index }) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.4, delay: index * 0.1 }}
      whileHover={{ scale: 1.02, transition: { duration: 0.2 } }}
      className="relative overflow-hidden rounded-xl group transition-all duration-300 hover:ring-1 hover:ring-white/10"
    >
      {/* Background image with overlay */}
      <div className="absolute inset-0 z-0">
        <div className="absolute inset-0 bg-gradient-to-br from-recruiter-navy/90 to-recruiter-navy/70 z-10" />
        <img
          src={feature.image}
          alt={feature.title}
          className="w-full h-full object-cover object-center transition-transform duration-500 group-hover:scale-110"
        />
      </div>

      {/* Content */}
      <div className="relative z-20 p-5 h-full flex flex-col">
        <div className="flex items-center gap-3 mb-2">
          <div className={`w-10 h-10 rounded-lg bg-gradient-to-br ${feature.color} flex items-center justify-center`}>
            {React.cloneElement(feature.icon as React.ReactElement, { className: "h-5 w-5 text-white" })}
          </div>
          <h3 className="text-lg font-bold text-white">{feature.title}</h3>
        </div>

        <p className="text-gray-300 text-sm mb-3">
          {feature.description}
        </p>

        <div className="mt-auto">
          <div className="flex items-center gap-1.5 text-xs font-medium text-white/80 mb-2">
            <Zap className="h-3.5 w-3.5 text-yellow-400" />
            <span>{feature.stats}</span>
          </div>

          {/* Plan Availability Badges */}
          {feature.planAvailability && feature.planAvailability.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {feature.planAvailability.map((plan) => (
                <span
                  key={plan}
                  className="px-2 py-1 text-xs font-medium bg-white/20 text-white rounded-full"
                >
                  {plan}+
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </motion.div>
  );
};

// Main Component
export const ParallaxFeatureSection: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start end", "end start"]
  });

  const y1 = useTransform(scrollYProgress, [0, 1], ["0%", "20%"]);
  const y2 = useTransform(scrollYProgress, [0, 1], ["0%", "-20%"]);
  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);

  // Get dynamic pricing data
  const { pricingTiers, hasDbFeatures } = useDynamicPricing();

  // Combine static and dynamic features
  const allFeatures = [...featureData, ...getDynamicFeatures(pricingTiers)];

  return (
    <section
      ref={containerRef}
      className="bg-recruiter-navy relative overflow-hidden pb-16"
    >
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          style={{ y: y1 }}
          className="absolute -top-[100px] -right-[100px] w-[400px] h-[400px] rounded-full bg-indigo-500/5 blur-3xl"
        ></motion.div>
        <motion.div
          style={{ y: y2 }}
          className="absolute -bottom-[100px] -left-[100px] w-[400px] h-[400px] rounded-full bg-blue-500/5 blur-3xl"
        ></motion.div>
      </div>

      <div className="container mx-auto px-4 sm:px-6 relative z-10">
        <motion.div
          style={{ opacity }}
          className="text-center mb-8"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center gap-2 px-4 py-1.5 bg-white/5 rounded-full mb-4"
          >
            <Sparkles className="h-4 w-4 text-yellow-400" />
            <span className="text-sm font-medium text-white/80">AI-Powered Recruitment</span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-3xl sm:text-4xl font-bold text-white mb-3"
          >
            Powerful Features for Recruitment Agencies
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-base sm:text-lg text-gray-300 max-w-2xl mx-auto"
          >
            Our platform streamlines the recruitment process with AI-powered tools designed specifically for agencies
          </motion.p>

        </motion.div>

        {/* Feature grid */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
          className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4"
        >
          {allFeatures.map((feature, index) => (
            <FeatureCard
              key={feature.id}
              feature={feature}
              index={index}
            />
          ))}
        </motion.div>
      </div>
    </section>
  );
};
