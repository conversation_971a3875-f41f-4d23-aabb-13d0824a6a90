-- Add <PERSON> as a supported AI provider
-- Drop the existing constraint
ALTER TABLE public.ai_settings DROP CONSTRAINT IF EXISTS ai_settings_provider_check;

-- Add the new constraint with Gemini included
ALTER TABLE public.ai_settings ADD CONSTRAINT ai_settings_provider_check 
CHECK (provider IN ('groq', 'anthropic', 'openai', 'gemini'));

-- Insert default Gemini settings with properly encrypted API key
-- The API key 'AIzaSyAEx8rFrwEPtJXCuCxP5w0SmfRhpFLdBzU' encrypted with base64('ai-settings-key-2025:AIzaSyAEx8rFrwEPtJXCuCxP5w0SmfRhpFLdBzU')
INSERT INTO public.ai_settings (provider, model, api_key, is_active, metadata)
VALUES (
    'gemini',
    'gemini-2.5-pro',
    'YWktc2V0dGluZ3Mta2V5LTIwMjU6QUl6YVN5QUV4OHJGcndFUHRKWEN1Q3hQNXcwU21mUmhwRkxkQnpV', -- Encrypted API key
    false, -- Not active by default
    '{"description": "Default Gemini configuration", "max_tokens": 65536, "temperature": 0.1}'
) ON CONFLICT DO NOTHING;
