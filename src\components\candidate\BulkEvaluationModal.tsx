import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useBulkEvaluateCandidates } from '@/hooks/use-jobs';
import { BulkEvaluationResult } from '@/services/cv-processing/jobMatcher';
import BulkEvaluationProgress from './BulkEvaluationProgress';
import BulkEvaluationResults from './BulkEvaluationResults';
import {
  Users,
  AlertTriangle,
  RefreshCw,
  X,
} from 'lucide-react';

interface BulkEvaluationModalProps {
  isOpen: boolean;
  onClose: () => void;
  jobId: string;
  jobTitle: string;
  candidateCount: number;
  onCandidateSelect?: (candidateId: string) => void;
}

const BulkEvaluationModal: React.FC<BulkEvaluationModalProps> = ({
  isOpen,
  onClose,
  jobId,
  jobTitle,
  candidateCount,
  onCandidateSelect,
}) => {
  const { toast } = useToast();
  const [evaluationResult, setEvaluationResult] = useState<BulkEvaluationResult | null>(null);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [hasStarted, setHasStarted] = useState(false);

  const {
    mutateAsync: bulkEvaluate,
    isPending: isEvaluating,
    error: evaluationError,
    reset: resetEvaluation,
  } = useBulkEvaluateCandidates();

  // Reset state when modal opens/closes
  useEffect(() => {
    if (isOpen && !hasStarted) {
      setEvaluationResult(null);
      setProgress(0);
      setCurrentStep('');
      resetEvaluation();
    } else if (!isOpen) {
      setHasStarted(false);
      setEvaluationResult(null);
      setProgress(0);
      setCurrentStep('');
      resetEvaluation();
    }
  }, [isOpen, hasStarted, resetEvaluation]);

  // Start evaluation automatically when modal opens
  useEffect(() => {
    if (isOpen && !hasStarted && !isEvaluating && !evaluationResult) {
      startEvaluation();
    }
  }, [isOpen, hasStarted, isEvaluating, evaluationResult]);

  // Simulate progress updates during evaluation
  useEffect(() => {
    if (isEvaluating && !evaluationResult) {
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev < 90) {
            // Update step based on progress
            if (prev < 25) {
              setCurrentStep('Fetching candidate data and CVs...');
            } else if (prev < 50) {
              setCurrentStep('Analyzing candidate profiles...');
            } else if (prev < 75) {
              setCurrentStep('Running AI evaluation and scoring...');
            } else {
              setCurrentStep('Ranking candidates and generating insights...');
            }
            return prev + Math.random() * 10;
          }
          return prev;
        });
      }, 500);

      return () => clearInterval(progressInterval);
    }
  }, [isEvaluating, evaluationResult]);

  const startEvaluation = async () => {
    if (candidateCount === 0) {
      toast({
        title: 'No Candidates',
        description: 'There are no candidates to evaluate for this job.',
        variant: 'destructive',
      });
      return;
    }

    setHasStarted(true);
    setProgress(10);
    setCurrentStep('Initializing bulk evaluation...');

    try {
      const result = await bulkEvaluate(jobId);
      setEvaluationResult(result);
      setProgress(100);
      setCurrentStep('Evaluation completed successfully!');
      
      toast({
        title: 'Evaluation Complete',
        description: `Successfully evaluated ${result.evaluatedCandidates} candidates.`,
      });
    } catch (error) {
      console.error('Bulk evaluation failed:', error);
      setProgress(0);
      setCurrentStep('Evaluation failed');
      
      toast({
        title: 'Evaluation Failed',
        description: error.message || 'Failed to evaluate candidates. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleRetry = () => {
    setEvaluationResult(null);
    setProgress(0);
    setCurrentStep('');
    setHasStarted(false);
    resetEvaluation();
  };

  const handleCandidateSelect = (candidateId: string) => {
    onCandidateSelect?.(candidateId);
    onClose();
  };

  const handleExportResults = () => {
    if (!evaluationResult) return;

    // Create CSV content
    const csvContent = [
      ['Rank', 'Name', 'Overall Score', 'Skills Score', 'Experience Score', 'Education Score', 'Recommendation', 'Strengths', 'Areas for Improvement'],
      ...evaluationResult.rankings.map((candidate, index) => [
        index + 1,
        candidate.name,
        candidate.overallScore,
        candidate.skillsScore,
        candidate.experienceScore,
        candidate.educationScore,
        candidate.recommendation,
        candidate.strengths.join('; '),
        candidate.areasForImprovement.join('; ')
      ])
    ].map(row => row.map(cell => `"${cell}"`).join(',')).join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `bulk-evaluation-${jobTitle.replace(/\s+/g, '-').toLowerCase()}-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);

    toast({
      title: 'Export Complete',
      description: 'Evaluation results have been downloaded as CSV.',
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Bulk Candidate Evaluation
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-4 h-4" />
            </Button>
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Show progress while evaluating or if no results yet */}
          {(isEvaluating || (!evaluationResult && !evaluationError)) && (
            <BulkEvaluationProgress
              isEvaluating={isEvaluating}
              jobTitle={jobTitle}
              totalCandidates={candidateCount}
              currentStep={currentStep}
              progress={progress}
              onCancel={onClose}
              canCancel={!hasStarted || progress < 50}
            />
          )}

          {/* Show error state */}
          {evaluationError && !isEvaluating && (
            <div className="text-center py-8">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">Evaluation Failed</h3>
              <p className="text-gray-600 mb-4">
                {evaluationError.message || 'An error occurred while evaluating candidates.'}
              </p>
              <div className="flex justify-center space-x-3">
                <Button onClick={handleRetry} className="bg-blue-600 hover:bg-blue-700">
                  <RefreshCw className="w-4 h-4 mr-2" />
                  Try Again
                </Button>
                <Button variant="outline" onClick={onClose}>
                  Close
                </Button>
              </div>
            </div>
          )}

          {/* Show results when evaluation is complete */}
          {evaluationResult && !isEvaluating && (
            <BulkEvaluationResults
              evaluationResult={evaluationResult}
              onCandidateSelect={handleCandidateSelect}
              onExportResults={handleExportResults}
            />
          )}

          {/* Show empty state if no candidates */}
          {candidateCount === 0 && !isEvaluating && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800">No Candidates to Evaluate</h3>
              <p className="text-gray-500 mt-1">
                This job doesn't have any candidates yet. Add some candidates first.
              </p>
              <Button variant="outline" onClick={onClose} className="mt-4">
                Close
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BulkEvaluationModal;
