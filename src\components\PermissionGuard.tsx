import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions, ResourceType, ActionType, RoleType } from '@/contexts/PermissionsContext';
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react';

interface PermissionGuardProps {
  children: React.ReactNode;
  resource?: ResourceType;
  action?: ActionType;
  requiredRoles?: RoleType[];
  fallback?: React.ReactNode;
}

/**
 * A component that guards routes based on user permissions
 *
 * @param children The content to render if the user has permission
 * @param resource The resource being accessed
 * @param action The action being performed on the resource
 * @param requiredRoles Specific roles that are allowed to access this route
 * @param fallback Optional fallback UI to show instead of redirecting
 */
const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  resource,
  action,
  requiredRoles,
  fallback
}) => {
  const { isAuthenticated, isLoading: authLoading, user } = useAuth();
  const { can, hasRole, isLoading: permissionsLoading } = usePermissions();
  const location = useLocation();

  const [showError, setShowError] = useState(false);
  const isLoading = authLoading || permissionsLoading;

  // Set a timeout to show an error message if loading takes too long
  useEffect(() => {
    let timeoutId: NodeJS.Timeout;

    if (isLoading) {
      timeoutId = setTimeout(() => {
        setShowError(true);
      }, 8000); // Show error after 8 seconds of loading
    } else {
      setShowError(false);
    }

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
    };
  }, [isLoading]);

  // Force reload after 20 seconds if still loading
  useEffect(() => {
    let reloadTimeoutId: NodeJS.Timeout;

    if (isLoading) {
      reloadTimeoutId = setTimeout(() => {
        console.log('Still loading after 20 seconds, forcing reload');
        window.location.reload();
      }, 20000);
    }

    return () => {
      if (reloadTimeoutId) clearTimeout(reloadTimeoutId);
    };
  }, [isLoading]);

  // Show loading state while checking authentication and permissions
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex flex-col items-center space-y-4">
          {showError ? (
            <>
              <div className="p-4 rounded-full bg-red-100">
                <AlertCircle className="h-12 w-12 text-red-500" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">Loading Error</h2>
              <p className="text-gray-600 text-center max-w-md">
                We're having trouble loading your permissions. Please try refreshing the page.
              </p>
              <div className="flex space-x-4 mt-2">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Refresh Page
                </button>
                <button
                  onClick={() => {
                    // Clear local storage and session storage
                    localStorage.clear();
                    sessionStorage.clear();
                    // Redirect to login
                    window.location.href = '/login';
                  }}
                  className="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
                >
                  Go to Login
                </button>
              </div>
            </>
          ) : (
            <>
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
              <p className="text-lg text-gray-600">Loading...</p>
            </>
          )}
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Redirect candidates away from dashboard routes
  if (user?.userType === 'candidate' && location.pathname.startsWith('/dashboard')) {
    return <Navigate to="/candidate-dashboard" replace />;
  }

  // Check resource and action permissions if specified
  if (resource && action && !can(resource, action)) {
    if (fallback) {
      return <>{fallback}</>;
    }
    // Redirect candidates to their dashboard instead of main dashboard
    const redirectTo = user?.userType === 'candidate' ? '/candidate-dashboard' : '/dashboard';
    return <Navigate to={redirectTo} replace />;
  }

  // Check role permissions if specified
  if (requiredRoles && !hasRole(requiredRoles)) {
    if (fallback) {
      return <>{fallback}</>;
    }
    // Redirect candidates to their dashboard instead of main dashboard
    const redirectTo = user?.userType === 'candidate' ? '/candidate-dashboard' : '/dashboard';
    return <Navigate to={redirectTo} replace />;
  }

  // Render children if all checks pass
  return <>{children}</>;
};

export default PermissionGuard;
