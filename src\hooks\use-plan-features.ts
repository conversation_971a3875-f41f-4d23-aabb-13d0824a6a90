import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useSubscription } from '@/hooks/use-profile';
import { FEATURES, PLAN_FEATURES } from '@/config/paypal';
import { useQuery } from '@tanstack/react-query';
import { getPlanFeaturesConfig } from '@/services/supabase/planFeatures';

/**
 * Hook to check feature access based on user's subscription plan
 */
export const usePlanFeatures = () => {
  const { user } = useAuth();
  const { hasRole } = usePermissions();
  const { data: subscription } = useSubscription();

  // Get user's current plan
  const currentPlan = subscription?.tier?.toUpperCase() as keyof typeof PLAN_FEATURES || 'STARTER';

  // Platform admins always have access to all features
  const isPlatformAdmin = hasRole(['platform_admin']);

  // Fetch plan features from database with fallback to static config
  const { data: dbPlanFeatures } = useQuery({
    queryKey: ['plan-features'],
    queryFn: getPlanFeaturesConfig,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });

  // Use database config if available, otherwise fall back to static config
  const planFeaturesConfig = dbPlanFeatures || PLAN_FEATURES;

  /**
   * Check if a specific feature is available for the current user
   */
  const hasFeature = (feature: keyof typeof FEATURES): boolean => {
    if (isPlatformAdmin) return true;

    const featureKey = FEATURES[feature];
    return planFeaturesConfig[currentPlan]?.[featureKey] || false;
  };

  /**
   * Get the minimum plan required for a feature
   */
  const getMinimumPlan = (feature: keyof typeof FEATURES): string => {
    const featureKey = FEATURES[feature];
    const plans = ['STARTER', 'GROWTH', 'PRO'] as const;

    for (const plan of plans) {
      if (planFeaturesConfig[plan]?.[featureKey]) {
        return plan;
      }
    }
    return 'PRO'; // Default to PRO if feature not found
  };

  /**
   * Get all available features for the current plan
   */
  const getAvailableFeatures = (): string[] => {
    if (isPlatformAdmin) {
      return Object.values(FEATURES);
    }

    const planFeatures = planFeaturesConfig[currentPlan];
    return Object.entries(planFeatures || {})
      .filter(([_, enabled]) => enabled)
      .map(([feature, _]) => feature);
  };

  /**
   * Get all disabled features for the current plan
   */
  const getDisabledFeatures = (): string[] => {
    if (isPlatformAdmin) {
      return [];
    }

    const planFeatures = planFeaturesConfig[currentPlan];
    return Object.entries(planFeatures || {})
      .filter(([_, enabled]) => !enabled)
      .map(([feature, _]) => feature);
  };

  /**
   * Check if user can upgrade to access a feature
   */
  const canUpgradeForFeature = (feature: keyof typeof FEATURES): boolean => {
    const requiredPlan = getMinimumPlan(feature);
    const planHierarchy = ['STARTER', 'GROWTH', 'PRO'];
    const currentIndex = planHierarchy.indexOf(currentPlan);
    const requiredIndex = planHierarchy.indexOf(requiredPlan);
    
    return currentIndex < requiredIndex;
  };

  return {
    currentPlan: currentPlan.toLowerCase(),
    hasFeature,
    getMinimumPlan,
    getAvailableFeatures,
    getDisabledFeatures,
    canUpgradeForFeature,
    isPlatformAdmin
  };
};

/**
 * Convenience hooks for specific features
 */
export const useInterviewScheduling = () => {
  const { hasFeature } = usePlanFeatures();
  return hasFeature('INTERVIEW_SCHEDULING');
};

export const useTeamManagement = () => {
  const { hasFeature } = usePlanFeatures();
  return hasFeature('TEAM_MANAGEMENT');
};

export const useCustomScoring = () => {
  const { hasFeature } = usePlanFeatures();
  return hasFeature('CUSTOM_SCORING');
};

export const useAdvancedAnalytics = () => {
  const { hasFeature } = usePlanFeatures();
  return hasFeature('ADVANCED_ANALYTICS');
};

export const useApiAccess = () => {
  const { hasFeature } = usePlanFeatures();
  return hasFeature('API_ACCESS');
};

export const useWhiteLabel = () => {
  const { hasFeature } = usePlanFeatures();
  return hasFeature('WHITE_LABEL');
};
