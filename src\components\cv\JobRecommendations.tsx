import React from 'react';
import { <PERSON>, Check, Briefcase } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { JobSpecificMatch } from '@/services/cv-processing/jobMatcher';

interface JobRecommendationsProps {
  topJobs: JobSpecificMatch[];
  candidateName: string;
  onViewJob?: (jobId: string) => void;
}

const JobRecommendations: React.FC<JobRecommendationsProps> = ({ 
  topJobs, 
  candidateName,
  onViewJob 
}) => {
  // Get color based on match score
  const getMatchColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 75) return 'text-blue-500';
    if (score >= 60) return 'text-amber-500';
    return 'text-red-500';
  };

  // Get recommendation text based on score
  const getRecommendationText = (score: number) => {
    if (score >= 90) return 'Excellent Match';
    if (score >= 75) return 'Strong Match';
    if (score >= 60) return 'Good Match';
    return 'Potential Match';
  };

  // Get badge color based on score
  const getBadgeColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 75) return 'bg-blue-500';
    if (score >= 60) return 'bg-amber-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-4">
      {topJobs.length > 0 ? (
        <>
          <p className="text-sm text-gray-500 mb-4">
            Based on {candidateName}'s profile, here are the top job recommendations:
          </p>
          
          <div className="space-y-4">
            {topJobs.map((job, index) => (
              <div 
                key={job.jobId}
                className={`p-4 rounded-lg border ${index === 0 ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'}`}
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-medium text-gray-800 flex items-center">
                      {index === 0 && <Badge className={`mr-2 ${getBadgeColor(job.overallScore)}`}>Best Match</Badge>}
                      {job.jobTitle}
                    </h3>
                    <p className="text-sm text-gray-500 mt-1">
                      {getRecommendationText(job.overallScore)}
                    </p>
                  </div>
                  <div className="flex items-center">
                    <span className={`text-xl font-bold ${getMatchColor(job.overallScore)}`}>
                      {job.overallScore}%
                    </span>
                    <Star className={`ml-1 h-5 w-5 ${getMatchColor(job.overallScore)}`} fill="currentColor" />
                  </div>
                </div>
                
                <div className="mb-3">
                  <h4 className="text-sm font-medium text-gray-700 mb-2">Why this is a good match:</h4>
                  <ul className="space-y-1">
                    {job.strengths.slice(0, 3).map((strength, i) => (
                      <li key={i} className="flex items-start text-sm">
                        <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{strength}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {onViewJob && (
                  <Button 
                    variant="outline" 
                    size="sm" 
                    className="w-full mt-2 text-xs border-gray-200 text-gray-700 hover:bg-gray-100"
                    onClick={() => onViewJob(job.jobId)}
                  >
                    <Briefcase className="mr-2 h-4 w-4" /> View Job Details
                  </Button>
                )}
              </div>
            ))}
          </div>
        </>
      ) : (
        <div className="text-center py-6">
          <p className="text-gray-500">No job recommendations available</p>
        </div>
      )}
    </div>
  );
};

export default JobRecommendations;
