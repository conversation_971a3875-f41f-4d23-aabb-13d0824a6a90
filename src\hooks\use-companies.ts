/**
 * React Query hooks for company data
 */

import { useAuth } from '@/contexts/AuthContext';
import { useEnhancedQuery, useEnhancedMutation, queryKeys } from '@/lib/queryUtils';
import * as companyService from '@/services/supabase/companies';
import { Company, CompanyInsert, CompanyUpdate } from '@/services/supabase/companies';

/**
 * Hook to fetch all companies
 */
export function useCompanies() {
  return useEnhancedQuery<Company[]>(
    queryKeys.companies.all,
    () => companyService.getCompanies(),
    {
      fallbackData: [],
      errorMessage: 'Failed to load companies',
    }
  );
}

/**
 * Hook to fetch a specific company
 */
export function useCompany(id: string) {
  return useEnhancedQuery<Company | null>(
    queryKeys.companies.byId(id),
    () => companyService.getCompany(id),
    {
      enabled: !!id,
      fallbackData: null,
      errorMessage: `Failed to load company with ID ${id}`,
    }
  );
}

/**
 * Hook to create a new company
 */
export function useCreateCompany() {
  const { user } = useAuth();

  return useEnhancedMutation<Company, CompanyInsert>(
    (company) => companyService.createCompany({
      ...company,
      user_id: company.user_id || user?.id,
    }),
    {
      errorMessage: 'Failed to create company',
      successMessage: 'Company created successfully',
      invalidateQueries: [queryKeys.companies.all],
    }
  );
}

/**
 * Hook to update a company
 */
export function useUpdateCompany() {
  return useEnhancedMutation<Company, { id: string; company: CompanyUpdate }>(
    ({ id, company }) => companyService.updateCompany(id, company),
    {
      errorMessage: 'Failed to update company',
      successMessage: 'Company updated successfully',
      invalidateQueries: [queryKeys.companies.all],
      onSuccess: (data, variables) => {
        // Also invalidate the specific company query
        return [queryKeys.companies.byId(variables.id)];
      },
    }
  );
}

/**
 * Hook to delete a company
 */
export function useDeleteCompany() {
  return useEnhancedMutation<boolean, string>(
    (id) => companyService.deleteCompany(id),
    {
      errorMessage: 'Failed to delete company',
      successMessage: 'Company deleted successfully',
      invalidateQueries: [queryKeys.companies.all],
    }
  );
}

/**
 * Hook to upload a company logo
 */
export function useUploadCompanyLogo() {
  return useEnhancedMutation<string, { companyId: string; file: File }>(
    ({ companyId, file }) => companyService.uploadCompanyLogo(companyId, file),
    {
      errorMessage: 'Failed to upload company logo',
      successMessage: 'Company logo uploaded successfully',
      invalidateQueries: [queryKeys.companies.all],
      onSuccess: (_, variables) => {
        // Also invalidate the specific company query
        return [queryKeys.companies.byId(variables.companyId)];
      },
    }
  );
}
