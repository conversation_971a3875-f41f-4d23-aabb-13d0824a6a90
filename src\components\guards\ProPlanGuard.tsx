import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useSubscription } from '@/hooks/use-profile';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Lock } from 'lucide-react';

interface ProPlanGuardProps {
  children: React.ReactNode;
  featureName?: string;
}

/**
 * ProPlanGuard component
 *
 * Restricts access to features that are only available to Pro plan subscribers and admins.
 * If the user is not on the Pro plan or an admin, it shows a message to upgrade.
 *
 * @example
 * <ProPlanGuard featureName="Interview Scheduling">
 *   <InterviewSchedulingComponent />
 * </ProPlanGuard>
 */
const ProPlanGuard: React.FC<ProPlanGuardProps> = ({
  children,
  featureName = 'this feature'
}) => {
  const { user, isLoading } = useAuth();
  const { hasRole, isLoading: permissionsLoading } = usePermissions();
  const { data: subscription, isLoading: subscriptionLoading } = useSubscription();
  const navigate = useNavigate();

  // Check if user is on Pro plan or is an admin
  // Primary check: role-based (Pro users get 'admin' role)
  // Fallback check: direct subscription tier check
  const hasAccess = hasRole(['admin', 'platform_admin']) || subscription?.tier === 'pro';

  if (isLoading || permissionsLoading || subscriptionLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-pulse text-gray-500">Loading...</div>
      </div>
    );
  }

  if (!hasAccess) {
    return (
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardHeader className="text-center">
          <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 mb-4">
            <Lock className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl">Pro Plan Feature</CardTitle>
          <CardDescription className="text-lg">
            {featureName} is only available on the Pro plan
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <p className="text-gray-600">
            Upgrade to our Pro plan to unlock this feature and many more advanced capabilities.
          </p>
          <div className="flex justify-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard/pricing')}
              className="gap-2"
            >
              <Lock className="h-4 w-4" />
              Upgrade to Pro
            </Button>
            <Button
              variant="default"
              onClick={() => navigate('/dashboard')}
            >
              Return to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If user has access, render children
  return <>{children}</>;
};

export default ProPlanGuard;
