import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { format } from 'date-fns';
import { CalendarIcon, Clock } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyContext } from '@/contexts/CompanyContext';
import { useCreateInterviewWithNotifications } from '@/hooks/use-interviews';
import { getCandidates } from '@/services/supabase/candidates';
import { getJobs } from '@/services/supabase/jobs';
import { getTeamMembers } from '@/services/supabase/team';
import { cn } from '@/lib/utils';
import { useToast } from '@/hooks/use-toast';
import { getUserTimezone, formatTimeInTimezone } from '@/lib/timezones';

import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { TimezoneSelect } from '@/components/ui/timezone-select';

// Define the form schema
const formSchema = z.object({
  candidate_id: z.string().min(1, 'Candidate is required'),
  job_id: z.string().min(1, 'Job is required'),
  scheduled_date: z.date({
    required_error: 'Date is required',
  }),
  scheduled_time: z.string().min(1, 'Time is required'),
  timezone: z.string().min(1, 'Timezone is required'),
  duration_minutes: z.coerce.number().min(15, 'Duration must be at least 15 minutes'),
  location: z.string().optional(),
  interview_type: z.enum(['phone', 'video', 'in-person'], {
    required_error: 'Interview type is required',
  }),
  notes: z.string().optional(),
  interviewers: z.array(z.string()).optional(),
});

type FormValues = z.infer<typeof formSchema>;

interface ScheduleInterviewFormProps {
  onSuccess?: () => void;
  preselectedCandidateId?: string;
  preselectedJobId?: string;
  isModal?: boolean;
}

const ScheduleInterviewForm: React.FC<ScheduleInterviewFormProps> = ({
  onSuccess,
  preselectedCandidateId,
  preselectedJobId,
  isModal = false
}) => {
  const { user } = useAuth();
  const { activeCompany } = useCompanyContext();
  const { mutateAsync: createInterview, isPending } = useCreateInterviewWithNotifications();
  const { toast } = useToast();

  const [candidates, setCandidates] = useState<any[]>([]);
  const [jobs, setJobs] = useState<any[]>([]);
  const [teamMembers, setTeamMembers] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      candidate_id: preselectedCandidateId || '',
      job_id: preselectedJobId || '',
      scheduled_date: new Date(),
      scheduled_time: '09:00',
      timezone: getUserTimezone(),
      duration_minutes: 60,
      location: '',
      interview_type: 'video',
      notes: '',
      interviewers: [],
    },
  });

  // Fetch data
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch candidates
        const candidatesData = await getCandidates();
        setCandidates(candidatesData);

        // Fetch jobs for the active company
        if (activeCompany) {
          const jobsData = await getJobs(activeCompany.id);
          console.log('Fetched jobs for company:', activeCompany.id, jobsData);
          setJobs(jobsData);
        }

        // Fetch team members
        if (activeCompany) {
          const teamMembersData = await getTeamMembers(activeCompany.id);
          console.log('Team members data:', teamMembersData);
          setTeamMembers(teamMembersData);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [activeCompany]);

  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    if (!user || !activeCompany) return;

    try {
      // Create the scheduled date and time in the selected timezone
      const scheduledDateTime = new Date(values.scheduled_date);
      const [hours, minutes] = values.scheduled_time.split(':').map(Number);
      scheduledDateTime.setHours(hours, minutes, 0, 0);

      // Convert the local time to the selected timezone
      // We need to create a date that represents the time in the selected timezone
      const timeString = `${values.scheduled_date.toISOString().split('T')[0]}T${values.scheduled_time}:00`;
      const localDateTime = new Date(timeString);

      // Adjust for timezone offset
      const userTimezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
      const selectedTimezone = values.timezone;

      // Create the final datetime - we'll store it as UTC but it represents the time in the selected timezone
      const finalDateTime = new Date(localDateTime.toLocaleString('en-US', { timeZone: selectedTimezone }));

      const interviewData = {
        candidate_id: values.candidate_id,
        job_id: values.job_id,
        company_id: activeCompany.id,
        scheduled_at: localDateTime.toISOString(), // Store the time as entered by user
        timezone: values.timezone, // Store the timezone separately
        duration_minutes: values.duration_minutes,
        location: values.location,
        interview_type: values.interview_type as 'phone' | 'video' | 'in-person',
        status: 'scheduled' as const,
        notes: values.notes,
        created_by: user.id,
        user_id: user.id
      };

      // Create interview with notifications
      const interview = await createInterview({
        interview: interviewData,
        interviewerIds: values.interviewers
      });

      if (interview) {
        toast({
          title: 'Interview Scheduled',
          description: 'Interview has been scheduled and invitations sent.',
        });
        onSuccess?.();
      }
    } catch (error) {
      console.error('Error scheduling interview:', error);
      toast({
        title: 'Error',
        description: 'Failed to schedule interview. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Update location based on interview type
  const interviewType = form.watch('interview_type');
  useEffect(() => {
    if (interviewType === 'phone') {
      form.setValue('location', 'Phone call');
    } else if (interviewType === 'video') {
      form.setValue('location', 'Video conference (link will be sent)');
    } else if (interviewType === 'in-person') {
      form.setValue('location', activeCompany?.name ? `${activeCompany.name} Office` : 'Office');
    }
  }, [interviewType, form, activeCompany]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center">
            <div className="animate-pulse">Loading...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const formContent = (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Candidate */}
              <FormField
                control={form.control}
                name="candidate_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Candidate</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={!!preselectedCandidateId}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select candidate" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {candidates.map(candidate => (
                          <SelectItem key={candidate.id} value={candidate.id}>
                            {candidate.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Job */}
              <FormField
                control={form.control}
                name="job_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Position</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={!!preselectedJobId}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select position" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {jobs.length > 0 ? (
                          jobs.map(job => (
                            <SelectItem key={job.id} value={job.id}>
                              {job.title}
                            </SelectItem>
                          ))
                        ) : (
                          <SelectItem value="no-jobs" disabled>
                            No positions available - Create a job first
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    {jobs.length === 0 && (
                      <FormDescription>
                        No positions found for this company. Please create a job posting first in the Jobs section.
                      </FormDescription>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Date */}
              <FormField
                control={form.control}
                name="scheduled_date"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Time */}
              <FormField
                control={form.control}
                name="scheduled_time"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Time</FormLabel>
                    <div className="flex items-center space-x-2">
                      <FormControl>
                        <Input
                          type="time"
                          {...field}
                          className="w-full"
                        />
                      </FormControl>
                      <Clock className="h-4 w-4 text-gray-500" />
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Timezone */}
              <FormField
                control={form.control}
                name="timezone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Timezone</FormLabel>
                    <FormControl>
                      <TimezoneSelect
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select timezone"
                      />
                    </FormControl>
                    <FormDescription>
                      The timezone for the interview time
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Duration */}
              <FormField
                control={form.control}
                name="duration_minutes"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Duration (minutes)</FormLabel>
                    <Select
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      defaultValue={field.value.toString()}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select duration" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="15">15 minutes</SelectItem>
                        <SelectItem value="30">30 minutes</SelectItem>
                        <SelectItem value="45">45 minutes</SelectItem>
                        <SelectItem value="60">1 hour</SelectItem>
                        <SelectItem value="90">1.5 hours</SelectItem>
                        <SelectItem value="120">2 hours</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Interview Type */}
              <FormField
                control={form.control}
                name="interview_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Interview Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="phone">Phone</SelectItem>
                        <SelectItem value="video">Video</SelectItem>
                        <SelectItem value="in-person">In-person</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Location */}
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location / Link</FormLabel>
                  <FormControl>
                    <Input {...field} />
                  </FormControl>
                  <FormDescription>
                    {interviewType === 'video' ? 'Video conference link or platform' :
                     interviewType === 'in-person' ? 'Physical location of the interview' :
                     'Phone number or call details'}
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Interviewers */}
            <FormField
              control={form.control}
              name="interviewers"
              render={() => (
                <FormItem>
                  <div className="mb-4">
                    <FormLabel className="text-base">Interviewers</FormLabel>
                    <FormDescription>
                      Select team members who will conduct the interview
                    </FormDescription>
                  </div>
                  {teamMembers.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {teamMembers.map((member) => (
                        <FormField
                          key={member.user_id || member.id}
                          control={form.control}
                          name="interviewers"
                          render={({ field }) => {
                            return (
                              <FormItem
                                key={member.user_id || member.id}
                                className="flex flex-row items-start space-x-3 space-y-0"
                              >
                                <FormControl>
                                  <Checkbox
                                    checked={field.value?.includes(member.user_id || member.id)}
                                    onCheckedChange={(checked) => {
                                      const memberId = member.user_id || member.id;
                                      return checked
                                        ? field.onChange([...field.value || [], memberId])
                                        : field.onChange(
                                            field.value?.filter(
                                              (value) => value !== memberId
                                            )
                                          );
                                    }}
                                  />
                                </FormControl>
                                <FormLabel className="font-normal cursor-pointer">
                                  {member.metadata?.name || member.profiles?.full_name || member.metadata?.email || member.profiles?.email || 'Unknown Member'}
                                </FormLabel>
                              </FormItem>
                            );
                          }}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-sm text-gray-500 p-4 border border-dashed rounded-lg">
                      No team members found. Add team members to your company first.
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Add any additional notes or instructions for the interview"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" disabled={isPending}>
              {isPending ? 'Scheduling...' : 'Schedule Interview'}
            </Button>
          </form>
        </Form>
  );

  if (isModal) {
    return formContent;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedule Interview</CardTitle>
      </CardHeader>
      <CardContent>
        {formContent}
      </CardContent>
    </Card>
  );
};

export default ScheduleInterviewForm;








