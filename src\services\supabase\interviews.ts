import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';
import { getCandidate } from './candidates';
import { getJob } from './jobs';
import { getCompany } from './companies';
import { getTeamMembers } from './team';
import { addInterviewParticipants } from './interview-participants';
import { sendInterviewInvitation } from '../email/interviewNotifications';
import { getCandidates } from './candidates';
import { createNotification } from './notifications';

/**
 * Interview interface
 */
export interface Interview {
  id: string;
  created_at: string;
  updated_at: string;
  candidate_id: string;
  job_id: string;
  company_id: string;
  scheduled_at: string;
  timezone: string;
  duration_minutes: number;
  location: string | null;
  interview_type: 'phone' | 'video' | 'in-person';
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  notes: string | null;
  created_by: string;
  user_id: string;
  // Additional fields for UI
  candidate_name?: string;
  job_title?: string;
  company_name?: string;
}

/**
 * Interview participant interface
 */
export interface InterviewParticipant {
  id: string;
  created_at: string;
  interview_id: string;
  user_id: string;
  role: 'interviewer' | 'observer';
  status: 'invited' | 'confirmed' | 'declined';
  // Additional fields for UI
  user_name?: string;
  user_email?: string;
  user_avatar?: string;
}

/**
 * Interview feedback interface
 */
export interface InterviewFeedback {
  id: string;
  created_at: string;
  updated_at: string;
  interview_id: string;
  user_id: string;
  overall_rating: number;
  technical_rating: number | null;
  cultural_rating: number | null;
  strengths: string | null;
  weaknesses: string | null;
  notes: string | null;
  recommendation: 'strong_yes' | 'yes' | 'maybe' | 'no' | 'strong_no';
  // Additional fields for UI
  user_name?: string;
}

/**
 * Create interview
 */
export const createInterview = async (
  interview: Omit<Interview, 'id' | 'created_at' | 'updated_at'>
): Promise<Interview | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interviews')
        .insert(interview)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    'Failed to create interview',
    null
  );
};

/**
 * Create interview with participants and send invitations
 */
export const createInterviewWithNotifications = async (
  interview: Omit<Interview, 'id' | 'created_at' | 'updated_at'>,
  interviewerIds: string[] = []
): Promise<Interview | null> => {
  return safeDbOperation(
    async () => {
      // Create the interview
      const { data: interviewData, error: interviewError } = await supabase
        .from('interviews')
        .insert(interview)
        .select()
        .single();

      if (interviewError) throw interviewError;

      // Add participants if any
      if (interviewerIds.length > 0) {
        await addInterviewParticipants(interviewData.id, interviewerIds, 'interviewer');
      }

      // Get enhanced interview data
      const enhancedInterview = await getInterviewById(interviewData.id);
      if (!enhancedInterview) throw new Error('Failed to retrieve created interview');

      // Send email notifications
      try {
        console.log('🔍 Starting email notification process...');

        // Get candidate details
        const candidates = await getCandidates();
        console.log('📋 Found candidates:', candidates.length);
        const candidate = candidates.find(c => c.id === interview.candidate_id);
        console.log('👤 Found candidate:', candidate ? `${candidate.name} (${candidate.email})` : 'NOT FOUND');

        if (candidate) {
          console.log('✅ Candidate found, preparing to send email...');

          // Use current user as organizer (simplified approach)
          const { data: { user: authUser } } = await supabase.auth.getUser();
          const organizerInfo = {
            email: authUser?.email || '<EMAIL>',
            name: authUser?.user_metadata?.name || 'Recruitment Team'
          };

          console.log('👨‍💼 Using organizer:', `${organizerInfo.name} (${organizerInfo.email})`);

          // Skip participants for now to avoid company ID issues
          const participants: any[] = [];
          console.log('🎯 Interview participants:', participants.length);

          await sendInterviewInvitation(
            enhancedInterview,
            participants,
            { email: candidate.email, name: candidate.name },
            { email: organizerInfo.email, name: organizerInfo.name }
          );
          console.log('📧 Email sent successfully!');

          // Create notification in database
          await createNotification({
            user_id: interview.created_by,
            title: 'Interview Scheduled',
            message: `Interview with ${candidate.name} for ${enhancedInterview.job_title} has been scheduled`,
            type: 'success',
            link: `/dashboard/interviews`
          });
          console.log('🔔 Notification created successfully!');

        } else {
          console.error('❌ Candidate not found for email notification:', {
            candidateId: interview.candidate_id,
            createdBy: interview.created_by
          });
        }
      } catch (emailError) {
        console.error('💥 Failed to send interview notifications:', emailError);
        // Don't fail the interview creation if email fails
      }

      return enhancedInterview;
    },
    'Failed to create interview',
    null
  );
};

/**
 * Get interviews
 */
export const getInterviews = async (
  filters: {
    userId?: string;
    candidateId?: string;
    jobId?: string;
    companyId?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  } = {}
): Promise<Interview[]> => {
  return safeDbOperation(
    async () => {
      let query = supabase
        .from('interviews')
        .select('*');

      // Apply filters
      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }
      if (filters.candidateId) {
        query = query.eq('candidate_id', filters.candidateId);
      }
      if (filters.jobId) {
        query = query.eq('job_id', filters.jobId);
      }
      if (filters.companyId) {
        query = query.eq('company_id', filters.companyId);
      }
      if (filters.status) {
        query = query.eq('status', filters.status);
      }
      if (filters.startDate) {
        query = query.gte('scheduled_at', filters.startDate);
      }
      if (filters.endDate) {
        query = query.lte('scheduled_at', filters.endDate);
      }

      // Order by scheduled_at
      query = query.order('scheduled_at', { ascending: true });

      const { data, error } = await query;

      if (error) throw error;

      // Enhance interviews with additional information
      const enhancedInterviews = await Promise.all(
        data.map(async (interview) => {
          try {
            const candidate = await getCandidate(interview.candidate_id);
            const job = await getJob(interview.job_id);
            const company = await getCompany(interview.company_id);

            return {
              ...interview,
              candidate_name: candidate?.name || 'Unknown Candidate',
              job_title: job?.title || 'Unknown Position',
              company_name: company?.name || 'Unknown Company'
            };
          } catch (error) {
            console.error('Error enhancing interview:', error);
            return interview;
          }
        })
      );

      return enhancedInterviews;
    },
    'Failed to fetch interviews',
    []
  );
};

/**
 * Get interview by ID
 */
export const getInterviewById = async (id: string): Promise<Interview | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interviews')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;

      // Enhance interview with additional information
      try {
        const candidate = await getCandidate(data.candidate_id);
        const job = await getJob(data.job_id);
        const company = await getCompany(data.company_id);

        return {
          ...data,
          candidate_name: candidate?.name || 'Unknown Candidate',
          job_title: job?.title || 'Unknown Position',
          company_name: company?.name || 'Unknown Company'
        };
      } catch (error) {
        console.error('Error enhancing interview:', error);
        return data;
      }
    },
    `Failed to fetch interview with ID ${id}`,
    null
  );
};

/**
 * Update interview
 */
export const updateInterview = async (
  id: string,
  updates: Partial<Omit<Interview, 'id' | 'created_at' | 'updated_at'>>
): Promise<Interview | null> => {
  return safeDbOperation(
    async () => {
      // Add updated_at timestamp
      const updatedData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('interviews')
        .update(updatedData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to update interview with ID ${id}`,
    null
  );
};

/**
 * Delete interview
 */
export const deleteInterview = async (id: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('interviews')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    },
    `Failed to delete interview with ID ${id}`,
    false
  );
};

/**
 * Add interview participant
 */
export const addInterviewParticipant = async (
  participant: Omit<InterviewParticipant, 'id' | 'created_at'>
): Promise<InterviewParticipant | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_participants')
        .insert(participant)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    'Failed to add interview participant',
    null
  );
};

/**
 * Get interview participants
 */
export const getInterviewParticipants = async (
  interviewId: string
): Promise<InterviewParticipant[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_participants')
        .select('*')
        .eq('interview_id', interviewId);

      if (error) throw error;

      // Enhance participants with user information
      const teamMembers = await getTeamMembers();

      return data.map(participant => {
        const user = teamMembers.find(member => member.user_id === participant.user_id);
        return {
          ...participant,
          user_name: user?.name || 'Unknown User',
          user_email: user?.email || '',
          user_avatar: user?.avatar_url || ''
        };
      });
    },
    `Failed to fetch participants for interview ${interviewId}`,
    []
  );
};

/**
 * Update interview participant
 */
export const updateInterviewParticipant = async (
  id: string,
  updates: Partial<Omit<InterviewParticipant, 'id' | 'created_at'>>
): Promise<InterviewParticipant | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_participants')
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to update interview participant with ID ${id}`,
    null
  );
};

/**
 * Remove interview participant
 */
export const removeInterviewParticipant = async (id: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('interview_participants')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    },
    `Failed to remove interview participant with ID ${id}`,
    false
  );
};

/**
 * Add interview feedback
 */
export const addInterviewFeedback = async (
  feedback: Omit<InterviewFeedback, 'id' | 'created_at' | 'updated_at'>
): Promise<InterviewFeedback | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_feedback')
        .insert(feedback)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    'Failed to add interview feedback',
    null
  );
};

/**
 * Get interview feedback
 */
export const getInterviewFeedback = async (
  interviewId: string
): Promise<InterviewFeedback[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('interview_feedback')
        .select('*')
        .eq('interview_id', interviewId);

      if (error) throw error;

      // Enhance feedback with user information
      const teamMembers = await getTeamMembers();

      return data.map(feedback => {
        const user = teamMembers.find(member => member.user_id === feedback.user_id);
        return {
          ...feedback,
          user_name: user?.name || 'Unknown User'
        };
      });
    },
    `Failed to fetch feedback for interview ${interviewId}`,
    []
  );
};

/**
 * Update interview feedback
 */
export const updateInterviewFeedback = async (
  id: string,
  updates: Partial<Omit<InterviewFeedback, 'id' | 'created_at' | 'updated_at'>>
): Promise<InterviewFeedback | null> => {
  return safeDbOperation(
    async () => {
      // Add updated_at timestamp
      const updatedData = {
        ...updates,
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('interview_feedback')
        .update(updatedData)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to update interview feedback with ID ${id}`,
    null
  );
};

/**
 * Delete interview feedback
 */
export const deleteInterviewFeedback = async (id: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('interview_feedback')
        .delete()
        .eq('id', id);

      if (error) throw error;
      return true;
    },
    `Failed to delete interview feedback with ID ${id}`,
    false
  );
};

/**
 * Get upcoming interviews for dashboard
 */
export const getUpcomingInterviews = async (
  userId: string,
  limit: number = 5
): Promise<Interview[]> => {
  return safeDbOperation(
    async () => {
      // Get interviews where user is the owner or a participant
      const { data: ownedInterviews, error: ownedError } = await supabase
        .from('interviews')
        .select('*')
        .eq('user_id', userId)
        .eq('status', 'scheduled')
        .gte('scheduled_at', new Date().toISOString())
        .order('scheduled_at', { ascending: true })
        .limit(limit);

      if (ownedError) throw ownedError;

      const { data: participantInterviews, error: participantError } = await supabase
        .from('interview_participants')
        .select('interview_id')
        .eq('user_id', userId)
        .eq('status', 'confirmed');

      if (participantError) throw participantError;

      // If user is a participant in any interviews, get those interviews
      let participatingInterviews: any[] = [];
      if (participantInterviews.length > 0) {
        const interviewIds = participantInterviews.map(p => p.interview_id);
        const { data, error } = await supabase
          .from('interviews')
          .select('*')
          .in('id', interviewIds)
          .eq('status', 'scheduled')
          .gte('scheduled_at', new Date().toISOString())
          .order('scheduled_at', { ascending: true });

        if (error) throw error;
        participatingInterviews = data;
      }

      // Combine and sort interviews
      const allInterviews = [...ownedInterviews, ...participatingInterviews]
        .sort((a, b) => new Date(a.scheduled_at).getTime() - new Date(b.scheduled_at).getTime())
        .slice(0, limit);

      // Enhance interviews with additional information
      const enhancedInterviews = await Promise.all(
        allInterviews.map(async (interview) => {
          try {
            const candidate = await getCandidate(interview.candidate_id);
            const job = await getJob(interview.job_id);
            const company = await getCompany(interview.company_id);

            return {
              ...interview,
              candidate_name: candidate?.name || 'Unknown Candidate',
              job_title: job?.title || 'Unknown Position',
              company_name: company?.name || 'Unknown Company'
            };
          } catch (error) {
            console.error('Error enhancing interview:', error);
            return interview;
          }
        })
      );

      return enhancedInterviews;
    },
    'Failed to fetch upcoming interviews',
    []
  );
};

/**
 * Generate dummy interview data for demonstration
 */
export const generateDummyInterviews = async (
  userId: string,
  companyId: string
): Promise<Interview[]> => {
  // Get candidates and jobs for the company
  const { data: candidates, error: candidatesError } = await supabase
    .from('candidates')
    .select('*')
    .limit(5);

  if (candidatesError) {
    console.error('Error fetching candidates:', candidatesError);
    return [];
  }

  const { data: jobs, error: jobsError } = await supabase
    .from('jobs')
    .select('*')
    .eq('company_id', companyId)
    .limit(5);

  if (jobsError) {
    console.error('Error fetching jobs:', jobsError);
    return [];
  }

  if (candidates.length === 0 || jobs.length === 0) {
    console.error('No candidates or jobs found');
    return [];
  }

  // Generate random interviews
  const dummyInterviews: Omit<Interview, 'id' | 'created_at' | 'updated_at'>[] = [];

  // Generate interviews for the next 14 days
  for (let i = 0; i < 10; i++) {
    const candidate = candidates[Math.floor(Math.random() * candidates.length)];
    const job = jobs[Math.floor(Math.random() * jobs.length)];

    // Random date in the next 14 days
    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + Math.floor(Math.random() * 14) + 1);
    scheduledDate.setHours(9 + Math.floor(Math.random() * 8), 0, 0, 0); // Between 9 AM and 5 PM

    const interviewTypes = ['phone', 'video', 'in-person'];
    const interviewType = interviewTypes[Math.floor(Math.random() * interviewTypes.length)] as 'phone' | 'video' | 'in-person';

    const statuses = ['scheduled', 'completed', 'cancelled', 'rescheduled'];
    const status = statuses[Math.floor(Math.random() * statuses.length)] as 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';

    dummyInterviews.push({
      candidate_id: candidate.id,
      job_id: job.id,
      company_id: companyId,
      scheduled_at: scheduledDate.toISOString(),
      timezone: 'America/New_York', // Default timezone for dummy data
      duration_minutes: [30, 45, 60][Math.floor(Math.random() * 3)],
      location: interviewType === 'in-person' ? 'Company HQ, Meeting Room 3' :
                interviewType === 'video' ? 'https://meet.google.com/abc-defg-hij' :
                'Phone call to candidate',
      interview_type: interviewType,
      status,
      notes: `Interview with ${candidate.name} for the ${job.title} position.`,
      created_by: userId,
      user_id: userId
    });
  }

  // Insert dummy interviews
  const { data, error } = await supabase
    .from('interviews')
    .insert(dummyInterviews)
    .select();

  if (error) {
    console.error('Error inserting dummy interviews:', error);
    return [];
  }

  return data;
};

