-- Add all Stripe subscription statuses to subscriptions table
-- Stripe subscriptions can have various statuses that need to be supported

-- Drop the existing constraint
ALTER TABLE public.subscriptions DROP CONSTRAINT IF EXISTS subscriptions_status_check;

-- Add the updated constraint with all possible Stripe subscription statuses
ALTER TABLE public.subscriptions
ADD CONSTRAINT subscriptions_status_check
CHECK (status IN (
  'active',           -- Subscription is active and current
  'cancelled',        -- Subscription has been cancelled
  'suspended',        -- Subscription is temporarily suspended
  'expired',          -- Subscription has expired
  'pending',          -- Subscription is pending activation
  'incomplete',       -- Stripe: Payment required but not completed
  'incomplete_expired', -- Stripe: Payment was required but expired
  'trialing',         -- Stripe: Subscription is in trial period
  'past_due',         -- Stripe: Payment failed, retrying
  'unpaid',           -- Stripe: Payment failed, no more retries
  'paused'            -- Stripe: Subscription is paused
));

-- Add comment for documentation
COMMENT ON CONSTRAINT subscriptions_status_check ON public.subscriptions IS 'Allowed subscription statuses including all possible Stripe subscription statuses';
