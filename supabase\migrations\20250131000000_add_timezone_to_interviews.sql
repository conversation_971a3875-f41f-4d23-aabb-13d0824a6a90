-- Add timezone column to interviews table
ALTER TABLE public.interviews 
ADD COLUMN timezone TEXT DEFAULT 'UTC';

-- Add comment for documentation
COMMENT ON COLUMN public.interviews.timezone IS 'Timezone for the scheduled interview time (IANA timezone identifier)';

-- Update existing interviews to have UTC timezone if they don't have one
UPDATE public.interviews 
SET timezone = 'UTC' 
WHERE timezone IS NULL;

-- Make timezone column NOT NULL after setting default values
ALTER TABLE public.interviews 
ALTER COLUMN timezone SET NOT NULL;
