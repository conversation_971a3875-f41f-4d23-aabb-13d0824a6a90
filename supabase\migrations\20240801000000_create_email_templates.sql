-- Create email templates table
CREATE TABLE public.email_templates (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now(),
  name text NOT NULL UNIQUE,
  display_name text NOT NULL,
  description text,
  subject text NOT NULL,
  content text NOT NULL,
  template_type text NOT NULL,
  variables jsonb DEFAULT '[]'::jsonb,
  is_active boolean DEFAULT true,
  created_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
  updated_by uuid REFERENCES auth.users(id) ON DELETE SET NULL,
  PRIMARY KEY (id)
);

-- Create index on template_type for faster queries
CREATE INDEX idx_email_templates_type ON public.email_templates(template_type);
CREATE INDEX idx_email_templates_active ON public.email_templates(is_active);

-- Enable RLS
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Platform admins can manage email templates" ON public.email_templates
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.profiles
      WHERE profiles.user_id = auth.uid()
      AND profiles.platform_admin = true
    )
  );

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_email_template_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  NEW.updated_by = auth.uid();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
CREATE TRIGGER update_email_templates_updated_at
  BEFORE UPDATE ON public.email_templates
  FOR EACH ROW
  EXECUTE FUNCTION update_email_template_updated_at();

-- Insert default email templates
INSERT INTO public.email_templates (name, display_name, description, subject, content, template_type, variables) VALUES
(
  'interview_invitation_candidate',
  'Interview Invitation - Candidate',
  'Email sent to candidates when an interview is scheduled',
  'Interview Scheduled: {{jobTitle}}',
  '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #2563eb; margin: 0;">{{companyName}}</h1>
    </div>
    
    <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
      Interview Scheduled
    </h2>
    
    <p style="font-size: 16px; line-height: 1.6;">Dear {{candidateName}},</p>
    
    <p style="font-size: 16px; line-height: 1.6;">
      We''re pleased to inform you that an interview has been scheduled for the <strong>{{jobTitle}}</strong> position.
    </p>
    
    <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
      <h3 style="margin-top: 0; color: #1f2937;">Interview Details</h3>
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="padding: 8px 0; font-weight: bold; color: #374151;">Date:</td>
          <td style="padding: 8px 0; color: #1f2937;">{{interviewDate}}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold; color: #374151;">Time:</td>
          <td style="padding: 8px 0; color: #1f2937;">{{interviewTime}}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold; color: #374151;">Duration:</td>
          <td style="padding: 8px 0; color: #1f2937;">{{duration}} minutes</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold; color: #374151;">Type:</td>
          <td style="padding: 8px 0; color: #1f2937;">{{interviewType}}</td>
        </tr>
        <tr>
          <td style="padding: 8px 0; font-weight: bold; color: #374151;">Location:</td>
          <td style="padding: 8px 0; color: #1f2937;">{{location}}</td>
        </tr>
      </table>
    </div>
    
    <p style="font-size: 16px; line-height: 1.6;">
      Please find the calendar invitation attached to this email. We look forward to speaking with you!
    </p>
    
    <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
      <p style="margin: 0; color: #92400e; font-size: 14px;">
        <strong>Tip:</strong> Please arrive 5 minutes early and ensure you have a stable internet connection if this is a video interview.
      </p>
    </div>
    
    <p style="font-size: 16px; line-height: 1.6;">
      Best regards,<br>
      <strong>The Recruitment Team</strong><br>
      {{companyName}}
    </p>
    
    <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
      <p style="color: #6b7280; font-size: 12px; margin: 0;">
        This is an automated message from {{companyName}}. Please do not reply to this email.
      </p>
    </div>
  </div>',
  'interview',
  '[
    {"name": "companyName", "description": "Name of the company", "required": true},
    {"name": "candidateName", "description": "Name of the candidate", "required": true},
    {"name": "jobTitle", "description": "Title of the job position", "required": true},
    {"name": "interviewDate", "description": "Date of the interview", "required": true},
    {"name": "interviewTime", "description": "Time of the interview", "required": true},
    {"name": "duration", "description": "Duration of the interview in minutes", "required": true},
    {"name": "interviewType", "description": "Type of interview (video, phone, in-person)", "required": true},
    {"name": "location", "description": "Location or link for the interview", "required": true}
  ]'::jsonb
),
(
  'application_received',
  'Application Received',
  'Email sent to candidates when their application is received',
  'Application Received: {{jobTitle}} at {{companyName}}',
  '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #2563eb; margin: 0;">{{companyName}}</h1>
    </div>

    <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
      Application Received
    </h2>

    <p style="font-size: 16px; line-height: 1.6;">Dear {{candidateName}},</p>

    <p style="font-size: 16px; line-height: 1.6;">
      Thank you for your interest in the <strong>{{jobTitle}}</strong> position at <strong>{{companyName}}</strong>.
    </p>

    <p style="font-size: 16px; line-height: 1.6;">
      We have received your application and our recruitment team will review it carefully. 
      We will contact you within the next few days if your profile matches our requirements.
    </p>

    <p style="font-size: 16px; line-height: 1.6;">
      Best regards,<br>
      <strong>The Recruitment Team</strong><br>
      {{companyName}}
    </p>

    <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
      <p style="color: #6b7280; font-size: 12px; margin: 0;">
        This is an automated message from {{companyName}}. Please do not reply to this email.
      </p>
    </div>
  </div>',
  'candidate',
  '[
    {"name": "companyName", "description": "Name of the company", "required": true},
    {"name": "candidateName", "description": "Name of the candidate", "required": true},
    {"name": "jobTitle", "description": "Title of the job position", "required": true}
  ]'::jsonb
),
(
  'team_invitation',
  'Team Invitation',
  'Email sent when inviting someone to join a team',
  'You''re invited to join {{companyName}} on Sourcio.ai',
  '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #2563eb; margin: 0;">{{companyName}}</h1>
    </div>

    <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
      <h2 style="color: #1e293b; margin: 0 0 15px 0;">You''re Invited to Join a Team!</h2>
      <p style="color: #475569; margin: 0; line-height: 1.6;">
        Hi {{inviteeName}},<br><br>
        {{inviterName}} has invited you to join <strong>{{companyName}}</strong> as a <strong>{{role}}</strong> on Sourcio.ai.
      </p>
    </div>

    <div style="margin: 30px 0; text-align: center;">
      <a href="{{invitationLink}}" style="background: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 600;">
        Accept Invitation
      </a>
    </div>

    <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
      <p style="color: #92400e; margin: 0; font-size: 14px;">
        <strong>Note:</strong> This invitation will expire in 7 days. If you don''t have a Sourcio.ai account, you''ll be prompted to create one.
      </p>
    </div>

    <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
      <p style="color: #6b7280; font-size: 12px; margin: 0;">
        This is an automated message from {{companyName}}. Please do not reply to this email.
      </p>
    </div>
  </div>',
  'team',
  '[
    {"name": "companyName", "description": "Name of the company", "required": true},
    {"name": "inviteeName", "description": "Name of the person being invited", "required": true},
    {"name": "inviterName", "description": "Name of the person sending the invitation", "required": true},
    {"name": "role", "description": "Role being offered", "required": true},
    {"name": "invitationLink", "description": "Link to accept the invitation", "required": true}
  ]'::jsonb
),
(
  'status_update',
  'Application Status Update',
  'Email sent when a candidate''s application status changes',
  'Application Update: {{jobTitle}} at {{companyName}}',
  '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #2563eb; margin: 0;">{{companyName}}</h1>
    </div>

    <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
      Application Update
    </h2>

    <p style="font-size: 16px; line-height: 1.6;">Dear {{candidateName}},</p>

    <p style="font-size: 16px; line-height: 1.6;">
      We have an update regarding your application for the <strong>{{jobTitle}}</strong> position at <strong>{{companyName}}</strong>.
    </p>

    <div style="background: #f0f9ff; border-left: 4px solid #2563eb; padding: 15px; margin: 20px 0;">
      <p style="margin: 0; font-size: 16px;">
        <strong>Status:</strong> {{status}}
      </p>
    </div>

    <p style="font-size: 16px; line-height: 1.6;">
      {{message}}
    </p>

    <p style="font-size: 16px; line-height: 1.6;">
      Best regards,<br>
      <strong>The Recruitment Team</strong><br>
      {{companyName}}
    </p>

    <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
      <p style="color: #6b7280; font-size: 12px; margin: 0;">
        This is an automated message from {{companyName}}. Please do not reply to this email.
      </p>
    </div>
  </div>',
  'candidate',
  '[
    {"name": "companyName", "description": "Name of the company", "required": true},
    {"name": "candidateName", "description": "Name of the candidate", "required": true},
    {"name": "jobTitle", "description": "Title of the job position", "required": true},
    {"name": "status", "description": "New application status", "required": true},
    {"name": "message", "description": "Custom message or feedback", "required": false}
  ]'::jsonb
),
(
  'job_offer',
  'Job Offer',
  'Email sent when offering a job to a candidate',
  'Great News! Job Offer: {{jobTitle}} at {{companyName}}',
  '<div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
    <div style="text-align: center; margin-bottom: 30px;">
      <h1 style="color: #059669; margin: 0;">{{companyName}}</h1>
    </div>

    <h2 style="color: #059669; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
      🎉 Congratulations! Job Offer
    </h2>

    <p style="font-size: 16px; line-height: 1.6;">Dear {{candidateName}},</p>

    <p style="font-size: 16px; line-height: 1.6;">
      We are delighted to offer you the position of <strong>{{jobTitle}}</strong> at {{companyName}}.
    </p>

    <p style="font-size: 16px; line-height: 1.6;">
      {{message}}
    </p>

    <div style="background: #f0fdf4; border-left: 4px solid #059669; padding: 15px; margin: 20px 0;">
      <h3 style="color: #059669; margin: 0 0 10px 0;">Next Steps</h3>
      <p style="margin: 0;">{{nextSteps}}</p>
    </div>

    <p style="font-size: 16px; line-height: 1.6;">
      We look forward to welcoming you to the team and are excited about the contributions you''ll make to our organization.
    </p>

    <p style="font-size: 16px; line-height: 1.6;">
      Best regards,<br>
      <strong>The Recruitment Team</strong><br>
      {{companyName}}
    </p>

    <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
      <p style="color: #6b7280; font-size: 12px; margin: 0;">
        This is an automated message from {{companyName}}. Please do not reply to this email.
      </p>
    </div>
  </div>',
  'candidate',
  '[
    {"name": "companyName", "description": "Name of the company", "required": true},
    {"name": "candidateName", "description": "Name of the candidate", "required": true},
    {"name": "jobTitle", "description": "Title of the job position", "required": true},
    {"name": "message", "description": "Custom offer message", "required": false},
    {"name": "nextSteps", "description": "Information about next steps", "required": false}
  ]'::jsonb
);
