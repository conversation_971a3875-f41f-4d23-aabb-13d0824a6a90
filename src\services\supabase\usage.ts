import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

/**
 * Usage tracking types
 */
export type UsageType = 'cv_uploads' | 'active_jobs' | 'company_profiles' | 'team_members';

/**
 * Usage data interface
 */
export interface UsageData {
  id: string;
  user_id: string;
  month_year: string;
  cv_uploads_count: number;
  active_jobs_count: number;
  company_profiles_count: number;
  team_members_count: number;
  created_at: string;
  updated_at: string;
}

/**
 * Subscription limits interface
 */
export interface SubscriptionLimits {
  id: string;
  tier: 'starter' | 'growth' | 'pro';
  cv_upload_limit: number;
  job_posting_limit: number;
  company_profile_limit: number;
  team_member_limit: number;
  created_at: string;
  updated_at: string;
}

/**
 * Get current month in YYYY-MM format
 */
const getCurrentMonth = (): string => {
  const now = new Date();
  return `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}`;
};

/**
 * Get user's current usage for the current month
 */
export const getCurrentUsage = async (userId: string): Promise<UsageData | null> => {
  return safeDbOperation(
    async () => {
      const currentMonth = getCurrentMonth();

      // Check if a record exists for the current month
      const { data, error } = await supabase
        .from('usage_tracking')
        .select('*')
        .eq('user_id', userId)
        .eq('month_year', currentMonth)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          // Record doesn't exist, create a new one
          const { data: newData, error: insertError } = await supabase
            .from('usage_tracking')
            .insert({
              user_id: userId,
              month_year: currentMonth,
              cv_uploads_count: 0,
              active_jobs_count: 0,
              company_profiles_count: 0,
              team_members_count: 0,
            })
            .select()
            .single();

          if (insertError) throw insertError;
          return newData;
        }
        throw error;
      }

      return data;
    },
    `Failed to fetch usage data for user ${userId}`,
    null
  );
};

/**
 * Get subscription limits for a specific tier
 */
export const getSubscriptionLimits = async (tier: 'starter' | 'growth' | 'pro'): Promise<SubscriptionLimits | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('subscription_limits')
        .select('*')
        .eq('tier', tier)
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to fetch subscription limits for tier ${tier}`,
    null
  );
};

/**
 * Increment usage counter
 */
export const incrementUsage = async (
  userId: string,
  usageType: UsageType,
  incrementBy: number = 1
): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const currentMonth = getCurrentMonth();
      const usage = await getCurrentUsage(userId);

      if (!usage) {
        throw new Error('Failed to get or create usage record');
      }

      // Determine which column to update
      let updateColumn: string;
      switch (usageType) {
        case 'cv_uploads':
          updateColumn = 'cv_uploads_count';
          break;
        case 'active_jobs':
          updateColumn = 'active_jobs_count';
          break;
        case 'company_profiles':
          updateColumn = 'company_profiles_count';
          break;
        case 'team_members':
          updateColumn = 'team_members_count';
          break;
        default:
          throw new Error(`Invalid usage type: ${usageType}`);
      }

      // Update the usage counter
      const { error } = await supabase
        .from('usage_tracking')
        .update({
          [updateColumn]: usage[updateColumn as keyof UsageData] + incrementBy,
          updated_at: new Date().toISOString(),
        })
        .eq('id', usage.id);

      if (error) throw error;
      return true;
    },
    `Failed to increment ${usageType} usage for user ${userId}`,
    false
  );
};

/**
 * Decrement usage counter
 */
export const decrementUsage = async (
  userId: string,
  usageType: UsageType,
  decrementBy: number = 1
): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const currentMonth = getCurrentMonth();
      const usage = await getCurrentUsage(userId);

      if (!usage) {
        throw new Error('Failed to get or create usage record');
      }

      // Determine which column to update
      let updateColumn: string;
      switch (usageType) {
        case 'cv_uploads':
          updateColumn = 'cv_uploads_count';
          break;
        case 'active_jobs':
          updateColumn = 'active_jobs_count';
          break;
        case 'company_profiles':
          updateColumn = 'company_profiles_count';
          break;
        case 'team_members':
          updateColumn = 'team_members_count';
          break;
        default:
          throw new Error(`Invalid usage type: ${usageType}`);
      }

      // Calculate new value, ensuring it doesn't go below 0
      const currentValue = usage[updateColumn as keyof UsageData] as number;
      const newValue = Math.max(0, currentValue - decrementBy);

      // Update the usage counter
      const { error } = await supabase
        .from('usage_tracking')
        .update({
          [updateColumn]: newValue,
          updated_at: new Date().toISOString(),
        })
        .eq('id', usage.id);

      if (error) throw error;
      return true;
    },
    `Failed to decrement ${usageType} usage for user ${userId}`,
    false
  );
};

/**
 * Check if user has reached their limit for a specific usage type
 */
export const checkUsageLimit = async (
  userId: string,
  usageType: UsageType,
  tier: 'starter' | 'growth' | 'pro'
): Promise<{ hasReachedLimit: boolean; currentUsage: number; limit: number }> => {
  return safeDbOperation(
    async () => {
      // Get current usage
      const usage = await getCurrentUsage(userId);
      if (!usage) {
        throw new Error('Failed to get usage data');
      }

      // Get subscription limits
      const limits = await getSubscriptionLimits(tier);
      if (!limits) {
        throw new Error(`Failed to get subscription limits for tier ${tier}`);
      }

      // Determine which values to check
      let currentUsage: number;
      let limit: number;

      switch (usageType) {
        case 'cv_uploads':
          currentUsage = usage.cv_uploads_count;
          limit = limits.cv_upload_limit;
          break;
        case 'active_jobs':
          currentUsage = usage.active_jobs_count;
          limit = limits.job_posting_limit;
          break;
        case 'company_profiles':
          currentUsage = usage.company_profiles_count;
          limit = limits.company_profile_limit;
          break;
        case 'team_members':
          currentUsage = usage.team_members_count;
          limit = limits.team_member_limit;
          break;
        default:
          throw new Error(`Invalid usage type: ${usageType}`);
      }

      return {
        hasReachedLimit: currentUsage >= limit,
        currentUsage,
        limit,
      };
    },
    `Failed to check usage limit for ${usageType}`,
    { hasReachedLimit: true, currentUsage: 0, limit: 0 } // Conservative default
  );
};

/**
 * Reset usage for a specific user
 */
export const resetUserUsage = async (userId: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const currentMonth = getCurrentMonth();

      const { error } = await supabase
        .from('usage_tracking')
        .update({
          cv_uploads_count: 0,
          active_jobs_count: 0,
          company_profiles_count: 0,
          team_members_count: 0,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId)
        .eq('month_year', currentMonth);

      if (error) throw error;
      return true;
    },
    `Failed to reset usage for user ${userId}`,
    false
  );
};

/**
 * Set specific usage value
 */
export const setUsageValue = async (
  userId: string,
  usageType: UsageType,
  value: number
): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const usage = await getCurrentUsage(userId);

      if (!usage) {
        throw new Error('Failed to get or create usage record');
      }

      // Determine which column to update
      let updateColumn: string;
      switch (usageType) {
        case 'cv_uploads':
          updateColumn = 'cv_uploads_count';
          break;
        case 'active_jobs':
          updateColumn = 'active_jobs_count';
          break;
        case 'company_profiles':
          updateColumn = 'company_profiles_count';
          break;
        case 'team_members':
          updateColumn = 'team_members_count';
          break;
        default:
          throw new Error(`Invalid usage type: ${usageType}`);
      }

      // Update the usage counter
      const { error } = await supabase
        .from('usage_tracking')
        .update({
          [updateColumn]: Math.max(0, value), // Ensure value is not negative
          updated_at: new Date().toISOString(),
        })
        .eq('id', usage.id);

      if (error) throw error;
      return true;
    },
    `Failed to set ${usageType} usage for user ${userId}`,
    false
  );
};
