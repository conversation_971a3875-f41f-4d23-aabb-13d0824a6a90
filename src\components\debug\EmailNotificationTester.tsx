import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { candidateNotificationOrchestrator } from '@/services/notifications/candidateNotificationOrchestrator';
import { 
  sendCandidateStatusUpdateEmail, 
  sendRejectionEmail, 
  sendOfferEmail 
} from '@/services/email/candidateNotifications';
import { env } from '@/lib/env';

export const EmailNotificationTester: React.FC = () => {
  const [testEmail, setTestEmail] = useState('');
  const [candidateName, setCandidateName] = useState('Test Candidate');
  const [jobTitle, setJobTitle] = useState('Software Developer');
  const [companyName, setCompanyName] = useState('Test Company');
  const [notificationType, setNotificationType] = useState('status_update');
  const [oldStatus, setOldStatus] = useState('applied');
  const [newStatus, setNewStatus] = useState('screening');
  const [isSending, setIsSending] = useState(false);
  const [result, setResult] = useState<{ success: boolean; message: string } | null>(null);

  const handleSendTest = async () => {
    if (!testEmail) {
      setResult({ success: false, message: 'Please enter an email address' });
      return;
    }

    setIsSending(true);
    setResult(null);

    try {
      const testData = {
        candidateEmail: testEmail,
        candidateName,
        jobTitle,
        companyName
      };

      console.log('🧪 Sending test email notification...');
      console.log('Test data:', testData);
      console.log('Email notifications enabled:', env.ENABLE_EMAIL_NOTIFICATIONS);

      switch (notificationType) {
        case 'status_update':
          await sendCandidateStatusUpdateEmail({
            ...testData,
            oldStatus,
            newStatus
          });
          break;
        
        case 'rejection':
          await sendRejectionEmail({
            ...testData,
            message: 'Thank you for your interest. While we have decided to move forward with other candidates, we encourage you to apply for future opportunities.'
          });
          break;
        
        case 'offer':
          await sendOfferEmail({
            ...testData,
            message: 'We are excited to extend this offer and look forward to having you join our team.',
            nextSteps: 'Our HR team will contact you within 24 hours with detailed offer information.'
          });
          break;
        
        default:
          throw new Error('Unknown notification type');
      }

      setResult({ 
        success: true, 
        message: `Test ${notificationType} email sent successfully to ${testEmail}` 
      });
    } catch (error) {
      console.error('Test email failed:', error);
      setResult({ 
        success: false, 
        message: `Failed to send test email: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    } finally {
      setIsSending(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Email Notification Tester</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="testEmail">Test Email Address</Label>
            <Input
              id="testEmail"
              type="email"
              placeholder="<EMAIL>"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="notificationType">Notification Type</Label>
            <Select value={notificationType} onValueChange={setNotificationType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="status_update">Status Update</SelectItem>
                <SelectItem value="rejection">Rejection</SelectItem>
                <SelectItem value="offer">Job Offer</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="candidateName">Candidate Name</Label>
            <Input
              id="candidateName"
              value={candidateName}
              onChange={(e) => setCandidateName(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="jobTitle">Job Title</Label>
            <Input
              id="jobTitle"
              value={jobTitle}
              onChange={(e) => setJobTitle(e.target.value)}
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="companyName">Company Name</Label>
          <Input
            id="companyName"
            value={companyName}
            onChange={(e) => setCompanyName(e.target.value)}
          />
        </div>

        {notificationType === 'status_update' && (
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="oldStatus">Old Status</Label>
              <Select value={oldStatus} onValueChange={setOldStatus}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="applied">Applied</SelectItem>
                  <SelectItem value="screening">Screening</SelectItem>
                  <SelectItem value="interview">Interview</SelectItem>
                  <SelectItem value="offer">Offer</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="newStatus">New Status</Label>
              <Select value={newStatus} onValueChange={setNewStatus}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="screening">Screening</SelectItem>
                  <SelectItem value="interview">Interview</SelectItem>
                  <SelectItem value="offer">Offer</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        <div className="space-y-2">
          <div className="text-sm text-muted-foreground">
            Email Notifications Enabled: {env.ENABLE_EMAIL_NOTIFICATIONS ? '✅ Yes' : '❌ No'}
          </div>
        </div>

        <Button 
          onClick={handleSendTest} 
          disabled={isSending || !testEmail}
          className="w-full"
        >
          {isSending ? 'Sending...' : 'Send Test Email'}
        </Button>

        {result && (
          <Alert className={result.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
            <AlertDescription className={result.success ? "text-green-800" : "text-red-800"}>
              {result.message}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
};
