import React, { useState, useMemo } from 'react';
import { FileText, Download, Eye, Loader2, Filter, Search, Calendar, DollarSign, CreditCard } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { format } from 'date-fns';
import { Invoice } from '@/services/supabase/invoices';
import { downloadInvoicePdf } from '@/utils/pdfGenerator';
import { useToast } from '@/hooks/use-toast';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface InvoicesListProps {
  invoices: Invoice[];
  companyInfo?: any;
  userInfo?: any;
}

const InvoicesList: React.FC<InvoicesListProps> = ({
  invoices,
  companyInfo,
  userInfo,
}) => {
  const [isDownloading, setIsDownloading] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date-desc');
  const { toast } = useToast();

  // Filter and sort invoices
  const filteredAndSortedInvoices = useMemo(() => {
    let filtered = invoices.filter(invoice => {
      const matchesSearch = invoice.invoice_number.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           invoice.amount.toString().includes(searchTerm);
      const matchesStatus = statusFilter === 'all' || invoice.status === statusFilter;
      return matchesSearch && matchesStatus;
    });

    // Sort invoices
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'date-desc':
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
        case 'date-asc':
          return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
        case 'amount-desc':
          return b.amount - a.amount;
        case 'amount-asc':
          return a.amount - b.amount;
        case 'number':
          return a.invoice_number.localeCompare(b.invoice_number);
        default:
          return 0;
      }
    });

    return filtered;
  }, [invoices, searchTerm, statusFilter, sortBy]);

  // Calculate totals
  const totalAmount = filteredAndSortedInvoices.reduce((sum, invoice) => sum + invoice.amount, 0);
  const paidAmount = filteredAndSortedInvoices
    .filter(invoice => invoice.status === 'paid')
    .reduce((sum, invoice) => sum + invoice.amount, 0);

  // Handle invoice download
  const handleDownload = (invoice: Invoice) => {
    setIsDownloading(invoice.id);

    try {
      downloadInvoicePdf(invoice, companyInfo, userInfo);

      toast({
        title: 'Invoice downloaded',
        description: `Invoice ${invoice.invoice_number} has been downloaded successfully.`,
      });
    } catch (error) {
      console.error('Error downloading invoice:', error);
      toast({
        title: 'Error',
        description: 'Failed to download invoice. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsDownloading(null);
    }
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'paid':
        return <Badge className="bg-green-500">Paid</Badge>;
      case 'overdue':
        return <Badge className="bg-red-500">Overdue</Badge>;
      case 'draft':
        return <Badge className="bg-gray-500">Draft</Badge>;
      case 'sent':
        return <Badge className="bg-blue-500">Sent</Badge>;
      case 'void':
        return <Badge className="bg-gray-500">Void</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMMM d, yyyy');
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Invoices</p>
                <p className="text-2xl font-bold">{filteredAndSortedInvoices.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Amount</p>
                <p className="text-2xl font-bold">${totalAmount.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CreditCard className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Paid Amount</p>
                <p className="text-2xl font-bold">${paidAmount.toFixed(2)}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Invoices & Payment History
          </CardTitle>
          <CardDescription>
            View, filter, and download your invoices and payment receipts
          </CardDescription>
        </CardHeader>
        <CardContent>
          {invoices.length === 0 ? (
            <Alert>
              <FileText className="h-4 w-4" />
              <AlertDescription>
                No invoices found. Invoices are automatically generated when payments are processed through Stripe or PayPal.
              </AlertDescription>
            </Alert>
          ) : (
            <div className="space-y-6">
              {/* Filters and Search */}
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search by invoice number or amount..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="sent">Sent</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                    <SelectItem value="overdue">Overdue</SelectItem>
                    <SelectItem value="void">Void</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-full sm:w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Newest First</SelectItem>
                    <SelectItem value="date-asc">Oldest First</SelectItem>
                    <SelectItem value="amount-desc">Highest Amount</SelectItem>
                    <SelectItem value="amount-asc">Lowest Amount</SelectItem>
                    <SelectItem value="number">Invoice Number</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {filteredAndSortedInvoices.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900">No invoices match your filters</h3>
                  <p className="text-gray-500 mt-2">
                    Try adjusting your search terms or filters.
                  </p>
                </div>
              ) : (
                <div className="border rounded-lg overflow-hidden">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Invoice</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredAndSortedInvoices.map((invoice) => (
                        <TableRow key={invoice.id} className="hover:bg-gray-50">
                          <TableCell>
                            <div className="flex items-center space-x-3">
                              <div className="bg-blue-100 p-2 rounded-full">
                                <FileText className="h-4 w-4 text-blue-600" />
                              </div>
                              <div>
                                <div className="font-medium">#{invoice.invoice_number}</div>
                                <div className="text-sm text-gray-500">
                                  {invoice.subscription_id ? 'Subscription' : 'One-time'}
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{formatDate(invoice.issue_date)}</div>
                              {invoice.payment_date && (
                                <div className="text-sm text-gray-500">
                                  Paid: {formatDate(invoice.payment_date)}
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="font-medium">
                              {invoice.currency.toUpperCase()} {invoice.amount.toFixed(2)}
                            </div>
                          </TableCell>
                          <TableCell>
                            {getStatusBadge(invoice.status)}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Dialog>
                                <DialogTrigger asChild>
                                  <Button variant="ghost" size="sm" title="View Invoice">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </DialogTrigger>
                      <DialogContent className="max-w-3xl">
                        <DialogHeader>
                          <DialogTitle>Invoice #{invoice.invoice_number}</DialogTitle>
                          <DialogDescription>
                            Issued on {formatDate(invoice.issue_date)}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="mt-4 space-y-6">
                          <div className="grid grid-cols-2 gap-6">
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">From</h3>
                              <div className="mt-1">
                                <p className="font-medium">{companyInfo?.name || 'Sourcio.ai'}</p>
                                <p className="text-sm text-gray-500">123 Business St</p>
                                <p className="text-sm text-gray-500">Business City, ST 12345</p>
                                <p className="text-sm text-gray-500">United States</p>
                              </div>
                            </div>
                            <div>
                              <h3 className="text-sm font-medium text-gray-500">Bill To</h3>
                              <div className="mt-1">
                                <p className="font-medium">{invoice.billing_info.company_name || userInfo?.full_name || 'Client'}</p>
                                <p className="text-sm text-gray-500">{invoice.billing_info.address_line1}</p>
                                {invoice.billing_info.address_line2 && (
                                  <p className="text-sm text-gray-500">{invoice.billing_info.address_line2}</p>
                                )}
                                <p className="text-sm text-gray-500">
                                  {invoice.billing_info.city}, {invoice.billing_info.state} {invoice.billing_info.postal_code}
                                </p>
                                <p className="text-sm text-gray-500">{invoice.billing_info.country}</p>
                              </div>
                            </div>
                          </div>

                          <Separator />

                          <div>
                            <h3 className="text-sm font-medium text-gray-500 mb-2">Invoice Details</h3>
                            <div className="grid grid-cols-3 gap-4 mb-2">
                              <div>
                                <p className="text-sm text-gray-500">Invoice Number</p>
                                <p className="font-medium">{invoice.invoice_number}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Issue Date</p>
                                <p className="font-medium">{formatDate(invoice.issue_date)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-gray-500">Due Date</p>
                                <p className="font-medium">{formatDate(invoice.due_date)}</p>
                              </div>
                            </div>
                            <div className="grid grid-cols-3 gap-4">
                              <div>
                                <p className="text-sm text-gray-500">Status</p>
                                <div className="mt-1">{getStatusBadge(invoice.status)}</div>
                              </div>
                              {invoice.payment_date && (
                                <div>
                                  <p className="text-sm text-gray-500">Payment Date</p>
                                  <p className="font-medium">{formatDate(invoice.payment_date)}</p>
                                </div>
                              )}
                            </div>
                          </div>

                          <Separator />

                          <div>
                            <h3 className="text-sm font-medium text-gray-500 mb-2">Invoice Items</h3>
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead>Description</TableHead>
                                  <TableHead className="text-right">Quantity</TableHead>
                                  <TableHead className="text-right">Unit Price</TableHead>
                                  <TableHead className="text-right">Amount</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {invoice.invoice_items.map((item, index) => (
                                  <TableRow key={index}>
                                    <TableCell>{item.description}</TableCell>
                                    <TableCell className="text-right">{item.quantity}</TableCell>
                                    <TableCell className="text-right">
                                      {invoice.currency} {(item.unit_price || 0).toFixed(2)}
                                    </TableCell>
                                    <TableCell className="text-right">
                                      {invoice.currency} {(item.amount || 0).toFixed(2)}
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>

                          <div className="flex justify-end">
                            <div className="w-64">
                              <div className="flex justify-between py-2 border-t">
                                <span className="font-medium">Total</span>
                                <span className="font-bold">
                                  {invoice.currency} {invoice.amount.toFixed(2)}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="flex justify-end mt-4">
                            <Button onClick={() => handleDownload(invoice)}>
                              <Download className="mr-2 h-4 w-4" />
                              Download PDF
                            </Button>
                          </div>
                        </div>
                      </DialogContent>
                    </Dialog>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDownload(invoice)}
                                disabled={isDownloading === invoice.id}
                                title="Download PDF"
                              >
                                {isDownloading === invoice.id ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <Download className="h-4 w-4" />
                                )}
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default InvoicesList;
