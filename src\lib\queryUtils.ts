/**
 * React Query utilities for data fetching and caching
 */

import { useQuery, useMutation, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';

/**
 * Default stale time for queries (5 minutes)
 */
export const DEFAULT_STALE_TIME = 5 * 60 * 1000;

/**
 * Default cache time for queries (30 minutes)
 */
export const DEFAULT_CACHE_TIME = 30 * 60 * 1000;

/**
 * Enhanced useQuery hook with default options and error handling
 */
export function useEnhancedQuery<TData, TError = Error>(
  queryKey: unknown[],
  queryFn: () => Promise<TData>,
  options?: Omit<UseQueryOptions<TData, TError, TData>, 'queryKey' | 'queryFn'> & {
    fallbackData?: TData;
    errorMessage?: string;
  }
) {
  const { fallbackData, errorMessage = 'Failed to fetch data', ...queryOptions } = options || {};

  return useQuery<TData, TError, TData>({
    queryKey,
    queryFn,
    staleTime: DEFAULT_STALE_TIME,
    gcTime: DEFAULT_CACHE_TIME,
    retry: 1,
    ...queryOptions,
    onError: (error) => {
      console.error(errorMessage, error);
      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });

      if (options?.onError) {
        options.onError(error);
      }
    },
  });
}

/**
 * Enhanced useMutation hook with default options and error handling
 */
export function useEnhancedMutation<TData, TVariables, TError = Error>(
  mutationFn: (variables: TVariables) => Promise<TData>,
  options?: Omit<UseMutationOptions<TData, TError, TVariables>, 'mutationFn'> & {
    errorMessage?: string;
    successMessage?: string;
    invalidateQueries?: unknown[][];
  }
) {
  const {
    errorMessage = 'Operation failed',
    successMessage,
    invalidateQueries = [],
    onSuccess: userOnSuccess,
    ...mutationOptions
  } = options || {};

  const queryClient = useQueryClient();

  return useMutation<TData, TError, TVariables>({
    mutationFn,
    ...mutationOptions,
    onSuccess: (data, variables, context) => {
      // Invalidate relevant queries
      invalidateQueries.forEach(queryKey => {
        queryClient.invalidateQueries({ queryKey });
      });

      // Show success toast if message provided
      if (successMessage) {
        toast({
          title: 'Success',
          description: successMessage,
        });
      }

      // Call user's onSuccess handler if provided
      if (userOnSuccess) {
        userOnSuccess(data, variables, context);
      }
    },
    onError: (error, variables, context) => {
      console.error(errorMessage, error);

      toast({
        title: 'Error',
        description: errorMessage,
        variant: 'destructive',
      });

      if (mutationOptions.onError) {
        mutationOptions.onError(error, variables, context);
      }
    },
  });
}

/**
 * Utility to create query keys for consistent caching
 */
export const queryKeys = {
  jobs: {
    all: ['jobs'] as const,
    byId: (id: string) => ['jobs', id] as const,
    byCompany: (companyId: string) => ['jobs', 'company', companyId] as const,
  },
  candidates: {
    all: ['candidates'] as const,
    byId: (id: string) => ['candidates', id] as const,
    byJob: (jobId: string) => ['candidates', 'job', jobId] as const,
  },
  companies: {
    all: ['companies'] as const,
    byId: (id: string) => ['companies', id] as const,
  },
  profiles: {
    all: ['profiles'] as const,
    byId: (id: string) => ['profiles', id] as const,
    current: ['profiles', 'current'] as const,
    detail: (id?: string) => ['profiles', 'detail', id] as const,
  },
  team: {
    all: ['team'] as const,
    byCompany: (companyId: string) => ['team', 'byCompany', companyId] as const,
    detail: (id: string) => ['team', 'detail', id] as const,
  },
  notifications: {
    all: ['notifications'] as const,
  },
  subscriptions: {
    current: ['subscriptions', 'current'] as const,
  },
  usage: {
    all: ['usage'] as const,
    current: (userId?: string) => ['usage', 'current', userId] as const,
    limits: (tier: string) => ['usage', 'limits', tier] as const,
    limit: (userId?: string, usageType?: string, tier?: string) =>
      ['usage', 'limit', userId, usageType, tier] as const,
  },
};
