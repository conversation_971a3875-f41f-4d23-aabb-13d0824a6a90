import React, { useState } from 'react';
import { Download, FileText, FileSpreadsheet, ChevronDown, Loader2, ArrowUpRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useReportExport } from '@/hooks/use-reports';
import { ReportType, ReportFormat, ReportData } from '@/services/reports/reportExporter';
import { useNavigate } from 'react-router-dom';

interface ReportExportButtonProps {
  reportData: ReportData;
  reportType: ReportType;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost';
  size?: 'default' | 'sm' | 'lg';
  className?: string;
  children?: React.ReactNode;
}

const ReportExportButton: React.FC<ReportExportButtonProps> = ({
  reportData,
  reportType,
  variant = 'outline',
  size = 'default',
  className = '',
  children,
}) => {
  const navigate = useNavigate();
  const {
    exportReportData,
    isExporting,
    availableFormats,
    isReportTypeAvailable,
    subscriptionTier,
  } = useReportExport();
  const [isOpen, setIsOpen] = useState(false);

  // Check if this report type is available for the user's subscription tier
  const canAccessReport = isReportTypeAvailable(reportType);

  // If user can't access this report type, show upgrade button
  if (!canAccessReport) {
    return (
      <Button
        variant="outline"
        size={size}
        className={`border-amber-300 text-amber-800 hover:bg-amber-100 ${className}`}
        onClick={() => navigate('/dashboard/billing')}
      >
        <ArrowUpRight className="mr-2 h-4 w-4" /> Upgrade to Export
      </Button>
    );
  }

  // If no export formats are available, show upgrade button
  if (availableFormats.length === 0) {
    return (
      <Button
        variant="outline"
        size={size}
        className={`border-amber-300 text-amber-800 hover:bg-amber-100 ${className}`}
        onClick={() => navigate('/dashboard/billing')}
      >
        <ArrowUpRight className="mr-2 h-4 w-4" /> Upgrade to Export
      </Button>
    );
  }

  // If only one format is available, show a simple button
  if (availableFormats.length === 1) {
    return (
      <Button
        variant={variant}
        size={size}
        className={className}
        disabled={isExporting}
        onClick={() => exportReportData(reportData, reportType, availableFormats[0])}
      >
        {isExporting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Exporting...
          </>
        ) : (
          <>
            {children || (
              <>
                <Download className="mr-2 h-4 w-4" /> Export {availableFormats[0] === 'pdf' ? 'PDF' : 'Excel'}
              </>
            )}
          </>
        )}
      </Button>
    );
  }

  // If multiple formats are available, show a dropdown
  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant={variant}
          size={size}
          className={className}
          disabled={isExporting}
        >
          {isExporting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Exporting...
            </>
          ) : (
            <>
              {children || (
                <>
                  <Download className="mr-2 h-4 w-4" /> Export <ChevronDown className="ml-2 h-4 w-4" />
                </>
              )}
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Export Options</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {availableFormats.includes('pdf') && (
          <DropdownMenuItem
            onClick={() => {
              exportReportData(reportData, reportType, 'pdf');
              setIsOpen(false);
            }}
          >
            <FileText className="mr-2 h-4 w-4" /> Export as PDF
          </DropdownMenuItem>
        )}
        {availableFormats.includes('excel') && (
          <DropdownMenuItem
            onClick={() => {
              exportReportData(reportData, reportType, 'excel');
              setIsOpen(false);
            }}
          >
            <FileSpreadsheet className="mr-2 h-4 w-4" /> Export as Excel
          </DropdownMenuItem>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default ReportExportButton;
