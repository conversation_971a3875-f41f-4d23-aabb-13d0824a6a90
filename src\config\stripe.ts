/**
 * Stripe Configuration
 *
 * This file contains configuration settings for Stripe integration.
 * Different settings are used for development and production environments.
 */

// Environment detection
const stripeEnv = import.meta.env.VITE_STRIPE_ENVIRONMENT || 'test';
const isProduction = stripeEnv === 'production';

// Stripe Publishable Keys - These are safe to expose in client-side code
// For production, use the live publishable key from your Stripe dashboard
// For development, use the test publishable key
export const STRIPE_PUBLISHABLE_KEY = isProduction
  ? import.meta.env.VITE_STRIPE_LIVE_PUBLISHABLE_KEY || 'pk_live_...' // Live publishable key
  : import.meta.env.VITE_STRIPE_TEST_PUBLISHABLE_KEY || 'pk_test_...'; // Test publishable key

// Stripe API URLs
export const STRIPE_API_URL = 'https://api.stripe.com/v1';

// Subscription plan price IDs - These will be created via the admin interface
// Update these with actual price IDs after creating them in Stripe dashboard or admin interface
export const STRIPE_SUBSCRIPTION_PLANS = {
  STARTER: isProduction
    ? 'price_live_starter_replace_me' // Live Starter Price ID - replace with actual ID
    : '', // Test Starter Price ID - will be loaded dynamically
  GROWTH: isProduction
    ? 'price_live_growth_replace_me' // Live Growth Price ID - replace with actual ID
    : '', // Test Growth Price ID - will be loaded dynamically
  PRO: isProduction
    ? 'price_live_pro_replace_me' // Live Pro Price ID - replace with actual ID
    : '', // Test Pro Price ID - will be loaded dynamically
};

// Pricing information (for display purposes) - matches PayPal pricing
export const STRIPE_PRICING = {
  STARTER: {
    price: 49,
    currency: 'USD',
    period: 'month',
    features: [
      'Upload up to 50 CVs per month',
      'Post 1 job at a time',
      '1 company profile',
      'Automatic CV evaluation',
      'Simple hiring reports',
    ],
  },
  GROWTH: {
    price: 99,
    currency: 'USD',
    period: 'month',
    features: [
      'Upload up to 500 CVs per month',
      'Post up to 5 jobs at a time',
      'Manage up to 5 company profiles',
      'Smart CV matching with job roles',
      'Download reports (PDF or Excel)',
      'Insights to improve your hiring',
    ],
  },
  PRO: {
    price: 199,
    currency: 'USD',
    period: 'month',
    features: [
      'Unlimited CV uploads',
      'Unlimited job postings',
      'Unlimited company profiles',
      'Custom rules for candidate scoring',
      'Add team members and collaborate',
      'Detailed hiring reports and insights',
    ],
  },
};

// Webhook endpoint secret for verifying Stripe webhook events
// This should be set in your environment variables for security
export const STRIPE_WEBHOOK_SECRET = import.meta.env.VITE_STRIPE_WEBHOOK_SECRET || '';

// Project prefix for filtering plans and products (same as PayPal)
export const PROJECT_PREFIX = "ExpertRecruiter";

// Helper function to format plan name with tier
export const formatStripePlanName = (tier: string) => {
  return `${PROJECT_PREFIX} ${tier} Plan`;
};

// Helper function to format plan description with tier
export const formatStripePlanDescription = (tier: string) => {
  return `${PROJECT_PREFIX} ${tier} subscription plan`;
};

// Stripe configuration for client initialization
export const STRIPE_CONFIG = {
  apiVersion: '2023-10-16' as const,
  typescript: true,
};
