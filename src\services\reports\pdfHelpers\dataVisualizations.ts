import { jsPDF } from 'jspdf';
import { ReportData, ReportType } from '../types';

/**
 * Helper function to add data visualizations based on report type
 * @param doc The PDF document
 * @param reportData The report data
 * @param reportType The type of report
 * @param startY The Y position to start drawing
 * @returns The new Y position after drawing
 */
export const addDataVisualizations = (doc: jsPDF, reportData: ReportData, reportType: ReportType, startY: number): number => {
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;

  // Add section title
  doc.setFontSize(16);
  doc.setTextColor(41, 128, 185);
  doc.text('Data Visualization', 14, startY);
  startY += 10;

  // Add visualization based on report type
  switch (reportType) {
    case 'recruitment_funnel':
      // Draw a funnel chart
      if (reportData.data && reportData.data.length > 0) {
        // Set up chart dimensions
        const chartWidth = 160;
        const chartHeight = 120;
        const chartStartX = (pageWidth - chartWidth) / 2;

        // Find max value for scaling
        const maxValue = Math.max(...reportData.data.map(item => item.count || 0));

        // Draw funnel
        doc.setFontSize(10);
        reportData.data.forEach((item, index) => {
          // Ensure we have the required properties
          if (!item.stage || !('count' in item)) {
            return; // Skip this item if it doesn't have the required properties
          }

          const value = item.count || 0;
          const ratio = maxValue > 0 ? value / maxValue : 0;
          const width = Math.max(20, chartWidth * (1 - (index * 0.15)));
          const height = reportData.data.length > 0 ? chartHeight / reportData.data.length : 0;
          if (height <= 0) return; // Skip if height is invalid

          const x = chartStartX + (chartWidth - width) / 2;
          const y = startY + (index * height);

          // Draw funnel segment - only if all parameters are valid numbers
          if (!isNaN(x) && !isNaN(y) && !isNaN(width) && !isNaN(height)) {
            doc.setFillColor(41, 128, 185); // Use standard RGB color
            doc.rect(x, y, width, height, 'F');
          }

          // Add label
          doc.setTextColor(50);
          doc.text(item.stage, x - 5, y + height / 2, { align: 'right' });

          // Add value
          doc.setTextColor(50);
          doc.text(value.toString(), x + width + 5, y + height / 2);

          // Add percentage if not first item
          if (index > 0 && reportData.data[0] && reportData.data[0].count) {
            const percentage = (value / reportData.data[0].count * 100).toFixed(1);
            doc.text(`${percentage}%`, x + width + 30, y + height / 2);
          }
        });

        startY += chartHeight + 20;
      }
      break;

    case 'time_to_hire':
      // Draw a horizontal bar chart
      if (reportData.data && reportData.data.length > 0) {
        // Set up chart dimensions
        const chartWidth = 160;
        const barHeight = 15;
        const chartStartX = 50;
        const maxDays = Math.max(...reportData.data.map(item => item.days || 0));

        // Draw axis
        doc.setDrawColor(200);
        doc.setLineWidth(0.5);
        doc.line(chartStartX, startY, chartStartX, startY + (reportData.data.length * (barHeight + 5)));

        // Draw bars
        doc.setFontSize(8);
        reportData.data.forEach((item, index) => {
          // Ensure we have the required properties
          if (!item.position || !('days' in item)) {
            return; // Skip this item if it doesn't have the required properties
          }

          const days = item.days || 0;
          const barWidth = Math.max(0.1, (days / maxDays) * chartWidth); // Ensure minimum width
          const y = startY + (index * (barHeight + 5));

          // Draw bar - only if all parameters are valid numbers
          if (!isNaN(chartStartX) && !isNaN(y) && !isNaN(barWidth) && !isNaN(barHeight)) {
            doc.setFillColor(41, 128, 185);
            doc.rect(chartStartX, y, barWidth, barHeight, 'F');
          }

          // Add position label
          doc.setTextColor(50);
          doc.text(item.position, chartStartX - 2, y + barHeight / 2, { align: 'right' });

          // Add days value
          doc.setTextColor(255);
          doc.text(`${days} days`, chartStartX + 5, y + barHeight / 2 + 3);
        });

        startY += (reportData.data.length * (barHeight + 5)) + 20;
      }
      break;

    case 'source_effectiveness':
      // Draw a bar chart for applicants and hires by source
      if (reportData.data && reportData.data.length > 0) {
        // Set up chart dimensions
        const chartWidth = 160;
        const barHeight = 10;
        const chartStartX = 70;
        const maxApplicants = Math.max(...reportData.data.map(item => item.applicants || 0));

        // Draw legend - only if parameters are valid numbers
        if (!isNaN(chartStartX) && !isNaN(startY)) {
          doc.setFillColor(41, 128, 185);
          doc.rect(chartStartX, startY, 10, 5, 'F');
          doc.setTextColor(50);
          doc.setFontSize(8);
          doc.text('Applicants', chartStartX + 15, startY + 4);

          doc.setFillColor(82, 190, 128);
          doc.rect(chartStartX + 60, startY, 10, 5, 'F');
        }
        doc.text('Hires', chartStartX + 75, startY + 4);

        startY += 15;

        // Draw bars
        doc.setFontSize(8);
        reportData.data.forEach((item, index) => {
          // Ensure we have the required properties
          if (!item.source || !('applicants' in item) || !('hires' in item)) {
            return; // Skip this item if it doesn't have the required properties
          }

          const applicants = item.applicants || 0;
          const hires = item.hires || 0;
          const applicantWidth = Math.max(0.1, (applicants / maxApplicants) * chartWidth); // Ensure minimum width
          const hireWidth = Math.max(0.1, (hires / maxApplicants) * chartWidth); // Ensure minimum width
          const y = startY + (index * (barHeight * 2 + 10));

          // Add source label
          doc.setTextColor(50);
          doc.text(item.source, chartStartX - 5, y + barHeight / 2, { align: 'right' });

          // Draw applicant bar - only if all parameters are valid numbers
          if (!isNaN(chartStartX) && !isNaN(y) && !isNaN(applicantWidth) && !isNaN(barHeight)) {
            doc.setFillColor(41, 128, 185);
            doc.rect(chartStartX, y, applicantWidth, barHeight, 'F');
            doc.setTextColor(255);
            doc.text(`${applicants}`, chartStartX + 5, y + barHeight - 2);
          }

          // Draw hire bar - only if all parameters are valid numbers
          if (!isNaN(chartStartX) && !isNaN(y) && !isNaN(hireWidth) && !isNaN(barHeight)) {
            doc.setFillColor(82, 190, 128);
            doc.rect(chartStartX, y + barHeight + 2, hireWidth, barHeight, 'F');
          }
          doc.setTextColor(255);
          doc.text(`${hires}`, chartStartX + 5, y + barHeight * 2);

          // Add conversion rate
          const conversionRate = applicants > 0 ? (hires / applicants * 100).toFixed(1) : '0.0';
          doc.setTextColor(50);
          doc.text(`${conversionRate}%`, chartStartX + chartWidth + 10, y + barHeight, { align: 'left' });
        });

        startY += (reportData.data.length * (barHeight * 2 + 10)) + 20;
      }
      break;

    default:
      // Generic bar chart
      if (reportData.data && reportData.data.length > 0) {
        // Set up chart dimensions
        const chartWidth = 160;
        const barHeight = 15;
        const chartStartX = 50;

        // Find the value property
        const valueKey = Object.keys(reportData.data[0]).find(key =>
          typeof reportData.data[0][key] === 'number' && key !== 'id'
        ) || '';

        if (valueKey) {
          const maxValue = Math.max(...reportData.data.map(item => item[valueKey] || 0));

          // Draw bars
          doc.setFontSize(8);
          reportData.data.forEach((item, index) => {
            // Ensure we have the required properties
            if (!valueKey || !(valueKey in item)) {
              return; // Skip this item if it doesn't have the required properties
            }

            const value = item[valueKey] || 0;
            // Ensure all values are valid numbers
            const safeValue = isNaN(value) ? 0 : Number(value);
            const safeMaxValue = isNaN(maxValue) ? 1 : Number(maxValue);
            const ratio = safeMaxValue > 0 ? safeValue / safeMaxValue : 0;
            const barWidth = Math.max(0.1, ratio * chartWidth); // Ensure minimum width
            const y = startY + (index * (barHeight + 5));

            // Find label key (usually the first string property)
            const labelKey = Object.keys(item).find(key =>
              typeof item[key] === 'string' && key !== 'id'
            ) || '';

            // Draw bar - only if all parameters are valid numbers
            if (!isNaN(chartStartX) && !isNaN(y) && !isNaN(barWidth) && !isNaN(barHeight)) {
              doc.setFillColor(41, 128, 185);
              doc.rect(chartStartX, y, barWidth, barHeight, 'F');
            }

            // Add label
            if (labelKey) {
              doc.setTextColor(50);
              doc.text(item[labelKey], chartStartX - 2, y + barHeight / 2, { align: 'right' });
            }

            // Add value
            doc.setTextColor(255);
            doc.text(value.toString(), chartStartX + 5, y + barHeight / 2 + 3);
          });

          startY += (reportData.data.length * (barHeight + 5)) + 20;
        }
      }
      break;
  }

  return startY;
};
