in http://localhost:8080/dashboard/admin/ai-model-settings, please check whether when using openAI model, will it work fine just like groq? do we have a fixed schema that all AI model should follow?

Ex, see how groq input is, see the system prompt and the user prompt and also the response_format is in json:

groq official api doc:
"import { Groq } from 'groq-sdk';

const groq = new Groq();

const chatCompletion = await groq.chat.completions.create({
  "messages": [
    {
      "role": "system",
      "content": "prompt hereee"
    },
    {
      "role": "user",
      "content": "user msg here"
    }
  ],
  "model": "llama-3.3-70b-versatile",
  "temperature": 1,
  "max_completion_tokens": 32768,
  "top_p": 1,
  "stream": false,
  "response_format": {
    "type": "json_object"
  },
  "stop": null
});

console.log(chatCompletion.choices[0].message.content);"

NOTE: You must instruct the model to use JSON when using this model, ex:
"const chatCompletion = await groq.chat.completions.create({
  model: "llama-3.3-70b-versatile",
  temperature: 1,
  max_completion_tokens: 32768,
  top_p: 1,
  stream: false,
  response_format: {
    type: "json_object"
  },
  stop: null,
  messages: [
    {
      role: "system",
      content: `You are a helpful assistant that returns responses strictly in JSON format. 
Follow this JSON schema exactly:

{
  "name": "string",
  "age": number,
  "occupation": "string",
  "skills": ["string"]
}

Respond only with a JSON object, no extra text or formatting.`
    },
    {
      role: "user",
      content: "Generate a profile for someone named Alice, age 30, who is a data scientist skilled in Python and SQL."
    }
  ]
});
"

So here is openai_doc:



----------------