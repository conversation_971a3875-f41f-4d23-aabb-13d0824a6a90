import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { safeDbOperation } from '@/lib/errorHandling';
import { profileSchema, sanitizeObject, validateAndSanitize } from '@/lib/validation';

export type Profile = Database['public']['Tables']['profiles']['Row'];
export type ProfileInsert = Database['public']['Tables']['profiles']['Insert'];
export type ProfileUpdate = Database['public']['Tables']['profiles']['Update'];

/**
 * Get a user's profile
 */
export const getProfile = async (userId: string): Promise<Profile | null> => {
  if (!userId) {
    console.warn('getProfile called with empty userId');
    return null;
  }

  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        // Don't throw on 'No rows found' error, just return null
        if (error.code === 'PGRST116') {
          console.warn(`No profile found for user ${userId}`);
          return null;
        }
        throw error;
      }

      return data;
    },
    `Failed to fetch profile for user ${userId}`,
    null
  );
};

/**
 * Update a user's profile
 */
export const updateProfile = async (profile: ProfileUpdate): Promise<Profile | null> => {
  // Validate and sanitize the profile data
  const validationResult = validateAndSanitize(
    profileSchema.partial(), // Use partial() to make all fields optional for updates
    profile
  );

  if (!validationResult.success) {
    console.error('Profile validation failed:', validationResult.errors);
    throw new Error('Invalid profile data');
  }

  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('profiles')
        .update(validationResult.data)
        .eq('user_id', profile.user_id!)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to update profile for user ${profile.user_id}`,
    null
  );
};

/**
 * Upload a profile avatar
 *
 * This function uploads a user's avatar to the profiles storage bucket
 * using a folder structure that works with Supabase RLS policies.
 */
export const uploadAvatar = async (userId: string, file: File): Promise<string> => {
  return safeDbOperation(
    async () => {
      // Create a more structured path that works with RLS policies
      const fileExt = file.name.split('.').pop();
      const fileName = `${Date.now()}.${fileExt}`;
      const filePath = `avatars/${userId}/${fileName}`;

      // Upload the file
      const { error } = await supabase.storage
        .from('profiles')
        .upload(filePath, file, { upsert: true });

      if (error) throw error;

      // Get the public URL
      const { data } = supabase.storage.from('profiles').getPublicUrl(filePath);

      return data.publicUrl;
    },
    `Failed to upload avatar for user ${userId}`,
  );
};

/**
 * Remove a user's avatar
 *
 * This function removes the avatar from the user's profile
 */
export const removeAvatar = async (userId: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      // Update the profile to remove the avatar_url
      const { error } = await supabase
        .from('profiles')
        .update({ avatar_url: null })
        .eq('user_id', userId);

      if (error) throw error;

      return true;
    },
    `Failed to remove avatar for user ${userId}`,
    false
  );
};

/**
 * Get subscription details
 */
export const getSubscription = async (userId: string) => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('subscription_tier, subscription_status, subscription_end_date')
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      return {
        tier: data.subscription_tier,
        status: data.subscription_status,
        endDate: data.subscription_end_date,
      };
    },
    `Failed to fetch subscription details for user ${userId}`,
    { tier: 'starter', status: 'inactive', endDate: null } // Default fallback
  );
};
