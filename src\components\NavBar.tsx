
import { ArrowR<PERSON>, LogOut, Menu } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { PartnerCompanies } from "@/components/PartnerCompanies";
import { WhiteLabelSolution } from "@/components/WhiteLabelSolution";

export const NavBar = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <div className="w-full px-6 py-5 backdrop-blur-sm bg-gradient-to-b from-[#0A0C1B]/90 to-transparent z-50 absolute top-0 left-0 right-0">
      <div className="max-w-screen-xl mx-auto flex flex-col md:flex-row md:items-center justify-between relative">
        <div className="flex items-center justify-between mb-4 md:mb-0">
          <Link to="/" className="flex items-center">
            <img src="/logo.png" alt="RecruitAI Logo" className="h-10" />
          </Link>
          <button
            className="md:hidden text-white"
            onClick={toggleMobileMenu}
            aria-label="Toggle menu"
          >
            <Menu className="h-6 w-6" />
          </button>
        </div>
        <div className={`${mobileMenuOpen ? 'flex bg-[#0A0C1B]/95 backdrop-blur-sm p-4 rounded-lg mt-2 absolute top-full left-0 right-0 mx-6' : 'hidden'} md:flex md:static md:bg-transparent md:p-0 md:flex-row md:items-center gap-4 md:gap-8 z-50`}>
          {/* <Link to="#features" className="text-white hover:text-recruiter-blue transition">Features</Link>
          <Link to="#pricing" className="text-white hover:text-recruiter-blue transition">Pricing</Link> */}
          {/* <Link to="#testimonials" className="text-white hover:text-recruiter-blue transition">Testimonials</Link> */}
          <Link to="/jobs" className="text-white hover:text-recruiter-blue transition">Browse Jobs</Link>
          <PartnerCompanies />
          <WhiteLabelSolution />
          <Link to="/contact" className="text-white hover:text-recruiter-blue transition">Contact</Link>

          {isAuthenticated ? (
            <div className="flex gap-4 w-full md:w-auto">
              <Link
                to="/dashboard"
                className="text-white border border-gray-700 hover:bg-[#2a2f3d] py-2 px-5 rounded-lg flex items-center transition-colors w-full md:w-auto justify-center"
              >
                Dashboard
              </Link>
              <Button
                variant="outline"
                size="sm"
                onClick={handleLogout}
                className="border-gray-700 py-2 px-5 rounded-lg flex items-center transition-colors w-full md:w-auto justify-center"
              >
                <LogOut className="mr-2 h-4 w-4" /> Logout
              </Button>
            </div>
          ) : (
            <div className="flex gap-4 w-full md:w-auto">
              <Link
                to="/login"
                className="text-white border border-gray-700 hover:bg-[#2a2f3d] py-2 px-5 rounded-lg flex items-center transition-colors w-full md:w-auto justify-center"
              >
                Login
              </Link>
              <Link
                to="/signup"
                className="bg-recruiter-lightblue hover:bg-blue-500 text-white py-2 px-5 rounded-lg flex items-center transition-colors w-full md:w-auto justify-center"
              >
                Sign Up <ArrowRight size={16} className="ml-2" />
              </Link>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
