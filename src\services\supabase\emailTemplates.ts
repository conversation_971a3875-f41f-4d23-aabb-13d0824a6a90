import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

export type EmailTemplate = Database['public']['Tables']['email_templates']['Row'];
export type EmailTemplateInsert = Database['public']['Tables']['email_templates']['Insert'];
export type EmailTemplateUpdate = Database['public']['Tables']['email_templates']['Update'];

export interface EmailTemplateVariable {
  name: string;
  description: string;
  required: boolean;
}

/**
 * Get all email templates
 */
export const getEmailTemplates = async (): Promise<EmailTemplate[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .order('display_name');

      if (error) throw error;
      return data || [];
    },
    'Failed to fetch email templates',
    []
  );
};

/**
 * Get email template by ID
 */
export const getEmailTemplate = async (id: string): Promise<EmailTemplate | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .eq('id', id)
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to fetch email template with ID ${id}`,
    null
  );
};

/**
 * Get email template by name
 */
export const getEmailTemplateByName = async (name: string): Promise<EmailTemplate | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .eq('name', name)
        .eq('is_active', true)
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to fetch email template with name ${name}`,
    null
  );
};

/**
 * Create email template
 */
export const createEmailTemplate = async (template: EmailTemplateInsert): Promise<EmailTemplate> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('email_templates')
        .insert({
          ...template,
          created_by: (await supabase.auth.getUser()).data.user?.id
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    'Failed to create email template'
  );
};

/**
 * Update email template
 */
export const updateEmailTemplate = async (id: string, template: EmailTemplateUpdate): Promise<EmailTemplate> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('email_templates')
        .update({
          ...template,
          updated_by: (await supabase.auth.getUser()).data.user?.id
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to update email template with ID ${id}`
  );
};

/**
 * Delete email template
 */
export const deleteEmailTemplate = async (id: string): Promise<void> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('email_templates')
        .delete()
        .eq('id', id);

      if (error) throw error;
    },
    `Failed to delete email template with ID ${id}`
  );
};

/**
 * Toggle email template active status
 */
export const toggleEmailTemplateStatus = async (id: string, isActive: boolean): Promise<EmailTemplate> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('email_templates')
        .update({ 
          is_active: isActive,
          updated_by: (await supabase.auth.getUser()).data.user?.id
        })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    `Failed to toggle email template status for ID ${id}`
  );
};

/**
 * Render email template with variables
 */
export const renderEmailTemplate = (template: EmailTemplate, variables: Record<string, string>): { subject: string; content: string } => {
  let renderedSubject = template.subject;
  let renderedContent = template.content;

  // Replace variables in both subject and content
  Object.entries(variables).forEach(([key, value]) => {
    const regex = new RegExp(`{{${key}}}`, 'g');
    renderedSubject = renderedSubject.replace(regex, value || '');
    renderedContent = renderedContent.replace(regex, value || '');
  });

  return {
    subject: renderedSubject,
    content: renderedContent
  };
};

/**
 * Get available template types
 */
export const getTemplateTypes = (): Array<{ value: string; label: string; description: string }> => {
  return [
    {
      value: 'interview',
      label: 'Interview',
      description: 'Templates for interview-related communications'
    },
    {
      value: 'candidate',
      label: 'Candidate',
      description: 'Templates for candidate communications (applications, status updates)'
    },
    {
      value: 'team',
      label: 'Team',
      description: 'Templates for team invitations and internal communications'
    },
    {
      value: 'auth',
      label: 'Authentication',
      description: 'Templates for authentication-related emails (welcome, password reset)'
    },
    {
      value: 'system',
      label: 'System',
      description: 'Templates for system notifications and alerts'
    }
  ];
};

/**
 * Get common variables for template types
 */
export const getCommonVariables = (templateType: string): EmailTemplateVariable[] => {
  const commonVars: EmailTemplateVariable[] = [
    { name: 'companyName', description: 'Name of the company', required: true }
  ];

  switch (templateType) {
    case 'interview':
      return [
        ...commonVars,
        { name: 'candidateName', description: 'Name of the candidate', required: true },
        { name: 'jobTitle', description: 'Title of the job position', required: true },
        { name: 'interviewDate', description: 'Date of the interview', required: true },
        { name: 'interviewTime', description: 'Time of the interview', required: true },
        { name: 'duration', description: 'Duration of the interview in minutes', required: true },
        { name: 'interviewType', description: 'Type of interview (video, phone, in-person)', required: true },
        { name: 'location', description: 'Location or link for the interview', required: true }
      ];
    case 'candidate':
      return [
        ...commonVars,
        { name: 'candidateName', description: 'Name of the candidate', required: true },
        { name: 'jobTitle', description: 'Title of the job position', required: true },
        { name: 'status', description: 'Application status', required: false },
        { name: 'message', description: 'Custom message or feedback', required: false },
        { name: 'nextSteps', description: 'Information about next steps', required: false }
      ];
    case 'team':
      return [
        ...commonVars,
        { name: 'inviteeName', description: 'Name of the person being invited', required: true },
        { name: 'inviterName', description: 'Name of the person sending the invitation', required: true },
        { name: 'role', description: 'Role being offered', required: true },
        { name: 'invitationLink', description: 'Link to accept the invitation', required: true }
      ];
    case 'auth':
      return [
        { name: 'userName', description: 'Name of the user', required: true },
        { name: 'userEmail', description: 'Email of the user', required: true },
        { name: 'resetLink', description: 'Password reset link', required: false },
        { name: 'verificationLink', description: 'Email verification link', required: false }
      ];
    default:
      return commonVars;
  }
};
