import React, { useRef } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls, Sphere, Text } from '@react-three/drei';
import * as THREE from 'three';

// Sample company locations
const companyLocations = [
  { name: 'TechCorp', lat: 37.7749, lng: -122.4194, color: '#4F46E5' }, // San Francisco
  { name: 'DesignHub', lat: 40.7128, lng: -74.0060, color: '#0EA5E9' }, // New York
  { name: 'ProductLabs', lat: 51.5074, lng: -0.1278, color: '#EC4899' }, // London
  { name: 'CloudTech', lat: -33.8688, lng: 151.2093, color: '#8B5CF6' }, // Sydney
  { name: 'DataSystems', lat: 35.6762, lng: 139.6503, color: '#10B981' }, // Tokyo
  { name: 'InnovateTech', lat: 1.3521, lng: 103.8198, color: '#F59E0B' }, // Singapore
  { name: 'FutureSoft', lat: 55.7558, lng: 37.6173, color: '#EF4444' }, // Moscow
  { name: 'TalentForce', lat: -22.9068, lng: -43.1729, color: '#6366F1' }, // Rio
  { name: 'RecruitPro', lat: 28.6139, lng: 77.2090, color: '#14B8A6' }, // New Delhi
  { name: 'HireMaster', lat: -26.2041, lng: 28.0473, color: '#F97316' }, // Johannesburg
];

// Convert lat/lng to 3D coordinates on a sphere
const latLngToVector3 = (lat: number, lng: number, radius: number) => {
  const phi = (90 - lat) * (Math.PI / 180);
  const theta = (lng + 180) * (Math.PI / 180);
  
  const x = -(radius * Math.sin(phi) * Math.cos(theta));
  const z = radius * Math.sin(phi) * Math.sin(theta);
  const y = radius * Math.cos(phi);
  
  return new THREE.Vector3(x, y, z);
};

const Globe = () => {
  const globeRef = useRef<THREE.Group>(null);
  
  useFrame(({ clock }) => {
    if (globeRef.current) {
      globeRef.current.rotation.y = clock.getElapsedTime() * 0.1;
    }
  });

  return (
    <group ref={globeRef}>
      {/* Earth sphere */}
      <Sphere args={[2, 64, 64]}>
        <meshStandardMaterial
          color="#1a1a2e"
          metalness={0.5}
          roughness={0.7}
          emissive="#0a0a15"
          emissiveIntensity={0.2}
        />
      </Sphere>
      
      {/* Grid lines */}
      <Sphere args={[2.01, 64, 64]}>
        <meshBasicMaterial
          color="#4F46E5"
          wireframe
          transparent
          opacity={0.15}
        />
      </Sphere>
      
      {/* Company markers */}
      {companyLocations.map((company, index) => {
        const position = latLngToVector3(company.lat, company.lng, 2.05);
        return (
          <group key={index} position={position}>
            {/* Marker dot */}
            <mesh>
              <sphereGeometry args={[0.04, 16, 16]} />
              <meshBasicMaterial color={company.color} />
            </mesh>
            
            {/* Company name */}
            <Text
              position={[0, 0.15, 0]}
              fontSize={0.1}
              color="white"
              anchorX="center"
              anchorY="middle"
              outlineWidth={0.01}
              outlineColor="#000000"
            >
              {company.name}
            </Text>
            
            {/* Connection line to globe */}
            <line>
              <bufferGeometry
                attach="geometry"
                attributes={{
                  position: new THREE.BufferAttribute(
                    new Float32Array([0, 0, 0, 0, 0.15, 0]),
                    3
                  )
                }}
              />
              <lineBasicMaterial
                attach="material"
                color={company.color}
                linewidth={2}
                transparent
                opacity={0.7}
              />
            </line>
          </group>
        );
      })}
    </group>
  );
};

export const CompanyGlobe: React.FC = () => {
  return (
    <div className="w-full h-[500px] rounded-xl overflow-hidden">
      <Canvas camera={{ position: [0, 0, 6], fov: 45 }}>
        <ambientLight intensity={0.5} />
        <pointLight position={[10, 10, 10]} intensity={1} />
        <pointLight position={[-10, -10, -10]} intensity={0.5} color="#8B5CF6" />
        <Globe />
        <OrbitControls
          enableZoom={false}
          enablePan={false}
          rotateSpeed={0.5}
          autoRotate
          autoRotateSpeed={0.5}
        />
      </Canvas>
    </div>
  );
};
