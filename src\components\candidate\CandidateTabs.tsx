import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { BarChart3, MessageSquare, LayoutDashboard, Star, Mail, Download, FileText } from 'lucide-react';
import CandidateOverview from './CandidateOverview';
import EvaluationTab from './EvaluationTab';
import NotesTab from './NotesTab';
import CVStructuredDetailsTab from './CVStructuredDetailsTab';
import { Evaluation } from '@/services/supabase/evaluations';

interface CandidateTabsProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  candidate: any;
  evaluations: Evaluation[];
  selectedEvaluation: Evaluation | null;
  evaluationData: any;
  isLoadingEvaluations: boolean;
  handleEvaluationSelect: (evaluationId: string) => void;
  handleAddNote: () => void;
  newNote: string;
  setNewNote: (note: string) => void;
  isSubmittingNote: boolean;
  setIsEvaluateModalOpen: (isOpen: boolean) => void;
  setIsScheduleInterviewModalOpen: (isOpen: boolean) => void;
  status: string;
  getStatusColor: (status: string) => string;
  getProgressColor: (score: number) => string;
  getMatchScoreColor: (score: number) => string;
  parsedCV?: any; // Add parsed CV data
}

const CandidateTabs: React.FC<CandidateTabsProps> = ({
  activeTab,
  setActiveTab,
  candidate,
  evaluations,
  selectedEvaluation,
  evaluationData,
  isLoadingEvaluations,
  handleEvaluationSelect,
  handleAddNote,
  newNote,
  setNewNote,
  isSubmittingNote,
  setIsEvaluateModalOpen,
  setIsScheduleInterviewModalOpen,
  status,
  getStatusColor,
  getProgressColor,
  getMatchScoreColor,
  parsedCV
}) => {
  return (
    <Tabs value={activeTab} onValueChange={setActiveTab}>
      <div className="flex items-center justify-between bg-white border-b border-gray-200 w-full">
        <TabsList className="bg-transparent border-none">
          <TabsTrigger value="overview" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            <LayoutDashboard className="mr-2 h-4 w-4" /> Overview
          </TabsTrigger>
          <TabsTrigger value="cv-details" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            <FileText className="mr-2 h-4 w-4" /> CV Details
          </TabsTrigger>
          <TabsTrigger value="evaluation" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            <BarChart3 className="mr-2 h-4 w-4" /> Evaluation
          </TabsTrigger>
          <TabsTrigger value="notes" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            <MessageSquare className="mr-2 h-4 w-4" /> Notes
          </TabsTrigger>
        </TabsList>

        {/* Quick Action Buttons */}
        <div className="flex items-center gap-2 pr-4">
          <Button
            variant="outline"
            size="sm"
            className="text-xs border-gray-200 text-gray-700 hover:bg-gray-100"
            onClick={() => setIsEvaluateModalOpen(true)}
          >
            <Star className="mr-1 h-3 w-3" /> Evaluate
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="text-xs border-gray-200 text-gray-700 hover:bg-gray-100"
            onClick={() => window.open(`mailto:${candidate.email}`)}
          >
            <Mail className="mr-1 h-3 w-3" /> Contact
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="text-xs border-gray-200 text-gray-700 hover:bg-gray-100"
            onClick={() => window.open(candidate.cv_url, '_blank')}
          >
            <Download className="mr-1 h-3 w-3" /> Download CV
          </Button>
        </div>
      </div>

      {/* Overview Tab */}
      <TabsContent value="overview" className="mt-6">
        <CandidateOverview
          candidate={candidate}
          evaluations={evaluations}
          evaluationData={evaluationData}
          status={status}
          setActiveTab={setActiveTab}
          setIsEvaluateModalOpen={setIsEvaluateModalOpen}
          setIsScheduleInterviewModalOpen={setIsScheduleInterviewModalOpen}
          getStatusColor={getStatusColor}
          getProgressColor={getProgressColor}
          getMatchScoreColor={getMatchScoreColor}
        />
      </TabsContent>

      {/* CV Details Tab */}
      <TabsContent value="cv-details" className="mt-6">
        <CVStructuredDetailsTab
          candidate={candidate}
          parsedCV={parsedCV}
        />
      </TabsContent>

      {/* Evaluation Tab */}
      <TabsContent value="evaluation" className="mt-6">
        <EvaluationTab
          evaluations={evaluations}
          selectedEvaluation={selectedEvaluation}
          evaluationData={evaluationData}
          isLoadingEvaluations={isLoadingEvaluations}
          handleEvaluationSelect={handleEvaluationSelect}
          setIsEvaluateModalOpen={setIsEvaluateModalOpen}
          setIsScheduleInterviewModalOpen={setIsScheduleInterviewModalOpen}
          candidate={candidate}
        />
      </TabsContent>

      {/* Notes Tab */}
      <TabsContent value="notes" className="mt-6">
        <NotesTab
          candidate={candidate}
          newNote={newNote}
          setNewNote={setNewNote}
          handleAddNote={handleAddNote}
          isSubmittingNote={isSubmittingNote}
        />
      </TabsContent>
    </Tabs>
  );
};

export default CandidateTabs;
