import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { corsHeaders } from '../_shared/cors.ts'

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'GET') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    })
  }

  try {
    // Check SMTP configuration
    const smtpConfig = {
      host: Deno.env.get('SMTP_HOST'),
      port: parseInt(Deno.env.get('SMTP_PORT') || '587'),
      username: Deno.env.get('SMTP_USER'),
      password: Deno.env.get('SMTP_PASSWORD'),
    }

    if (!smtpConfig.host || !smtpConfig.username || !smtpConfig.password) {
      return new Response(JSON.stringify({ 
        success: false, 
        error: 'SMTP configuration incomplete' 
      }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 500,
      })
    }

    // Check if Resend API is available
    const resendApiKey = Deno.env.get('RESEND_API_KEY')
    
    if (resendApiKey) {
      // Verify Resend API key
      const response = await fetch('https://api.resend.com/domains', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${resendApiKey}`,
        },
      })

      if (response.ok) {
        console.log('Email service verified successfully (Resend)')
        return new Response(JSON.stringify({ success: true, service: 'resend' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
    }

    // Basic SMTP configuration check
    console.log('Email service configured (SMTP)')
    return new Response(JSON.stringify({ success: true, service: 'smtp' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
    
  } catch (error) {
    console.error('Email service verification failed:', error)
    
    return new Response(JSON.stringify({ 
      success: false, 
      error: 'Email service verification failed' 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})