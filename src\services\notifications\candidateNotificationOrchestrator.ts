import { supabase } from '@/lib/supabase';
import { 
  sendApplicationReceivedEmail,
  sendCandidateStatusUpdateEmail,
  sendRejectionEmail,
  sendOfferEmail,
  CandidateNotificationData,
  StatusUpdateData
} from '@/services/email/candidateNotifications';
import { StatusManager } from '@/services/assignments/statusManager';

/**
 * Candidate Notification Orchestrator
 * Manages all automated email notifications to candidates throughout the hiring process
 */
export class CandidateNotificationOrchestrator {
  private statusManager: StatusManager;

  constructor() {
    this.statusManager = new StatusManager();
  }

  /**
   * Handle application received notification
   */
  async handleApplicationReceived(
    candidateId: string,
    jobId: string,
    applicationId: string
  ): Promise<void> {
    try {
      // Get candidate, job, and company details
      const { data: candidate } = await supabase
        .from('candidates')
        .select('name, email')
        .eq('id', candidateId)
        .single();

      const { data: job } = await supabase
        .from('jobs')
        .select(`
          title,
          companies:company_id (name)
        `)
        .eq('id', jobId)
        .single();

      if (!candidate || !job) {
        console.error('Missing candidate or job data for application notification');
        return;
      }

      await sendApplicationReceivedEmail({
        candidateEmail: candidate.email,
        candidateName: candidate.name,
        jobTitle: job.title,
        companyName: job.companies?.name || 'Unknown Company',
        applicationId,
        jobId
      });

      console.log(`✅ Application received email sent to ${candidate.email}`);
    } catch (error) {
      console.error('Failed to send application received notification:', error);
    }
  }

  /**
   * Handle status change notification
   */
  async handleStatusChange(
    candidateId: string,
    oldStatus: string,
    newStatus: string,
    context?: {
      message?: string;
      nextSteps?: string;
      jobId?: string;
    }
  ): Promise<void> {
    try {
      console.log(`🔔 Processing status change notification: ${candidateId} (${oldStatus} → ${newStatus})`);

      // Get candidate details
      const { data: candidate } = await supabase
        .from('candidates')
        .select('name, email, job_id')
        .eq('id', candidateId)
        .single();

      if (!candidate) {
        console.error('❌ Candidate not found for status change notification');
        return;
      }

      if (!candidate.email) {
        console.error('❌ No email address for candidate');
        return;
      }

      // Get job and company details - try multiple approaches
      const jobId = context?.jobId || candidate.job_id;
      let job = null;

      if (jobId) {
        const { data: jobData } = await supabase
          .from('jobs')
          .select(`
            title,
            companies:company_id (name)
          `)
          .eq('id', jobId)
          .single();
        job = jobData;
      }

      // If no job found, try to get from candidate assignments
      if (!job) {
        const { data: assignment } = await supabase
          .from('candidate_assignments')
          .select(`
            jobs:job_id (
              title,
              companies:company_id (name)
            )
          `)
          .eq('candidate_id', candidateId)
          .limit(1)
          .single();

        if (assignment?.jobs) {
          job = assignment.jobs;
        }
      }

      // Use fallback values if job not found
      const jobTitle = job?.title || 'Position';
      const companyName = job?.companies?.name || 'Company';

      const notificationData: StatusUpdateData = {
        candidateEmail: candidate.email,
        candidateName: candidate.name || 'Candidate',
        jobTitle,
        companyName,
        oldStatus,
        newStatus,
        message: context?.message,
        nextSteps: context?.nextSteps,
        jobId
      };

      console.log(`📧 Sending ${newStatus} notification to ${candidate.email}`);

      // Send appropriate notification based on new status
      switch (newStatus) {
        case 'rejected':
          await sendRejectionEmail({
            ...notificationData,
            message: context?.message
          });
          break;

        case 'offer':
          await sendOfferEmail({
            ...notificationData,
            message: context?.message,
            nextSteps: context?.nextSteps
          });
          break;

        default:
          await sendCandidateStatusUpdateEmail(notificationData);
          break;
      }

      console.log(`✅ Status change email sent to ${candidate.email} (${oldStatus} → ${newStatus})`);
    } catch (error) {
      console.error('❌ Failed to send status change notification:', error);
    }
  }

  /**
   * Handle interview scheduled notification
   * Note: This is already handled by the interview notification service
   * This method is here for completeness and future enhancements
   */
  async handleInterviewScheduled(
    candidateId: string,
    interviewId: string
  ): Promise<void> {
    try {
      console.log(`Interview scheduled notification handled by interview service for candidate ${candidateId}`);
      
      // Optionally trigger status change to 'interview' if not already set
      const { data: candidate } = await supabase
        .from('candidates')
        .select('status')
        .eq('id', candidateId)
        .single();

      if (candidate && candidate.status === 'screening') {
        await this.statusManager.applyAutoStatus('interview_scheduled', candidateId);
      }
    } catch (error) {
      console.error('Failed to handle interview scheduled notification:', error);
    }
  }

  /**
   * Handle interview completed notification
   */
  async handleInterviewCompleted(
    candidateId: string,
    interviewId: string,
    feedback?: string
  ): Promise<void> {
    try {
      // Trigger auto-status change if configured
      await this.statusManager.applyAutoStatus('interview_completed', candidateId);
      
      console.log(`Interview completed notification processed for candidate ${candidateId}`);
    } catch (error) {
      console.error('Failed to handle interview completed notification:', error);
    }
  }

  /**
   * Send bulk status update notifications
   */
  async handleBulkStatusUpdate(
    candidateIds: string[],
    newStatus: string,
    context?: {
      message?: string;
      nextSteps?: string;
    }
  ): Promise<void> {
    const results = await Promise.allSettled(
      candidateIds.map(candidateId => 
        this.handleStatusChange(candidateId, 'unknown', newStatus, context)
      )
    );

    const successful = results.filter(r => r.status === 'fulfilled').length;
    const failed = results.filter(r => r.status === 'rejected').length;

    console.log(`Bulk status update notifications: ${successful} successful, ${failed} failed`);
  }

  /**
   * Test notification system
   */
  async testNotifications(candidateEmail: string): Promise<void> {
    try {
      const testData: CandidateNotificationData = {
        candidateEmail,
        candidateName: 'Test Candidate',
        jobTitle: 'Test Position',
        companyName: 'Test Company'
      };

      await sendApplicationReceivedEmail(testData);
      console.log('✅ Test notification sent successfully');
    } catch (error) {
      console.error('❌ Test notification failed:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const candidateNotificationOrchestrator = new CandidateNotificationOrchestrator();
