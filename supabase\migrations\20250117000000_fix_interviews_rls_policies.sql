-- Fix infinite recursion in interviews RLS policies
-- Drop existing problematic policies

-- Drop all existing policies for interviews table
DROP POLICY IF EXISTS "Users can view their own interviews" ON public.interviews;
DROP POLICY IF EXISTS "Users can insert their own interviews" ON public.interviews;
DROP POLICY IF EXISTS "Users can update their own interviews" ON public.interviews;
DROP POLICY IF EXISTS "Users can delete their own interviews" ON public.interviews;

-- Drop all existing policies for interview_participants table
DROP POLICY IF EXISTS "Users can view interview participants" ON public.interview_participants;
DROP POLICY IF EXISTS "Users can insert interview participants" ON public.interview_participants;
DROP POLICY IF EXISTS "Users can update interview participants" ON public.interview_participants;
DROP POLICY IF EXISTS "Users can delete interview participants" ON public.interview_participants;

-- Drop all existing policies for interview_feedback table
DROP POLICY IF EXISTS "Users can view interview feedback" ON public.interview_feedback;
DROP POLICY IF EXISTS "Users can insert their own interview feedback" ON public.interview_feedback;
DROP POLICY IF EXISTS "Users can update their own interview feedback" ON public.interview_feedback;
DROP POLICY IF EXISTS "Users can delete their own interview feedback" ON public.interview_feedback;

-- Temporarily disable <PERSON>LS on interviews tables to avoid recursion
ALTER TABLE public.interviews DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.interview_participants DISABLE ROW LEVEL SECURITY;
ALTER TABLE public.interview_feedback DISABLE ROW LEVEL SECURITY;

-- Create simple, non-recursive policies for interviews
-- Re-enable RLS
ALTER TABLE public.interviews ENABLE ROW LEVEL SECURITY;

-- Simple policy: Users can only see interviews they created
CREATE POLICY "Users can view their own interviews"
  ON public.interviews FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own interviews"
  ON public.interviews FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own interviews"
  ON public.interviews FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own interviews"
  ON public.interviews FOR DELETE
  USING (auth.uid() = user_id);

-- Re-enable RLS for interview_participants
ALTER TABLE public.interview_participants ENABLE ROW LEVEL SECURITY;

-- Simple policy: Users can manage participants for interviews they own
CREATE POLICY "Users can view interview participants for their interviews"
  ON public.interview_participants FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE interviews.id = interview_participants.interview_id 
      AND interviews.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert interview participants for their interviews"
  ON public.interview_participants FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE interviews.id = interview_participants.interview_id 
      AND interviews.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can update interview participants for their interviews"
  ON public.interview_participants FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE interviews.id = interview_participants.interview_id 
      AND interviews.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can delete interview participants for their interviews"
  ON public.interview_participants FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE interviews.id = interview_participants.interview_id 
      AND interviews.user_id = auth.uid()
    )
  );

-- Re-enable RLS for interview_feedback
ALTER TABLE public.interview_feedback ENABLE ROW LEVEL SECURITY;

-- Simple policy: Users can manage feedback for interviews they own or participate in
CREATE POLICY "Users can view interview feedback for their interviews"
  ON public.interview_feedback FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE interviews.id = interview_feedback.interview_id 
      AND interviews.user_id = auth.uid()
    ) OR
    interview_feedback.user_id = auth.uid()
  );

CREATE POLICY "Users can insert their own interview feedback"
  ON public.interview_feedback FOR INSERT
  WITH CHECK (
    interview_feedback.user_id = auth.uid()
  );

CREATE POLICY "Users can update their own interview feedback"
  ON public.interview_feedback FOR UPDATE
  USING (interview_feedback.user_id = auth.uid());

CREATE POLICY "Users can delete their own interview feedback"
  ON public.interview_feedback FOR DELETE
  USING (interview_feedback.user_id = auth.uid());

-- Add admin bypass policies for platform admins
CREATE POLICY "Platform admins can view all interviews"
  ON public.interviews FOR SELECT
  USING (
    auth.uid() IN (
      '778d128d-606f-484e-b257-257390743df4'  -- Your admin ID
    )
  );

CREATE POLICY "Platform admins can view all interview participants"
  ON public.interview_participants FOR SELECT
  USING (
    auth.uid() IN (
      '778d128d-606f-484e-b257-257390743df4'  -- Your admin ID
    )
  );

CREATE POLICY "Platform admins can view all interview feedback"
  ON public.interview_feedback FOR SELECT
  USING (
    auth.uid() IN (
      '778d128d-606f-484e-b257-257390743df4'  -- Your admin ID
    )
  );
