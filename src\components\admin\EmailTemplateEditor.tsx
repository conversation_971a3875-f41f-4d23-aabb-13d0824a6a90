import React, { useState, useEffect } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { 
  createEmailTemplate, 
  updateEmailTemplate, 
  getTemplateTypes, 
  getCommonVariables,
  type EmailTemplate,
  type EmailTemplateVariable 
} from '@/services/supabase/emailTemplates';
import { Plus, X, Eye, Code } from 'lucide-react';

interface EmailTemplateEditorProps {
  template?: EmailTemplate | null;
  onSave: () => void;
  onCancel: () => void;
}

export const EmailTemplateEditor: React.FC<EmailTemplateEditorProps> = ({
  template,
  onSave,
  onCancel
}) => {
  const [formData, setFormData] = useState({
    name: '',
    display_name: '',
    description: '',
    subject: '',
    content: '',
    template_type: 'candidate',
    is_active: true
  });
  
  const [variables, setVariables] = useState<EmailTemplateVariable[]>([]);
  const [showPreview, setShowPreview] = useState(false);
  const [previewVariables, setPreviewVariables] = useState<Record<string, string>>({});

  const { toast } = useToast();
  const templateTypes = getTemplateTypes();

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name,
        display_name: template.display_name,
        description: template.description || '',
        subject: template.subject,
        content: template.content,
        template_type: template.template_type,
        is_active: template.is_active
      });
      setVariables(template.variables as EmailTemplateVariable[] || []);
    } else {
      // Reset form for new template
      setFormData({
        name: '',
        display_name: '',
        description: '',
        subject: '',
        content: '',
        template_type: 'candidate',
        is_active: true
      });
      setVariables(getCommonVariables('candidate'));
    }
  }, [template]);

  useEffect(() => {
    // Update variables when template type changes
    if (!template) {
      setVariables(getCommonVariables(formData.template_type));
    }
  }, [formData.template_type, template]);

  useEffect(() => {
    // Initialize preview variables with sample data
    const sampleData: Record<string, string> = {};
    variables.forEach(variable => {
      switch (variable.name) {
        case 'companyName':
          sampleData[variable.name] = 'Acme Corporation';
          break;
        case 'candidateName':
          sampleData[variable.name] = 'John Doe';
          break;
        case 'jobTitle':
          sampleData[variable.name] = 'Senior Software Engineer';
          break;
        case 'interviewDate':
          sampleData[variable.name] = 'Monday, January 15, 2024';
          break;
        case 'interviewTime':
          sampleData[variable.name] = '2:00 PM';
          break;
        case 'duration':
          sampleData[variable.name] = '60';
          break;
        case 'interviewType':
          sampleData[variable.name] = 'Video';
          break;
        case 'location':
          sampleData[variable.name] = 'https://meet.google.com/abc-defg-hij';
          break;
        default:
          sampleData[variable.name] = `Sample ${variable.name}`;
      }
    });
    setPreviewVariables(sampleData);
  }, [variables]);

  const createMutation = useMutation({
    mutationFn: createEmailTemplate,
    onSuccess: () => {
      toast({
        title: 'Template created',
        description: 'Email template has been created successfully.',
      });
      onSave();
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to create email template.',
        variant: 'destructive',
      });
    }
  });

  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: string; data: any }) => updateEmailTemplate(id, data),
    onSuccess: () => {
      toast({
        title: 'Template updated',
        description: 'Email template has been updated successfully.',
      });
      onSave();
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to update email template.',
        variant: 'destructive',
      });
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const templateData = {
      ...formData,
      variables: variables
    };

    if (template) {
      updateMutation.mutate({ id: template.id, data: templateData });
    } else {
      createMutation.mutate(templateData);
    }
  };

  const addVariable = () => {
    setVariables([...variables, { name: '', description: '', required: false }]);
  };

  const updateVariable = (index: number, field: keyof EmailTemplateVariable, value: any) => {
    const newVariables = [...variables];
    newVariables[index] = { ...newVariables[index], [field]: value };
    setVariables(newVariables);
  };

  const removeVariable = (index: number) => {
    setVariables(variables.filter((_, i) => i !== index));
  };

  const insertVariable = (variableName: string) => {
    const textarea = document.getElementById('content-textarea') as HTMLTextAreaElement;
    if (textarea) {
      const start = textarea.selectionStart;
      const end = textarea.selectionEnd;
      const text = textarea.value;
      const before = text.substring(0, start);
      const after = text.substring(end, text.length);
      const newText = before + `{{${variableName}}}` + after;
      
      setFormData({ ...formData, content: newText });
      
      // Set cursor position after the inserted variable
      setTimeout(() => {
        textarea.focus();
        textarea.setSelectionRange(start + variableName.length + 4, start + variableName.length + 4);
      }, 0);
    }
  };

  const renderPreview = () => {
    let previewContent = formData.content;
    let previewSubject = formData.subject;
    
    Object.entries(previewVariables).forEach(([key, value]) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      previewContent = previewContent.replace(regex, value);
      previewSubject = previewSubject.replace(regex, value);
    });

    return (
      <div className="space-y-4">
        <div>
          <Label className="text-sm font-medium">Subject Preview:</Label>
          <div className="mt-1 p-3 bg-gray-50 rounded-md border">
            {previewSubject}
          </div>
        </div>
        <div>
          <Label className="text-sm font-medium">Content Preview:</Label>
          <div 
            className="mt-1 p-4 bg-white rounded-md border max-h-96 overflow-y-auto"
            dangerouslySetInnerHTML={{ __html: previewContent }}
          />
        </div>
      </div>
    );
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Left Column - Form Fields */}
        <div className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Template Name *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                placeholder="e.g., interview_invitation_candidate"
                required
              />
            </div>
            <div>
              <Label htmlFor="template_type">Type *</Label>
              <select
                id="template_type"
                value={formData.template_type}
                onChange={(e) => setFormData({ ...formData, template_type: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                required
              >
                {templateTypes.map(type => (
                  <option key={type.value} value={type.value}>
                    {type.label}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div>
            <Label htmlFor="display_name">Display Name *</Label>
            <Input
              id="display_name"
              value={formData.display_name}
              onChange={(e) => setFormData({ ...formData, display_name: e.target.value })}
              placeholder="e.g., Interview Invitation - Candidate"
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Brief description of when this template is used"
            />
          </div>

          <div>
            <Label htmlFor="subject">Subject *</Label>
            <Input
              id="subject"
              value={formData.subject}
              onChange={(e) => setFormData({ ...formData, subject: e.target.value })}
              placeholder="e.g., Interview Scheduled: {{jobTitle}}"
              required
            />
          </div>

          <div>
            <Label htmlFor="content-textarea">Content *</Label>
            <Textarea
              id="content-textarea"
              value={formData.content}
              onChange={(e) => setFormData({ ...formData, content: e.target.value })}
              placeholder="HTML content of the email template"
              className="min-h-[200px] font-mono text-sm"
              required
            />
          </div>
        </div>

        {/* Right Column - Variables and Preview */}
        <div className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Variables</CardTitle>
                <Button type="button" variant="outline" size="sm" onClick={addVariable}>
                  <Plus className="h-4 w-4 mr-1" />
                  Add
                </Button>
              </div>
              <CardDescription>
                Define variables that can be used in the template
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 max-h-64 overflow-y-auto">
              {variables.map((variable, index) => (
                <div key={index} className="flex items-center gap-2 p-2 border rounded">
                  <div className="flex-1 space-y-1">
                    <Input
                      placeholder="Variable name"
                      value={variable.name}
                      onChange={(e) => updateVariable(index, 'name', e.target.value)}
                      className="h-8 text-sm"
                    />
                    <Input
                      placeholder="Description"
                      value={variable.description}
                      onChange={(e) => updateVariable(index, 'description', e.target.value)}
                      className="h-8 text-sm"
                    />
                  </div>
                  <div className="flex flex-col gap-1">
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => insertVariable(variable.name)}
                      disabled={!variable.name}
                      className="h-8 px-2"
                    >
                      Insert
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeVariable(index)}
                      className="h-8 px-2"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Preview</CardTitle>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPreview(!showPreview)}
                >
                  {showPreview ? <Code className="h-4 w-4 mr-1" /> : <Eye className="h-4 w-4 mr-1" />}
                  {showPreview ? 'Code' : 'Preview'}
                </Button>
              </div>
            </CardHeader>
            <CardContent className="max-h-96 overflow-y-auto">
              {showPreview ? renderPreview() : (
                <div className="text-sm text-gray-500">
                  Click "Preview" to see how the template will look with sample data
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end gap-3 pt-4 border-t">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={createMutation.isPending || updateMutation.isPending}
        >
          {template ? 'Update Template' : 'Create Template'}
        </Button>
      </div>
    </form>
  );
};
