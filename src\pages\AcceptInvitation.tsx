import { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { useAcceptInvitationWithAutoSignup } from '@/hooks/use-team';
import { useAuth } from '@/contexts/AuthContext';
import { getInvitationDetails } from '@/services/supabase/invitationAuth';
import { supabase } from '@/lib/supabase';
import { Loader2, Users } from 'lucide-react';
import PasswordSetup from '@/components/auth/PasswordSetup';

export default function AcceptInvitation() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const acceptInvitationMutation = useAcceptInvitationWithAutoSignup();
  const [isProcessing, setIsProcessing] = useState(false);
  const [invitationData, setInvitationData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [showPasswordSetup, setShowPasswordSetup] = useState(false);

  const token = searchParams.get('token');

  useEffect(() => {
    if (!token) {
      toast({
        title: 'Invalid invitation',
        description: 'The invitation link is invalid or expired.',
        variant: 'destructive',
      });
      navigate('/dashboard');
      return;
    }

    fetchInvitationDetails();
  }, [token, navigate, toast]);

  const fetchInvitationDetails = async () => {
    if (!token) return;

    try {
      setIsLoading(true);
      const details = await getInvitationDetails(token);
      setInvitationData(details);
    } catch (error) {
      console.error('Failed to fetch invitation details:', error);
      toast({
        title: 'Invalid invitation',
        description: 'The invitation link is invalid or expired.',
        variant: 'destructive',
      });
      navigate('/dashboard');
    } finally {
      setIsLoading(false);
    }
  };

  const handleAcceptInvitation = async () => {
    if (!token) return;

    setIsProcessing(true);
    try {
      const result = await acceptInvitationMutation.mutateAsync(token);
      
      // If a new user was created, we need to refresh the auth context
      if (result.needsPasswordSetup) {
        // Wait a moment for the auth state to update
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Check if user is now authenticated
        const { data: session } = await supabase.auth.getSession();
        if (!session.session) {
          throw new Error('Authentication failed after account creation');
        }
      }
      
      toast({
        title: 'Invitation accepted',
        description: 'You have successfully joined the team!',
      });
      
      // Check if user needs to set password
      if (result.needsPasswordSetup) {
        setShowPasswordSetup(true);
      } else {
        navigate('/dashboard/team');
      }
    } catch (error: any) {
      toast({
        title: 'Failed to accept invitation',
        description: error.message || 'The invitation may be invalid or expired.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  const handlePasswordSetupComplete = () => {
    setShowPasswordSetup(false);
    navigate('/dashboard/team');
  };

  if (!token || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center p-6">
            <Loader2 className="h-6 w-6 animate-spin" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (showPasswordSetup) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <PasswordSetup onComplete={handlePasswordSetupComplete} />
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Team Invitation
          </CardTitle>
          <CardDescription>
            You've been invited to join a team on Sourcio.ai.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {invitationData && (
            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
              <div className="text-sm">
                <span className="font-medium">Company:</span> {invitationData.companies?.name}
              </div>
              <div className="text-sm">
                <span className="font-medium">Role:</span> {invitationData.role}
              </div>
              <div className="text-sm">
                <span className="font-medium">Invited by:</span> {invitationData.profiles?.full_name}
              </div>
              {invitationData.metadata?.name && (
                <div className="text-sm">
                  <span className="font-medium">Invited as:</span> {invitationData.metadata.name}
                </div>
              )}
            </div>
          )}
          
          <Button
            onClick={handleAcceptInvitation}
            disabled={isProcessing || acceptInvitationMutation.isPending}
            className="w-full"
          >
            {isProcessing || acceptInvitationMutation.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                {user ? 'Accepting...' : 'Creating Account & Accepting...'}
              </>
            ) : (
              'Accept Invitation'
            )}
          </Button>
          
          <Button
            variant="outline"
            onClick={() => navigate('/dashboard')}
            className="w-full"
          >
            Cancel
          </Button>
          
          {!user && (
            <p className="text-xs text-gray-500 text-center">
              An account will be automatically created for you. You'll set your password after joining.
            </p>
          )}
        </CardContent>
      </Card>
    </div>
  );
}





