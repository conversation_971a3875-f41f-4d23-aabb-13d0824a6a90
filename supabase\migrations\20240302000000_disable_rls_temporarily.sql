-- Temporarily disable <PERSON><PERSON> on all tables to fix the infinite recursion issue

-- Disable <PERSON><PERSON> on team_members table
ALTER TABLE team_members DISABLE ROW LEVEL SECURITY;

-- Disable RLS on candidates table
ALTER TABLE candidates DISABLE ROW LEVEL SECURITY;

-- Disable <PERSON><PERSON> on jobs table
ALTER TABLE jobs DISABLE ROW LEVEL SECURITY;

-- Disable RLS on companies table
ALTER TABLE companies DISABLE ROW LEVEL SECURITY;

-- Disable RLS on profiles table
ALTER TABLE profiles DISABLE ROW LEVEL SECURITY;

-- Disable RLS on notifications table
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;

-- Drop all policies that might be causing issues
DROP POLICY IF EXISTS "Users can view own team_members" ON team_members;
DROP POLICY IF EXISTS "Users can insert own team_members" ON team_members;
DROP POLICY IF EXISTS "Users can update own team_members" ON team_members;
DROP POLICY IF EXISTS "Users can delete own team_members" ON team_members;
DROP POLICY IF EXISTS "Admin bypass for team_members" ON team_members;
DROP POLICY IF EXISTS "Platform admins can view all data" ON team_members;

DROP POLICY IF EXISTS "Users can view own candidates" ON candidates;
DROP POLICY IF EXISTS "Users can insert own candidates" ON candidates;
DROP POLICY IF EXISTS "Users can update own candidates" ON candidates;
DROP POLICY IF EXISTS "Users can delete own candidates" ON candidates;
DROP POLICY IF EXISTS "Admin bypass for candidates" ON candidates;
DROP POLICY IF EXISTS "Platform admins can view all data" ON candidates;

DROP POLICY IF EXISTS "Users can view own jobs" ON jobs;
DROP POLICY IF EXISTS "Users can insert own jobs" ON jobs;
DROP POLICY IF EXISTS "Users can update own jobs" ON jobs;
DROP POLICY IF EXISTS "Users can delete own jobs" ON jobs;
DROP POLICY IF EXISTS "Admin bypass for jobs" ON jobs;
DROP POLICY IF EXISTS "Platform admins can view all data" ON jobs;

DROP POLICY IF EXISTS "Users can view own companies" ON companies;
DROP POLICY IF EXISTS "Users can insert own companies" ON companies;
DROP POLICY IF EXISTS "Users can update own companies" ON companies;
DROP POLICY IF EXISTS "Users can delete own companies" ON companies;
DROP POLICY IF EXISTS "Admin bypass for companies" ON companies;
DROP POLICY IF EXISTS "Platform admins can view all data" ON companies;

-- Note: This is a temporary solution to get the application working
-- In a production environment, you would want to implement proper RLS policies
-- to ensure data security and multi-tenant isolation
