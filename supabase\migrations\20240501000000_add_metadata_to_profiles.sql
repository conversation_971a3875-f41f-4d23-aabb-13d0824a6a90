-- Add metadata column to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.profiles.metadata IS 'Additional profile data stored as JSON, including preferences, social links, etc.';

-- Create index for faster queries on metadata
CREATE INDEX IF NOT EXISTS idx_profiles_metadata ON public.profiles USING GIN (metadata);
