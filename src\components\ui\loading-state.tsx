import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";

interface LoadingStateProps {
  /**
   * Text to display below the loading spinner
   */
  text?: string;
  
  /**
   * Size of the loading spinner (small, medium, large)
   */
  size?: "small" | "medium" | "large";
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Whether to center the loading state
   */
  center?: boolean;
}

/**
 * A reusable loading state component
 */
export function LoadingState({
  text = "Loading...",
  size = "medium",
  className,
  center = true,
}: LoadingStateProps) {
  const sizeClasses = {
    small: "h-4 w-4",
    medium: "h-8 w-8",
    large: "h-12 w-12",
  };
  
  const containerClasses = cn(
    "flex flex-col items-center space-y-2",
    center && "justify-center",
    className
  );
  
  return (
    <div className={containerClasses}>
      <Loader2 className={cn("animate-spin text-primary", sizeClasses[size])} />
      {text && <p className="text-sm text-muted-foreground">{text}</p>}
    </div>
  );
}
