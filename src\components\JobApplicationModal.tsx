import React, { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useApplyToJob } from '@/hooks/use-candidate-portal';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Upload, FileText, X } from 'lucide-react';
import { uploadCV } from '@/services/supabase/candidates';

interface JobApplicationModalProps {
  isOpen: boolean;
  onClose: () => void;
  job: {
    id: string;
    title: string;
    company_name?: string;
  };
}

export const JobApplicationModal: React.FC<JobApplicationModalProps> = ({
  isOpen,
  onClose,
  job
}) => {
  const { user } = useAuth();
  const applyToJobMutation = useApplyToJob();
  
  const [formData, setFormData] = useState({
    candidateName: user?.name || '',
    candidateEmail: user?.email || '',
    coverLetter: ''
  });
  
  const [cvFile, setCvFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      if (!allowedTypes.includes(file.type)) {
        setErrors({ cv: 'Please upload a PDF or Word document' });
        return;
      }
      
      // Validate file size (5MB max)
      if (file.size > 5 * 1024 * 1024) {
        setErrors({ cv: 'File size must be less than 5MB' });
        return;
      }
      
      setCvFile(file);
      setErrors(prev => ({ ...prev, cv: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!formData.candidateName.trim()) {
      newErrors.candidateName = 'Name is required';
    }
    
    if (!formData.candidateEmail.trim()) {
      newErrors.candidateEmail = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.candidateEmail)) {
      newErrors.candidateEmail = 'Please enter a valid email';
    }
    
    if (!cvFile) {
      newErrors.cv = 'Please upload your CV/Resume';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    if (!cvFile) {
      return;
    }
    
    setIsUploading(true);
    
    try {
      // Upload CV file (without usage limit checking for candidates)
      const cvUrl = await uploadCV(cvFile, false);
      
      // Submit application
      await applyToJobMutation.mutateAsync({
        jobId: job.id,
        candidateName: formData.candidateName,
        candidateEmail: formData.candidateEmail,
        cvFileUrl: cvUrl,
        coverLetterContent: formData.coverLetter || undefined
      });
      
      // Close modal on success
      onClose();
      
      // Reset form
      setFormData({
        candidateName: user?.name || '',
        candidateEmail: user?.email || '',
        coverLetter: ''
      });
      setCvFile(null);
      
    } catch (error: any) {
      setErrors({ submit: error.message || 'Failed to submit application' });
    } finally {
      setIsUploading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const isLoading = isUploading || applyToJobMutation.isPending;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Apply for {job.title}</DialogTitle>
          <DialogDescription>
            {job.company_name && `at ${job.company_name}`}
          </DialogDescription>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {errors.submit && (
            <Alert variant="destructive">
              <AlertDescription>{errors.submit}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-2">
            <Label htmlFor="candidateName">Full Name *</Label>
            <Input
              id="candidateName"
              name="candidateName"
              value={formData.candidateName}
              onChange={handleInputChange}
              className={errors.candidateName ? 'border-red-500' : ''}
              disabled={isLoading}
            />
            {errors.candidateName && (
              <p className="text-sm text-red-500">{errors.candidateName}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="candidateEmail">Email Address *</Label>
            <Input
              id="candidateEmail"
              name="candidateEmail"
              type="email"
              value={formData.candidateEmail}
              onChange={handleInputChange}
              className={errors.candidateEmail ? 'border-red-500' : ''}
              disabled={isLoading}
            />
            {errors.candidateEmail && (
              <p className="text-sm text-red-500">{errors.candidateEmail}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cv">CV/Resume * (PDF or Word, max 5MB)</Label>
            <div className="flex items-center gap-2">
              <Input
                id="cv"
                type="file"
                accept=".pdf,.doc,.docx"
                onChange={handleFileChange}
                className={`${errors.cv ? 'border-red-500' : ''} file:mr-2 file:py-1 file:px-2 file:rounded file:border-0 file:text-sm file:bg-gray-100 file:text-gray-700 hover:file:bg-gray-200`}
                disabled={isLoading}
              />
              {cvFile && (
                <div className="flex items-center text-sm text-green-600">
                  <FileText className="h-4 w-4 mr-1" />
                  {cvFile.name}
                </div>
              )}
            </div>
            {errors.cv && (
              <p className="text-sm text-red-500">{errors.cv}</p>
            )}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="coverLetter">Cover Letter (Optional)</Label>
            <Textarea
              id="coverLetter"
              name="coverLetter"
              placeholder="Tell us why you're interested in this position..."
              value={formData.coverLetter}
              onChange={handleInputChange}
              rows={4}
              disabled={isLoading}
            />
          </div>
          
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isUploading ? 'Uploading...' : 'Submitting...'}
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Submit Application
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
