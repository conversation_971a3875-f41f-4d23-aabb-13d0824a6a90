import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { FileText, CheckCircle, Building, BarChart2, ArrowRight } from 'lucide-react';
import { useGSAP } from '@gsap/react';
import gsap from 'gsap';

// Step 1: CV Upload Component
const CVUploadStep: React.FC<{ onComplete: () => void }> = ({ onComplete }) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploaded, setIsUploaded] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const progressRef = useRef<HTMLDivElement>(null);
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = () => {
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    setIsUploaded(true);
    
    // Simulate processing
    setIsProcessing(true);
    
    // Animate progress bar
    if (progressRef.current) {
      gsap.fromTo(
        progressRef.current,
        { width: '0%' },
        { 
          width: '100%', 
          duration: 3,
          ease: 'power2.inOut',
          onComplete: () => {
            setIsProcessing(false);
            setTimeout(() => {
              onComplete();
            }, 1000);
          }
        }
      );
    }
  };
  
  return (
    <div className="bg-card-gradient rounded-xl p-6 border border-gray-800">
      <h3 className="text-xl font-bold text-white mb-4">Step 1: CV Upload & Processing</h3>
      
      {!isUploaded ? (
        <div 
          className={`border-2 border-dashed ${isDragging ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'} rounded-lg p-8 text-center transition-colors duration-200`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-300 mb-2">Drag & drop a CV here</p>
          <p className="text-gray-500 text-sm">Our AI will automatically extract key information</p>
          <button 
            onClick={() => handleDrop({} as React.DragEvent)}
            className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Or Click to Upload
          </button>
        </div>
      ) : (
        <div className="rounded-lg p-6 bg-gray-800/50">
          {isProcessing ? (
            <div>
              <div className="flex items-center justify-between mb-4">
                <span className="text-gray-300">Processing CV...</span>
                <span className="text-blue-400">Extracting data</span>
              </div>
              <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                <div ref={progressRef} className="h-full bg-blue-500 rounded-full" style={{ width: '0%' }}></div>
              </div>
            </div>
          ) : (
            <div>
              <div className="flex items-center mb-4">
                <CheckCircle className="h-6 w-6 text-green-500 mr-2" />
                <span className="text-green-400">CV processed successfully!</span>
              </div>
              
              <div className="bg-gray-900 rounded-lg p-4 mb-4">
                <h4 className="text-white font-medium mb-2">Extracted Information:</h4>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <p className="text-gray-500 text-xs">Name</p>
                    <p className="text-gray-300">John Smith</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-xs">Position</p>
                    <p className="text-gray-300">Senior Developer</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-xs">Experience</p>
                    <p className="text-gray-300">8 years</p>
                  </div>
                  <div>
                    <p className="text-gray-500 text-xs">Education</p>
                    <p className="text-gray-300">MSc Computer Science</p>
                  </div>
                </div>
                
                <div className="mt-3">
                  <p className="text-gray-500 text-xs mb-1">Skills</p>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs">React</span>
                    <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">Node.js</span>
                    <span className="px-2 py-1 bg-purple-500/20 text-purple-400 rounded text-xs">TypeScript</span>
                    <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded text-xs">AWS</span>
                    <span className="px-2 py-1 bg-pink-500/20 text-pink-400 rounded text-xs">GraphQL</span>
                  </div>
                </div>
              </div>
              
              <button 
                onClick={onComplete}
                className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center justify-center"
              >
                Continue to Next Step <ArrowRight size={16} className="ml-2" />
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Step 2: Skills Extraction Component
const SkillsExtractionStep: React.FC<{ onComplete: () => void }> = ({ onComplete }) => {
  const [selectedSkill, setSelectedSkill] = useState<string | null>(null);
  
  const skills = [
    { name: 'React', level: 'Expert', confidence: 95, category: 'Frontend' },
    { name: 'TypeScript', level: 'Advanced', confidence: 90, category: 'Languages' },
    { name: 'Node.js', level: 'Intermediate', confidence: 75, category: 'Backend' },
    { name: 'AWS', level: 'Beginner', confidence: 60, category: 'DevOps' },
    { name: 'GraphQL', level: 'Intermediate', confidence: 80, category: 'API' },
  ];
  
  return (
    <div className="bg-card-gradient rounded-xl p-6 border border-gray-800">
      <h3 className="text-xl font-bold text-white mb-4">Step 2: Skills Extraction & Analysis</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-medium mb-3">CV Content</h4>
          <div className="bg-gray-900 rounded p-3 text-gray-300 text-sm h-64 overflow-y-auto">
            <p className="mb-2"><span className="font-bold">John Smith</span></p>
            <p className="mb-4 text-gray-400">Senior Software Developer</p>
            
            <p className="mb-2 text-gray-400">PROFESSIONAL SUMMARY</p>
            <p className="mb-4">Experienced software developer with <span className={`${selectedSkill === 'React' ? 'bg-blue-500/30 text-blue-300' : ''}`}>8 years of experience in React</span> and <span className={`${selectedSkill === 'TypeScript' ? 'bg-blue-500/30 text-blue-300' : ''}`}>TypeScript</span> development. Proficient in building scalable web applications using <span className={`${selectedSkill === 'Node.js' ? 'bg-blue-500/30 text-blue-300' : ''}`}>Node.js</span> and <span className={`${selectedSkill === 'GraphQL' ? 'bg-blue-500/30 text-blue-300' : ''}`}>GraphQL</span>. Experience with cloud deployment on <span className={`${selectedSkill === 'AWS' ? 'bg-blue-500/30 text-blue-300' : ''}`}>AWS</span>.</p>
            
            <p className="mb-2 text-gray-400">EXPERIENCE</p>
            <p className="font-medium">Senior Developer - TechCorp Inc.</p>
            <p className="text-gray-400 mb-1">2018 - Present</p>
            <ul className="list-disc list-inside mb-4 space-y-1">
              <li>Led development of customer-facing web applications using <span className={`${selectedSkill === 'React' ? 'bg-blue-500/30 text-blue-300' : ''}`}>React</span> and <span className={`${selectedSkill === 'TypeScript' ? 'bg-blue-500/30 text-blue-300' : ''}`}>TypeScript</span></li>
              <li>Implemented <span className={`${selectedSkill === 'GraphQL' ? 'bg-blue-500/30 text-blue-300' : ''}`}>GraphQL</span> APIs for improved data fetching</li>
              <li>Deployed and maintained applications on <span className={`${selectedSkill === 'AWS' ? 'bg-blue-500/30 text-blue-300' : ''}`}>AWS</span></li>
            </ul>
            
            <p className="font-medium">Developer - WebSolutions Ltd.</p>
            <p className="text-gray-400 mb-1">2015 - 2018</p>
            <ul className="list-disc list-inside mb-4 space-y-1">
              <li>Developed backend services using <span className={`${selectedSkill === 'Node.js' ? 'bg-blue-500/30 text-blue-300' : ''}`}>Node.js</span></li>
              <li>Created frontend components with <span className={`${selectedSkill === 'React' ? 'bg-blue-500/30 text-blue-300' : ''}`}>React</span></li>
            </ul>
          </div>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-medium mb-3">Extracted Skills</h4>
          <div className="space-y-3">
            {skills.map((skill) => (
              <div 
                key={skill.name}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedSkill === skill.name 
                    ? 'bg-indigo-900/50 border border-indigo-500/50' 
                    : 'bg-gray-900 hover:bg-gray-800'
                }`}
                onClick={() => setSelectedSkill(skill.name)}
              >
                <div className="flex justify-between items-center mb-2">
                  <span className="font-medium text-white">{skill.name}</span>
                  <span className="text-xs px-2 py-1 rounded bg-indigo-500/20 text-indigo-300">
                    {skill.confidence}% confidence
                  </span>
                </div>
                <div className="flex justify-between text-sm text-gray-400">
                  <span>{skill.category}</span>
                  <span>{skill.level}</span>
                </div>
                <div className="mt-2 w-full h-1 bg-gray-700 rounded-full overflow-hidden">
                  <div 
                    className="h-full bg-indigo-500 rounded-full" 
                    style={{ width: `${skill.confidence}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
          
          <button 
            onClick={onComplete}
            className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center justify-center mt-4"
          >
            Continue to Next Step <ArrowRight size={16} className="ml-2" />
          </button>
        </div>
      </div>
    </div>
  );
};

// Step 3: Company Matching Component
const CompanyMatchingStep: React.FC<{ onComplete: () => void }> = ({ onComplete }) => {
  const [selectedCompany, setSelectedCompany] = useState<string | null>(null);
  const matchScoreRef = useRef<HTMLDivElement>(null);
  
  const companies = [
    { name: 'TechCorp Inc.', position: 'Frontend Lead', matchScore: 85 },
    { name: 'InnovateSoft', position: 'Senior Developer', matchScore: 92 },
    { name: 'WebSolutions Ltd.', position: 'Full Stack Developer', matchScore: 78 },
  ];
  
  useGSAP(() => {
    if (selectedCompany && matchScoreRef.current) {
      const company = companies.find(c => c.name === selectedCompany);
      if (company) {
        gsap.fromTo(
          matchScoreRef.current,
          { width: '0%' },
          { 
            width: `${company.matchScore}%`, 
            duration: 1.5,
            ease: 'power2.out',
          }
        );
      }
    }
  }, [selectedCompany]);
  
  return (
    <div className="bg-card-gradient rounded-xl p-6 border border-gray-800">
      <h3 className="text-xl font-bold text-white mb-4">Step 3: Company Matching</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-medium mb-3">Available Companies</h4>
          <div className="space-y-3">
            {companies.map((company) => (
              <div 
                key={company.name}
                className={`p-3 rounded-lg cursor-pointer transition-colors ${
                  selectedCompany === company.name 
                    ? 'bg-indigo-900/50 border border-indigo-500/50' 
                    : 'bg-gray-900 hover:bg-gray-800'
                }`}
                onClick={() => setSelectedCompany(company.name)}
              >
                <div className="flex items-center mb-2">
                  <div className="w-10 h-10 rounded-full bg-indigo-500/20 flex items-center justify-center mr-3">
                    <Building className="h-5 w-5 text-indigo-400" />
                  </div>
                  <div>
                    <div className="font-medium text-white">{company.name}</div>
                    <div className="text-sm text-gray-400">{company.position}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        <div className="bg-gray-800/50 rounded-lg p-4">
          <h4 className="text-white font-medium mb-3">Match Analysis</h4>
          
          {selectedCompany ? (
            <>
              <div className="bg-gray-900 rounded-lg p-4 mb-4">
                <div className="flex justify-between items-center mb-3">
                  <span className="text-white">Match Score</span>
                  <span className="text-2xl font-bold text-indigo-400">
                    {companies.find(c => c.name === selectedCompany)?.matchScore}%
                  </span>
                </div>
                
                <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden mb-4">
                  <div 
                    ref={matchScoreRef}
                    className="h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full" 
                    style={{ width: '0%' }}
                  ></div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Skills Match</span>
                    <span className="text-green-400">90%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Experience Match</span>
                    <span className="text-green-400">85%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Education Match</span>
                    <span className="text-yellow-400">75%</span>
                  </div>
                </div>
              </div>
              
              <button 
                onClick={onComplete}
                className="w-full py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors flex items-center justify-center"
              >
                Continue to Results <ArrowRight size={16} className="ml-2" />
              </button>
            </>
          ) : (
            <div className="bg-gray-900 rounded-lg p-6 text-center">
              <Building className="h-12 w-12 text-gray-600 mx-auto mb-3" />
              <p className="text-gray-400">Select a company to see match analysis</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

// Step 4: Results & Insights Component
const ResultsInsightsStep: React.FC<{ onReset: () => void }> = ({ onReset }) => {
  return (
    <div className="bg-card-gradient rounded-xl p-6 border border-gray-800">
      <h3 className="text-xl font-bold text-white mb-4">Step 4: Results & Insights</h3>
      
      <div className="bg-gray-800/50 rounded-lg p-6 mb-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h4 className="text-white font-medium">John Smith</h4>
            <p className="text-gray-400 text-sm">Senior Developer</p>
          </div>
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-full bg-indigo-500/20 flex items-center justify-center mr-2">
              <Building className="h-5 w-5 text-indigo-400" />
            </div>
            <div className="text-right">
              <div className="text-white">InnovateSoft</div>
              <div className="text-gray-400 text-sm">Senior Developer</div>
            </div>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-gray-900 rounded-lg p-4">
            <div className="text-center mb-3">
              <div className="text-3xl font-bold text-indigo-400">92%</div>
              <div className="text-gray-400 text-sm">Overall Match</div>
            </div>
            <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
              <div className="h-full bg-indigo-500 rounded-full" style={{ width: '92%' }}></div>
            </div>
          </div>
          
          <div className="bg-gray-900 rounded-lg p-4">
            <div className="text-center mb-3">
              <div className="text-3xl font-bold text-green-400">90%</div>
              <div className="text-gray-400 text-sm">Skills Match</div>
            </div>
            <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
              <div className="h-full bg-green-500 rounded-full" style={{ width: '90%' }}></div>
            </div>
          </div>
          
          <div className="bg-gray-900 rounded-lg p-4">
            <div className="text-center mb-3">
              <div className="text-3xl font-bold text-blue-400">85%</div>
              <div className="text-gray-400 text-sm">Experience Match</div>
            </div>
            <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
              <div className="h-full bg-blue-500 rounded-full" style={{ width: '85%' }}></div>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-900 rounded-lg p-4 mb-6">
          <h5 className="text-white font-medium mb-3">AI Insights</h5>
          <p className="text-gray-300 text-sm mb-3">
            This candidate is an excellent match for the Senior Developer position at InnovateSoft. Their strong React and TypeScript skills align perfectly with the company's tech stack requirements.
          </p>
          <p className="text-gray-300 text-sm">
            <span className="text-green-400 font-medium">Strengths:</span> Frontend development, React ecosystem, TypeScript
          </p>
          <p className="text-gray-300 text-sm">
            <span className="text-yellow-400 font-medium">Areas for growth:</span> Cloud deployment experience could be stronger
          </p>
        </div>
        
        <div className="flex gap-4">
          <button className="flex-1 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors">
            Download Full Report
          </button>
          <button className="flex-1 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors">
            Contact Candidate
          </button>
        </div>
      </div>
      
      <button 
        onClick={onReset}
        className="w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
      >
        Start New Evaluation
      </button>
    </div>
  );
};

// Progress Indicator Component
const ProgressIndicator: React.FC<{ currentStep: number, totalSteps: number }> = ({ currentStep, totalSteps }) => {
  return (
    <div className="flex items-center justify-between mb-8 px-4">
      {Array.from({ length: totalSteps }).map((_, index) => (
        <React.Fragment key={index}>
          {index > 0 && (
            <div className="flex-1 h-1 bg-gray-700 mx-2">
              <div 
                className="h-full bg-blue-500" 
                style={{ width: currentStep > index ? '100%' : '0%' }}
              ></div>
            </div>
          )}
          <div 
            className={`w-8 h-8 rounded-full flex items-center justify-center ${
              currentStep > index 
                ? 'bg-blue-500 text-white' 
                : currentStep === index 
                  ? 'bg-blue-900 text-white border-2 border-blue-500' 
                  : 'bg-gray-800 text-gray-400'
            }`}
          >
            {index + 1}
          </div>
        </React.Fragment>
      ))}
    </div>
  );
};

// Main Component
export const InteractiveWorkflowJourney: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const totalSteps = 4;
  
  const handleNextStep = () => {
    setCurrentStep(prev => Math.min(prev + 1, totalSteps - 1));
  };
  
  const handleReset = () => {
    setCurrentStep(0);
  };
  
  return (
    <section className="py-16 bg-recruiter-darknavy">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Experience the Recruitment Journey</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            See how our platform transforms the recruitment process with AI-powered automation
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <ProgressIndicator currentStep={currentStep} totalSteps={totalSteps} />
          
          {currentStep === 0 && <CVUploadStep onComplete={handleNextStep} />}
          {currentStep === 1 && <SkillsExtractionStep onComplete={handleNextStep} />}
          {currentStep === 2 && <CompanyMatchingStep onComplete={handleNextStep} />}
          {currentStep === 3 && <ResultsInsightsStep onReset={handleReset} />}
        </div>
      </div>
    </section>
  );
};
