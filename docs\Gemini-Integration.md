# Google Gemini Integration

## Overview

The Sourcio.ai application now supports Google Gemini as an AI provider, offering advanced multimodal AI capabilities with strong reasoning and native structured output support.

## Features

### ✅ **What's Included**

- **Full Gemini API Integration**: Native support for Google's Gemini models
- **Structured Output**: Native JSON schema support for consistent data extraction
- **Multiple Models**: Support for Gemini 2.5 Pro, 2.0 Flash, 1.5 Pro, 1.5 Flash, and 1.0 Pro
- **Admin Configuration**: Easy switching between Gemini and other providers
- **Centralized Operations**: Uses the same centralized AI system as other providers
- **Edge Function Support**: Works in both client-side and server-side operations

### 🎯 **Key Benefits**

- **Native JSON Support**: Gemini has built-in structured output capabilities
- **High Quality**: Excellent at understanding complex instructions and generating accurate results
- **Cost Effective**: Competitive pricing for high-quality AI operations
- **Fast Performance**: Optimized for speed and efficiency
- **Reliable**: Consistent output format and error handling

## Setup Instructions

### 1. Get Your Gemini API Key

1. Visit [Google AI Studio](https://ai.google.dev)
2. Sign in with your Google account
3. Create a new API key
4. Copy the API key (starts with `AIzaSy...`)

### 2. Configure in Admin Settings

1. Navigate to `/dashboard/admin/ai-model-settings`
2. Select the **Gemini** tab
3. Choose your preferred model:
   - **Gemini 2.5 Pro**: Best performance and capabilities (recommended)
   - **Gemini 2.0 Flash Experimental**: Latest experimental features
   - **Gemini 1.5 Pro**: Balanced performance and cost
   - **Gemini 1.5 Flash**: Fastest response times
   - **Gemini 1.0 Pro**: Basic capabilities
4. Enter your API key
5. Click **Save Settings**
6. Test the configuration

### 3. Verify Integration

The system will automatically use Gemini for all AI operations:
- CV parsing and data extraction
- Job-candidate matching and scoring
- Skills extraction from text
- Candidate ranking and evaluation

## Technical Implementation

### Client-Side Integration

The centralized AI operations automatically detect when Gemini is configured and use the appropriate client:

```typescript
// Automatic provider detection
const result = await parseCV(cvText);
const match = await matchCandidateToJob(candidate, jobDescription);
const skills = await extractSkills(text);
const rankings = await rankCandidatesForJob(candidates, jobDescription);
```

### Server-Side Integration

Edge functions also support Gemini through the shared AI operations:

```typescript
import { rankCandidatesForJob } from '../_shared/ai-operations.ts';

// Works with Gemini automatically
const result = await rankCandidatesForJob(
  apiKey, 
  model, 
  candidates, 
  jobDescription, 
  baseURL, 
  'gemini'
);
```

### Schema Conversion

The system automatically converts JSON schemas to Gemini's native format:

```typescript
// Your schema is automatically converted for Gemini
const geminiSchema = convertToGeminiSchema(AI_SCHEMAS.CV_PARSING);
```

## Model Recommendations

### For Production Use

- **Gemini 2.5 Pro**: Best overall performance for complex CV parsing and job matching
- **Gemini 1.5 Flash**: Good balance of speed and quality for high-volume operations

### For Development/Testing

- **Gemini 1.5 Pro**: Reliable performance for development and testing
- **Gemini 1.0 Pro**: Basic functionality for initial testing

## API Limits and Pricing

### Rate Limits
- **Free Tier**: 15 requests per minute
- **Paid Tier**: Higher limits based on your plan

### Pricing
- **Free Tier**: Limited requests per day
- **Pay-as-you-go**: Competitive pricing per token
- Check [Google AI Pricing](https://ai.google.dev/pricing) for current rates

## Troubleshooting

### Common Issues

1. **API Key Invalid**
   - Ensure your API key is correct and active
   - Check that the key has the necessary permissions

2. **Rate Limit Exceeded**
   - Reduce the frequency of requests
   - Consider upgrading to a paid plan

3. **Model Not Available**
   - Some models may have regional restrictions
   - Try a different model if one is not available

4. **JSON Parsing Errors**
   - The system includes automatic error handling
   - Check the logs for specific error details

### Error Handling

The system includes comprehensive error handling:

```typescript
try {
  const result = await parseCV(cvText);
} catch (error) {
  // Automatic fallback and user-friendly error messages
  console.error('CV parsing failed:', error.message);
}
```

## Migration from Other Providers

### Switching to Gemini

1. Go to AI Model Settings
2. Select Gemini tab
3. Configure your API key and model
4. Save settings
5. All operations will automatically use Gemini

### Switching Back

The same process works in reverse - just select a different provider and save.

## Performance Comparison

| Provider | Speed | Quality | Cost | JSON Support |
|----------|-------|---------|------|--------------|
| Gemini 2.5 Pro | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Gemini 1.5 Flash | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| GROQ Llama | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| OpenAI GPT-4o | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| Claude 3.5 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐ | ⭐⭐⭐⭐ |

## Support

For issues specific to Gemini integration:

1. Check the [Google AI Documentation](https://ai.google.dev/docs)
2. Review the error logs in the browser console
3. Test with a different Gemini model
4. Verify your API key permissions

The Gemini integration is fully supported and maintained as part of the centralized AI system.
