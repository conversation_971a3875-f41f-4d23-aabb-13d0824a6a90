import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyContext } from '@/contexts/CompanyContext';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { DashboardOverview } from '@/components/dashboard/DashboardOverview';
import { GradientStatCard } from '@/components/dashboard/GradientStatCard';
import { AnimatedBarChart } from '@/components/dashboard/AnimatedBarChart';
import UpcomingInterviewsWidget from '@/components/dashboard/UpcomingInterviewsWidget';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useJobStatistics } from '@/hooks/use-jobs';
import {
  useDashboardStats,
  useRecentActivities,
  useRecentEvaluations,
  useTopSkills,
  useMatchQualityDistribution,
  useEvaluationFunnelData
} from '@/hooks/use-dashboard';
import { useSetupStatus } from '@/hooks/use-setup-status';
import { SetupWizard } from '@/components/onboarding/SetupWizard';
import { formatTimeAgo } from '@/services/supabase/dashboard';
import {
  BarChart3,
  Briefcase,
  FileText,
  Users,
  TrendingUp,
  Clock,
  Calendar,
  CheckCircle2,
  Building,
  Award,
  AlertCircle
} from 'lucide-react';

const EnhancedDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { activeCompany, activeCompanyId } = useCompanyContext();
  const { isLoading: setupLoading, needsSetup } = useSetupStatus();
  const { data: jobStats, isLoading: jobStatsLoading } = useJobStatistics(activeCompanyId);
  const { data: dashboardStats, isLoading: dashboardStatsLoading } = useDashboardStats();
  const { data: recentActivities, isLoading: activitiesLoading } = useRecentActivities(4);
  const { data: recentEvaluations, isLoading: evaluationsLoading } = useRecentEvaluations(4);
  const { data: topSkills, isLoading: skillsLoading } = useTopSkills(5);
  const { data: matchQualityData, isLoading: matchQualityLoading } = useMatchQualityDistribution();
  const { data: evaluationFunnelData, isLoading: funnelLoading } = useEvaluationFunnelData();
  const [showSetupWizard, setShowSetupWizard] = useState(false);

  // Check if user needs setup and show wizard
  useEffect(() => {
    if (!setupLoading && needsSetup) {
      setShowSetupWizard(true);
    }
  }, [setupLoading, needsSetup]);

  // Loading state
  const isLoading = jobStatsLoading || dashboardStatsLoading || activitiesLoading || evaluationsLoading || skillsLoading || matchQualityLoading || funnelLoading;

  // Format numbers for display
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };

  // Fallback data for skills if no real data is available
  const fallbackSkillsData = [
    { label: 'No Skills', value: 0, color: 'bg-gray-300' }
  ];

  // Use real skills data if available, otherwise use fallback
  const skillsData = topSkills && topSkills.length > 0 ? topSkills : fallbackSkillsData;

  return (
    <div className="space-y-8">
      {/* Setup Wizard */}
      {showSetupWizard && (
        <SetupWizard
          isOpen={showSetupWizard}
          onClose={() => setShowSetupWizard(false)}
        />
      )}

      {/* Welcome section with gradient background */}
      <div className="bg-primary-gradient rounded-xl p-6 shadow-lg animate-fade-in">
        <h1 className="text-2xl font-bold text-white">Welcome back, {user?.name}!</h1>
        <p className="text-white/80 mt-2">
          {activeCompany
            ? `Here's what's happening with ${activeCompany.name} recruitment activities today.`
            : `Here's what's happening with your recruitment activities today.`
          }
        </p>
      </div>

      {/* Setup alert for users without companies */}
      {needsSetup && !showSetupWizard && (
        <Alert className="bg-blue-50 border-blue-200 text-blue-800 animate-fade-in">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Welcome to Sourcio.ai! Let's set up your account by creating your first company and job.</span>
            <Button
              variant="outline"
              size="sm"
              className="ml-4 border-blue-300 text-blue-800 hover:bg-blue-100"
              onClick={() => setShowSetupWizard(true)}
            >
              <Building className="mr-2 h-4 w-4" /> Start Setup
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Company selection alert */}
      {!activeCompanyId && !needsSetup && (
        <Alert className="bg-amber-50 border-amber-200 text-amber-800 animate-fade-in">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>You haven't selected an active company. Statistics are showing for all companies.</span>
            <Button
              variant="outline"
              size="sm"
              className="ml-4 border-amber-300 text-amber-800 hover:bg-amber-100"
              onClick={() => navigate('/dashboard/companies')}
            >
              <Building className="mr-2 h-4 w-4" /> Select Company
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Colorful stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="animate-fade-in animate-delay-100">
          <GradientStatCard
            title={activeCompany ? `${activeCompany.name} CVs` : "Total CVs"}
            value={isLoading ? "Loading..." : formatNumber(dashboardStats?.totalCVs || 0)}
            icon={FileText}
            description={`${formatNumber(dashboardStats?.newCVsThisMonth || 0)} new this month${activeCompany ? ` for ${activeCompany.name}` : ''}`}
            trend={{ value: dashboardStats?.newCVsThisMonth || 0, isPositive: true }}
            gradientFrom="#1E7BEA"
            gradientTo="#00B2FF"
          />
        </div>
        <div className="animate-fade-in animate-delay-200">
          <GradientStatCard
            title={activeCompany ? `${activeCompany.name} Jobs` : "Active Jobs"}
            value={isLoading ? "Loading..." : (jobStats?.active || 0).toString()}
            icon={Briefcase}
            description={`${jobStats?.draft || 0} in draft, ${jobStats?.closed || 0} closed`}
            trend={{ value: 8, isPositive: true }}
            gradientFrom="#4CAF50"
            gradientTo="#8BC34A"
          />
        </div>
        <div className="animate-fade-in animate-delay-300">
          <GradientStatCard
            title="Companies"
            value={isLoading ? "Loading..." : formatNumber(dashboardStats?.totalCompanies || 0)}
            icon={Building}
            description={`${formatNumber(dashboardStats?.newCompaniesThisMonth || 0)} added this month`}
            trend={{ value: dashboardStats?.newCompaniesThisMonth || 0, isPositive: true }}
            gradientFrom="#00BCD4"
            gradientTo="#03A9F4"
          />
        </div>
        <div className="animate-fade-in animate-delay-400">
          <GradientStatCard
            title="Avg. Match Score"
            value={isLoading ? "Loading..." : `${dashboardStats?.avgMatchScore || 0}%`}
            icon={BarChart3}
            description={`${Math.abs(dashboardStats?.matchScoreChange || 0)}% ${dashboardStats?.isMatchScorePositive ? 'higher' : 'lower'} than last month`}
            trend={{ value: dashboardStats?.matchScoreChange || 0, isPositive: dashboardStats?.isMatchScorePositive || false }}
            gradientFrom="#FF9800"
            gradientTo="#FFEB3B"
          />
        </div>
      </div>

      {/* Charts and activity section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Top skills in candidate pool */}
        <Card className="lg:col-span-1 animate-fade-in animate-delay-100">
          <CardHeader>
            <CardTitle className="text-lg font-medium">Top Skills in Candidate Pool</CardTitle>
            <CardDescription>
              {activeCompany
                ? `Most common skills in ${activeCompany.name} CVs`
                : 'Most common skills in uploaded CVs'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AnimatedBarChart data={skillsData} />
          </CardContent>
        </Card>

        {/* Evaluation funnel chart */}
        <Card className="lg:col-span-1 animate-fade-in animate-delay-200">
          <CardHeader>
            <CardTitle className="text-lg font-medium">Candidate Evaluation Funnel</CardTitle>
            <CardDescription>
              {activeCompany
                ? `${activeCompany.name} evaluation pipeline`
                : 'Current evaluation pipeline'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AnimatedBarChart data={evaluationFunnelData?.stages || [
              { label: 'CVs Uploaded', value: 0, color: 'bg-blue-500' },
              { label: 'Evaluated', value: 0, color: 'bg-purple-500' },
              { label: 'Good Match (70%+)', value: 0, color: 'bg-green-500' },
              { label: 'Hired', value: 0, color: 'bg-recruiter-blue' },
            ]} />
          </CardContent>
        </Card>

        {/* Match quality distribution */}
        <Card className="lg:col-span-1 animate-fade-in animate-delay-300">
          <CardHeader>
            <CardTitle className="text-lg font-medium">Match Quality Distribution</CardTitle>
            <CardDescription>
              {activeCompany
                ? `${activeCompany.name} CV-to-job match score ranges`
                : 'CV-to-job match score ranges'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AnimatedBarChart data={matchQualityData || []} />
          </CardContent>
        </Card>
      </div>

      {/* Recent evaluations and company listings */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent evaluations */}
        <Card className="lg:col-span-2 animate-fade-in animate-delay-100">
          <CardHeader>
            <CardTitle className="text-lg font-medium">Recent Evaluations</CardTitle>
            <CardDescription>
              {activeCompany
                ? `Your latest CV evaluations for ${activeCompany.name}`
                : 'Your latest CV evaluations across all companies'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {isLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
                  <span className="ml-2 text-sm text-gray-500">Loading evaluations...</span>
                </div>
              ) : recentEvaluations && recentEvaluations.length > 0 ? (
                recentEvaluations.map((evaluation, index) => (
                  <div key={evaluation.id} className="p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all duration-200 animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center">
                          <h3 className="text-gray-800 font-medium">{evaluation.candidateName}</h3>
                          <span className="ml-2 text-xs text-gray-500">• {evaluation.company}</span>
                        </div>
                        <div className="flex items-center mt-2 text-sm text-gray-600">
                          <Briefcase className="h-4 w-4 mr-1 text-gray-400" />
                          <span>{evaluation.position}</span>
                          <span className="ml-4 text-xs text-gray-500">{formatTimeAgo(new Date(evaluation.date))}</span>
                        </div>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs ${
                        evaluation.matchScore >= 90 ? 'bg-green-100 text-green-600' :
                        evaluation.matchScore >= 70 ? 'bg-blue-100 text-blue-600' :
                        evaluation.matchScore >= 50 ? 'bg-amber-100 text-amber-600' :
                        'bg-red-100 text-red-600'
                      }`}>
                        {evaluation.matchScore}% Match
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500 text-sm">
                    {activeCompany
                      ? `No evaluations found for ${activeCompany.name}. Try uploading and evaluating CVs for this company.`
                      : 'No recent evaluations found. Try uploading and evaluating some CVs.'}
                  </p>
                  <div className="flex justify-center gap-4 mt-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate('/dashboard/cvs')}
                    >
                      <FileText className="mr-2 h-4 w-4" />
                      Upload CVs
                    </Button>
                    {activeCompany && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => navigate('/dashboard/companies')}
                      >
                        <Building className="mr-2 h-4 w-4" />
                        View All Companies
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="space-y-6 animate-fade-in animate-delay-200">
          {/* Upcoming Interviews Widget */}
          <UpcomingInterviewsWidget />

          {/* Recent activity */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg font-medium">Recent Activity</CardTitle>
              <CardDescription>
                {activeCompany
                  ? `Latest actions for ${activeCompany.name}`
                  : 'Latest actions in your account'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin h-6 w-6 border-2 border-primary border-t-transparent rounded-full"></div>
                    <span className="ml-2 text-sm text-gray-500">Loading activities...</span>
                  </div>
                ) : recentActivities && recentActivities.length > 0 ? (
                  recentActivities.map((activity, index) => {
                    // Determine icon based on activity type
                    let Icon;
                    switch (activity.icon) {
                      case 'FileText':
                        Icon = FileText;
                        break;
                      case 'Building':
                        Icon = Building;
                        break;
                      case 'Briefcase':
                        Icon = Briefcase;
                        break;
                      case 'BarChart3':
                        Icon = BarChart3;
                        break;
                      default:
                        Icon = FileText;
                    }

                    return (
                      <div key={activity.id} className="flex items-start space-x-3 animate-fade-in" style={{ animationDelay: `${index * 100}ms` }}>
                        <div className="bg-gray-100 p-2 rounded-full">
                          <Icon className="h-4 w-4 text-gray-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-gray-800 text-sm">{activity.text}</p>
                          <p className="text-gray-500 text-xs">{formatTimeAgo(new Date(activity.time))}</p>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center py-8">
                    <p className="text-gray-500 text-sm">No recent activities found</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

// Wrap the Dashboard component with the DashboardLayout
const EnhancedDashboardWithLayout = () => (
  <DashboardLayout>
    <EnhancedDashboard />
  </DashboardLayout>
);

export default EnhancedDashboardWithLayout;
