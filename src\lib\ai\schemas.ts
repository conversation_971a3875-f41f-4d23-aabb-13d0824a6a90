/**
 * Centralized AI Schemas
 * 
 * This file contains all JSON schemas used for AI operations.
 * These schemas ensure consistent input/output formats regardless of which AI model is selected.
 */

// ============================================================================
// CV PARSING SCHEMAS
// ============================================================================

export const CV_PARSING_SCHEMA = {
  type: "object",
  required: ["personalDetails", "workExperience", "education", "skills"],
  properties: {
    personalDetails: {
      type: "object",
      required: ["name", "email"],
      properties: {
        name: { type: "string" },
        email: { type: "string" },
        phone: { type: "string" },
        location: { type: "string" },
        linkedIn: { type: "string" },
        website: { type: "string" }
      }
    },
    professionalSummary: { type: "string" },
    workExperience: {
      type: "array",
      items: {
        type: "object",
        required: ["company", "title", "startDate", "responsibilities"],
        properties: {
          company: { type: "string" },
          title: { type: "string" },
          startDate: { type: "string" },
          endDate: { type: "string" },
          current: { type: "boolean" },
          responsibilities: {
            type: "array",
            items: { type: "string" }
          },
          achievements: {
            type: "array",
            items: { type: "string" }
          }
        }
      }
    },
    education: {
      type: "array",
      items: {
        type: "object",
        required: ["institution", "degree", "startDate"],
        properties: {
          institution: { type: "string" },
          degree: { type: "string" },
          field: { type: "string" },
          startDate: { type: "string" },
          endDate: { type: "string" },
          current: { type: "boolean" },
          gpa: { type: "string" },
          achievements: {
            type: "array",
            items: { type: "string" }
          }
        }
      }
    },
    skills: {
      type: "object",
      required: ["technical"],
      properties: {
        technical: {
          type: "array",
          items: { type: "string" }
        },
        soft: {
          type: "array",
          items: { type: "string" }
        },
        languages: {
          type: "array",
          items: { type: "string" }
        }
      }
    },
    languages: {
      type: "array",
      items: {
        type: "object",
        required: ["name", "proficiency"],
        properties: {
          name: { type: "string" },
          proficiency: { type: "string" }
        }
      }
    },
    certifications: {
      type: "array",
      items: {
        type: "object",
        required: ["name", "issuer"],
        properties: {
          name: { type: "string" },
          issuer: { type: "string" },
          date: { type: "string" },
          expiryDate: { type: "string" }
        }
      }
    },
    projects: {
      type: "array",
      items: {
        type: "object",
        required: ["name", "description"],
        properties: {
          name: { type: "string" },
          description: { type: "string" },
          technologies: {
            type: "array",
            items: { type: "string" }
          },
          url: { type: "string" }
        }
      }
    }
  }
} as const;

// ============================================================================
// JOB MATCHING SCHEMAS
// ============================================================================

export const JOB_MATCHING_SCHEMA = {
  type: "object",
  required: ["overallScore", "skillsMatch", "experienceMatch", "educationMatch", "locationMatch", "strengths", "gaps", "recommendation"],
  properties: {
    overallScore: {
      type: "number",
      minimum: 0,
      maximum: 100
    },
    skillsMatch: {
      type: "object",
      required: ["score", "analysis"],
      properties: {
        score: { type: "number", minimum: 0, maximum: 100 },
        analysis: { type: "string" },
        skills: {
          type: "array",
          items: {
            type: "object",
            required: ["name", "match", "required"],
            properties: {
              name: { type: "string" },
              match: { type: "number", minimum: 0, maximum: 100 },
              required: { type: "boolean" }
            }
          }
        }
      }
    },
    experienceMatch: {
      type: "object",
      required: ["score", "analysis"],
      properties: {
        score: { type: "number", minimum: 0, maximum: 100 },
        analysis: { type: "string" },
        details: {
          type: "array",
          items: {
            type: "object",
            required: ["title", "company", "duration", "relevance"],
            properties: {
              title: { type: "string" },
              company: { type: "string" },
              duration: { type: "string" },
              relevance: { type: "number", minimum: 0, maximum: 100 }
            }
          }
        }
      }
    },
    educationMatch: {
      type: "object",
      required: ["score", "analysis"],
      properties: {
        score: { type: "number", minimum: 0, maximum: 100 },
        analysis: { type: "string" },
        details: {
          type: "array",
          items: {
            type: "object",
            required: ["degree", "institution", "year", "relevance"],
            properties: {
              degree: { type: "string" },
              institution: { type: "string" },
              year: { type: "string" },
              relevance: { type: "number", minimum: 0, maximum: 100 }
            }
          }
        }
      }
    },
    locationMatch: {
      type: "object",
      required: ["score", "analysis"],
      properties: {
        score: { type: "number", minimum: 0, maximum: 100 },
        analysis: { type: "string" }
      }
    },
    strengths: {
      type: "array",
      items: { type: "string" },
      minItems: 3
    },
    gaps: {
      type: "array",
      items: { type: "string" },
      minItems: 3
    },
    recommendation: { type: "string" }
  }
} as const;

// ============================================================================
// SKILL EXTRACTION SCHEMAS
// ============================================================================

export const SKILL_EXTRACTION_SCHEMA = {
  type: "object",
  required: ["technical", "domain", "soft"],
  properties: {
    technical: {
      type: "array",
      items: { type: "string" }
    },
    domain: {
      type: "array",
      items: { type: "string" }
    },
    soft: {
      type: "array",
      items: { type: "string" }
    }
  }
} as const;

// ============================================================================
// CANDIDATE RANKING SCHEMAS
// ============================================================================

export const CANDIDATE_RANKING_SCHEMA = {
  type: "object",
  required: ["rankings"],
  properties: {
    rankings: {
      type: "array",
      items: {
        type: "object",
        required: ["candidateId", "name", "overallScore", "skillsScore", "experienceScore", "educationScore", "culturalFitScore", "strengths", "areasForImprovement", "recommendation"],
        properties: {
          candidateId: { type: "string" },
          name: { type: "string" },
          overallScore: { type: "number", minimum: 0, maximum: 100 },
          skillsScore: { type: "number", minimum: 0, maximum: 100 },
          experienceScore: { type: "number", minimum: 0, maximum: 100 },
          educationScore: { type: "number", minimum: 0, maximum: 100 },
          culturalFitScore: { type: "number", minimum: 0, maximum: 100 },
          strengths: {
            type: "array",
            items: { type: "string" },
            minItems: 2
          },
          areasForImprovement: {
            type: "array",
            items: { type: "string" },
            minItems: 2
          },
          recommendation: {
            type: "string",
            enum: ["Hire", "Consider", "Reject"]
          }
        }
      }
    }
  }
} as const;

// ============================================================================
// SCHEMA UTILITIES
// ============================================================================

/**
 * Convert schema to JSON string for use in prompts
 */
export function schemaToString(schema: any): string {
  return JSON.stringify(schema, null, 2);
}

/**
 * Get all available schemas
 */
export const AI_SCHEMAS = {
  CV_PARSING: CV_PARSING_SCHEMA,
  JOB_MATCHING: JOB_MATCHING_SCHEMA,
  SKILL_EXTRACTION: SKILL_EXTRACTION_SCHEMA,
  CANDIDATE_RANKING: CANDIDATE_RANKING_SCHEMA
} as const;

export type AISchemaType = keyof typeof AI_SCHEMAS;
