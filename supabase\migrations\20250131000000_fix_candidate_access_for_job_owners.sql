-- Fix candidate access for job owners
-- This migration ensures that recruiters can see candidates who applied to their jobs

-- Drop the existing policy
DROP POLICY IF EXISTS "Users can view their own candidates" ON public.candidates;

-- Create a new policy that includes job ownership
CREATE POLICY "Users can view candidates for their jobs"
  ON public.candidates FOR SELECT
  USING (
    -- Users can see candidates they created
    auth.uid() = user_id 
    OR
    -- Users can see candidates who applied to jobs they own
    EXISTS (
      SELECT 1 FROM public.jobs
      WHERE candidates.job_id = jobs.id
      AND jobs.user_id = auth.uid()
    )
    OR
    -- Users can see candidates for jobs from companies they are team members of
    EXISTS (
      SELECT 1 FROM public.jobs
      JOIN public.team_members ON jobs.company_id = team_members.company_id
      WHERE candidates.job_id = jobs.id
      AND team_members.user_id = auth.uid()
      AND team_members.status = 'active'
    )
  );

-- Also update the applications policy to ensure consistency
DROP POLICY IF EXISTS "Users can view their own applications" ON public.applications;

CREATE POLICY "Users can view applications for their jobs"
  ON public.applications FOR SELECT
  USING (
    -- Users can see applications they created (for candidates)
    auth.uid() = user_id 
    OR
    -- Users can see applications for jobs they own
    EXISTS (
      SELECT 1 FROM public.jobs
      WHERE applications.job_id = jobs.id
      AND jobs.user_id = auth.uid()
    )
    OR
    -- Users can see applications for jobs from companies they are team members of
    EXISTS (
      SELECT 1 FROM public.jobs
      JOIN public.team_members ON jobs.company_id = team_members.company_id
      WHERE applications.job_id = jobs.id
      AND team_members.user_id = auth.uid()
      AND team_members.status = 'active'
    )
  );
