import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { format } from 'date-fns';
import { ReportData, ReportType } from '../types';
import { addCoverPage } from '../pdfHelpers/coverPage';
import { addTableOfContents } from '../pdfHelpers/tableOfContents';
import { addDataVisualizations } from '../pdfHelpers/dataVisualizations';
import { addDetailedMetrics } from '../pdfHelpers/detailedMetrics';
import { addRecommendations } from '../pdfHelpers/recommendations';
import { generateExecutiveSummary } from '../pdfHelpers/executiveSummary';

/**
 * Generate a PDF report
 */
export const generatePdfReport = (reportData: ReportData, reportType: ReportType): jsPDF => {
  // Create a new PDF document
  const doc = new jsPDF();

  // Add autoTable to the document
  autoTable(doc, {});

  // Set document properties
  doc.setProperties({
    title: reportData.title,
    subject: 'Report',
    author: 'Sourcio.ai',
    keywords: 'report, recruitment, analytics',
    creator: 'Sourcio.ai Platform',
  });

  // PHASE 1: Enhanced PDF Structure
  // ==============================

  // Add cover page as the first page
  addCoverPage(doc, reportData, reportType);

  // Track sections for table of contents
  const sections = [];
  let currentPage = 2; // Cover page is page 1

  // Add main content page
  doc.addPage();
  doc.setPage(currentPage);

  // Special handling for comprehensive report type
  if (reportType === 'comprehensive' && reportData.sections) {
    // Add a special introduction for comprehensive reports
    doc.setFontSize(20);
    doc.setTextColor(41, 128, 185);
    doc.text(reportData.title, 14, 22);

    if (reportData.companyName) {
      doc.setFontSize(12);
      doc.setTextColor(100);
      doc.text(`Company: ${reportData.companyName}`, 14, 32);
    }

    doc.setFontSize(10);
    doc.setTextColor(100);
    doc.text(`Generated on: ${format(reportData.date, 'PPP')}`, 14, reportData.companyName ? 38 : 32);

    if (reportData.description) {
      doc.setFontSize(10);
      doc.setTextColor(100);
      doc.text(reportData.description, 14, reportData.companyName ? 44 : 38);
    }

    // Add executive summary
    let startY = reportData.companyName ? (reportData.description ? 54 : 44) : (reportData.description ? 48 : 38);

    doc.setFontSize(12);
    doc.setTextColor(41, 128, 185);
    doc.text('Executive Summary', 14, startY);
    startY += 6;

    doc.setFontSize(10);
    doc.setTextColor(100);
    const summaryText = reportData.executiveSummary ||
      'This comprehensive report provides a complete overview of all recruitment analytics, including recruitment funnel, time to hire, source effectiveness, and evaluation analytics.';
    const summaryLines = doc.splitTextToSize(summaryText, doc.internal.pageSize.width - 28);
    doc.text(summaryLines, 14, startY);
    startY += summaryLines.length * 6 + 10;

    sections.push({ title: 'Executive Summary', page: currentPage });

    // Process each section in the comprehensive report
    for (const section of reportData.sections) {
      // Add a page break for each new section
      doc.addPage();
      currentPage = doc.getCurrentPageInfo().pageNumber;
      startY = 20;

      // Add section header
      doc.setFontSize(16);
      doc.setTextColor(41, 128, 185);
      doc.text(section.title, 14, startY);
      startY += 10;

      if (section.description) {
        doc.setFontSize(10);
        doc.setTextColor(100);
        const descLines = doc.splitTextToSize(section.description, doc.internal.pageSize.width - 28);
        doc.text(descLines, 14, startY);
        startY += descLines.length * 6 + 6;
      }

      // Add data visualization for this section if applicable
      if (section.type) {
        // Create a temporary ReportData object with the necessary fields
        const tempReportData: ReportData = {
          ...section,
          date: reportData.date,
          title: section.title || '',
          description: section.description || '',
          companyName: reportData.companyName,
          data: section.data || [],
          columns: section.columns || [],
        };
        startY = addDataVisualizations(doc, tempReportData, section.type, startY);
      }

      // Add table for this section
      const tableColumn = section.columns.map(col => col.header);
      const tableRows = section.data.map((item: any) =>
        section.columns.map(col => item[col.dataKey])
      );

      autoTable(doc, {
        head: [tableColumn],
        body: tableRows,
        startY,
        theme: 'grid',
        styles: { fontSize: 10, cellPadding: 5 },
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        columnStyles: section.columns.reduce((acc, col, index) => {
          if (col.width) {
            acc[index] = { cellWidth: col.width };
          }
          return acc;
        }, {} as { [key: number]: { cellWidth: number } }),
      });

      sections.push({ title: section.title, page: currentPage });
    }

    // Add glossary of terms
    doc.addPage();
    currentPage = doc.getCurrentPageInfo().pageNumber;
    startY = 20;

    doc.setFontSize(16);
    doc.setTextColor(41, 128, 185);
    doc.text('Glossary of Terms', 14, startY);
    startY += 10;

    doc.setFontSize(10);
    doc.setTextColor(70);

    // Combine glossary terms from all report types
    const allTerms = [
      ...getGlossaryTerms('recruitment_funnel'),
      ...getGlossaryTerms('time_to_hire'),
      ...getGlossaryTerms('source_effectiveness')
    ];

    // Remove duplicates
    const uniqueTerms = allTerms.filter((term, index, self) =>
      index === self.findIndex(t => t.term === term.term)
    );

    uniqueTerms.forEach((term: GlossaryTerm, index: number) => {
      doc.setFontSize(11);
      doc.setTextColor(50);
      doc.text(term.term, 14, startY);
      startY += 5;

      doc.setFontSize(9);
      doc.setTextColor(100);
      const descriptionLines = doc.splitTextToSize(term.description, doc.internal.pageSize.width - 28);
      doc.text(descriptionLines, 20, startY);
      startY += descriptionLines.length * 5 + 8;

      // Add page break if needed
      if (startY > doc.internal.pageSize.height - 30 && index < uniqueTerms.length - 1) {
        doc.addPage();
        currentPage = doc.getCurrentPageInfo().pageNumber;
        startY = 20;
      }
    });

    sections.push({ title: 'Glossary of Terms', page: currentPage });

    // Add table of contents as the second page (after cover page)
    doc.setPage(1); // Go back to first page (cover page)
    doc.addPage(); // Add new page after cover page
    doc.movePage(doc.getNumberOfPages(), 2); // Move the new page to be the second page

    // Add table of contents
    addTableOfContents(doc, sections);

    // Add footer
    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
      doc.setPage(i);
      doc.setFontSize(8);
      doc.setTextColor(100);
      doc.text(
        'This report was generated by Sourcio.ai Platform.',
        105,
        doc.internal.pageSize.height - 10,
        { align: 'center' }
      );
      doc.text(
        `Page ${i} of ${pageCount}`,
        doc.internal.pageSize.width - 20,
        doc.internal.pageSize.height - 10
      );
    }

    return doc;
  }

  // Add header
  doc.setFontSize(20);
  doc.setTextColor(41, 128, 185);
  doc.text(reportData.title, 14, 22);

  // Add company name if available
  if (reportData.companyName) {
    doc.setFontSize(12);
    doc.setTextColor(100);
    doc.text(`Company: ${reportData.companyName}`, 14, 32);
  }

  // Add date
  doc.setFontSize(10);
  doc.setTextColor(100);
  doc.text(`Generated on: ${format(reportData.date, 'PPP')}`, 14, reportData.companyName ? 38 : 32);

  // Add description if available
  if (reportData.description) {
    doc.setFontSize(10);
    doc.setTextColor(100);
    doc.text(reportData.description, 14, reportData.companyName ? 44 : 38);
  }

  // Add executive summary
  let startY = reportData.companyName ? (reportData.description ? 54 : 44) : (reportData.description ? 48 : 38);
  const summary = generateExecutiveSummary(reportData, reportType);

  doc.setFontSize(12);
  doc.setTextColor(41, 128, 185);
  doc.text('Executive Summary', 14, startY);
  startY += 6;

  doc.setFontSize(10);
  doc.setTextColor(100);
  const summaryLines = doc.splitTextToSize(summary, doc.internal.pageSize.width - 28);
  doc.text(summaryLines, 14, startY);
  startY += summaryLines.length * 6 + 10;

  sections.push({ title: 'Executive Summary', page: currentPage });

  // Add filters if available
  if (reportData.filters && Object.keys(reportData.filters).length > 0) {
    doc.setFontSize(10);
    doc.setTextColor(100);
    doc.text('Filters:', 14, startY);
    startY += 6;

    Object.entries(reportData.filters).forEach(([key, value]) => {
      let displayValue = value;
      if (value instanceof Date) {
        displayValue = format(value, 'PPP');
      }
      doc.text(`${key}: ${displayValue}`, 20, startY);
      startY += 5;
    });

    startY += 5;
  }

  // PHASE 2: Add Data Visualizations
  // ===============================
  startY = addDataVisualizations(doc, reportData, reportType, startY);
  sections.push({ title: 'Data Visualization', page: doc.getCurrentPageInfo().pageNumber });
  currentPage = doc.getCurrentPageInfo().pageNumber;

  // Prepare table data
  const tableColumn = reportData.columns.map(col => col.header);
  const tableRows = reportData.data.map((item: any) =>
    reportData.columns.map(col => item[col.dataKey])
  );

  // Add the table
  autoTable(doc, {
    head: [tableColumn],
    body: tableRows,
    startY,
    theme: 'grid',
    styles: { fontSize: 10, cellPadding: 5 },
    headStyles: { fillColor: [41, 128, 185], textColor: 255 },
    columnStyles: reportData.columns.reduce((acc, col, index) => {
      if (col.width) {
        acc[index] = { cellWidth: col.width };
      }
      return acc;
    }, {} as { [key: number]: { cellWidth: number } }),
  });

  sections.push({ title: 'Data Table', page: currentPage });
  currentPage = doc.getCurrentPageInfo().pageNumber;

  // Add detailed metrics
  startY = (doc as any).lastAutoTable.finalY + 10;
  startY = addDetailedMetrics(doc, reportData, reportType, startY);
  sections.push({ title: 'Detailed Metrics', page: doc.getCurrentPageInfo().pageNumber });
  currentPage = doc.getCurrentPageInfo().pageNumber;

  // PHASE 2: Add Recommendations
  // ===========================
  startY = addRecommendations(doc, reportData, reportType, startY);
  sections.push({ title: 'Recommendations', page: doc.getCurrentPageInfo().pageNumber });
  currentPage = doc.getCurrentPageInfo().pageNumber;

  // Add summary if available
  if (reportData.summary && Object.keys(reportData.summary).length > 0) {
    // Check if we need to add a page break
    if (startY > doc.internal.pageSize.height - 40) {
      doc.addPage();
      startY = 20;
      currentPage = doc.getCurrentPageInfo().pageNumber;
    }

    doc.setFontSize(12);
    doc.setTextColor(41, 128, 185);
    doc.text('Summary', 14, startY);

    let summaryY = startY + 6;
    Object.entries(reportData.summary).forEach(([key, value]) => {
      doc.setFontSize(10);
      doc.setTextColor(100);
      doc.text(`${key}: ${value}`, 14, summaryY);
      summaryY += 5;
    });

    sections.push({ title: 'Summary', page: currentPage });
  }

  // PHASE 3: Add Historical Comparisons if available
  // ==============================================
  if (reportData.historicalData && reportData.historicalData.length > 0) {
    doc.addPage();
    currentPage = doc.getCurrentPageInfo().pageNumber;
    startY = 20;

    doc.setFontSize(16);
    doc.setTextColor(41, 128, 185);
    doc.text('Historical Comparisons', 14, startY);
    startY += 10;

    doc.setFontSize(10);
    doc.setTextColor(70);

    reportData.historicalData.forEach((period) => {
      doc.setFontSize(12);
      doc.text(`${period.period}:`, 14, startY);
      startY += 8;

      // Create a comparison table
      const comparisonData = period.data.map((item: any) => {
        const row = [];
        for (const col of reportData.columns) {
          row.push(item[col.dataKey] || '-');
        }
        return row;
      });

      autoTable(doc, {
        head: [tableColumn],
        body: comparisonData,
        startY,
        theme: 'grid',
        styles: { fontSize: 9, cellPadding: 3 },
        headStyles: { fillColor: [41, 128, 185], textColor: 255 },
      });

      startY = (doc as any).lastAutoTable.finalY + 15;
    });

    sections.push({ title: 'Historical Comparisons', page: currentPage });
  }

  // PHASE 3: Add Industry Benchmarks if available
  // ===========================================
  if (reportData.benchmarkData && reportData.benchmarkData.length > 0) {
    // Check if we need to add a page break
    if (startY > doc.internal.pageSize.height - 60) {
      doc.addPage();
      startY = 20;
      currentPage = doc.getCurrentPageInfo().pageNumber;
    } else {
      startY += 10;
    }

    doc.setFontSize(16);
    doc.setTextColor(41, 128, 185);
    doc.text('Industry Benchmarks', 14, startY);
    startY += 10;

    doc.setFontSize(10);
    doc.setTextColor(70);

    // Create a benchmark table
    const benchmarkTableData = reportData.benchmarkData.map(benchmark => [
      benchmark.name,
      benchmark.value.toString(),
      benchmark.description || ''
    ]);

    autoTable(doc, {
      head: [['Benchmark', 'Value', 'Description']],
      body: benchmarkTableData,
      startY,
      theme: 'grid',
      styles: { fontSize: 9, cellPadding: 3 },
      headStyles: { fillColor: [41, 128, 185], textColor: 255 },
    });

    sections.push({ title: 'Industry Benchmarks', page: currentPage });
  }

  // PHASE 3: Add Glossary of Terms
  // ============================
  doc.addPage();
  currentPage = doc.getCurrentPageInfo().pageNumber;
  startY = 20;

  doc.setFontSize(16);
  doc.setTextColor(41, 128, 185);
  doc.text('Glossary of Terms', 14, startY);
  startY += 10;

  doc.setFontSize(10);
  doc.setTextColor(70);

  // Add glossary terms based on report type
  const glossaryTerms = getGlossaryTerms(reportType);

  glossaryTerms.forEach((term: GlossaryTerm, index: number) => {
    doc.setFontSize(11);
    doc.setTextColor(50);
    doc.text(term.term, 14, startY);
    startY += 5;

    doc.setFontSize(9);
    doc.setTextColor(100);
    const descriptionLines = doc.splitTextToSize(term.description, doc.internal.pageSize.width - 28);
    doc.text(descriptionLines, 20, startY);
    startY += descriptionLines.length * 5 + 8;

    // Add page break if needed
    if (startY > doc.internal.pageSize.height - 30 && index < glossaryTerms.length - 1) {
      doc.addPage();
      currentPage = doc.getCurrentPageInfo().pageNumber;
      startY = 20;
    }
  });

  sections.push({ title: 'Glossary of Terms', page: currentPage });

  // Add table of contents as the second page (after cover page)
  doc.setPage(1); // Go back to first page (cover page)
  doc.addPage(); // Add new page after cover page
  doc.movePage(doc.getNumberOfPages(), 2); // Move the new page to be the second page

  // Add table of contents
  addTableOfContents(doc, sections);

  // Add footer
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.setTextColor(100);
    doc.text(
      'This report was generated by Sourcio.ai Platform.',
      105,
      doc.internal.pageSize.height - 10,
      { align: 'center' }
    );
    doc.text(
      `Page ${i} of ${pageCount}`,
      doc.internal.pageSize.width - 20,
      doc.internal.pageSize.height - 10
    );
  }

  return doc;
};

/**
 * Download a PDF report
 */
export const downloadPdfReport = (reportData: ReportData, reportType: ReportType): void => {
  const doc = generatePdfReport(reportData, reportType);
  const fileName = `${reportData.title.replace(/\s+/g, '_')}_${format(reportData.date, 'yyyy-MM-dd')}.pdf`;
  doc.save(fileName);
};

/**
 * Helper function to get glossary terms based on report type
 */
interface GlossaryTerm {
  term: string;
  description: string;
}

function getGlossaryTerms(reportType: ReportType): GlossaryTerm[] {
  const commonTerms: GlossaryTerm[] = [
    {
      term: 'Conversion Rate',
      description: 'The percentage of candidates who progress from one stage to another in the recruitment process.'
    },
    {
      term: 'Match Score',
      description: 'A percentage indicating how well a candidate\'s skills and experience match the requirements of a job.'
    },
    {
      term: 'Time to Hire',
      description: 'The number of days from when a job is posted to when a candidate accepts an offer.'
    }
  ];

  const specificTerms: Partial<Record<ReportType, GlossaryTerm[]>> = {
    recruitment_funnel: [
      {
        term: 'Funnel Stage',
        description: 'A distinct phase in the recruitment process, such as application, screening, interview, offer, and hire.'
      },
      {
        term: 'Dropoff Rate',
        description: 'The percentage of candidates who exit the recruitment process at a particular stage.'
      },
      {
        term: 'Overall Conversion Rate',
        description: 'The percentage of candidates who complete the entire recruitment process from application to hire.'
      }
    ],
    comprehensive: [
      {
        term: 'Comprehensive Analytics',
        description: 'A complete view of all recruitment metrics combined into a single report.'
      },
      {
        term: 'Cross-metric Analysis',
        description: 'Analysis that examines relationships between different recruitment metrics.'
      }
    ],
    time_to_hire: [
      {
        term: 'Time in Stage',
        description: 'The number of days a candidate spends in a particular stage of the recruitment process.'
      },
      {
        term: 'Average Time to Hire',
        description: 'The average number of days it takes to fill a position, from job posting to offer acceptance.'
      },
      {
        term: 'Time to Fill',
        description: 'The number of days from when a job is posted to when a candidate starts the job.'
      }
    ],
    source_effectiveness: [
      {
        term: 'Source',
        description: 'The channel through which a candidate was recruited, such as job boards, referrals, or social media.'
      },
      {
        term: 'Applicant Quality',
        description: 'A measure of how well candidates from a particular source meet the job requirements.'
      },
      {
        term: 'Cost per Hire',
        description: 'The total cost of recruiting divided by the number of hires from a particular source.'
      },
      {
        term: 'Time to Hire by Source',
        description: 'The average number of days it takes to hire a candidate from a particular source.'
      }
    ],
    candidate_pipeline: [
      {
        term: 'Pipeline',
        description: 'The pool of candidates at various stages of the recruitment process.'
      },
      {
        term: 'Pipeline Velocity',
        description: 'The speed at which candidates move through the recruitment process.'
      },
      {
        term: 'Pipeline Coverage',
        description: 'The ratio of qualified candidates to open positions.'
      }
    ],
    skill_analysis: [
      {
        term: 'Skill Gap',
        description: 'The difference between the skills required for a job and the skills possessed by candidates.'
      },
      {
        term: 'Skill Frequency',
        description: 'How often a particular skill appears in candidate profiles.'
      },
      {
        term: 'Skill Relevance',
        description: 'How important a skill is for a particular job or industry.'
      }
    ]
  };

  return [...commonTerms, ...(specificTerms[reportType] || [])];
}
