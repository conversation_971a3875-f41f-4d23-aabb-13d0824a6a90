# Sourcio.ai Platform - Scope of Work

## Overview
The Sourcio.ai Platform is an AI recruitment solution that helps companies evaluate and connect the right people with the right jobs. This document outlines the scope of work.

## Intellectual Property Rights
- The client will receive full ownership of all intellectual property rights to the developed software
- Complete source code will be provided with unrestricted usage rights
- The client will have the right to modify, distribute, and commercialize the platform
- All documentation, designs, and assets created during development will be the property of the client
- No third-party components with restrictive licenses will be used without client approval

## Deliverables

### Project Deliverables

| Phase | Deliverable |
|-------|-------------|
| **Phase 1** | Authentication & User Management |
| **Phase 1** | Dashboard & UI Framework |
| **Phase 2** | CV Upload & Management |
| **Phase 2** | Job Posting System |
| **Phase 3** | AI Integration (GROQ API) |
| **Phase 3** | CV Evaluation System |
| **Phase 4** | Candidate Pipeline Management |
| **Phase 4** | Analytics & Reporting |
| **Phase 5** | Email Notifications |
| **Phase 5** | Company Management |
| **Final** | Testing & Bug Fixes |
| **Final** | Documentation & Deployment |


### Detailed Deliverables

1. Fully functional Next.js-based recruitment platform
2. Flexible AI integration system with support for multiple providers
3. Responsive UI for all device types
4. User authentication and role-based access control
5. CV upload, parsing, and evaluation system
6. Job posting and management system
7. Candidate pipeline management
8. Analytics and reporting dashboard
9. Email notification system
10. Complete source code with full intellectual property rights
11. Comprehensive technical documentation including:
    - System architecture documentation
    - API documentation
    - Database schema
    - Deployment instructions
    - User manual
12. Clean, well-commented code following industry best practices

<div style="page-break-after: always;"></div>

## Platform Workflow

1. **Upload CVs**
   Companies or agencies upload candidate CVs directly to the platform — no formatting needed.

2. **Post Jobs**
   Add job listings. The system understands requirements and matches them to the right candidates.

3. **Automatic Evaluation**
   Every CV is automatically analyzed and scored based on job fit. Users instantly see top matches.

4. **View Reports**
   Clear and simple reports for each candidate — no manual sorting or guesswork.

5. **Shortlist & Hire Faster**
   Save hours of work. Focus on interviewing top candidates and make faster hiring decisions.

### Workflow Diagram

```mermaid
graph LR
    A[Upload CVs] --> B[Post Jobs]
    B --> C[Automatic Evaluation]
    C --> D[View Reports]
    D --> E[Shortlist & Hire]

    style A fill:#4CAF50,stroke:#388E3C,color:black
    style B fill:#2196F3,stroke:#1976D2,color:black
    style C fill:#FF9800,stroke:#F57C00,color:black
    style D fill:#9C27B0,stroke:#7B1FA2,color:black
    style E fill:#F44336,stroke:#D32F2F,color:black
```

<div style="page-break-after: always;"></div>

## Core Features

### CV Management
- CV upload interface with support for multiple file formats (PDF, DOC, DOCX)
- CV parsing and information extraction using AI
- CV preview functionality with zoom and navigation controls
- CV evaluation results display with detailed analysis
- Candidate comparison view for side-by-side assessment

### Job Management
- Job posting creation and management
- Job listing page with filtering and search
- Job details view with requirements and responsibilities
- Job status management (active/inactive)
- LinkedIn job integration (subject to API limitations)

### AI-Powered Evaluation
- Flexible AI integration architecture supporting multiple providers
- Configurable AI settings including API endpoints, context window size, and API keys
- Default integration with GROQ API (Llama 3.3 model)
- Support for other AI providers (OpenAI, Claude, etc.) through configuration
- Agentic RAG (Retrieval Augmented Generation) implementation
- Skill extraction and matching against job requirements
- Candidate scoring and ranking system
- Customizable evaluation criteria

### Candidate Pipeline
- Status management workflow (new, screening, interview, offer, hired, rejected)
- Interview scheduling and feedback collection
- Automated notifications via email
- Timeline view of candidate progress
- Notes and communication history

```mermaid
stateDiagram-v2
    [*] --> New
    New --> Screening
    Screening --> Interview
    Screening --> Rejected
    Interview --> Offer
    Interview --> Rejected
    Offer --> Hired
    Offer --> Rejected
    Hired --> [*]
    Rejected --> [*]

    state Screening {
        [*] --> CV_Review
        CV_Review --> Phone_Screen
        Phone_Screen --> [*]
    }

    state Interview {
        [*] --> First_Round
        First_Round --> Second_Round
        Second_Round --> Final_Round
        Final_Round --> [*]
    }
```

### Analytics & Reporting
- Enhanced dashboard with comprehensive recruitment metrics and KPIs
- Interactive data visualizations and charts
- Visualization of recruitment funnel
- Source effectiveness analysis
- Time-to-hire and other efficiency metrics
- Performance benchmarking against industry standards
- Custom report generation with export capabilities

### Company Management
- Company profile creation and management
- Team member management with different access levels
- Branding customization options
- Subscription management for SaaS model

### Authentication & User Management
- User registration and login system for both candidates and companies
- Admin approval workflow for company registrations
- Role-based access control (admin, company, candidate)
- User profile management
- Password reset functionality

```mermaid
flowchart TD
    subgraph UserRoles
        A[User Authentication] --> B[Admin]
        A --> C[Company User]
        A --> D[Candidate]
    end

    subgraph Permissions
        B --> B1[Full System Access]
        B --> B2[Manage All Users]
        B --> B3[Configure AI Settings]

        C --> C1[Manage Company Profile]
        C --> C2[Post Jobs]
        C --> C3[Evaluate CVs]
        C --> C4[Manage Pipeline]

        D --> D1[View Jobs]
        D --> D2[Submit Applications]
        D --> D3[Track Status]
    end

    style UserRoles fill:#f9f9f9,stroke:#333,stroke-width:1px,color:black
    style Permissions fill:#f0f0f0,stroke:#333,stroke-width:1px,color:black
    style A color:black
    style B color:black
    style C color:black
    style D color:black
    style B1 color:black
    style B2 color:black
    style B3 color:black
    style C1 color:black
    style C2 color:black
    style C3 color:black
    style C4 color:black
    style D1 color:black
    style D2 color:black
    style D3 color:black
```

### Dashboard & UI
- Enhanced company dashboard with comprehensive metrics and visualizations
- Detailed analytics dashboard with customizable KPI tracking
- Candidate dashboard for job applications and status tracking
- Admin dashboard for platform management
- White-label solution with customizable branding options

<div style="page-break-after: always;"></div>

## Technical Specifications

### Technology Stack

| Component | Technologies |
|-----------|-------------|
| **Frontend** | Next.js (React), Tailwind CSS, TypeScript |
| **Backend Integration** | API Integration, Serverless Functions |
| **Database** | Supabase (PostgreSQL) |
| **AI Integration** | GROQ API (Llama 3.3), OpenAI, Claude (configurable) |
| **Authentication** | NextAuth.js |
| **File Storage** | Supabase Storage |

### System Architecture

```mermaid
flowchart TD
    subgraph Client
        A[Web Browser] --> B[Next.js Frontend]
    end

    subgraph ServerlessFunctions
        B <--> C[API Routes]
        C <--> D[Authentication]
        C <--> E[File Processing]
        C <--> F[AI Integration]
        C <--> G[Email Service]
    end

    subgraph ExternalServices
        F <--> H[GROQ API]
        F <--> I[OpenAI/Claude]
        G <--> J[Email Provider]
    end

    subgraph Database
        C <--> K[Supabase]
        K --- L[(PostgreSQL)]
        K --- M[File Storage]
    end

    style Client fill:#f9f9f9,stroke:#333,stroke-width:1px,color:black
    style ServerlessFunctions fill:#f0f0f0,stroke:#333,stroke-width:1px,color:black
    style ExternalServices fill:#e6f7ff,stroke:#333,stroke-width:1px,color:black
    style Database fill:#f0fff0,stroke:#333,stroke-width:1px,color:black
    style A color:black
    style B color:black
    style C color:black
    style D color:black
    style E color:black
    style F color:black
    style G color:black
    style H color:black
    style I color:black
    style J color:black
    style K color:black
    style L color:black
    style M color:black
```

### Frontend
- Next.js-based application (React framework)
- Client-side form validation and error handling
- Clean, maintainable code architecture following best practices

### Backend Integration
- Flexible AI provider integration system with configurable endpoints
- Admin panel for managing AI provider settings (API keys, models, parameters)
- Email notification system
- Database integration for storing user data, jobs, and candidates
- File storage for CV documents

### SaaS Capabilities
- Multi-tenant architecture
- Tiered subscription model with feature access control
- White-labeling capabilities for enterprise clients
- Usage analytics and monitoring

<div style="page-break-after: always;"></div>

## Subscription Plans (Only an example but you will be able to update the prices in admin dashboard)
The platform will offer the following subscription tiers. New users can try the platform with 1 free CV evaluation before subscribing:

### Subscription Plans Comparison

| Feature | Starter ($49/month) | Growth ($149/month) | Pro ($299/month) |
|---------|-------------------|-------------------|------------------|
| **CV Uploads** | 50 per month | 500 per month | Unlimited |
| **Job Postings** | 1 at a time | 5 at a time | Unlimited |
| **Company Profiles** | 1 | 5 | Unlimited |
| **CV Evaluation** | Basic automatic | Smart matching | Custom scoring rules |
| **Reports** | Simple | Downloadable (PDF/Excel) | Detailed with insights |
| **Team Collaboration** | No | Limited | Full access |

## Current Subscription Tiers

The platform offers three subscription tiers:

1. **Starter Plan** ($49/month)
   - Upload up to 50 CVs per month
   - Post 1 job at a time
   - 1 company profile
   - Automatic CV evaluation
   - Simple hiring reports

2. **Growth Plan** ($149/month)
   - Upload up to 500 CVs per month
   - Post up to 5 jobs at a time
   - Manage up to 5 company profiles
   - Smart CV matching with job roles
   - Download reports (PDF or Excel)
   - Insights to improve your hiring

3. **Pro Plan** ($299/month)
   - Unlimited CV uploads
   - Unlimited job postings
   - Unlimited company profiles
   - Custom rules for candidate scoring
   - Add team members and collaborate
   - Detailed hiring reports and insights


### Pricing Tiers Visualization

```mermaid
graph TD
    A[Sourcio.ai Platform] --> B[Starter: $49/month]
    A --> C[Growth: $149/month]
    A --> D[Pro: $299/month]

    B --> B1[50 CVs/month]
    B --> B2[1 job posting]
    B --> B3[Basic features]

    C --> C1[500 CVs/month]
    C --> C2[5 job postings]
    C --> C3[Advanced features]

    D --> D1[Unlimited CVs]
    D --> D2[Unlimited jobs]
    D --> D3[Premium features]

    style A fill:#4CAF50,stroke:#388E3C,color:black
    style B fill:#2196F3,stroke:#1976D2,color:black
    style C fill:#FF9800,stroke:#F57C00,color:black
    style D fill:#F44336,stroke:#D32F2F,color:black
```