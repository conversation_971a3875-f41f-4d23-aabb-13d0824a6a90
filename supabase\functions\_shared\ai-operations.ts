/**
 * Shared AI Operations for Supabase Edge Functions
 * 
 * This module provides centralized AI prompts and schemas for use in edge functions.
 * It mirrors the client-side centralized system to ensure consistency.
 */

// ============================================================================
// AI SCHEMAS
// ============================================================================

export const CV_PARSING_SCHEMA = {
  type: "object",
  required: ["personalDetails", "workExperience", "education", "skills"],
  properties: {
    personalDetails: {
      type: "object",
      required: ["name", "email"],
      properties: {
        name: { type: "string" },
        email: { type: "string" },
        phone: { type: "string" },
        location: { type: "string" },
        linkedIn: { type: "string" },
        website: { type: "string" }
      }
    },
    professionalSummary: { type: "string" },
    workExperience: {
      type: "array",
      items: {
        type: "object",
        required: ["company", "title", "startDate", "responsibilities"],
        properties: {
          company: { type: "string" },
          title: { type: "string" },
          startDate: { type: "string" },
          endDate: { type: "string" },
          current: { type: "boolean" },
          responsibilities: {
            type: "array",
            items: { type: "string" }
          },
          achievements: {
            type: "array",
            items: { type: "string" }
          }
        }
      }
    },
    education: {
      type: "array",
      items: {
        type: "object",
        required: ["institution", "degree", "startDate"],
        properties: {
          institution: { type: "string" },
          degree: { type: "string" },
          field: { type: "string" },
          startDate: { type: "string" },
          endDate: { type: "string" },
          current: { type: "boolean" },
          gpa: { type: "string" },
          achievements: {
            type: "array",
            items: { type: "string" }
          }
        }
      }
    },
    skills: {
      type: "object",
      required: ["technical"],
      properties: {
        technical: {
          type: "array",
          items: { type: "string" }
        },
        soft: {
          type: "array",
          items: { type: "string" }
        },
        languages: {
          type: "array",
          items: { type: "string" }
        }
      }
    },
    languages: {
      type: "array",
      items: {
        type: "object",
        required: ["name", "proficiency"],
        properties: {
          name: { type: "string" },
          proficiency: { type: "string" }
        }
      }
    },
    certifications: {
      type: "array",
      items: {
        type: "object",
        required: ["name", "issuer"],
        properties: {
          name: { type: "string" },
          issuer: { type: "string" },
          date: { type: "string" },
          expiryDate: { type: "string" }
        }
      }
    },
    projects: {
      type: "array",
      items: {
        type: "object",
        required: ["name", "description"],
        properties: {
          name: { type: "string" },
          description: { type: "string" },
          technologies: {
            type: "array",
            items: { type: "string" }
          },
          url: { type: "string" }
        }
      }
    }
  }
} as const;

export const CANDIDATE_RANKING_SCHEMA = {
  type: "object",
  required: ["rankings"],
  properties: {
    rankings: {
      type: "array",
      items: {
        type: "object",
        required: ["candidateId", "name", "overallScore", "skillsScore", "experienceScore", "educationScore", "culturalFitScore", "strengths", "areasForImprovement", "recommendation"],
        properties: {
          candidateId: { type: "string" },
          name: { type: "string" },
          overallScore: { type: "number", minimum: 0, maximum: 100 },
          skillsScore: { type: "number", minimum: 0, maximum: 100 },
          experienceScore: { type: "number", minimum: 0, maximum: 100 },
          educationScore: { type: "number", minimum: 0, maximum: 100 },
          culturalFitScore: { type: "number", minimum: 0, maximum: 100 },
          strengths: {
            type: "array",
            items: { type: "string" },
            minItems: 2
          },
          areasForImprovement: {
            type: "array",
            items: { type: "string" },
            minItems: 2
          },
          recommendation: {
            type: "string",
            enum: ["Hire", "Consider", "Reject"]
          }
        }
      }
    }
  }
} as const;

// ============================================================================
// AI PROMPTS
// ============================================================================

function schemaToString(schema: any): string {
  return JSON.stringify(schema, null, 2);
}

export const CANDIDATE_RANKING_PROMPT = `You are an expert candidate evaluation assistant. Your task is to score and rank candidates based on their match to a job description and return the results in JSON format.

Consider the following factors with their weights:
- Skills match (40%)
- Experience match (30%)
- Education match (20%)
- Cultural fit (10%)

You MUST respond with a valid JSON object that follows this EXACT schema:

${schemaToString(CANDIDATE_RANKING_SCHEMA)}

SCORING GUIDELINES:
- Overall Score: Weighted average of all factors
- Skills Score: How well candidate's skills match job requirements
- Experience Score: Relevance and quality of work experience
- Education Score: Relevance of educational background
- Cultural Fit Score: Alignment with company values and culture

COVER LETTER POLICY:
- Cover letters are OPTIONAL and should NEVER negatively impact a candidate's evaluation
- Do NOT penalize candidates for not providing a cover letter
- Do NOT mention lack of cover letter as a weakness or area for improvement
- Do NOT reference motivation, interest, or enthusiasm based on cover letter presence/absence
- Base your evaluation ONLY on the candidate's CV/resume and professional qualifications

RANKING RECOMMENDATIONS:
- "Hire": Overall score 80%+ with strong skills and experience match
- "Consider": Overall score 60-79% with some gaps but potential
- "Reject": Overall score below 60% or missing critical requirements

IMPORTANT RULES:
1. Rank candidates from highest to lowest overall score
2. Provide specific, actionable strengths and areas for improvement
3. Be objective and consistent in scoring
4. Consider both technical and soft skills
5. Evaluate experience quality, not just quantity
6. Provide at least 2 strengths and 2 areas for improvement per candidate
7. Be specific in feedback - avoid generic statements
8. NEVER mention cover letter absence as a negative factor

CRITICAL: You must respond ONLY with valid JSON. Do not include any explanatory text before or after the JSON.`;

// ============================================================================
// AI OPERATIONS
// ============================================================================

/**
 * Enhanced retry logic for Gemini API calls
 */
async function withGeminiRetry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 10,
  delayMs: number = 5000
): Promise<T> {
  let retryCount = 0;

  while (true) {
    try {
      return await fn();
    } catch (error: any) {
      // Check if we should retry
      const shouldRetry = 
        error.status === 429 || // Rate limit
        error.status >= 500 || // Server errors
        (error.message && (
          error.message.toLowerCase().includes('rate limit') ||
          error.message.toLowerCase().includes('quota exceeded') ||
          error.message.toLowerCase().includes('resource exhausted') ||
          error.message.toLowerCase().includes('unavailable') ||
          error.message.toLowerCase().includes('timeout') ||
          error.message.toLowerCase().includes('internal error')
        ));

      if (retryCount >= maxRetries || !shouldRetry) {
        console.error(`Gemini API call failed after ${retryCount} retries:`, error);
        throw error;
      }

      // Calculate delay with slight jitter
      const jitter = Math.random() * 0.3 + 0.85; // 0.85 to 1.15
      const actualDelay = Math.min(delayMs * Math.pow(1.2, retryCount) * jitter, 30000);
      
      console.warn(
        `Gemini API call failed: ${error.message}. Retrying in ${actualDelay}ms (attempt ${retryCount + 1}/${maxRetries})`
      );

      await new Promise(resolve => setTimeout(resolve, actualDelay));
      retryCount++;
    }
  }
}

/**
 * Make AI API call with enhanced retry logic
 */
export async function makeAICall(
  apiKey: string,
  model: string,
  prompt: string,
  userMessage: string,
  baseURL: string = 'https://api.groq.com/openai/v1',
  provider: string = 'groq'
): Promise<any> {
  if (provider === 'gemini') {
    // Handle Gemini API calls with enhanced retry
    return await withGeminiRetry(async () => {
      console.log(`Making Gemini API call to model: ${model}`);
      
      const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${encodeURIComponent(apiKey)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contents: [
            {
              parts: [
                {
                  text: `${prompt}\n\n${userMessage}`
                }
              ]
            }
          ],
          generationConfig: {
            temperature: 0.1,
            topP: 1,
            maxOutputTokens: 32768,
            responseMimeType: 'application/json'
          }
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const errorMessage = errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`;
        
        const error = new Error(`Gemini API error: ${errorMessage}`);
        (error as any).status = response.status;
        throw error;
      }

      const data = await response.json();
      const content = data.candidates?.[0]?.content?.parts?.[0]?.text;

      if (!content) {
        throw new Error('Empty response from Gemini API');
      }

      return JSON.parse(content);
    });
  } else {
    // Handle OpenAI-compatible APIs with standard retry
    const response = await fetch(`${baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model,
        max_completion_tokens: 32768,
        temperature: 0.1,
        top_p: 1,
        stream: false,
        response_format: {
          type: "json_object"
        },
        stop: null,
        messages: [
          {
            role: 'system',
            content: prompt
          },
          {
            role: 'user',
            content: userMessage
          }
        ],
      }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`AI API error: ${errorData.error?.message || 'Unknown error'}`);
    }

    const data = await response.json();
    return JSON.parse(data.choices[0].message.content);
  }
}

/**
 * Score and rank candidates for a job using centralized prompt
 */
export async function rankCandidatesForJob(
  apiKey: string,
  model: string,
  candidates: any[],
  jobDescription: string,
  baseURL?: string,
  provider?: string
): Promise<any> {
  const candidatesStr = JSON.stringify(candidates);
  const userMessage = `Score and rank the following candidates for this job description as JSON:\n\nJob Description:\n${jobDescription}\n\nCandidates:\n${candidatesStr}`;

  return makeAICall(apiKey, model, CANDIDATE_RANKING_PROMPT, userMessage, baseURL, provider);
}

// ============================================================================
// EXPORTS
// ============================================================================

export const AI_SCHEMAS = {
  CV_PARSING: CV_PARSING_SCHEMA,
  CANDIDATE_RANKING: CANDIDATE_RANKING_SCHEMA
} as const;

export const AI_PROMPTS = {
  CANDIDATE_RANKING: CANDIDATE_RANKING_PROMPT
} as const;

