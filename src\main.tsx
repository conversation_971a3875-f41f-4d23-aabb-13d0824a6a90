import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// Import and validate environment variables
import { validateEnv } from './lib/env';

try {
  // Validate environment variables before rendering the app
  validateEnv();

  // Render the app
  createRoot(document.getElementById("root")!).render(<App />);
} catch (error) {
  // Display error message if environment variables are missing
  const rootElement = document.getElementById("root");
  if (rootElement) {
    rootElement.innerHTML = `
      <div style="font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; padding: 2rem; max-width: 800px; margin: 0 auto;">
        <h1 style="color: #e11d48;">Environment Configuration Error</h1>
        <p style="font-size: 1.1rem;">${error instanceof Error ? error.message : 'An unknown error occurred during initialization.'}</p>
        <p>Please check your <code>.env</code> file and make sure all required environment variables are set.</p>
        <p>See the <a href="/docs/environment-setup.md" style="color: #0284c7;">Environment Setup Guide</a> for more information.</p>
      </div>
    `;
  }

  // Log the error to the console
  console.error('Environment configuration error:', error);
}
