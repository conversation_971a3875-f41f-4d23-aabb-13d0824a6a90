-- Migration for Candidate Portal Support
-- This migration adds support for candidate users and public job applications

-- Create function to get all public active jobs
CREATE OR REPLACE FUNCTION get_public_jobs()
RETURNS TABLE (
  id uuid,
  created_at timestamptz,
  title text,
  description text,
  requirements text,
  location text,
  salary_min numeric,
  salary_max numeric,
  job_type text,
  experience_level text,
  status text,
  company_id uuid,
  company_name text,
  company_logo_url text
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT
    j.id,
    j.created_at,
    j.title,
    j.description,
    j.requirements,
    j.location,
    j.salary_min,
    j.salary_max,
    j.job_type,
    j.experience_level,
    j.status,
    j.company_id,
    c.name as company_name,
    c.logo_url as company_logo_url
  FROM public.jobs j
  LEFT JOIN public.companies c ON j.company_id = c.id
  WHERE j.status = 'active'
  ORDER BY j.created_at DESC;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_public_jobs() TO authenticated;
GRANT EXECUTE ON FUNCTION get_public_jobs() TO anon;

-- Create RLS policy for candidates to view all active jobs
CREATE POLICY "Anyone can view active jobs" ON public.jobs
  FOR SELECT USING (status = 'active');

-- Create RLS policy for candidates to create applications for themselves
CREATE POLICY "Candidates can create their own applications" ON public.applications
  FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policy for candidates to view their own applications
CREATE POLICY "Candidates can view their own applications" ON public.applications
  FOR SELECT USING (auth.uid() = user_id);

-- Create function to apply to a job (for candidates)
CREATE OR REPLACE FUNCTION apply_to_job(
  job_uuid uuid,
  candidate_name text,
  candidate_email text,
  cv_file_url text,
  cover_letter_content text DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  candidate_id uuid;
  application_id uuid;
BEGIN
  -- First, create or get the candidate record
  INSERT INTO public.candidates (
    name,
    email,
    cv_url,
    status,
    job_id,
    user_id,
    cover_letter_content
  ) VALUES (
    candidate_name,
    candidate_email,
    cv_file_url,
    'new',
    job_uuid,
    auth.uid()
  )
  ON CONFLICT (email, job_id) DO UPDATE SET
    name = EXCLUDED.name,
    cv_url = EXCLUDED.cv_url,
    cover_letter_content = EXCLUDED.cover_letter_content
  RETURNING id INTO candidate_id;
  
  -- Then create the application record
  INSERT INTO public.applications (
    candidate_id,
    job_id,
    user_id,
    status
  ) VALUES (
    candidate_id,
    job_uuid,
    auth.uid(),
    'applied'
  )
  ON CONFLICT (candidate_id, job_id) DO UPDATE SET
    applied_at = now(),
    status = 'applied'
  RETURNING id INTO application_id;
  
  RETURN application_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION apply_to_job(uuid, text, text, text, text) TO authenticated;

-- Create function to get candidate's applications
CREATE OR REPLACE FUNCTION get_my_applications()
RETURNS TABLE (
  id uuid,
  created_at timestamptz,
  applied_at timestamptz,
  status text,
  notes text,
  job_title text,
  job_location text,
  job_type text,
  company_name text,
  company_logo_url text
)
LANGUAGE sql
SECURITY DEFINER
AS $$
  SELECT
    a.id,
    a.created_at,
    a.applied_at,
    a.status,
    a.notes,
    j.title as job_title,
    j.location as job_location,
    j.job_type,
    c.name as company_name,
    c.logo_url as company_logo_url
  FROM public.applications a
  LEFT JOIN public.jobs j ON a.job_id = j.id
  LEFT JOIN public.companies c ON j.company_id = c.id
  WHERE a.user_id = auth.uid()
  ORDER BY a.applied_at DESC;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_my_applications() TO authenticated;
