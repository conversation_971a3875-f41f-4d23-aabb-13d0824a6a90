-- Create tables for Expert-Recruiter application

-- Enable RLS (Row Level Security)
alter default privileges revoke execute on functions from public;

-- Create companies table
create table public.companies (
  id uuid not null default uuid_generate_v4(),
  created_at timestamp with time zone not null default now(),
  name text not null,
  description text,
  logo_url text,
  website text,
  industry text,
  size text,
  location text,
  user_id uuid not null references auth.users(id) on delete cascade,
  primary key (id)
);

-- Create jobs table
create table public.jobs (
  id uuid not null default uuid_generate_v4(),
  created_at timestamp with time zone not null default now(),
  title text not null,
  description text not null,
  requirements text not null,
  location text not null,
  salary_min numeric,
  salary_max numeric,
  job_type text not null,
  experience_level text not null,
  status text not null default 'draft',
  company_id uuid not null references public.companies(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  primary key (id)
);

-- Create candidates table
create table public.candidates (
  id uuid not null default uuid_generate_v4(),
  created_at timestamp with time zone not null default now(),
  name text not null,
  email text not null,
  phone text,
  cv_url text not null,
  status text not null default 'new',
  job_id uuid not null references public.jobs(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  evaluation_score numeric,
  evaluation_summary text,
  notes text,
  primary key (id)
);

-- Create profiles table
create table public.profiles (
  id uuid not null default uuid_generate_v4(),
  created_at timestamp with time zone not null default now(),
  user_id uuid not null references auth.users(id) on delete cascade,
  full_name text not null,
  avatar_url text,
  job_title text,
  company_id uuid references public.companies(id) on delete set null,
  subscription_tier text not null default 'starter',
  subscription_status text not null default 'active',
  subscription_end_date timestamp with time zone,
  primary key (id),
  unique (user_id)
);

-- Create team_members table
create table public.team_members (
  id uuid not null default uuid_generate_v4(),
  created_at timestamp with time zone not null default now(),
  user_id uuid references auth.users(id) on delete cascade,
  company_id uuid not null references public.companies(id) on delete cascade,
  role text not null default 'viewer',
  status text not null default 'pending',
  invited_by uuid not null references auth.users(id) on delete cascade,
  invitation_token text,
  metadata jsonb default null,
  primary key (id),
  unique (user_id, company_id)
);

-- Create notifications table
create table public.notifications (
  id uuid not null default uuid_generate_v4(),
  created_at timestamp with time zone not null default now(),
  user_id uuid not null references auth.users(id) on delete cascade,
  title text not null,
  message text not null,
  type text not null default 'info',
  read boolean not null default false,
  link text,
  primary key (id)
);

-- Set up Row Level Security (RLS) policies

-- Companies: Users can only see their own companies or companies they are a team member of
alter table public.companies enable row level security;

create policy "Users can view their own companies"
  on public.companies for select
  using (auth.uid() = user_id);

create policy "Team members can view their companies"
  on public.companies for select
  using (
    exists (
      select 1 from public.team_members
      where team_members.company_id = companies.id
      and team_members.user_id = auth.uid()
    )
  );

create policy "Users can insert their own companies"
  on public.companies for insert
  with check (auth.uid() = user_id);

create policy "Users can update their own companies"
  on public.companies for update
  using (auth.uid() = user_id);

create policy "Users can delete their own companies"
  on public.companies for delete
  using (auth.uid() = user_id);

-- Jobs: Users can only see jobs from their companies or companies they are a team member of
alter table public.jobs enable row level security;

create policy "Users can view their own jobs"
  on public.jobs for select
  using (
    auth.uid() = user_id or
    exists (
      select 1 from public.team_members
      where team_members.company_id = jobs.company_id
      and team_members.user_id = auth.uid()
    )
  );

create policy "Users can insert jobs for their companies"
  on public.jobs for insert
  with check (
    auth.uid() = user_id and
    exists (
      select 1 from public.companies
      where companies.id = jobs.company_id
      and companies.user_id = auth.uid()
    )
  );

create policy "Users can update their own jobs"
  on public.jobs for update
  using (
    auth.uid() = user_id or
    (
      exists (
        select 1 from public.team_members
        where team_members.company_id = jobs.company_id
        and team_members.user_id = auth.uid()
        and team_members.role in ('admin', 'recruiter')
      )
    )
  );

create policy "Users can delete their own jobs"
  on public.jobs for delete
  using (
    auth.uid() = user_id or
    (
      exists (
        select 1 from public.team_members
        where team_members.company_id = jobs.company_id
        and team_members.user_id = auth.uid()
        and team_members.role = 'admin'
      )
    )
  );

-- Candidates: Users can only see candidates for their jobs or jobs from companies they are a team member of
alter table public.candidates enable row level security;

create policy "Users can view their own candidates"
  on public.candidates for select
  using (
    auth.uid() = user_id or
    exists (
      select 1 from public.jobs
      join public.team_members on jobs.company_id = team_members.company_id
      where candidates.job_id = jobs.id
      and team_members.user_id = auth.uid()
    )
  );

create policy "Users can insert candidates for their jobs"
  on public.candidates for insert
  with check (
    auth.uid() = user_id or
    exists (
      select 1 from public.jobs
      join public.team_members on jobs.company_id = team_members.company_id
      where candidates.job_id = jobs.id
      and team_members.user_id = auth.uid()
      and team_members.role in ('admin', 'recruiter')
    )
  );

create policy "Users can update their own candidates"
  on public.candidates for update
  using (
    auth.uid() = user_id or
    exists (
      select 1 from public.jobs
      join public.team_members on jobs.company_id = team_members.company_id
      where candidates.job_id = jobs.id
      and team_members.user_id = auth.uid()
      and team_members.role in ('admin', 'recruiter')
    )
  );

create policy "Users can delete their own candidates"
  on public.candidates for delete
  using (
    auth.uid() = user_id or
    exists (
      select 1 from public.jobs
      join public.team_members on jobs.company_id = team_members.company_id
      where candidates.job_id = jobs.id
      and team_members.user_id = auth.uid()
      and team_members.role = 'admin'
    )
  );

-- Profiles: Users can only see and modify their own profile
alter table public.profiles enable row level security;

create policy "Users can view their own profile"
  on public.profiles for select
  using (auth.uid() = user_id);

create policy "Users can insert their own profile"
  on public.profiles for insert
  with check (auth.uid() = user_id);

create policy "Users can update their own profile"
  on public.profiles for update
  using (auth.uid() = user_id);

-- Team Members: Users can see team members for companies they own or are a member of
alter table public.team_members enable row level security;

create policy "Users can view team members for their companies"
  on public.team_members for select
  using (
    auth.uid() = user_id or
    auth.uid() = invited_by or
    exists (
      select 1 from public.companies
      where companies.id = team_members.company_id
      and companies.user_id = auth.uid()
    ) or
    exists (
      select 1 from public.team_members as tm
      where tm.company_id = team_members.company_id
      and tm.user_id = auth.uid()
    )
  );

create policy "Users can insert team members for their companies"
  on public.team_members for insert
  with check (
    auth.uid() = invited_by and
    (
      exists (
        select 1 from public.companies
        where companies.id = team_members.company_id
        and companies.user_id = auth.uid()
      ) or
      exists (
        select 1 from public.team_members as tm
        where tm.company_id = team_members.company_id
        and tm.user_id = auth.uid()
        and tm.role = 'admin'
      )
    )
  );

create policy "Users can update team members for their companies"
  on public.team_members for update
  using (
    auth.uid() = user_id or
    auth.uid() = invited_by or
    exists (
      select 1 from public.companies
      where companies.id = team_members.company_id
      and companies.user_id = auth.uid()
    ) or
    exists (
      select 1 from public.team_members as tm
      where tm.company_id = team_members.company_id
      and tm.user_id = auth.uid()
      and tm.role = 'admin'
    )
  );

create policy "Users can delete team members for their companies"
  on public.team_members for delete
  using (
    auth.uid() = invited_by or
    exists (
      select 1 from public.companies
      where companies.id = team_members.company_id
      and companies.user_id = auth.uid()
    ) or
    exists (
      select 1 from public.team_members as tm
      where tm.company_id = team_members.company_id
      and tm.user_id = auth.uid()
      and tm.role = 'admin'
    )
  );

-- Notifications: Users can only see and modify their own notifications
alter table public.notifications enable row level security;

create policy "Users can view their own notifications"
  on public.notifications for select
  using (auth.uid() = user_id);

create policy "Users can insert notifications for themselves"
  on public.notifications for insert
  with check (auth.uid() = user_id);

create policy "Users can update their own notifications"
  on public.notifications for update
  using (auth.uid() = user_id);

create policy "Users can delete their own notifications"
  on public.notifications for delete
  using (auth.uid() = user_id);

-- Create function to handle new user signup
create or replace function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (user_id, full_name, subscription_tier, subscription_status)
  values (new.id, new.raw_user_meta_data->>'full_name', 'starter', 'active');
  return new;
end;
$$ language plpgsql security definer;

-- Trigger the function every time a user is created
create trigger on_auth_user_created
  after insert on auth.users
  for each row execute procedure public.handle_new_user();
