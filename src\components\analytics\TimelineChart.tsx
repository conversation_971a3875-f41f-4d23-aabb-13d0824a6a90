import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle, CardDescription } from '@/components/ui/card';
import { TimelineDataPoint } from '@/services/supabase/dashboard';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface TimelineChartProps {
  evaluations: TimelineDataPoint[];
  candidates: TimelineDataPoint[];
  title: string;
  description?: string;
  period: 'week' | 'month' | 'year';
  onPeriodChange: (period: 'week' | 'month' | 'year') => void;
}

export const TimelineChart: React.FC<TimelineChartProps> = ({
  evaluations,
  candidates,
  title,
  description,
  period,
  onPeriodChange
}) => {
  // Combine data for the chart
  const chartData = React.useMemo(() => {
    // Create a map of all dates
    const dateMap: Record<string, { date: string; evaluations: number; candidates: number }> = {};
    
    // Add evaluation data
    evaluations.forEach(item => {
      dateMap[item.date] = {
        date: formatDateForDisplay(item.date, period),
        evaluations: item.count,
        candidates: 0
      };
    });
    
    // Add candidate data
    candidates.forEach(item => {
      if (dateMap[item.date]) {
        dateMap[item.date].candidates = item.count;
      } else {
        dateMap[item.date] = {
          date: formatDateForDisplay(item.date, period),
          evaluations: 0,
          candidates: item.count
        };
      }
    });
    
    // Convert map to array and sort by date
    return Object.values(dateMap).sort((a, b) => a.date.localeCompare(b.date));
  }, [evaluations, candidates, period]);
  
  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle className="text-gray-800 text-lg">{title}</CardTitle>
          {description && <CardDescription>{description}</CardDescription>}
        </div>
        <Select value={period} onValueChange={(value) => onPeriodChange(value as 'week' | 'month' | 'year')}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="week">Last Week</SelectItem>
            <SelectItem value="month">Last Month</SelectItem>
            <SelectItem value="year">Last Year</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="h-80 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={chartData}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey="candidates"
                name="CV Uploads"
                stroke="#3b82f6"
                activeDot={{ r: 8 }}
              />
              <Line
                type="monotone"
                dataKey="evaluations"
                name="Evaluations"
                stroke="#8b5cf6"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
        
        {/* Summary */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-gray-500">Total CV Uploads</p>
              <p className="text-xl font-semibold">
                {candidates.reduce((sum, item) => sum + item.count, 0)}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Evaluations</p>
              <p className="text-xl font-semibold">
                {evaluations.reduce((sum, item) => sum + item.count, 0)}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper function to format dates for display
function formatDateForDisplay(dateStr: string, period: 'week' | 'month' | 'year'): string {
  // For YYYY-MM format (year period)
  if (dateStr.length === 7) {
    const [year, month] = dateStr.split('-');
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
  }
  
  // For YYYY-MM-DD format (week/month period)
  const date = new Date(dateStr);
  
  if (period === 'week') {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  } else if (period === 'year') {
    return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
  } else {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }
}
