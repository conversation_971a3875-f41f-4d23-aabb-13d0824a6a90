import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import PlanFeatureShowcase from '@/components/PlanFeatureShowcase';
import { EmailNotificationSettings } from '@/components/settings/EmailNotificationSettings';
import { EmailNotificationTester } from '@/components/debug/EmailNotificationTester';
import {
  Save,
  Globe,
  Bell,
  Shield,
  CreditCard,
  Lock,
  User,
  Palette,
  Star,
  Mail
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';

const Settings = () => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // In a real app, this would be an API call to update settings
      console.log('Updating settings...');

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      toast({
        title: 'Settings updated',
        description: 'Your settings have been updated successfully.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error updating your settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-2xl font-bold text-white">Settings</h1>
        </div>

        <Tabs defaultValue="general">
          <TabsList className="bg-[#1a2035] border-b border-gray-800 w-full justify-start">
            <TabsTrigger value="general" className="data-[state=active]:bg-[#2a2f3d] data-[state=active]:text-white">
              <Globe className="mr-2 h-4 w-4" /> General
            </TabsTrigger>
            <TabsTrigger value="appearance" className="data-[state=active]:bg-[#2a2f3d] data-[state=active]:text-white">
              <Palette className="mr-2 h-4 w-4" /> Appearance
            </TabsTrigger>
            <TabsTrigger value="notifications" className="data-[state=active]:bg-[#2a2f3d] data-[state=active]:text-white">
              <Bell className="mr-2 h-4 w-4" /> Notifications
            </TabsTrigger>
            <TabsTrigger value="email-notifications" className="data-[state=active]:bg-[#2a2f3d] data-[state=active]:text-white">
              <Mail className="mr-2 h-4 w-4" /> Email Notifications
            </TabsTrigger>
            <TabsTrigger value="security" className="data-[state=active]:bg-[#2a2f3d] data-[state=active]:text-white">
              <Shield className="mr-2 h-4 w-4" /> Security
            </TabsTrigger>
            <TabsTrigger value="billing" className="data-[state=active]:bg-[#2a2f3d] data-[state=active]:text-white">
              <CreditCard className="mr-2 h-4 w-4" /> Billing
            </TabsTrigger>
            <TabsTrigger value="features" className="data-[state=active]:bg-[#2a2f3d] data-[state=active]:text-white">
              <Star className="mr-2 h-4 w-4" /> Plan Features
            </TabsTrigger>
          </TabsList>

          {/* General Settings Tab */}
          <TabsContent value="general" className="mt-6">
            <Card className="bg-[#1a2035] border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white text-lg">General Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <form onSubmit={handleSubmit}>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="language" className="text-white">Language</Label>
                      <Select defaultValue="en">
                        <SelectTrigger className="bg-[#141b2d] border-gray-700 text-white">
                          <SelectValue placeholder="Select language" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#1a2035] border-gray-700 text-white">
                          <SelectItem value="en">English</SelectItem>
                          <SelectItem value="fr">French</SelectItem>
                          <SelectItem value="de">German</SelectItem>
                          <SelectItem value="es">Spanish</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="timezone" className="text-white">Timezone</Label>
                      <Select defaultValue="utc">
                        <SelectTrigger className="bg-[#141b2d] border-gray-700 text-white">
                          <SelectValue placeholder="Select timezone" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#1a2035] border-gray-700 text-white">
                          <SelectItem value="utc">UTC (Coordinated Universal Time)</SelectItem>
                          <SelectItem value="est">EST (Eastern Standard Time)</SelectItem>
                          <SelectItem value="cst">CST (Central Standard Time)</SelectItem>
                          <SelectItem value="pst">PST (Pacific Standard Time)</SelectItem>
                          <SelectItem value="gmt">GMT (Greenwich Mean Time)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="date-format" className="text-white">Date Format</Label>
                      <Select defaultValue="mdy">
                        <SelectTrigger className="bg-[#141b2d] border-gray-700 text-white">
                          <SelectValue placeholder="Select date format" />
                        </SelectTrigger>
                        <SelectContent className="bg-[#1a2035] border-gray-700 text-white">
                          <SelectItem value="mdy">MM/DD/YYYY</SelectItem>
                          <SelectItem value="dmy">DD/MM/YYYY</SelectItem>
                          <SelectItem value="ymd">YYYY/MM/DD</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex justify-end mt-6">
                      <Button
                        type="submit"
                        className="bg-recruiter-lightblue hover:bg-blue-500"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="mr-2 h-4 w-4" /> Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Appearance Tab */}
          <TabsContent value="appearance" className="mt-6">
            <Card className="bg-[#1a2035] border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white text-lg">Appearance Settings</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <form onSubmit={handleSubmit}>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label className="text-white">Theme</Label>
                      <div className="grid grid-cols-3 gap-4">
                        <div className="relative">
                          <input
                            type="radio"
                            id="theme-dark"
                            name="theme"
                            className="sr-only"
                            defaultChecked
                          />
                          <label
                            htmlFor="theme-dark"
                            className="block p-3 bg-[#141b2d] border border-gray-700 rounded-lg cursor-pointer hover:border-recruiter-lightblue"
                          >
                            <div className="h-20 bg-[#1a2035] rounded-md mb-2"></div>
                            <span className="text-white font-medium">Dark</span>
                          </label>
                          <div className="absolute top-2 right-2 w-4 h-4 rounded-full bg-recruiter-lightblue"></div>
                        </div>

                        <div className="relative">
                          <input
                            type="radio"
                            id="theme-light"
                            name="theme"
                            className="sr-only"
                          />
                          <label
                            htmlFor="theme-light"
                            className="block p-3 bg-[#141b2d] border border-gray-700 rounded-lg cursor-pointer hover:border-recruiter-lightblue"
                          >
                            <div className="h-20 bg-gray-100 rounded-md mb-2"></div>
                            <span className="text-white font-medium">Light</span>
                          </label>
                        </div>

                        <div className="relative">
                          <input
                            type="radio"
                            id="theme-system"
                            name="theme"
                            className="sr-only"
                          />
                          <label
                            htmlFor="theme-system"
                            className="block p-3 bg-[#141b2d] border border-gray-700 rounded-lg cursor-pointer hover:border-recruiter-lightblue"
                          >
                            <div className="h-20 bg-gradient-to-r from-[#1a2035] to-gray-100 rounded-md mb-2"></div>
                            <span className="text-white font-medium">System</span>
                          </label>
                        </div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label className="text-white">Accent Color</Label>
                      <div className="grid grid-cols-5 gap-4">
                        <div className="relative">
                          <input
                            type="radio"
                            id="color-blue"
                            name="accent-color"
                            className="sr-only"
                            defaultChecked
                          />
                          <label
                            htmlFor="color-blue"
                            className="block h-10 bg-blue-500 rounded-md cursor-pointer hover:ring-2 hover:ring-white"
                          ></label>
                          <div className="absolute top-3 right-3 w-4 h-4 rounded-full bg-white"></div>
                        </div>

                        <div className="relative">
                          <input
                            type="radio"
                            id="color-purple"
                            name="accent-color"
                            className="sr-only"
                          />
                          <label
                            htmlFor="color-purple"
                            className="block h-10 bg-purple-500 rounded-md cursor-pointer hover:ring-2 hover:ring-white"
                          ></label>
                        </div>

                        <div className="relative">
                          <input
                            type="radio"
                            id="color-green"
                            name="accent-color"
                            className="sr-only"
                          />
                          <label
                            htmlFor="color-green"
                            className="block h-10 bg-green-500 rounded-md cursor-pointer hover:ring-2 hover:ring-white"
                          ></label>
                        </div>

                        <div className="relative">
                          <input
                            type="radio"
                            id="color-red"
                            name="accent-color"
                            className="sr-only"
                          />
                          <label
                            htmlFor="color-red"
                            className="block h-10 bg-red-500 rounded-md cursor-pointer hover:ring-2 hover:ring-white"
                          ></label>
                        </div>

                        <div className="relative">
                          <input
                            type="radio"
                            id="color-orange"
                            name="accent-color"
                            className="sr-only"
                          />
                          <label
                            htmlFor="color-orange"
                            className="block h-10 bg-orange-500 rounded-md cursor-pointer hover:ring-2 hover:ring-white"
                          ></label>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end mt-6">
                      <Button
                        type="submit"
                        className="bg-recruiter-lightblue hover:bg-blue-500"
                      >
                        <Save className="mr-2 h-4 w-4" /> Save Changes
                      </Button>
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Notifications Tab */}
          <TabsContent value="notifications" className="mt-6">
            <Card className="bg-[#1a2035] border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white text-lg">Notification Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p className="text-gray-300">
                    Manage your notification preferences to control how and when you receive alerts.
                  </p>

                  <div className="bg-white p-6 rounded-lg border border-gray-200 text-center shadow-sm">
                    <Bell className="h-12 w-12 mx-auto mb-4 text-recruiter-lightblue" />
                    <h3 className="text-lg font-medium text-gray-800 mb-2">Notification Preferences</h3>
                    <p className="text-gray-500 mb-4">
                      Configure detailed notification settings including email preferences, in-app notifications, and more.
                    </p>
                    <Button
                      className="bg-recruiter-lightblue hover:bg-blue-500"
                      onClick={() => window.location.href = '/dashboard/notifications'}
                    >
                      <Bell className="mr-2 h-4 w-4" /> Manage Notification Preferences
                    </Button>
                  </div>

                  <div className="flex items-center justify-between py-3 border-b border-gray-800">
                    <div>
                      <h3 className="text-white font-medium">Email Notifications</h3>
                      <p className="text-gray-400 text-sm">Receive email notifications for important updates</p>
                    </div>
                    <div className="relative inline-block w-12 h-6 rounded-full bg-gray-700">
                      <input
                        type="checkbox"
                        id="email-toggle"
                        className="sr-only"
                        defaultChecked
                      />
                      <label
                        htmlFor="email-toggle"
                        className="absolute inset-0 rounded-full bg-gray-700 cursor-pointer"
                      >
                        <span
                          className="absolute inset-y-0 left-0 w-6 h-6 rounded-full bg-recruiter-lightblue transform translate-x-6"
                        ></span>
                      </label>
                    </div>
                  </div>

                  <div className="flex items-center justify-between py-3 border-b border-gray-800">
                    <div>
                      <h3 className="text-white font-medium">Browser Notifications</h3>
                      <p className="text-gray-400 text-sm">Receive notifications in your browser</p>
                    </div>
                    <div className="relative inline-block w-12 h-6 rounded-full bg-gray-700">
                      <input
                        type="checkbox"
                        id="browser-toggle"
                        className="sr-only"
                        defaultChecked
                      />
                      <label
                        htmlFor="browser-toggle"
                        className="absolute inset-0 rounded-full bg-gray-700 cursor-pointer"
                      >
                        <span
                          className="absolute inset-y-0 left-0 w-6 h-6 rounded-full bg-recruiter-lightblue transform translate-x-6"
                        ></span>
                      </label>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Security Tab */}
          <TabsContent value="security" className="mt-6">
            <Card className="bg-[#1a2035] border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white text-lg">Security Settings</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit}>
                  <div className="space-y-6">
                    <div className="space-y-4">
                      <h3 className="text-white font-medium">Change Password</h3>

                      <div className="space-y-2">
                        <Label htmlFor="current-password" className="text-white">Current Password</Label>
                        <Input
                          id="current-password"
                          type="password"
                          className="bg-[#141b2d] border-gray-700 text-white"
                        />
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="new-password" className="text-white">New Password</Label>
                          <Input
                            id="new-password"
                            type="password"
                            className="bg-[#141b2d] border-gray-700 text-white"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label htmlFor="confirm-password" className="text-white">Confirm New Password</Label>
                          <Input
                            id="confirm-password"
                            type="password"
                            className="bg-[#141b2d] border-gray-700 text-white"
                          />
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          type="button"
                          className="bg-recruiter-lightblue hover:bg-blue-500"
                        >
                          <Lock className="mr-2 h-4 w-4" /> Update Password
                        </Button>
                      </div>
                    </div>

                    <div className="border-t border-gray-800 pt-6 space-y-4">
                      <h3 className="text-white font-medium">Two-Factor Authentication</h3>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-gray-300">Enhance your account security by enabling two-factor authentication.</p>
                          <p className="text-gray-400 text-sm mt-1">You'll be asked for an additional authentication code when you sign in.</p>
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          className="border-gray-700 text-white hover:bg-[#2a2f3d]"
                        >
                          Enable 2FA
                        </Button>
                      </div>
                    </div>

                    <div className="border-t border-gray-800 pt-6 space-y-4">
                      <h3 className="text-white font-medium">Sessions</h3>

                      <div className="bg-[#141b2d] p-4 rounded-lg border border-gray-800">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-white">Current Session</h4>
                            <p className="text-gray-400 text-sm">Windows 10 • Chrome • New York, USA</p>
                          </div>
                          <div className="text-green-500 text-sm font-medium">Active Now</div>
                        </div>
                      </div>

                      <div className="bg-[#141b2d] p-4 rounded-lg border border-gray-800">
                        <div className="flex items-center justify-between">
                          <div>
                            <h4 className="text-white">Mobile App</h4>
                            <p className="text-gray-400 text-sm">iOS 15 • Sourcio.ai App • New York, USA</p>
                          </div>
                          <div className="text-gray-400 text-sm">Last active: 2 days ago</div>
                        </div>
                      </div>

                      <div className="flex justify-end">
                        <Button
                          type="button"
                          variant="outline"
                          className="border-gray-700 text-white hover:bg-[#2a2f3d]"
                        >
                          Sign Out All Devices
                        </Button>
                      </div>
                    </div>
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Billing Tab */}
          <TabsContent value="billing" className="mt-6">
            <Card className="bg-[#1a2035] border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white text-lg">Billing Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div className="bg-[#141b2d] p-4 rounded-lg border border-gray-800">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-white font-medium">Current Plan</h3>
                      <Badge className="bg-blue-500/20 text-blue-500 hover:bg-blue-500/30">
                        Growth Plan
                      </Badge>
                    </div>

                    <div className="flex items-baseline mb-2">
                      <span className="text-2xl font-bold text-white">$149</span>
                      <span className="text-gray-400 ml-1">/month</span>
                    </div>

                    <p className="text-gray-400 text-sm mb-4">Your plan renews on October 15, 2023</p>

                    <div className="flex gap-2">
                      <Button
                        type="button"
                        className="bg-recruiter-lightblue hover:bg-blue-500"
                      >
                        Upgrade Plan
                      </Button>
                      <Button
                        type="button"
                        variant="outline"
                        className="border-gray-700 text-white hover:bg-[#2a2f3d]"
                      >
                        Cancel Subscription
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-white font-medium">Payment Information</h3>
                    <div className="bg-[#141b2d] p-4 rounded-lg border border-gray-800">
                      <p className="text-gray-400 text-sm">
                        Payment methods are managed directly through Stripe and PayPal during checkout.
                        Visit the <a href="/dashboard/billing" className="text-blue-400 hover:underline">billing dashboard</a> to view your payment history and invoices.
                      </p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-white font-medium">Billing History</h3>

                    <div className="bg-[#141b2d] rounded-lg border border-gray-800 overflow-hidden">
                      <div className="grid grid-cols-4 gap-4 p-4 border-b border-gray-800 text-gray-400 text-sm font-medium">
                        <div>Date</div>
                        <div>Description</div>
                        <div>Amount</div>
                        <div className="text-right">Receipt</div>
                      </div>

                      <div className="grid grid-cols-4 gap-4 p-4 border-b border-gray-800">
                        <div className="text-gray-300">Sep 15, 2023</div>
                        <div className="text-gray-300">Growth Plan - Monthly</div>
                        <div className="text-gray-300">$149.00</div>
                        <div className="text-right">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="text-recruiter-lightblue hover:text-blue-500 hover:bg-transparent"
                          >
                            Download
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-4 gap-4 p-4 border-b border-gray-800">
                        <div className="text-gray-300">Aug 15, 2023</div>
                        <div className="text-gray-300">Growth Plan - Monthly</div>
                        <div className="text-gray-300">$149.00</div>
                        <div className="text-right">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="text-recruiter-lightblue hover:text-blue-500 hover:bg-transparent"
                          >
                            Download
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-4 gap-4 p-4">
                        <div className="text-gray-300">Jul 15, 2023</div>
                        <div className="text-gray-300">Growth Plan - Monthly</div>
                        <div className="text-gray-300">$149.00</div>
                        <div className="text-right">
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="text-recruiter-lightblue hover:text-blue-500 hover:bg-transparent"
                          >
                            Download
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Email Notifications Tab */}
          <TabsContent value="email-notifications" className="mt-6">
            <div className="space-y-6">
              <EmailNotificationSettings />

              {/* Debug Tester */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-medium text-white mb-4">Email Notification Tester</h3>
                <EmailNotificationTester />
              </div>
            </div>
          </TabsContent>

          {/* Plan Features Tab */}
          <TabsContent value="features" className="mt-6">
            <Card className="bg-[#1a2035] border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white">Plan Features</CardTitle>
                <p className="text-gray-400">
                  View and manage features available on your current subscription plan
                </p>
              </CardHeader>
              <CardContent>
                <PlanFeatureShowcase />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </DashboardLayout>
  );
};

export default Settings;
