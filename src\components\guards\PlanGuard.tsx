import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useSubscription } from '@/hooks/use-profile';
import { FEATURES, PLAN_FEATURES } from '@/config/paypal';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Lock, ArrowRight } from 'lucide-react';

interface PlanGuardProps {
  children: React.ReactNode;
  feature: keyof typeof FEATURES;
  featureName?: string;
  fallback?: React.ReactNode;
  /** Show a compact inline message instead of full card (default: false) */
  compact?: boolean;
  /** Minimum plan required for this feature */
  minimumPlan?: 'STARTER' | 'GROWTH' | 'PRO';
}

/**
 * PlanGuard component
 *
 * Restricts access to features based on the user's subscription plan.
 * Uses the PLAN_FEATURES configuration to determine if a feature is available.
 *
 * @example
 * <PlanGuard feature="INTERVIEW_SCHEDULING" featureName="Interview Scheduling">
 *   <InterviewSchedulingComponent />
 * </PlanGuard>
 */
const PlanGuard: React.FC<PlanGuardProps> = ({
  children,
  feature,
  featureName,
  fallback,
  compact = false,
  minimumPlan
}) => {
  const { user, isLoading } = useAuth();
  const { hasRole, isLoading: permissionsLoading } = usePermissions();
  const { data: subscription, isLoading: subscriptionLoading } = useSubscription();
  const navigate = useNavigate();

  // Get the feature key from FEATURES constant
  const featureKey = FEATURES[feature];
  
  // Get user's current plan
  const currentPlan = subscription?.tier?.toUpperCase() as keyof typeof PLAN_FEATURES || 'STARTER';
  
  // Check if user has access to this feature
  // Platform admins always have access
  const isPlatformAdmin = hasRole(['platform_admin']);
  const hasFeatureAccess = isPlatformAdmin || PLAN_FEATURES[currentPlan]?.[featureKey];

  // Get the display name for the feature
  const displayName = featureName || feature.toLowerCase().replace(/_/g, ' ');

  // Get the minimum plan required if not explicitly provided
  const getMinimumPlan = (): string => {
    if (minimumPlan) return minimumPlan;
    
    // Find the lowest plan that has this feature
    const plans = ['STARTER', 'GROWTH', 'PRO'] as const;
    for (const plan of plans) {
      if (PLAN_FEATURES[plan][featureKey]) {
        return plan;
      }
    }
    return 'PRO'; // Default to PRO if feature not found
  };

  const requiredPlan = getMinimumPlan();

  if (isLoading || permissionsLoading || subscriptionLoading) {
    return (
      <div className="flex justify-center items-center p-8">
        <div className="animate-pulse text-gray-500">Loading...</div>
      </div>
    );
  }

  if (!hasFeatureAccess) {
    // If a custom fallback is provided, use it
    if (fallback) {
      return <>{fallback}</>;
    }

    // Compact version - single line with button
    if (compact) {
      return (
        <div className="py-2 px-3 bg-blue-50 border border-blue-200 rounded-md text-blue-800 text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Lock className="h-4 w-4 flex-shrink-0" />
            <span>{displayName} requires {requiredPlan.toLowerCase()} plan</span>
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={() => navigate('/dashboard/pricing')}
            className="h-7 border-blue-300 text-blue-800 hover:bg-blue-100"
          >
            Upgrade
          </Button>
        </div>
      );
    }

    // Full card version
    return (
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardHeader className="text-center">
          <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 mb-4">
            <Lock className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl capitalize">{displayName}</CardTitle>
          <CardDescription className="text-lg">
            This feature requires the {requiredPlan.toLowerCase()} plan or higher
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <p className="text-gray-600">
            Upgrade to the {requiredPlan.toLowerCase()} plan to unlock {displayName} and other advanced features.
          </p>
          <div className="flex justify-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard/pricing')}
              className="gap-2"
            >
              <ArrowRight className="h-4 w-4" />
              View Plans
            </Button>
            <Button
              variant="default"
              onClick={() => navigate('/dashboard')}
            >
              Return to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  // If user has access, render children
  return <>{children}</>;
};

export default PlanGuard;
