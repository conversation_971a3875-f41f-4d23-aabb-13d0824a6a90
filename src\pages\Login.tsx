import React, { useState } from 'react';
import { Link, useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { ArrowRight, Mail, Lock } from 'lucide-react';
import { EmailVerificationReminder } from '@/components/auth/EmailVerificationReminder';

// Define form schema with Zod
const formSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type FormValues = z.infer<typeof formSchema>;

const Login = () => {
  const { login, user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const location = useLocation();
  const [isLoading, setIsLoading] = useState(false);
  const [emailVerificationError, setEmailVerificationError] = useState(false);
  const [unverifiedEmail, setUnverifiedEmail] = useState('');

  const [searchParams] = useSearchParams();
  const returnTo = searchParams.get('returnTo') || '/dashboard';
  const from = location.state?.from?.pathname || returnTo;

  // Initialize form with react-hook-form and zod validation
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);
    setEmailVerificationError(false);

    try {
      await login(data.email, data.password);
      toast({
        title: 'Login successful',
        description: 'Welcome back to Sourcio.ai!',
      });

      // Use a small delay to allow auth context to update with user data
      setTimeout(() => {
        // Get fresh user data from Supabase
        import('@/lib/supabase').then(({ supabase }) => {
          supabase.auth.getUser().then(({ data: { user: currentUser } }) => {
            if (currentUser?.user_metadata?.user_type === 'candidate') {
              navigate('/candidate-dashboard', { replace: true });
            } else {
              navigate(from, { replace: true });
            }
          });
        });
      }, 200);

    } catch (error) {
      // Check if it's an email verification error
      if (error instanceof Error &&
          error.message.includes('confirm your email')) {
        setEmailVerificationError(true);
        setUnverifiedEmail(data.email);
      }

      toast({
        title: 'Login failed',
        description: error instanceof Error ? error.message : 'Please check your credentials and try again',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-hero-gradient flex flex-col">
      {/* Header with logo */}
      <header className="container mx-auto px-6 py-5">
        <Link to="/" className="flex items-center">
          <img src="/logo.png" alt="RecruitAI Logo" className="h-10" />
        </Link>
      </header>

      {/* Main content */}
      <div className="flex-1 flex items-center justify-center px-6 py-12">
        <div className="w-full max-w-md space-y-8 bg-card-gradient p-8 rounded-xl border border-gray-800 shadow-xl">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white">Welcome back</h1>
            <p className="mt-2 text-gray-400">Sign in to your account to continue</p>
          </div>

          {emailVerificationError && unverifiedEmail && (
            <EmailVerificationReminder
              email={unverifiedEmail}
              onVerified={() => {
                setEmailVerificationError(false);
                toast({
                  title: 'Email verified',
                  description: 'You can now log in with your credentials.',
                });
              }}
            />
          )}

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Email</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          placeholder="<EMAIL>"
                          className="pl-10 bg-[#1a2035] border-gray-700 text-white"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          type="password"
                          placeholder="••••••••"
                          className="pl-10 bg-[#1a2035] border-gray-700 text-white"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex items-center justify-between">
                <Link
                  to="/forgot-password"
                  className="text-sm text-recruiter-blue hover:text-blue-400 transition"
                >
                  Forgot your password?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full bg-recruiter-lightblue hover:bg-blue-500"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                    Signing in...
                  </div>
                ) : (
                  <div className="flex items-center">
                    Sign In <ArrowRight className="ml-2 h-4 w-4" />
                  </div>
                )}
              </Button>
            </form>
          </Form>

          <div className="mt-6 text-center">
            <p className="text-gray-400">
              Don't have an account?{' '}
              <Link to="/signup" className="text-recruiter-blue hover:text-blue-400 transition">
                Sign up
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
