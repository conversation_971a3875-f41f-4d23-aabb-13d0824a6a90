/**
 * React Query hooks for notification data
 */

import { useAuth } from '@/contexts/AuthContext';
import { useEnhancedQuery, useEnhancedMutation, queryKeys } from '@/lib/queryUtils';
import * as notificationService from '@/services/supabase/notifications';
import { Notification, NotificationInsert } from '@/services/supabase/notifications';
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook to fetch all notifications
 */
export function useNotifications() {
  return useEnhancedQuery<Notification[]>(
    queryKeys.notifications.all,
    () => notificationService.getNotifications(),
    {
      fallbackData: [],
      errorMessage: 'Failed to load notifications',
    }
  );
}

/**
 * Hook to fetch unread notifications count
 */
export function useUnreadNotificationsCount() {
  return useEnhancedQuery<number>(
    ['unreadNotificationsCount'],
    () => notificationService.getUnreadNotificationsCount(),
    {
      fallbackData: 0,
      errorMessage: 'Failed to load unread notifications count',
    }
  );
}

/**
 * Hook to create a new notification
 */
export function useCreateNotification() {
  const { user } = useAuth();
  
  return useEnhancedMutation<Notification, NotificationInsert>(
    (notification) => notificationService.createNotification({
      ...notification,
      user_id: notification.user_id || user?.id,
    }),
    {
      errorMessage: 'Failed to create notification',
      invalidateQueries: [queryKeys.notifications.all, ['unreadNotificationsCount']],
    }
  );
}

/**
 * Hook to mark a notification as read
 */
export function useMarkNotificationAsRead() {
  return useEnhancedMutation<Notification, string>(
    (id) => notificationService.markNotificationAsRead(id),
    {
      errorMessage: 'Failed to mark notification as read',
      invalidateQueries: [queryKeys.notifications.all, ['unreadNotificationsCount']],
    }
  );
}

/**
 * Hook to mark all notifications as read
 */
export function useMarkAllNotificationsAsRead() {
  const { user } = useAuth();

  return useEnhancedMutation<boolean, void>(
    () => notificationService.markAllNotificationsAsRead(user?.id || ''),
    {
      errorMessage: 'Failed to mark all notifications as read',
      successMessage: 'All notifications marked as read',
      invalidateQueries: [queryKeys.notifications.all, ['unreadNotificationsCount']],
    }
  );
}

/**
 * Hook to delete a notification
 */
export function useDeleteNotification() {
  return useEnhancedMutation<boolean, string>(
    (id) => notificationService.deleteNotification(id),
    {
      errorMessage: 'Failed to delete notification',
      invalidateQueries: [queryKeys.notifications.all, ['unreadNotificationsCount']],
    }
  );
}

/**
 * Hook to delete all notifications
 */
export function useDeleteAllNotifications() {
  const { user } = useAuth();
  
  return useEnhancedMutation<boolean, void>(
    () => notificationService.deleteAllNotifications(user?.id || ''),
    {
      errorMessage: 'Failed to delete all notifications',
      successMessage: 'All notifications deleted',
      invalidateQueries: [queryKeys.notifications.all, ['unreadNotificationsCount']],
    }
  );
}

/**
 * Hook to subscribe to real-time notifications
 */
export function useNotificationSubscription() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  
  useEffect(() => {
    if (!user?.id) return;
    
    const subscription = notificationService.subscribeToNotifications(
      user.id,
      (notification) => {
        // Update notifications list
        queryClient.setQueryData<Notification[]>(
          queryKeys.notifications.all,
          (old) => [notification, ...(old || [])]
        );
        
        // Update unread count
        queryClient.setQueryData<number>(
          ['unreadNotificationsCount'],
          (old) => (old || 0) + 1
        );
      }
    );
    
    return () => {
      subscription.unsubscribe();
    };
  }, [user?.id, queryClient]);
}
