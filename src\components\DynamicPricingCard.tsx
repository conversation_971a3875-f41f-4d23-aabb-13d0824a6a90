import React from 'react';
import { motion } from 'framer-motion';
import { Check, X, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';
import { DynamicPricingTier, DynamicPricingFeature } from '@/hooks/use-dynamic-pricing';

interface DynamicPricingCardProps {
  tier: DynamicPricingTier;
  isPopular?: boolean;
  color?: string;
  index?: number;
}

const DynamicPricingCard: React.FC<DynamicPricingCardProps> = ({ 
  tier, 
  isPopular = false, 
  color = 'blue',
  index = 0 
}) => {
  const colorClasses = {
    blue: {
      gradient: 'from-blue-500 to-blue-600',
      border: 'border-blue-200',
      text: 'text-blue-600',
      bg: 'bg-blue-50'
    },
    purple: {
      gradient: 'from-purple-500 to-purple-600',
      border: 'border-purple-200',
      text: 'text-purple-600',
      bg: 'bg-purple-50'
    },
    green: {
      gradient: 'from-green-500 to-green-600',
      border: 'border-green-200',
      text: 'text-green-600',
      bg: 'bg-green-50'
    }
  };

  const currentColor = colorClasses[color as keyof typeof colorClasses] || colorClasses.blue;

  const FeatureItem = ({ feature }: { feature: DynamicPricingFeature }) => (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ delay: 0.1 * index + 0.05 }}
      className="flex items-start space-x-3 py-2"
    >
      {feature.enabled ? (
        <Check className="h-5 w-5 text-green-500 mt-0.5 flex-shrink-0" />
      ) : (
        <X className="h-5 w-5 text-gray-300 mt-0.5 flex-shrink-0" />
      )}
      <span 
        className={`text-sm ${
          feature.enabled 
            ? feature.highlighted 
              ? 'text-gray-900 font-medium' 
              : 'text-gray-700'
            : 'text-gray-400 line-through'
        }`}
      >
        {feature.text}
        {feature.highlighted && feature.enabled && (
          <Sparkles className="inline h-4 w-4 ml-1 text-yellow-500" />
        )}
      </span>
    </motion.div>
  );

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: 0.1 * index }}
      className={`relative bg-white rounded-2xl shadow-xl border-2 ${
        isPopular ? 'border-purple-300 scale-105' : currentColor.border
      } overflow-hidden group hover:shadow-2xl transition-all duration-300`}
    >
      {/* Popular Badge */}
      {isPopular && (
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
          <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-1 rounded-full text-sm font-medium shadow-lg">
            Most Popular
          </div>
        </div>
      )}

      <div className="p-8">
        {/* Header */}
        <div className="text-center mb-6">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.title}</h3>
          <div className="flex items-center justify-center mb-4">
            <span className="text-4xl font-bold text-gray-900">${tier.price}</span>
            <span className="text-gray-500 ml-2">/{tier.period}</span>
          </div>
          <p className="text-gray-600">{tier.description}</p>
        </div>

        {/* Features */}
        <div className="space-y-1 mb-8">
          {tier.features.map((feature, featureIndex) => (
            <FeatureItem key={featureIndex} feature={feature} />
          ))}
        </div>

        {/* CTA Button */}
        <motion.div
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <Link
            to="/signup"
            className={`block w-full text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 ${
              isPopular
                ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600 shadow-lg'
                : `bg-gradient-to-r ${currentColor.gradient} text-white hover:shadow-lg`
            }`}
          >
            Get Started
          </Link>
        </motion.div>

        {/* Feature Count Badge */}
        <div className="mt-4 text-center">
          <span className={`inline-block px-3 py-1 rounded-full text-xs font-medium ${currentColor.bg} ${currentColor.text}`}>
            {tier.features.filter(f => f.enabled).length} features included
          </span>
        </div>
      </div>

      {/* Hover Effect */}
      <div className={`absolute inset-0 bg-gradient-to-r ${currentColor.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-300 pointer-events-none`} />
    </motion.div>
  );
};

export default DynamicPricingCard;
