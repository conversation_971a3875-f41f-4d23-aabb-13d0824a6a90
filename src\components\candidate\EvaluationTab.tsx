import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Plus,
  ChevronDown,
  ChevronRight,
  Star,
  Calendar,
  CalendarClock,
  Building,
  Filter,
  Briefcase,
  BarChart3
} from 'lucide-react';
import { Evaluation } from '@/services/supabase/evaluations';
import { JobSpecificMatch } from '@/services/cv-processing/jobMatcher';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Check, AlertCircle } from 'lucide-react';

// Extended interface for job matches with company information
interface ExtendedJobMatch extends JobSpecificMatch {
  companyName: string;
  evaluationId: string;
  evaluationMode: string;
}
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import CVEvaluationResults from '@/components/cv/CVEvaluationResults';
import { Loader2 } from 'lucide-react';
import ProPlanGuard from '@/components/guards/ProPlanGuard';

interface EvaluationTabProps {
  evaluations: Evaluation[];
  selectedEvaluation: Evaluation | null;
  evaluationData: any;
  isLoadingEvaluations: boolean;
  handleEvaluationSelect: (evaluationId: string) => void;
  setIsEvaluateModalOpen: (isOpen: boolean) => void;
  setIsScheduleInterviewModalOpen: (isOpen: boolean) => void;
  candidate: any;
}

// Helper function to get color based on match score
const getMatchScoreColor = (score: number) => {
  if (score >= 90) return 'text-green-500';
  if (score >= 75) return 'text-blue-500';
  if (score >= 60) return 'text-amber-500';
  return 'text-red-500';
};

const EvaluationTab: React.FC<EvaluationTabProps> = ({
  evaluations,
  selectedEvaluation,
  evaluationData,
  isLoadingEvaluations,
  handleEvaluationSelect,
  setIsEvaluateModalOpen,
  setIsScheduleInterviewModalOpen,
  candidate
}) => {
  // State for filters
  const [companyFilter, setCompanyFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date-desc');

  // Parse job-specific matches from evaluation summary
  const getJobSpecificMatches = (evaluation: Evaluation) => {
    try {
      const summary = JSON.parse(evaluation.evaluation_summary);


      // Case 1: jobSpecificMatches exists in summary.matchResult
      if (summary.matchResult && summary.matchResult.jobSpecificMatches &&
          summary.matchResult.jobSpecificMatches.length > 0) {
        // Make sure each job has strengths and gaps from matchResult if available
        return summary.matchResult.jobSpecificMatches.map(job => ({
          ...job,
          strengths: job.strengths || summary.matchResult.strengths || [],
          gaps: job.gaps || summary.matchResult.gaps || []
        }));
      }

      // Case 2: topMatchingJobs exists in summary.matchResult
      if (summary.matchResult && summary.matchResult.topMatchingJobs &&
          summary.matchResult.topMatchingJobs.length > 0) {
        // Make sure each job has strengths and gaps from matchResult if available
        return summary.matchResult.topMatchingJobs.map(job => ({
          ...job,
          strengths: job.strengths || summary.matchResult.strengths || [],
          gaps: job.gaps || summary.matchResult.gaps || []
        }));
      }

      // Case 3: jobSpecificMatches exists directly in summary
      if (summary.jobSpecificMatches && summary.jobSpecificMatches.length > 0) {
        // Make sure each job has strengths and gaps from summary if available
        return summary.jobSpecificMatches.map(job => ({
          ...job,
          strengths: job.strengths || summary.strengths || [],
          gaps: job.gaps || summary.gaps || summary.weaknesses || []
        }));
      }

      // Case 4: topMatchingJobs exists directly in summary
      if (summary.topMatchingJobs && summary.topMatchingJobs.length > 0) {
        // Make sure each job has strengths and gaps from summary if available
        return summary.topMatchingJobs.map(job => ({
          ...job,
          strengths: job.strengths || summary.strengths || [],
          gaps: job.gaps || summary.gaps || summary.weaknesses || []
        }));
      }

      // Case 5: For specific job evaluations, create a synthetic job match
      if (evaluation.evaluation_mode === 'specific-job' ||
          (summary.position && (summary.overallMatch || summary.overallScore))) {

        // Extract data from the evaluation
        const jobTitle = evaluation.job_title || summary.position || 'Specific Job';
        const overallScore = evaluation.evaluation_score ||
                            summary.overallMatch ||
                            summary.overallScore || 0;

        // Extract skill scores if available
        let skillsScore = 70;
        if (summary.skillsMatch && summary.skillsMatch.score) {
          skillsScore = summary.skillsMatch.score;
        } else if (summary.skills && summary.skills.length > 0) {
          skillsScore = Math.round(summary.skills.reduce((sum, skill) => sum + (skill.match || 0), 0) / summary.skills.length);
        }

        // Extract experience score if available
        let experienceScore = 70;
        if (summary.experienceMatch && summary.experienceMatch.score) {
          experienceScore = summary.experienceMatch.score;
        } else if (summary.experience && summary.experience.length > 0) {
          experienceScore = Math.round(summary.experience.reduce((sum, exp) => sum + (exp.relevance || 0), 0) / summary.experience.length);
        }

        // Extract education score if available
        let educationScore = 70;
        if (summary.educationMatch && summary.educationMatch.score) {
          educationScore = summary.educationMatch.score;
        } else if (summary.education && summary.education.length > 0) {
          educationScore = Math.round(summary.education.reduce((sum, edu) => sum + (edu.relevance || 0), 0) / summary.education.length);
        }

        // Create a synthetic job match
        const syntheticJob = {
          jobId: evaluation.job_id || 'specific-job',
          jobTitle: jobTitle,
          overallScore: overallScore,
          skillsScore: skillsScore,
          experienceScore: experienceScore,
          educationScore: educationScore,
          locationScore: 70,
          strengths: summary.matchResult?.strengths || summary.strengths || [],
          gaps: summary.matchResult?.gaps || summary.gaps || summary.weaknesses || [],
          recommendation: summary.matchResult?.recommendation || summary.recommendation || ''
        };

        return [syntheticJob];
      }

      return [];
    } catch (error) {
      console.error('Error parsing job matches:', error);

      // For specific job evaluations, create a synthetic job match even if parsing fails
      if (evaluation.evaluation_mode === 'specific-job') {
        // Try to extract data from the evaluation even if parsing fails
        let strengths = [];
        let gaps = [];
        let recommendation = '';

        try {
          // Try to extract data from the evaluation_data field if available
          if (evaluation.evaluation_data) {
            const evalData = JSON.parse(evaluation.evaluation_data);
            strengths = evalData.strengths || evalData.matchResult?.strengths || [];
            gaps = evalData.gaps || evalData.weaknesses || evalData.matchResult?.gaps || [];
            recommendation = evalData.recommendation || evalData.matchResult?.recommendation || '';
          }
        } catch (e) {
          console.error('Error parsing evaluation_data:', e);
        }

        const syntheticJob = {
          jobId: evaluation.job_id || 'specific-job',
          jobTitle: evaluation.job_title || 'Specific Job',
          overallScore: evaluation.evaluation_score || 0,
          skillsScore: 70,
          experienceScore: 70,
          educationScore: 70,
          locationScore: 70,
          strengths: strengths,
          gaps: gaps,
          recommendation: recommendation
        };

        return [syntheticJob];
      }

      return [];
    }
  };

  // Extract unique companies from evaluations
  const companies = [...new Set(evaluations.map(e => e.company_name).filter(Boolean))];



  // Filter and sort evaluations
  const filteredEvaluations = evaluations
    .filter(e => companyFilter === 'all' || e.company_name === companyFilter)
    .filter(e => {
      if (dateFilter === 'all') return true;
      const evalDate = new Date(e.created_at);
      const now = new Date();

      if (dateFilter === 'today') {
        return evalDate.toDateString() === now.toDateString();
      } else if (dateFilter === 'week') {
        const weekAgo = new Date();
        weekAgo.setDate(now.getDate() - 7);
        return evalDate >= weekAgo;
      } else if (dateFilter === 'month') {
        const monthAgo = new Date();
        monthAgo.setMonth(now.getMonth() - 1);
        return evalDate >= monthAgo;
      }
      return true;
    })
    .sort((a, b) => {
      if (sortBy === 'date-desc') {
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      } else if (sortBy === 'date-asc') {
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      } else if (sortBy === 'score-desc') {
        return b.evaluation_score - a.evaluation_score;
      } else if (sortBy === 'score-asc') {
        return a.evaluation_score - b.evaluation_score;
      }
      return 0;
    });



  // Extract all job matches from all evaluations
  const allJobMatches: ExtendedJobMatch[] = filteredEvaluations.flatMap(evaluation => {
    const jobMatches = getJobSpecificMatches(evaluation);

    // Add company information to each job match
    return jobMatches.map(match => ({
      ...match,
      companyName: match.companyName || evaluation.company_name || 'Unknown Company',
      evaluationId: evaluation.id,
      evaluationMode: evaluation.evaluation_mode
    }));
  });



  // Sort job matches by score if needed
  const sortedJobMatches = [...allJobMatches].sort((a, b) => {
    if (sortBy === 'score-desc') {
      return b.overallScore - a.overallScore;
    } else if (sortBy === 'score-asc') {
      return a.overallScore - b.overallScore;
    }
    return 0;
  });

  // Get evaluation mode display text
  const getEvaluationModeText = (mode: string) => {
    switch (mode) {
      case 'specific-job': return 'Specific Job';
      case 'all-company-jobs': return 'All Jobs in Company';
      case 'all-companies': return 'All Companies';
      default: return mode;
    }
  };

  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-gray-800 text-lg">Evaluations</CardTitle>
        <div className="flex gap-2">
          <ProPlanGuard featureName="Interview Scheduling">
            <Button
              variant="outline"
              onClick={() => setIsScheduleInterviewModalOpen(true)}
            >
              <CalendarClock className="mr-2 h-4 w-4" /> Schedule Interview
            </Button>
          </ProPlanGuard>
          <Button
            className="bg-recruiter-lightblue hover:bg-blue-500"
            onClick={() => setIsEvaluateModalOpen(true)}
          >
            <Plus className="mr-2 h-4 w-4" /> New Evaluation
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {isLoadingEvaluations ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">Loading evaluations...</span>
          </div>
        ) : filteredEvaluations.length > 0 ? (
          <div className="space-y-6">
            {/* Filters */}
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex items-center">
                <Building className="mr-2 h-4 w-4 text-gray-500" />
                <Select value={companyFilter} onValueChange={setCompanyFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by company" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Companies</SelectItem>
                    {companies.map(company => (
                      <SelectItem key={company} value={company}>{company}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by date" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Time</SelectItem>
                    <SelectItem value="today">Today</SelectItem>
                    <SelectItem value="week">Last 7 Days</SelectItem>
                    <SelectItem value="month">Last 30 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4 text-gray-500" />
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Date (Newest First)</SelectItem>
                    <SelectItem value="date-asc">Date (Oldest First)</SelectItem>
                    <SelectItem value="score-desc">Score (Highest First)</SelectItem>
                    <SelectItem value="score-asc">Score (Lowest First)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* All Job Matches Table */}
            {sortedJobMatches.length > 0 ? (
              <div className="mt-4">
                <h3 className="text-md font-medium mb-2">Individual Job Matches</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2 px-4 font-medium text-gray-600">Company</th>
                        <th className="text-left py-2 px-4 font-medium text-gray-600">Job Title</th>
                        <th className="text-left py-2 px-4 font-medium text-gray-600">Score</th>
                        <th className="text-left py-2 px-4 font-medium text-gray-600">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {sortedJobMatches.map((match, index) => (
                        <React.Fragment key={index}>
                          <tr className="border-b hover:bg-gray-100">
                            <td className="py-2 px-4">{match.companyName}</td>
                            <td className="py-2 px-4">{match.jobTitle}</td>
                            <td className="py-2 px-4">
                              <div className="flex items-center">
                                <span className={`font-medium ${getMatchScoreColor(match.overallScore)}`}>
                                  {match.overallScore}%
                                </span>
                                <Star
                                  className={`ml-1 h-4 w-4 ${getMatchScoreColor(match.overallScore)}`}
                                  fill="currentColor"
                                />
                              </div>
                            </td>
                            <td className="py-2 px-4">
                              <div className="flex gap-1">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="text-xs"
                                  onClick={() => {
                                    const detailRow = document.getElementById(`job-details-${index}`);
                                    if (detailRow) {
                                      detailRow.classList.toggle('hidden');
                                    }
                                  }}
                                >
                                  View Details
                                </Button>
                                <ProPlanGuard featureName="Interview Scheduling">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="text-xs bg-blue-50 hover:bg-blue-100 text-blue-600 border-blue-200"
                                    onClick={() => setIsScheduleInterviewModalOpen(true)}
                                  >
                                    <CalendarClock className="mr-1 h-3 w-3" /> Interview
                                  </Button>
                                </ProPlanGuard>
                              </div>
                            </td>
                          </tr>
                          <tr id={`job-details-${index}`} className="hidden bg-gray-50">
                            <td colSpan={4} className="py-4 px-6">
                              <div className="space-y-4">
                                {/* Score breakdown */}
                                <div>
                                  <h4 className="text-sm font-medium text-gray-700 mb-2">Score Breakdown</h4>
                                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                                    <div>
                                      <p className="text-xs text-gray-500">Overall</p>
                                      <div className="flex items-center">
                                        <span className={`font-medium ${getMatchScoreColor(match.overallScore)}`}>
                                          {match.overallScore}%
                                        </span>
                                        <Progress
                                          value={match.overallScore}
                                          className="h-1.5 w-16 ml-2"
                                          indicatorClassName={
                                            match.overallScore >= 90 ? 'bg-green-500' :
                                            match.overallScore >= 75 ? 'bg-blue-500' :
                                            match.overallScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                                          }
                                        />
                                      </div>
                                    </div>
                                    <div>
                                      <p className="text-xs text-gray-500">Skills</p>
                                      <div className="flex items-center">
                                        <span className={`font-medium ${getMatchScoreColor(match.skillsScore)}`}>
                                          {match.skillsScore}%
                                        </span>
                                        <Progress
                                          value={match.skillsScore}
                                          className="h-1.5 w-16 ml-2"
                                          indicatorClassName={
                                            match.skillsScore >= 90 ? 'bg-green-500' :
                                            match.skillsScore >= 75 ? 'bg-blue-500' :
                                            match.skillsScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                                          }
                                        />
                                      </div>
                                    </div>
                                    <div>
                                      <p className="text-xs text-gray-500">Experience</p>
                                      <div className="flex items-center">
                                        <span className={`font-medium ${getMatchScoreColor(match.experienceScore)}`}>
                                          {match.experienceScore}%
                                        </span>
                                        <Progress
                                          value={match.experienceScore}
                                          className="h-1.5 w-16 ml-2"
                                          indicatorClassName={
                                            match.experienceScore >= 90 ? 'bg-green-500' :
                                            match.experienceScore >= 75 ? 'bg-blue-500' :
                                            match.experienceScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                                          }
                                        />
                                      </div>
                                    </div>
                                    <div>
                                      <p className="text-xs text-gray-500">Education</p>
                                      <div className="flex items-center">
                                        <span className={`font-medium ${getMatchScoreColor(match.educationScore)}`}>
                                          {match.educationScore}%
                                        </span>
                                        <Progress
                                          value={match.educationScore}
                                          className="h-1.5 w-16 ml-2"
                                          indicatorClassName={
                                            match.educationScore >= 90 ? 'bg-green-500' :
                                            match.educationScore >= 75 ? 'bg-blue-500' :
                                            match.educationScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                                          }
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Strengths and gaps */}
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                  <div className="bg-gray-100 p-3 rounded-lg">
                                    <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                                      <Check className="h-4 w-4 text-green-500 mr-1" /> Strengths
                                    </h4>
                                    <ul className="space-y-1">
                                      {match.strengths && match.strengths.length > 0 ? (
                                        match.strengths.map((strength, i) => (
                                          <li key={i} className="text-sm text-gray-600 flex items-start">
                                            <Check className="h-3 w-3 text-green-500 mr-1 mt-0.5" />
                                            <span>{strength}</span>
                                          </li>
                                        ))
                                      ) : (
                                        <li className="text-sm text-gray-500">No specific strengths identified</li>
                                      )}
                                    </ul>
                                  </div>
                                  <div className="bg-gray-100 p-3 rounded-lg">
                                    <h4 className="text-sm font-medium text-gray-700 mb-2 flex items-center">
                                      <AlertCircle className="h-4 w-4 text-amber-500 mr-1" /> Areas for Improvement
                                    </h4>
                                    <ul className="space-y-1">
                                      {match.gaps && match.gaps.length > 0 ? (
                                        match.gaps.map((gap, i) => (
                                          <li key={i} className="text-sm text-gray-600 flex items-start">
                                            <AlertCircle className="h-3 w-3 text-amber-500 mr-1 mt-0.5" />
                                            <span>{gap}</span>
                                          </li>
                                        ))
                                      ) : (
                                        <li className="text-sm text-gray-500">No specific gaps identified</li>
                                      )}
                                    </ul>
                                  </div>
                                </div>

                                {/* Recommendation */}
                                {match.recommendation && (
                                  <div>
                                    <h4 className="text-sm font-medium text-gray-700 mb-2">Recommendation</h4>
                                    <p className="text-sm text-gray-600 bg-gray-100 p-3 rounded-lg">
                                      {match.recommendation}
                                    </p>
                                  </div>
                                )}
                              </div>
                            </td>
                          </tr>
                        </React.Fragment>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            ) : (
              <div className="text-center py-6 bg-gray-50 rounded-lg">
                <p className="text-gray-500">No job matches available</p>
              </div>
            )}

            {/* Detailed evaluation results are now shown in expandable rows */}
          </div>
        ) : (
          <div className="text-center py-8">
            <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800">No evaluations found</h3>
            <p className="text-gray-500 mt-1">
              {companyFilter !== 'all' || dateFilter !== 'all'
                ? 'Try changing your filters to see more results.'
                : 'Evaluate this candidate against jobs or companies to see results here.'}
            </p>
            {companyFilter === 'all' && dateFilter === 'all' && (
              <Button
                className="mt-4 bg-recruiter-lightblue hover:bg-blue-500"
                onClick={() => setIsEvaluateModalOpen(true)}
              >
                <Plus className="mr-2 h-4 w-4" /> Evaluate Now
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default EvaluationTab;
