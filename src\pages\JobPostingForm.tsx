import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useCompanyContext } from '@/contexts/CompanyContext';
import {
  Save,
  X,
  Plus,
  Trash2,
  Calendar,
  DollarSign,
  MapPin,
  Briefcase,
  Building,
  Clock,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import SubscriptionGuard from '@/components/guards/SubscriptionGuard';
import { useCreateJob } from '@/hooks/use-jobs';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanies } from '@/hooks/use-companies';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Job type interface
interface JobFormData {
  title: string;
  department: string;
  location: string;
  locationType: string;
  type: string;
  experience: string;
  salary: {
    min: string;
    max: string;
    currency: string;
    period: string;
    showSalary: boolean;
  };
  description: string;
  requirements: string[];
  responsibilities: string[];
  benefits: string[];
  applicationDeadline: string;
  status: 'draft' | 'active';
}

const JobPostingForm = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { user } = useAuth();
  const createJobMutation = useCreateJob();
  const { data: companies, isLoading: isLoadingCompanies } = useCompanies();
  const { activeCompany, activeCompanyId } = useCompanyContext();

  // Initial form state
  const initialFormData: JobFormData = {
    title: '',
    department: '',
    location: '',
    locationType: 'onsite',
    type: 'full-time',
    experience: '',
    salary: {
      min: '',
      max: '',
      currency: 'USD',
      period: 'yearly',
      showSalary: true
    },
    description: '',
    requirements: [''],
    responsibilities: [''],
    benefits: [''],
    applicationDeadline: '',
    status: 'draft'
  };

  const [formData, setFormData] = useState<JobFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Handle input change for simple fields
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Handle nested salary object
    if (name.startsWith('salary.')) {
      const salaryField = name.split('.')[1];
      setFormData({
        ...formData,
        salary: {
          ...formData.salary,
          [salaryField]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Handle select change
  const handleSelectChange = (value: string, name: string) => {
    // Handle nested salary object
    if (name.startsWith('salary.')) {
      const salaryField = name.split('.')[1];
      setFormData({
        ...formData,
        salary: {
          ...formData.salary,
          [salaryField]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  // Handle checkbox change
  const handleCheckboxChange = (checked: boolean, name: string) => {
    // Handle nested salary object
    if (name.startsWith('salary.')) {
      const salaryField = name.split('.')[1];
      setFormData({
        ...formData,
        salary: {
          ...formData.salary,
          [salaryField]: checked
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: checked
      });
    }
  };

  // Handle array fields (requirements, responsibilities, benefits)
  const handleArrayItemChange = (index: number, value: string, field: 'requirements' | 'responsibilities' | 'benefits') => {
    const newArray = [...formData[field]];
    newArray[index] = value;
    setFormData({
      ...formData,
      [field]: newArray
    });
  };

  // Add new item to array fields
  const addArrayItem = (field: 'requirements' | 'responsibilities' | 'benefits') => {
    setFormData({
      ...formData,
      [field]: [...formData[field], '']
    });
  };

  // Remove item from array fields
  const removeArrayItem = (index: number, field: 'requirements' | 'responsibilities' | 'benefits') => {
    if (formData[field].length > 1) {
      const newArray = [...formData[field]];
      newArray.splice(index, 1);
      setFormData({
        ...formData,
        [field]: newArray
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent, saveAsDraft: boolean = false) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Set status based on saveAsDraft parameter
      const status = saveAsDraft ? 'draft' : 'active';

      // Check if we have any companies
      if (!companies || companies.length === 0) {
        throw new Error('You need to create a company before posting a job');
      }

      // Check if we have an active company
      if (!activeCompanyId && companies.length > 1) {
        throw new Error('Please select an active company before posting a job');
      }

      // Format the data for the API
      const jobData = {
        title: formData.title,
        description: formData.description,
        requirements: formData.requirements.join('\n'),
        location: formData.locationType === 'remote'
          ? `Remote${formData.location ? ` - ${formData.location}` : ''}`
          : `${formData.location} (${formData.locationType})`,
        // Parse salary values to numbers if provided
        salary_min: formData.salary.min ? parseFloat(formData.salary.min) : null,
        salary_max: formData.salary.max ? parseFloat(formData.salary.max) : null,
        job_type: formData.type,
        experience_level: formData.experience,
        status: status,
        user_id: user?.id,
        // Use the active company if available, otherwise use the first company
        company_id: activeCompanyId || companies[0].id,
      };

      console.log('Submitting job data:', jobData);

      // Create the job in the database
      await createJobMutation.mutateAsync(jobData);

      toast({
        title: saveAsDraft ? 'Draft saved' : 'Job posted successfully',
        description: saveAsDraft
          ? 'Your job draft has been saved.'
          : 'Your job has been published and is now visible to candidates.',
      });

      // Navigate back to jobs list
      navigate('/dashboard/jobs');
    } catch (error) {
      console.error('Error creating job:', error);
      toast({
        title: 'Error',
        description: 'There was an error saving the job. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate('/dashboard/jobs');
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Create New Job</h1>
            {activeCompany && (
              <div className="flex items-center mt-1 text-sm text-gray-600">
                <Building className="mr-1 h-4 w-4" />
                <span>Creating job for: <span className="font-medium">{activeCompany.name}</span></span>
              </div>
            )}
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              <X className="mr-2 h-4 w-4" /> Cancel
            </Button>
            <Button
              variant="outline"
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
              onClick={(e) => handleSubmit(e, true)}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                  Saving...
                </div>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" /> Save as Draft
                </>
              )}
            </Button>
            <Button
              className="bg-recruiter-lightblue hover:bg-blue-500"
              onClick={(e) => handleSubmit(e, false)}
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                  Posting...
                </div>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" /> Post Job
                </>
              )}
            </Button>
          </div>
        </div>

        {!activeCompanyId && companies && companies.length > 1 && (
          <Alert className="bg-amber-50 border-amber-200 text-amber-800">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>No active company selected</AlertTitle>
            <AlertDescription>
              You have multiple companies but haven't selected an active company.
              <Button
                variant="link"
                className="text-amber-800 p-0 h-auto ml-2 underline"
                onClick={() => navigate('/dashboard/companies')}
              >
                Go to Company Management
              </Button>
            </AlertDescription>
          </Alert>
        )}

        <SubscriptionGuard limitType="active_jobs" useCompact={true}>
          <form onSubmit={(e) => handleSubmit(e, false)}>
          <div className="grid grid-cols-1 gap-6">
            {/* Basic Information */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title" className="text-gray-700">Job Title <span className="text-red-500">*</span></Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="e.g. Frontend Developer"
                    className="bg-white border-gray-200 text-gray-800"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="department" className="text-gray-700">Department</Label>
                    <Input
                      id="department"
                      name="department"
                      value={formData.department}
                      onChange={handleInputChange}
                      placeholder="e.g. Engineering"
                      className="bg-white border-gray-200 text-gray-800"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location" className="text-gray-700">
                      Location
                      {formData.locationType === 'remote' && (
                        <span className="text-sm text-gray-500 ml-2">(Optional for remote jobs)</span>
                      )}
                    </Label>
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <Input
                          id="location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          placeholder={
                            formData.locationType === 'remote'
                              ? "e.g. Preferred time zone or 'Worldwide'"
                              : "e.g. New York, NY"
                          }
                          className="bg-white border-gray-200 text-gray-800"
                        />
                        {formData.locationType === 'remote' && (
                          <p className="text-xs text-gray-500 mt-1">
                            For remote jobs, you can specify time zone preferences or leave blank for worldwide
                          </p>
                        )}
                      </div>
                      <Select
                        value={formData.locationType}
                        onValueChange={(value) => handleSelectChange(value, 'locationType')}
                      >
                        <SelectTrigger className="w-[140px] bg-white border-gray-200 text-gray-800">
                          <SelectValue placeholder="Location Type" />
                        </SelectTrigger>
                        <SelectContent className="bg-white border-gray-200 text-gray-800">
                          <SelectItem value="onsite">On-site</SelectItem>
                          <SelectItem value="remote">Remote</SelectItem>
                          <SelectItem value="hybrid">Hybrid</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type" className="text-gray-700">Employment Type</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) => handleSelectChange(value, 'type')}
                    >
                      <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                        <SelectValue placeholder="Employment Type" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border-gray-200 text-gray-800">
                        <SelectItem value="full-time">Full-time</SelectItem>
                        <SelectItem value="part-time">Part-time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="internship">Internship</SelectItem>
                        <SelectItem value="temporary">Temporary</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="experience" className="text-gray-700">Experience Level</Label>
                    <Input
                      id="experience"
                      name="experience"
                      value={formData.experience}
                      onChange={handleInputChange}
                      placeholder="e.g. 3-5 years"
                      className="bg-white border-gray-200 text-gray-800"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-700">Salary Range</Label>
                  <div className="flex items-center gap-2 mb-2">
                    <Checkbox
                      id="showSalary"
                      checked={formData.salary.showSalary}
                      onCheckedChange={(checked) =>
                        handleCheckboxChange(checked as boolean, 'salary.showSalary')
                      }
                    />
                    <Label htmlFor="showSalary" className="text-gray-600 text-sm">
                      Display salary range in job posting
                    </Label>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <Select
                        value={formData.salary.currency}
                        onValueChange={(value) => handleSelectChange(value, 'salary.currency')}
                      >
                        <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                          <SelectValue placeholder="Currency" />
                        </SelectTrigger>
                        <SelectContent className="bg-white border-gray-200 text-gray-800">
                          <SelectItem value="USD">USD ($)</SelectItem>
                          <SelectItem value="EUR">EUR (€)</SelectItem>
                          <SelectItem value="GBP">GBP (£)</SelectItem>
                          <SelectItem value="CAD">CAD ($)</SelectItem>
                          <SelectItem value="AUD">AUD ($)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Input
                        id="salary.min"
                        name="salary.min"
                        value={formData.salary.min}
                        onChange={handleInputChange}
                        placeholder="Min"
                        className="bg-white border-gray-200 text-gray-800"
                        type="number"
                        min="0"
                      />
                    </div>
                    <div>
                      <Input
                        id="salary.max"
                        name="salary.max"
                        value={formData.salary.max}
                        onChange={handleInputChange}
                        placeholder="Max"
                        className="bg-white border-gray-200 text-gray-800"
                        type="number"
                        min="0"
                      />
                    </div>
                    <div>
                      <Select
                        value={formData.salary.period}
                        onValueChange={(value) => handleSelectChange(value, 'salary.period')}
                      >
                        <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                          <SelectValue placeholder="Period" />
                        </SelectTrigger>
                        <SelectContent className="bg-white border-gray-200 text-gray-800">
                          <SelectItem value="yearly">Per Year</SelectItem>
                          <SelectItem value="monthly">Per Month</SelectItem>
                          <SelectItem value="hourly">Per Hour</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="applicationDeadline" className="text-gray-700">Application Deadline</Label>
                  <Input
                    id="applicationDeadline"
                    name="applicationDeadline"
                    value={formData.applicationDeadline}
                    onChange={handleInputChange}
                    type="date"
                    className="bg-white border-gray-200 text-gray-800"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Job Description */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Job Description</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="description" className="text-gray-700">Description <span className="text-red-500">*</span></Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Provide a detailed description of the job..."
                    className="bg-white border-gray-200 text-gray-800 min-h-[150px]"
                    required
                  />
                </div>
              </CardContent>
            </Card>

            {/* Requirements */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Requirements</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.requirements.map((requirement, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={requirement}
                      onChange={(e) => handleArrayItemChange(index, e.target.value, 'requirements')}
                      placeholder={`Requirement ${index + 1}`}
                      className="bg-white border-gray-200 text-gray-800 flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => removeArrayItem(index, 'requirements')}
                      disabled={formData.requirements.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  className="border-gray-200 text-gray-700 hover:bg-gray-100"
                  onClick={() => addArrayItem('requirements')}
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Requirement
                </Button>
              </CardContent>
            </Card>

            {/* Responsibilities */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Responsibilities</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.responsibilities.map((responsibility, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={responsibility}
                      onChange={(e) => handleArrayItemChange(index, e.target.value, 'responsibilities')}
                      placeholder={`Responsibility ${index + 1}`}
                      className="bg-white border-gray-200 text-gray-800 flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => removeArrayItem(index, 'responsibilities')}
                      disabled={formData.responsibilities.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  className="border-gray-200 text-gray-700 hover:bg-gray-100"
                  onClick={() => addArrayItem('responsibilities')}
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Responsibility
                </Button>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Benefits</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.benefits.map((benefit, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={benefit}
                      onChange={(e) => handleArrayItemChange(index, e.target.value, 'benefits')}
                      placeholder={`Benefit ${index + 1}`}
                      className="bg-white border-gray-200 text-gray-800 flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => removeArrayItem(index, 'benefits')}
                      disabled={formData.benefits.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  className="border-gray-200 text-gray-700 hover:bg-gray-100"
                  onClick={() => addArrayItem('benefits')}
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Benefit
                </Button>
              </CardContent>
            </Card>
          </div>
        </form>
        </SubscriptionGuard>
      </div>
    </DashboardLayout>
  );
};

export default JobPostingForm;
