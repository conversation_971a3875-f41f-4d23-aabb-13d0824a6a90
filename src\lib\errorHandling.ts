/**
 * Error handling utilities for consistent error management across the application
 */

import { PostgrestError } from '@supabase/supabase-js';

/**
 * Custom error class for API errors
 */
export class ApiError extends Error {
  public statusCode: number;
  public isOperational: boolean;
  public originalError?: Error;

  constructor(message: string, statusCode = 500, isOperational = true, originalError?: Error) {
    super(message);
    this.name = 'ApiError';
    this.statusCode = statusCode;
    this.isOperational = isOperational;
    this.originalError = originalError;
    
    // Capture stack trace
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Custom error class for Supabase errors
 */
export class SupabaseError extends ApiError {
  constructor(message: string, originalError?: PostgrestError) {
    super(
      message,
      originalError?.code === 'PGRST116' ? 404 : 500,
      true,
      originalError as Error
    );
    this.name = 'SupabaseError';
  }
}

/**
 * Handle Supabase errors consistently
 * @param error The error to handle
 * @param defaultMessage Default message to use if error doesn't have one
 * @returns A standardized error object
 */
export function handleSupabaseError(error: unknown, defaultMessage = 'Database operation failed'): SupabaseError {
  if (error instanceof PostgrestError) {
    // Handle specific PostgrestError codes
    if (error.code === 'PGRST116') {
      return new SupabaseError('Resource not found', error);
    }
    return new SupabaseError(error.message || defaultMessage, error);
  }
  
  if (error instanceof Error) {
    return new SupabaseError(error.message || defaultMessage, undefined);
  }
  
  return new SupabaseError(defaultMessage);
}

/**
 * Safely execute a database operation with consistent error handling
 * @param operation The async database operation to execute
 * @param errorMessage Custom error message if operation fails
 * @param defaultValue Optional default value to return on error
 * @returns The result of the operation or the default value if it fails
 */
export async function safeDbOperation<T>(
  operation: () => Promise<T>,
  errorMessage: string,
  defaultValue?: T
): Promise<T> {
  try {
    return await operation();
  } catch (error) {
    console.error(`${errorMessage}:`, error);
    
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    
    throw handleSupabaseError(error, errorMessage);
  }
}
