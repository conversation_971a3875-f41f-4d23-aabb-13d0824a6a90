-- Add new columns to profiles table
ALTER TABLE profiles 
ADD COLUMN IF NOT EXISTS banned BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS email TEXT,
ADD COLUMN IF NOT EXISTS last_sign_in_at TIMESTAMPTZ;

-- Update email column with data from auth.users (this would be done in a real migration)
-- Note: This is just a placeholder. In a real migration, you would need to use a function
-- that has access to auth.users, which requires admin privileges.
-- COMMENT OUT THIS LINE IN PRODUCTION:
-- UPDATE profiles SET email = (SELECT email FROM auth.users WHERE auth.users.id = profiles.user_id);

-- Create or update RLS policies
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Policy for platform admins to see all profiles
DROP POLICY IF EXISTS "Platform admins can view all profiles" ON profiles;
CREATE POLICY "Platform admins can view all profiles" 
ON profiles FOR SELECT 
TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM profiles AS admin_profile 
    WHERE admin_profile.user_id = auth.uid() 
    AND admin_profile.platform_admin = TRUE
  )
);

-- Policy for users to see their own profile
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
CREATE POLICY "Users can view own profile" 
ON profiles FOR SELECT 
TO authenticated 
USING (auth.uid() = user_id);

-- Policy for platform admins to update any profile
DROP POLICY IF EXISTS "Platform admins can update any profile" ON profiles;
CREATE POLICY "Platform admins can update any profile" 
ON profiles FOR UPDATE
TO authenticated 
USING (
  EXISTS (
    SELECT 1 FROM profiles AS admin_profile 
    WHERE admin_profile.user_id = auth.uid() 
    AND admin_profile.platform_admin = TRUE
  )
);

-- Policy for users to update their own profile
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
CREATE POLICY "Users can update own profile" 
ON profiles FOR UPDATE
TO authenticated 
USING (auth.uid() = user_id);
