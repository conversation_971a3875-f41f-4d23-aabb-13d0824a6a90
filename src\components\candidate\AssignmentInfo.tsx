import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Building2, 
  Briefcase, 
  Star, 
  Calendar,
  User,
  TrendingUp
} from 'lucide-react';
import { CandidateAssignment } from '@/services/supabase/assignments';

interface AssignmentInfoProps {
  assignments: CandidateAssignment[];
  onViewAssignment?: (assignment: CandidateAssignment) => void;
  onUpdateStatus?: (assignmentId: string, newStatus: string) => void;
  compact?: boolean;
}

const AssignmentInfo: React.FC<AssignmentInfoProps> = ({
  assignments,
  onViewAssignment,
  onUpdateStatus,
  compact = false
}) => {
  if (!assignments || assignments.length === 0) {
    return null;
  }

  // Get match score color
  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 80) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (score >= 70) return 'text-amber-600 bg-amber-50 border-amber-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-500';
      case 'screening': return 'bg-yellow-500';
      case 'interview': return 'bg-purple-500';
      case 'offer': return 'bg-orange-500';
      case 'hired': return 'bg-green-500';
      case 'rejected': return 'bg-red-500';
      default: return 'bg-gray-500';
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  if (compact) {
    // Compact view for candidate cards
    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Briefcase className="h-4 w-4" />
          <span>Assigned to {assignments.length} job{assignments.length > 1 ? 's' : ''}</span>
        </div>
        <div className="flex flex-wrap gap-1">
          {assignments.slice(0, 3).map((assignment) => (
            <Badge
              key={assignment.id}
              variant="outline"
              className={`text-xs ${getMatchScoreColor(assignment.match_score)}`}
            >
              {assignment.match_score}% match
            </Badge>
          ))}
          {assignments.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{assignments.length - 3} more
            </Badge>
          )}
        </div>
      </div>
    );
  }

  // Full view for detailed assignment information
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <Briefcase className="h-5 w-5" />
          Job Assignments ({assignments.length})
        </h3>
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <TrendingUp className="h-4 w-4" />
          Avg: {Math.round(assignments.reduce((sum, a) => sum + a.match_score, 0) / assignments.length)}%
        </div>
      </div>

      <div className="grid gap-3">
        {assignments.map((assignment) => (
          <Card key={assignment.id} className="border border-gray-200 hover:shadow-md transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-medium text-gray-900">
                      {(assignment as any).jobs?.title || 'Job Title Not Available'}
                    </h4>
                    <Badge 
                      className={getStatusColor(assignment.status)}
                      variant="secondary"
                    >
                      {assignment.status}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                    <div className="flex items-center gap-1">
                      <Building2 className="h-4 w-4" />
                      <span>{(assignment as any).companies?.name || 'Company Name Not Available'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>Assigned {formatDate(assignment.created_at)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>By {(assignment as any).assigned_by_user?.email || 'System'}</span>
                    </div>
                  </div>

                  {assignment.notes && (
                    <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                      {assignment.notes}
                    </p>
                  )}
                </div>

                <div className="flex flex-col items-end gap-2 ml-4">
                  <div className={`px-3 py-1 rounded-full border text-sm font-medium ${getMatchScoreColor(assignment.match_score)}`}>
                    <div className="flex items-center gap-1">
                      <Star className="h-3 w-3" />
                      {assignment.match_score}%
                    </div>
                  </div>

                  <div className="flex gap-1">
                    {onViewAssignment && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onViewAssignment(assignment)}
                        className="text-xs"
                      >
                        View
                      </Button>
                    )}
                    {onUpdateStatus && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onUpdateStatus(assignment.id, getNextStatus(assignment.status))}
                        className="text-xs"
                      >
                        Update
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

// Helper function to get next logical status
const getNextStatus = (currentStatus: string): string => {
  const statusFlow = {
    'new': 'screening',
    'screening': 'interview',
    'interview': 'offer',
    'offer': 'hired',
    'hired': 'hired', // Final state
    'rejected': 'rejected' // Final state
  };
  
  return statusFlow[currentStatus as keyof typeof statusFlow] || currentStatus;
};

export default AssignmentInfo;
