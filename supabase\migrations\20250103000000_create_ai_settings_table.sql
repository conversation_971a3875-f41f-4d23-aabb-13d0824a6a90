-- Create AI settings table for configurable AI provider settings
CREATE TABLE IF NOT EXISTS public.ai_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    provider TEXT NOT NULL CHECK (provider IN ('groq', 'anthropic', 'openai')),
    model TEXT NOT NULL,
    api_key TEXT NOT NULL, -- Will be encrypted at application level
    is_active BOOLEAN NOT NULL DEFAULT true,
    metadata JSONB DEFAULT '{}',
    created_by UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    updated_by UUID REFERENCES auth.users(id) ON DELETE SET NULL
);

-- Create unique constraint to ensure only one active setting per provider
CREATE UNIQUE INDEX IF NOT EXISTS ai_settings_provider_active_unique
ON public.ai_settings (provider)
WHERE is_active = true;

-- Enable RLS
ALTER TABLE public.ai_settings ENABLE ROW LEVEL SECURITY;

-- Create policy for platform admins only
CREATE POLICY "Platform admins can manage AI settings" ON public.ai_settings
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.platform_admin = true
        )
    );

-- Create policy for platform admins to insert
CREATE POLICY "Platform admins can insert AI settings" ON public.ai_settings
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.profiles
            WHERE profiles.user_id = auth.uid()
            AND profiles.platform_admin = true
        )
    );

-- Create function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_ai_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    NEW.updated_by = auth.uid();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update the updated_at timestamp
CREATE TRIGGER update_ai_settings_updated_at_trigger
    BEFORE UPDATE ON public.ai_settings
    FOR EACH ROW
    EXECUTE FUNCTION update_ai_settings_updated_at();

-- Insert default GROQ settings (using placeholder for API key)
INSERT INTO public.ai_settings (provider, model, api_key, is_active, metadata)
VALUES (
    'groq',
    'llama-3.3-70b-versatile',
    'PLACEHOLDER_API_KEY', -- This will be replaced by the admin interface
    true,
    '{"description": "Default GROQ configuration", "max_tokens": 32768, "temperature": 1}'
) ON CONFLICT DO NOTHING;

-- Grant necessary permissions
GRANT ALL ON public.ai_settings TO authenticated;
GRANT ALL ON public.ai_settings TO service_role;
