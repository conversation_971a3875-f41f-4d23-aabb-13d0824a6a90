-- Create billing_info table
CREATE TABLE IF NOT EXISTS public.billing_info (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    company_name TEXT,
    address_line1 TEXT NOT NULL,
    address_line2 TEXT,
    city TEXT NOT NULL,
    state TEXT NOT NULL,
    postal_code TEXT NOT NULL,
    country TEXT NOT NULL,
    tax_id TEXT,
    billing_email TEXT NOT NULL,
    phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(user_id)
);

-- Add indexes for billing_info
CREATE INDEX IF NOT EXISTS idx_billing_info_user_id ON public.billing_info(user_id);

-- Add RLS policies for billing_info
ALTER TABLE public.billing_info ENABLE ROW LEVEL SECURITY;

-- Users can view their own billing info
CREATE POLICY "Users can view their own billing info"
ON public.billing_info
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Users can create their own billing info
CREATE POLICY "Users can create their own billing info"
ON public.billing_info
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Users can update their own billing info
CREATE POLICY "Users can update their own billing info"
ON public.billing_info
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

-- Users can delete their own billing info
CREATE POLICY "Users can delete their own billing info"
ON public.billing_info
FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- Create payment_methods table
CREATE TABLE IF NOT EXISTS public.payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    payment_method_id TEXT NOT NULL,
    payment_type TEXT NOT NULL,
    provider TEXT NOT NULL,
    last_four TEXT NOT NULL,
    expiry_date TEXT,
    is_default BOOLEAN DEFAULT false NOT NULL,
    billing_details JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(user_id, payment_method_id)
);

-- Add indexes for payment_methods
CREATE INDEX IF NOT EXISTS idx_payment_methods_user_id ON public.payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_payment_methods_payment_method_id ON public.payment_methods(payment_method_id);

-- Add RLS policies for payment_methods
ALTER TABLE public.payment_methods ENABLE ROW LEVEL SECURITY;

-- Users can view their own payment methods
CREATE POLICY "Users can view their own payment methods"
ON public.payment_methods
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Users can create their own payment methods
CREATE POLICY "Users can create their own payment methods"
ON public.payment_methods
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Users can update their own payment methods
CREATE POLICY "Users can update their own payment methods"
ON public.payment_methods
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

-- Users can delete their own payment methods
CREATE POLICY "Users can delete their own payment methods"
ON public.payment_methods
FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- Create invoices table
CREATE TABLE IF NOT EXISTS public.invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    invoice_number TEXT NOT NULL,
    amount DECIMAL(10, 2) NOT NULL,
    currency TEXT NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('draft', 'sent', 'paid', 'void', 'overdue')),
    issue_date TIMESTAMP WITH TIME ZONE NOT NULL,
    due_date TIMESTAMP WITH TIME ZONE NOT NULL,
    payment_date TIMESTAMP WITH TIME ZONE,
    subscription_id TEXT,
    payment_id TEXT,
    invoice_items JSONB DEFAULT '[]'::jsonb,
    billing_info JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(invoice_number)
);

-- Add indexes for invoices
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_invoices_invoice_number ON public.invoices(invoice_number);
CREATE INDEX IF NOT EXISTS idx_invoices_subscription_id ON public.invoices(subscription_id);
CREATE INDEX IF NOT EXISTS idx_invoices_status ON public.invoices(status);

-- Add RLS policies for invoices
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;

-- Users can view their own invoices
CREATE POLICY "Users can view their own invoices"
ON public.invoices
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Only service role can create invoices
CREATE POLICY "Service role can create invoices"
ON public.invoices
FOR INSERT
TO service_role
WITH CHECK (true);

-- Only service role can update invoices
CREATE POLICY "Service role can update invoices"
ON public.invoices
FOR UPDATE
TO service_role
USING (true);

-- Add trigger to update updated_at timestamp
CREATE TRIGGER update_billing_info_updated_at
BEFORE UPDATE ON public.billing_info
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_payment_methods_updated_at
BEFORE UPDATE ON public.payment_methods
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_invoices_updated_at
BEFORE UPDATE ON public.invoices
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE public.billing_info IS 'Stores billing information for users';
COMMENT ON TABLE public.payment_methods IS 'Stores payment methods for users';
COMMENT ON TABLE public.invoices IS 'Stores invoices for payments and subscriptions';
