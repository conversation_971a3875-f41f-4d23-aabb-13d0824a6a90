import React from 'react';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface GradientStatCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  description?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  gradientFrom: string;
  gradientTo: string;
  className?: string;
}

export const GradientStatCard: React.FC<GradientStatCardProps> = ({
  title,
  value,
  icon: Icon,
  description,
  trend,
  gradientFrom,
  gradientTo,
  className,
}) => {
  return (
    <div 
      className={cn(
        "relative overflow-hidden rounded-xl p-6 shadow-lg transition-all duration-300 hover:shadow-xl hover:-translate-y-1",
        className
      )}
      style={{
        background: `linear-gradient(135deg, ${gradientFrom}, ${gradientTo})`,
      }}
    >
      {/* Background pattern */}
      <div className="absolute top-0 right-0 w-32 h-32 opacity-10">
        <Icon size={128} className="text-white" />
      </div>
      
      {/* Content */}
      <div className="relative z-10">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-sm font-medium text-white/80">{title}</h3>
          <div className="bg-white/20 p-2 rounded-lg">
            <Icon size={18} className="text-white" />
          </div>
        </div>
        
        <div className="text-3xl font-bold text-white mb-1">{value}</div>
        
        {description && (
          <p className="text-white/70 text-sm">{description}</p>
        )}
        
        {trend && (
          <div className="mt-4 inline-flex items-center px-2 py-1 rounded-full bg-white/20 text-white text-xs">
            {trend.isPositive ? '↑' : '↓'} {Math.abs(trend.value)}% from last month
          </div>
        )}
      </div>
    </div>
  );
};
