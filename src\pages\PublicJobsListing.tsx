import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { usePublicJobs } from '@/hooks/use-jobs';
import { CandidatePortalHeader } from '@/components/CandidatePortalHeader';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { 
  Search, 
  MapPin, 
  Building, 
  Clock, 
  DollarSign, 
  Filter,
  Briefcase,
  Users,
  ArrowRight
} from 'lucide-react';

const PublicJobsListing = () => {
  const { data: jobs = [], isLoading, error } = usePublicJobs();
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [jobTypeFilter, setJobTypeFilter] = useState('');

  // Filter and search jobs
  const filteredJobs = useMemo(() => {
    return jobs.filter(job => {
      const matchesSearch = !searchTerm || 
        job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        job.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (job.company_name && job.company_name.toLowerCase().includes(searchTerm.toLowerCase()));
      
      const matchesLocation = !locationFilter || 
        (job.location && job.location.toLowerCase().includes(locationFilter.toLowerCase()));
      
      const matchesJobType = !jobTypeFilter || job.job_type === jobTypeFilter;
      
      return matchesSearch && matchesLocation && matchesJobType;
    });
  }, [jobs, searchTerm, locationFilter, jobTypeFilter]);

  // Get unique values for filters
  const uniqueLocations = useMemo(() => {
    const locations = jobs.map(job => job.location).filter(Boolean);
    return [...new Set(locations)];
  }, [jobs]);

  const uniqueJobTypes = useMemo(() => {
    const types = jobs.map(job => job.job_type).filter(Boolean);
    return [...new Set(types)];
  }, [jobs]);

  const formatSalary = (job: any) => {
    if (job.salary_range) return job.salary_range;
    if (job.salary_min && job.salary_max) {
      return `$${job.salary_min.toLocaleString()} - $${job.salary_max.toLocaleString()}`;
    }
    if (job.salary_min) return `From $${job.salary_min.toLocaleString()}`;
    return null;
  };

  const getJobTypeColor = (type: string) => {
    switch (type?.toLowerCase()) {
      case 'full-time': return 'bg-green-100 text-green-800';
      case 'part-time': return 'bg-blue-100 text-blue-800';
      case 'contract': return 'bg-purple-100 text-purple-800';
      case 'freelance': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">Unable to load jobs</h2>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <CandidatePortalHeader />

      {/* Hero Section */}
      <div className="bg-gradient-to-br from-[#0A0C1B] to-[#1a1f3a] text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            <h1 className="text-4xl font-bold mb-4">Find Your Dream Job</h1>
            <p className="text-xl text-gray-300">
              Discover amazing opportunities from top companies
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search and Filters */}
        <div className="mb-8 space-y-4">
          <div className="flex flex-col lg:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                placeholder="Search jobs, companies, or keywords..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Location Filter */}
            <div className="w-full lg:w-64">
              <select
                value={locationFilter}
                onChange={(e) => setLocationFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Locations</option>
                {uniqueLocations.map(location => (
                  <option key={location} value={location}>{location}</option>
                ))}
              </select>
            </div>
            
            {/* Job Type Filter */}
            <div className="w-full lg:w-48">
              <select
                value={jobTypeFilter}
                onChange={(e) => setJobTypeFilter(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">All Types</option>
                {uniqueJobTypes.map(type => (
                  <option key={type} value={type}>{type}</option>
                ))}
              </select>
            </div>
          </div>
          
          {/* Results count */}
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-600">
              {isLoading ? 'Loading...' : `${filteredJobs.length} job${filteredJobs.length !== 1 ? 's' : ''} found`}
            </p>
          </div>
        </div>

        {/* Jobs Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {isLoading ? (
            // Loading skeletons
            Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="h-64">
                <CardHeader>
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-2/3" />
                </CardContent>
              </Card>
            ))
          ) : filteredJobs.length === 0 ? (
            <div className="col-span-full text-center py-12">
              <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No jobs found</h3>
              <p className="text-gray-600">Try adjusting your search criteria.</p>
            </div>
          ) : (
            filteredJobs.map((job) => (
              <Card key={job.id} className="hover:shadow-lg transition-shadow cursor-pointer group">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg group-hover:text-blue-600 transition-colors">
                        {job.title}
                      </CardTitle>
                      <div className="flex items-center mt-2 text-gray-600">
                        <Building className="h-4 w-4 mr-1" />
                        <span className="text-sm">{job.company_name || job.companies?.name}</span>
                      </div>
                    </div>
                    {job.companies?.logo_url && (
                      <img 
                        src={job.companies.logo_url} 
                        alt={`${job.company_name} logo`}
                        className="h-10 w-10 rounded object-cover"
                      />
                    )}
                  </div>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-3">
                    {/* Job details */}
                    <div className="flex flex-wrap gap-2">
                      {job.job_type && (
                        <Badge className={getJobTypeColor(job.job_type)}>
                          {job.job_type}
                        </Badge>
                      )}
                      {job.experience_level && (
                        <Badge variant="outline">
                          {job.experience_level}
                        </Badge>
                      )}
                    </div>
                    
                    {/* Location and Salary */}
                    <div className="space-y-2 text-sm text-gray-600">
                      {job.location && (
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2" />
                          <span>{job.location}</span>
                        </div>
                      )}
                      {formatSalary(job) && (
                        <div className="flex items-center">
                          <DollarSign className="h-4 w-4 mr-2" />
                          <span>{formatSalary(job)}</span>
                        </div>
                      )}
                    </div>
                    
                    {/* Description preview */}
                    <p className="text-sm text-gray-600 line-clamp-2">
                      {job.description}
                    </p>
                    
                    {/* View job link */}
                    <Link to={`/jobs/${job.id}`} className="block">
                      <Button variant="outline" className="w-full group-hover:bg-blue-50 group-hover:border-blue-200">
                        View Details
                        <ArrowRight className="h-4 w-4 ml-2" />
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default PublicJobsListing;
