# Payment Setup Guide: What You Need to Provide

This guide explains what information you need to provide so we can configure Stripe and PayPal payments on the platform.

## Overview

As the business owner, you need to:
1. <PERSON><PERSON> accounts with Stripe and PayPal
2. Provide us with the API keys from both platforms
3. We'll handle all the technical configuration for you

---

## Stripe Setup

### Step 1: Create a Stripe Account

1. Go to [https://stripe.com](https://stripe.com)
2. Click **"Start now"** or **"Sign up"**
3. Fill in your business information
4. Verify your email address

### Step 2: Get Your Stripe API Keys

1. **Log into your Stripe Dashboard**
2. **Navigate to Developers → API keys**
3. You'll see two types of keys:

#### Live Keys (for production)
- **Publishable key**: Starts with `pk_live_...`
- **Secret key**: Starts with `sk_live_...`

### Step 3: Send Us Your Keys

**Copy and send us these 4 keys:**

**Live Keys (for real payments):**
- Publishable key: `pk_live_51234567890abcdef...`
- Secret key: `sk_live_51234567890abcdef...`

### Step 4: Set Up Stripe Webhooks

1. **Go to Developers → Webhooks**
2. **Click "Add endpoint/Destination"**
3. **Select these events** (copy each one and paste in the search to find them):
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
4. **Click next**
5. **Enter the webhook URL 'https://hxhjduaziqovdzmgqlmr.supabase.co/functions/v1/stripe-webhook'**
6. **Click "Add endpoint"**
7. **Send us the webhook secret** (starts with `whsec_...`)

---

## PayPal Setup

### Step 1: Create a PayPal Developer Account

1. Go to [https://developer.paypal.com](https://developer.paypal.com)
2. Click **"Log in to Dashboard"**
3. Sign in with your PayPal account (or create one)
4. Complete the developer account setup

### Step 2: Create a PayPal App

1. **Click "Create App"**
2. **Fill in the details**:
   - **App Name**: Sourcio.ai
   - **Merchant**: Select your business account
   - **Features**: Check "Subscriptions"
3. **Click "Create App"**

### Step 3: Get Your PayPal API Keys

#### Live (for production)
- **Client ID**: Starts with `AZ...` or `AS...`
- **Client Secret**: Long string of characters

### Step 4: Send Us Your Keys

**Copy and send us these 4 keys:**

**Live Keys (for real payments):**
- Client ID: `AZDxjDScFpQtjWTOUtWKbyN_bDt4OgqaF4eYXlewfBP4...`
- Client Secret: `EHLhw-L_Ua7QJdKMMqIKYOWaLSaONQI7HyD-NspwoCCRq...`

### Step 5: Set Up PayPal Webhooks

1. **Go to your app settings**
2. **Scroll to "Webhooks"**
3. **Click "Add Webhook"**
4. **Enter the webhook URL 'https://hxhjduaziqovdzmgqlmr.supabase.co/functions/v1/paypal-webhook'**
5. **Select these events**:
   - `BILLING.SUBSCRIPTION.CREATED`
   - `BILLING.SUBSCRIPTION.UPDATED`
   - `BILLING.SUBSCRIPTION.CANCELLED`
   - `PAYMENT.SALE.COMPLETED`
6. **Save the webhook**
7. **Send us the webhook ID** if provided

---

## Summary: What to Send Us

Once you've completed the steps above, please send us:

### **From Stripe:**
- Live Publishable Key (`pk_live_...`)
- Live Secret Key (`sk_live_...`)
- Webhook Secret (`whsec_...`)

### **From PayPal:**
- Live Client ID (`AZ...`)
- Live Client Secret (`EH...`)
- Webhook ID
