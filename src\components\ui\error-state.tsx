import { AlertCircle, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "./button";

interface ErrorStateProps {
  /**
   * Title of the error message
   */
  title?: string;
  
  /**
   * Error message to display
   */
  message?: string;
  
  /**
   * Function to call when retry button is clicked
   */
  onRetry?: () => void;
  
  /**
   * Text for the retry button
   */
  retryText?: string;
  
  /**
   * Additional CSS classes
   */
  className?: string;
  
  /**
   * Whether to center the error state
   */
  center?: boolean;
}

/**
 * A reusable error state component
 */
export function ErrorState({
  title = "Something went wrong",
  message = "We couldn't load the data. Please try again.",
  onRetry,
  retryText = "Try Again",
  className,
  center = true,
}: ErrorStateProps) {
  const containerClasses = cn(
    "flex flex-col items-center space-y-4 p-6 text-center",
    center && "justify-center",
    className
  );
  
  return (
    <div className={containerClasses}>
      <div className="p-4 rounded-full bg-red-100">
        <AlertCircle className="h-8 w-8 text-red-500" />
      </div>
      <h3 className="text-lg font-semibold">{title}</h3>
      <p className="text-sm text-muted-foreground max-w-md">{message}</p>
      {onRetry && (
        <Button onClick={onRetry} variant="outline" className="mt-2">
          <RefreshCw className="h-4 w-4 mr-2" />
          {retryText}
        </Button>
      )}
    </div>
  );
}
