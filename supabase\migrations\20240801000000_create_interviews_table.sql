-- Create interviews table
CREATE TABLE IF NOT EXISTS public.interviews (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  candidate_id UUID NOT NULL REFERENCES public.candidates(id) ON DELETE CASCADE,
  job_id UUID NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
  company_id UUID NOT NULL REFERENCES public.companies(id) ON DELETE CASCADE,
  scheduled_at TIMESTAMP WITH TIME ZONE NOT NULL,
  duration_minutes INTEGER NOT NULL DEFAULT 60,
  location TEXT, -- Can be physical location or video call link
  interview_type TEXT NOT NULL CHECK (interview_type IN ('phone', 'video', 'in-person')),
  status TEXT NOT NULL CHECK (status IN ('scheduled', 'completed', 'cancelled', 'rescheduled')),
  notes TEXT,
  created_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE -- Owner of the interview
);

-- Create interview participants table
CREATE TABLE IF NOT EXISTS public.interview_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  interview_id UUID NOT NULL REFERENCES public.interviews(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('interviewer', 'observer')),
  status TEXT NOT NULL CHECK (status IN ('invited', 'confirmed', 'declined')),
  UNIQUE(interview_id, user_id)
);

-- Create interview feedback table
CREATE TABLE IF NOT EXISTS public.interview_feedback (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
  interview_id UUID NOT NULL REFERENCES public.interviews(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  overall_rating INTEGER NOT NULL CHECK (overall_rating BETWEEN 1 AND 5),
  technical_rating INTEGER CHECK (technical_rating BETWEEN 1 AND 5),
  cultural_rating INTEGER CHECK (cultural_rating BETWEEN 1 AND 5),
  strengths TEXT,
  weaknesses TEXT,
  notes TEXT,
  recommendation TEXT NOT NULL CHECK (recommendation IN ('strong_yes', 'yes', 'maybe', 'no', 'strong_no')),
  UNIQUE(interview_id, user_id)
);

-- Set up Row Level Security (RLS) policies

-- Interviews: Users can only see interviews they created or are participants in
ALTER TABLE public.interviews ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own interviews"
  ON public.interviews FOR SELECT
  USING (
    auth.uid() = user_id OR 
    auth.uid() = created_by OR
    EXISTS (
      SELECT 1 FROM public.interview_participants 
      WHERE interview_id = id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert their own interviews"
  ON public.interviews FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own interviews"
  ON public.interviews FOR UPDATE
  USING (auth.uid() = user_id OR auth.uid() = created_by);

CREATE POLICY "Users can delete their own interviews"
  ON public.interviews FOR DELETE
  USING (auth.uid() = user_id OR auth.uid() = created_by);

-- Interview Participants: Users can only see participants for interviews they created or are participants in
ALTER TABLE public.interview_participants ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view interview participants"
  ON public.interview_participants FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE id = interview_id AND (user_id = auth.uid() OR created_by = auth.uid())
    ) OR
    EXISTS (
      SELECT 1 FROM public.interview_participants 
      WHERE interview_id = interview_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert interview participants"
  ON public.interview_participants FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE id = interview_id AND (user_id = auth.uid() OR created_by = auth.uid())
    )
  );

CREATE POLICY "Users can update interview participants"
  ON public.interview_participants FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE id = interview_id AND (user_id = auth.uid() OR created_by = auth.uid())
    ) OR
    (user_id = auth.uid()) -- Users can update their own participant status
  );

CREATE POLICY "Users can delete interview participants"
  ON public.interview_participants FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE id = interview_id AND (user_id = auth.uid() OR created_by = auth.uid())
    )
  );

-- Interview Feedback: Users can only see feedback for interviews they created or are participants in
ALTER TABLE public.interview_feedback ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view interview feedback"
  ON public.interview_feedback FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM public.interviews 
      WHERE id = interview_id AND (user_id = auth.uid() OR created_by = auth.uid())
    ) OR
    EXISTS (
      SELECT 1 FROM public.interview_participants 
      WHERE interview_id = interview_id AND user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert their own interview feedback"
  ON public.interview_feedback FOR INSERT
  WITH CHECK (
    user_id = auth.uid() AND
    EXISTS (
      SELECT 1 FROM public.interview_participants 
      WHERE interview_id = interview_id AND user_id = auth.uid() AND role = 'interviewer'
    )
  );

CREATE POLICY "Users can update their own interview feedback"
  ON public.interview_feedback FOR UPDATE
  USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own interview feedback"
  ON public.interview_feedback FOR DELETE
  USING (user_id = auth.uid());

-- Add comments for documentation
COMMENT ON TABLE public.interviews IS 'Stores interview scheduling information';
COMMENT ON TABLE public.interview_participants IS 'Stores interview participants (interviewers and observers)';
COMMENT ON TABLE public.interview_feedback IS 'Stores feedback from interviewers';
