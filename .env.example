# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_SUPABASE_SERVICE_KEY=your_supabase_service_role_key

# AI Provider Configuration
VITE_GROQ_API_KEY=your_groq_api_key
VITE_GROQ_MODEL=llama-3.3-70b-versatiles

# SMTP Email Configuration
VITE_SMTP_HOST=smtp.gmail.com
VITE_SMTP_PORT=587
VITE_SMTP_SECURE=false
VITE_SMTP_USER=<EMAIL>
VITE_SMTP_PASSWORD=your-app-password
VITE_SMTP_FROM=<EMAIL>

# PayPal Configuration - Sandbox
VITE_PAYPAL_ENVIRONMENT=sandbox
VITE_PAYPAL_CLIENT_ID=your_paypal_sandbox_client_id
VITE_PAYPAL_CLIENT_SECRET=your_paypal_sandbox_client_secret
VITE_PAYPAL_WEBHOOK_ID=your_paypal_sandbox_webhook_id
VITE_PAYPAL_PLAN_STARTER=your_paypal_sandbox_starter_plan_id
VITE_PAYPAL_PLAN_GROWTH=your_paypal_sandbox_growth_plan_id
VITE_PAYPAL_PLAN_PRO=your_paypal_sandbox_pro_plan_id

# Application Configuration
VITE_APP_URL=http://localhost:8080
VITE_API_URL=http://localhost:5000

# Storage Configuration
VITE_STORAGE_BUCKET=cvs

# Feature Flags
VITE_ENABLE_PAYPAL=true
VITE_ENABLE_REAL_DATA=true
VITE_ENABLE_EMAIL_NOTIFICATIONS=true

