import { sendEmail } from './emailService';

/**
 * Send welcome email to new user
 */
export const sendWelcomeEmail = async (
  email: string,
  name: string
): Promise<void> => {
  try {
    await sendEmail({
      to: email,
      subject: 'Welcome to Sourcio.ai!',
      html: generateWelcomeHTML(name)
    });
  } catch (error) {
    console.error('Failed to send welcome email:', error);
    throw error;
  }
};

/**
 * Send password reset email
 */
export const sendPasswordResetEmail = async (
  email: string,
  resetLink: string
): Promise<void> => {
  try {
    await sendEmail({
      to: email,
      subject: 'Reset Your Password - Sourcio.ai',
      html: generatePasswordResetHTML(resetLink)
    });
  } catch (error) {
    console.error('Failed to send password reset email:', error);
    throw error;
  }
};

/**
 * Send email verification
 */
export const sendEmailVerification = async (
  email: string,
  verificationLink: string
): Promise<void> => {
  try {
    await sendEmail({
      to: email,
      subject: 'Verify Your Email - Sourcio.ai',
      html: generateEmailVerificationHTML(verificationLink)
    });
  } catch (error) {
    console.error('Failed to send email verification:', error);
    throw error;
  }
};

const generateWelcomeHTML = (name: string): string => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">Sourcio.ai</h1>
      </div>
      
      <h2 style="color: #2563eb;">Welcome to Sourcio.ai!</h2>
      
      <p style="font-size: 16px; line-height: 1.6;">Dear ${name},</p>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Welcome to Sourcio.ai! We're excited to have you join our platform.
      </p>
      
      <p style="font-size: 16px; line-height: 1.6;">
        You can now start using our AI-powered recruitment tools to find the best candidates for your positions.
      </p>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${import.meta.env.VITE_APP_URL}/dashboard" 
           style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
          Get Started
        </a>
      </div>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Best
