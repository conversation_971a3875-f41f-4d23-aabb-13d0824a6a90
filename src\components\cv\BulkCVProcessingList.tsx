import React from 'react';
import { Loader2, Check, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';

export interface ProcessedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  progress: number;
  status: 'uploading' | 'processing' | 'success' | 'error';
  candidateId?: string;
  candidateName?: string;
  isEvaluated?: boolean;
  error?: string;
}

interface BulkCVProcessingListProps {
  files: ProcessedFile[];
  onEvaluate: (candidateId: string, candidateName: string) => void;
  onEvaluateAll: () => void;
}

const BulkCVProcessingList: React.FC<BulkCVProcessingListProps> = ({
  files,
  onEvaluate,
  onEvaluateAll
}) => {
  // Check if all files are processed
  const allProcessed = files.every(file => 
    file.status === 'success' && file.candidateId
  );
  
  // Check if any files are processed but not evaluated
  const anyProcessedNotEvaluated = files.some(file => 
    file.status === 'success' && file.candidateId && !file.isEvaluated
  );
  
  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-gray-800 font-medium">Processed CVs ({files.length})</h3>
        {allProcessed && anyProcessedNotEvaluated && (
          <Button 
            onClick={onEvaluateAll}
            className="bg-primary-gradient text-white hover:text-white"
          >
            Evaluate All
          </Button>
        )}
      </div>
      
      <div className="space-y-3">
        {files.map(file => (
          <div 
            key={file.id} 
            className="bg-gray-50 border border-gray-200 rounded-lg p-4"
          >
            {/* File details */}
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium text-gray-800">{file.name}</p>
                {file.candidateName && (
                  <p className="text-sm text-gray-500">{file.candidateName}</p>
                )}
              </div>
              
              {/* Status and actions */}
              <div className="flex items-center">
                <span className="text-sm text-gray-500 mr-3">{formatFileSize(file.size)}</span>
                
                {file.status === 'uploading' && (
                  <Badge variant="outline" className="bg-blue-50 text-blue-600 flex items-center">
                    <Loader2 className="animate-spin h-3 w-3 mr-1" />
                    Uploading
                  </Badge>
                )}
                
                {file.status === 'processing' && (
                  <Badge variant="outline" className="bg-amber-50 text-amber-600 flex items-center">
                    <Loader2 className="animate-spin h-3 w-3 mr-1" />
                    Processing
                  </Badge>
                )}
                
                {file.status === 'error' && (
                  <Badge variant="outline" className="bg-red-50 text-red-600 flex items-center">
                    <AlertCircle className="h-3 w-3 mr-1" />
                    Error
                  </Badge>
                )}
                
                {file.status === 'success' && file.candidateId && !file.isEvaluated && (
                  <Button 
                    size="sm"
                    onClick={() => onEvaluate(file.candidateId!, file.candidateName || 'Candidate')}
                    className="bg-primary-gradient text-white hover:text-white"
                  >
                    Evaluate
                  </Button>
                )}
                
                {file.status === 'success' && file.isEvaluated && (
                  <Badge variant="outline" className="bg-green-50 text-green-600 flex items-center">
                    <Check className="h-3 w-3 mr-1" />
                    Evaluated
                  </Badge>
                )}
              </div>
            </div>
            
            {/* Progress bar */}
            {(file.status === 'uploading' || file.status === 'processing') && (
              <div className="mt-2">
                <Progress 
                  value={file.progress} 
                  className="h-1.5" 
                  indicatorClassName={
                    file.status === 'error' 
                      ? 'bg-red-500' 
                      : file.status === 'processing'
                        ? 'bg-amber-500'
                        : 'bg-blue-500'
                  }
                />
                <div className="flex justify-between mt-1">
                  <span className="text-xs text-gray-500">
                    {file.status === 'uploading' && `Uploading... ${file.progress}%`}
                    {file.status === 'processing' && `Processing... ${file.progress}%`}
                  </span>
                  <span className="text-xs text-gray-500">{file.progress}%</span>
                </div>
              </div>
            )}
            
            {/* Error message */}
            {file.status === 'error' && file.error && (
              <p className="mt-2 text-sm text-red-600">{file.error}</p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default BulkCVProcessingList;
