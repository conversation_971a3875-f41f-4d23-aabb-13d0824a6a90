Overview
The Sourcio.ai Platform is an AI-powered recruitment solution that helps companies evaluate and match candidates to job positions. This document outlines the scope of work.
________________________________________
Intellectual Property Rights
•	The client will receive full ownership of all intellectual property rights to the developed software


•	Complete source code will be provided with unrestricted usage rights


•	The client will have the right to modify, distribute, and commercialize the platform


•	All documentation, designs, and assets created during development will be the property of the client


•	No third-party components with restrictive licenses will be used without client approval


________________________________________
Deliverables
Project Deliverables
Phase	Deliverable
Phase 1	Authentication & User Management
	Dashboard & UI Framework
Phase 2	CV Upload & Management
	Job Posting System
Phase 3	AI Integration (GROQ API)
	CV Evaluation System
Phase 4	Candidate Pipeline Management
	Analytics & Reporting
Phase 5	Email Notifications
	Company Management
Final	Testing & Bug Fixes, Documentation & Deployment
Detailed Deliverables
1.	Fully functional Next.js-based recruitment platform


2.	Flexible AI integration system with support for multiple providers


3.	Responsive UI for all device types


4.	User authentication and role-based access control


5.	CV upload, parsing, and evaluation system


6.	Job posting and management system


7.	Candidate pipeline management


8.	Analytics and reporting dashboard


9.	Email notification system


10.	Complete source code with full intellectual property rights


11.	Comprehensive technical documentation including:


o	System architecture documentation


o	API documentation


o	Database schema


o	Deployment instructions


o	User manual


12.	Clean, well-commented code following industry best practices


________________________________________
Platform Workflow
1.	Upload CVs – Companies or agencies upload candidate CVs directly to the platform — no formatting needed


2.	Post Jobs – Add job listings. The system understands requirements and matches them to the right candidates


3.	Automatic Evaluation – Every CV is automatically analyzed and scored based on job fit


4.	View Reports – Clear and simple reports for each candidate — no manual sorting or guesswork


5.	Shortlist & Hire Faster – Save hours of work. Focus on top candidates and make faster hiring decisions


________________________________________
Core Features
CV Management
•	Upload interface (PDF, DOC, DOCX)


•	AI parsing & information extraction


•	Preview with zoom & navigation


•	Evaluation results display


•	Candidate comparison view


Job Management
•	Job creation & listing


•	Filtering & search


•	Job details & responsibilities


•	Status (active/inactive)


•	LinkedIn integration (subject to API)


AI-Powered Evaluation
•	Support for multiple providers


•	Configurable API settings


•	Default: GROQ API (Llama 3.3)


•	Optional: OpenAI, Claude


•	RAG (Retrieval Augmented Generation)


•	Skill extraction & job matching


•	Scoring & ranking


•	Customizable evaluation criteria


Candidate Pipeline
•	Status: New, Screening, Interview, Offer, Hired, Rejected


•	Interview scheduling & feedback


•	Email notifications


•	Timeline view


•	Notes & communication history


Pipeline Stages:
•	New


•	Screening


•	CV Review


•	Phone Screen


•	First Round


•	Second Round


•	Final Round


•	Rejected


•	Offer


•	Hired


________________________________________
Analytics & Reporting
•	Recruitment metrics and KPIs


•	Visual dashboards


•	Funnel visualization


•	Source effectiveness


•	Time-to-hire, efficiency metrics


•	Industry performance benchmarks


•	Custom report generation with exports


________________________________________
Company Management
•	Profile management


•	Team access controls


•	Branding customization


•	SaaS subscription management


________________________________________
Authentication & User Management
•	Registration & login (companies & candidates)


•	Admin approval workflow


•	Role-based access:


o	Admin: Full system access, user management, AI config


o	Company: Profile, post jobs, evaluate, manage pipeline


o	Candidate: View/apply jobs, track status


•	Profile management


•	Password reset


________________________________________
Dashboard & UI
•	Company dashboard with metrics


•	Analytics dashboard (custom KPIs)


•	Candidate job/status dashboard


•	Admin management dashboard


•	White-label branding support


________________________________________
Technical Specifications
Technology Stack
Component	Technologies
Frontend	Next.js (React), Tailwind CSS, TypeScript
Backend	API Integration, Serverless Functions
Database	Supabase (PostgreSQL)
AI Integration	GROQ API (Llama 3.3), OpenAI, Claude (configurable)
Authentication	NextAuth.js
File Storage	Supabase Storage
System Architecture
•	Frontend: Next.js app, client-side validation, clean architecture


•	Backend:


o	AI integration via configurable endpoints


o	Admin panel for AI settings


o	Email notifications


o	Database for users, jobs, CVs


o	File storage


________________________________________
SaaS Capabilities
•	Multi-tenant architecture


•	Tiered subscriptions


•	White-label support


•	Usage analytics & monitoring


________________________________________
Subscription Plans (Example)
You can edit/remove plans or offer them free initially:
Feature	Starter ($49/mo)	Growth ($149/mo)	Pro ($299/mo)
CV Uploads	50/month	500/month	Unlimited
Job Postings	1	5	Unlimited
Company Profiles	1	5	Unlimited
CV Evaluation	Basic	Smart matching	Custom scoring
Reports	Simple PDF/Excel	Detailed Insights	Custom Reports
Support	Email only	Email + Chat	Priority Support
Team Collaboration	No	Limited	Full
Integrations	No	No	HR/CRM integrations
Trial Period	14 days	14 days	14 days

















Missing/Broken Features
1.	Ability to schedule and manage interviews directly within the platform. This should include:
•	Creating interview events with date, time, and location (or video call link)
•	Assigning interviewers and candidates
•	Sending calendar invites and automated reminders
•	Tracking interview status and collecting feedback post-interview


2.	Making job posting links easily shareable on external platforms like LinkedIn

3.	We’d also like to integrate a payment system to manage subscription plans directly within the platform. This should support common gateways like Visa, Stripe or PayPal, allow for tiered pricing (as outlined in the scope), and include functionality for free trial periods, invoicing, and subscription management via the admin panel.

4.	Enable automated email notifications to candidates at key stages of the hiring process (e.g., application received, interview scheduled, status updates). Let us know if this functionality is already in place or needs to be added to the roadmap.

5.	We're currently unable to view any candidates associated with a job posted under the "Jobs" section.

7.	We’re currently unable to add team members to a company account. This functionality appears to be missing or inactive. Could you please check if this feature is implemented, and if so, ensure it’s accessible from the admin or company dashboard?

8.	is a candidate portal currently implemented? Specifically, we’re referring to a dedicated interface where candidates can:
•	View and apply to open positions
•	Track the status of their applications
•	Manage their profile and uploaded documents