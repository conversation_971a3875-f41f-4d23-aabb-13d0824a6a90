-- Remove the incorrect stripe_customer_id column if it exists
-- We'll use the existing payment_methods table instead for better scalability
ALTER TABLE public.profiles DROP COLUMN IF EXISTS stripe_customer_id;

-- Ensure payment_methods table supports customer accounts properly
-- Add a customer_account_type to distinguish between payment methods and customer accounts
ALTER TABLE public.payment_methods ADD COLUMN IF NOT EXISTS account_type TEXT DEFAULT 'payment_method';

-- Add comment for documentation
COMMENT ON COLUMN public.payment_methods.account_type IS 'Type of account: payment_method or customer_account';

-- Create index for faster queries on provider and account_type
CREATE INDEX IF NOT EXISTS idx_payment_methods_provider_account_type ON public.payment_methods(provider, account_type);

-- Rename payment_history table to payments for consistency
ALTER TABLE public.payment_history RENAME TO payments;

-- Update comments for documentation
COMMENT ON TABLE public.payments IS 'Stores payment history for all transactions';

-- Update indexes for payments table
DROP INDEX IF EXISTS idx_payment_history_user_id;
DROP INDEX IF EXISTS idx_payment_history_payment_id;
DROP INDEX IF EXISTS idx_payment_history_subscription_id;

CREATE INDEX IF NOT EXISTS idx_payments_user_id ON public.payments(user_id);
CREATE INDEX IF NOT EXISTS idx_payments_payment_id ON public.payments(payment_id);
CREATE INDEX IF NOT EXISTS idx_payments_subscription_id ON public.payments(subscription_id);

-- Update RLS policies for payments table
DROP POLICY IF EXISTS "Users can view their own payment history" ON public.payments;
DROP POLICY IF EXISTS "Users can create their own payment records" ON public.payments;

-- Users can view their own payment history
CREATE POLICY "Users can view their own payments"
ON public.payments
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Users can create their own payment records
CREATE POLICY "Users can create their own payments"
ON public.payments
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);
