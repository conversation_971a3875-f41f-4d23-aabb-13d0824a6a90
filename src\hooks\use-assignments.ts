import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import {
  getCandidateAssignments,
  getJobAssignments,
  getCompanyAssignments,
  assignCandidateToJobs,
  updateAssignmentStatus,
  removeAssignment,
  bulkUpdateAssignmentStatus,
  getAssignmentStats,
  AssignmentData,
  CandidateAssignment,
  BulkAssignmentResult
} from '@/services/supabase/assignments';

// Query keys for assignments
export const assignmentKeys = {
  all: ['assignments'] as const,
  candidate: (candidateId: string) => [...assignmentKeys.all, 'candidate', candidateId] as const,
  job: (jobId: string) => [...assignmentKeys.all, 'job', jobId] as const,
  company: (companyId: string) => [...assignmentKeys.all, 'company', companyId] as const,
  stats: (companyId: string) => [...assignmentKeys.all, 'stats', companyId] as const,
};

/**
 * Hook to fetch candidate assignments
 */
export function useCandidateAssignments(candidateId: string) {
  return useQuery({
    queryKey: assignmentKeys.candidate(candidateId),
    queryFn: () => getCandidateAssignments(candidateId),
    enabled: !!candidateId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch job assignments
 */
export function useJobAssignments(jobId: string) {
  return useQuery({
    queryKey: assignmentKeys.job(jobId),
    queryFn: () => getJobAssignments(jobId),
    enabled: !!jobId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch company assignments
 */
export function useCompanyAssignments(companyId: string) {
  return useQuery({
    queryKey: assignmentKeys.company(companyId),
    queryFn: () => getCompanyAssignments(companyId),
    enabled: !!companyId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch assignment statistics
 */
export function useAssignmentStats(companyId: string) {
  return useQuery({
    queryKey: assignmentKeys.stats(companyId),
    queryFn: () => getAssignmentStats(companyId),
    enabled: !!companyId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to assign candidate to jobs
 */
export function useAssignCandidate() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<BulkAssignmentResult, Error, { candidateId: string; assignments: AssignmentData[] }>({
    mutationFn: ({ candidateId, assignments }) => assignCandidateToJobs(candidateId, assignments),
    onSuccess: (result, { candidateId }) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: assignmentKeys.candidate(candidateId) });
      queryClient.invalidateQueries({ queryKey: assignmentKeys.all });
      
      // Invalidate company and job queries for affected assignments
      result.assignments.forEach(assignment => {
        queryClient.invalidateQueries({ queryKey: assignmentKeys.company(assignment.company_id) });
        queryClient.invalidateQueries({ queryKey: assignmentKeys.job(assignment.job_id) });
        queryClient.invalidateQueries({ queryKey: assignmentKeys.stats(assignment.company_id) });
      });

      if (result.successful > 0) {
        toast({
          title: 'Assignment Successful',
          description: `Successfully assigned candidate to ${result.successful} job${result.successful > 1 ? 's' : ''}.`,
        });
      }

      if (result.failed > 0) {
        toast({
          title: 'Partial Assignment',
          description: `${result.failed} assignment${result.failed > 1 ? 's' : ''} failed.`,
          variant: 'destructive',
        });
      }
    },
    onError: (error) => {
      console.error('Assignment error:', error);
      toast({
        title: 'Assignment Failed',
        description: error.message || 'Failed to assign candidate. Please try again.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to update assignment status
 */
export function useUpdateAssignmentStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<
    CandidateAssignment | null,
    Error,
    { assignmentId: string; status: CandidateAssignment['status']; notes?: string }
  >({
    mutationFn: ({ assignmentId, status, notes }) => updateAssignmentStatus(assignmentId, status, notes),
    onSuccess: (result) => {
      if (result) {
        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: assignmentKeys.candidate(result.candidate_id) });
        queryClient.invalidateQueries({ queryKey: assignmentKeys.job(result.job_id) });
        queryClient.invalidateQueries({ queryKey: assignmentKeys.company(result.company_id) });
        queryClient.invalidateQueries({ queryKey: assignmentKeys.stats(result.company_id) });
        queryClient.invalidateQueries({ queryKey: assignmentKeys.all });

        toast({
          title: 'Status Updated',
          description: `Assignment status has been updated to ${result.status}.`,
        });
      }
    },
    onError: (error) => {
      console.error('Status update error:', error);
      toast({
        title: 'Update Failed',
        description: error.message || 'Failed to update assignment status. Please try again.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to remove assignment
 */
export function useRemoveAssignment() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<boolean, Error, string>({
    mutationFn: (assignmentId: string) => removeAssignment(assignmentId),
    onSuccess: () => {
      // Invalidate all assignment queries since we don't have the specific IDs
      queryClient.invalidateQueries({ queryKey: assignmentKeys.all });

      toast({
        title: 'Assignment Removed',
        description: 'The assignment has been successfully removed.',
      });
    },
    onError: (error) => {
      console.error('Remove assignment error:', error);
      toast({
        title: 'Removal Failed',
        description: error.message || 'Failed to remove assignment. Please try again.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to bulk update assignment statuses
 */
export function useBulkUpdateAssignmentStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation<
    number,
    Error,
    { assignmentIds: string[]; status: CandidateAssignment['status'] }
  >({
    mutationFn: ({ assignmentIds, status }) => bulkUpdateAssignmentStatus(assignmentIds, status),
    onSuccess: (updatedCount, { status }) => {
      // Invalidate all assignment queries
      queryClient.invalidateQueries({ queryKey: assignmentKeys.all });

      toast({
        title: 'Bulk Update Successful',
        description: `Updated ${updatedCount} assignment${updatedCount > 1 ? 's' : ''} to ${status}.`,
      });
    },
    onError: (error) => {
      console.error('Bulk update error:', error);
      toast({
        title: 'Bulk Update Failed',
        description: error.message || 'Failed to update assignments. Please try again.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to get multiple candidate assignments at once
 */
export function useMultipleCandidateAssignments(candidateIds: string[]) {
  return useQuery({
    queryKey: [...assignmentKeys.all, 'multiple', candidateIds.sort()],
    queryFn: async () => {
      const results = await Promise.all(
        candidateIds.map(id => getCandidateAssignments(id))
      );
      
      // Return a map of candidateId -> assignments
      const assignmentMap: Record<string, CandidateAssignment[]> = {};
      candidateIds.forEach((id, index) => {
        assignmentMap[id] = results[index] || [];
      });
      
      return assignmentMap;
    },
    enabled: candidateIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Helper hook to check if a candidate has assignments
 */
export function useCandidateHasAssignments(candidateId: string) {
  const { data: assignments, isLoading } = useCandidateAssignments(candidateId);
  
  return {
    hasAssignments: (assignments?.length || 0) > 0,
    assignmentCount: assignments?.length || 0,
    assignments,
    isLoading
  };
}
