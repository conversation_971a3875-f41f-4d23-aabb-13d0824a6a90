/**
 * Stripe Price Service
 * 
 * This service dynamically loads Stripe price IDs from the admin-configured plans
 * instead of relying on hardcoded values in the configuration file.
 */

interface StripePrice {
  id: string;
  nickname?: string;
  unit_amount: number;
  currency: string;
  recurring?: {
    interval: string;
    interval_count?: number;
  };
  product: string | any;
  active: boolean;
}

/**
 * Get all active Stripe prices from the admin configuration
 */
export const getStripePrices = async (): Promise<StripePrice[]> => {
  try {
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-plans/prices`, {
      headers: {
        'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
      }
    });

    if (!response.ok) {
      throw new Error('Failed to fetch Stripe prices');
    }

    const data = await response.json();
    const allPrices = data.data?.data || [];

    // Filter for active prices with ExpertRecruiter prefix
    const filteredPrices = allPrices.filter((price: StripePrice) => {
      const productName = typeof price.product === 'string' ? '' : price.product?.name || '';
      return price.active && (
        productName.startsWith('ExpertRecruiter') || 
        price.nickname?.includes('ExpertRecruiter')
      );
    });

    return filteredPrices;
  } catch (error) {
    console.error('Error fetching Stripe prices:', error);
    return [];
  }
};

/**
 * Get price ID for a specific tier
 */
export const getPriceIdForTier = async (tier: 'STARTER' | 'GROWTH' | 'PRO'): Promise<string | null> => {
  try {
    const prices = await getStripePrices();
    
    // Map tier to expected price amounts (in cents)
    const tierAmounts = {
      STARTER: 4900, // $49.00
      GROWTH: 9900,  // $99.00
      PRO: 19900     // $199.00
    };

    const targetAmount = tierAmounts[tier];
    
    // Find price by amount or nickname
    const matchingPrice = prices.find(price => 
      price.unit_amount === targetAmount ||
      price.nickname?.toLowerCase().includes(tier.toLowerCase())
    );

    return matchingPrice?.id || null;
  } catch (error) {
    console.error(`Error getting price ID for tier ${tier}:`, error);
    return null;
  }
};

/**
 * Get all tier price IDs
 */
export const getAllTierPriceIds = async (): Promise<{
  STARTER: string | null;
  GROWTH: string | null;
  PRO: string | null;
}> => {
  try {
    const [starterPrice, growthPrice, proPrice] = await Promise.all([
      getPriceIdForTier('STARTER'),
      getPriceIdForTier('GROWTH'),
      getPriceIdForTier('PRO')
    ]);

    return {
      STARTER: starterPrice,
      GROWTH: growthPrice,
      PRO: proPrice
    };
  } catch (error) {
    console.error('Error getting all tier price IDs:', error);
    return {
      STARTER: null,
      GROWTH: null,
      PRO: null
    };
  }
};

/**
 * Check if Stripe is properly configured with all required prices
 */
export const isStripeConfigured = async (): Promise<boolean> => {
  try {
    const priceIds = await getAllTierPriceIds();
    return !!(priceIds.STARTER && priceIds.GROWTH && priceIds.PRO);
  } catch (error) {
    console.error('Error checking Stripe configuration:', error);
    return false;
  }
};
