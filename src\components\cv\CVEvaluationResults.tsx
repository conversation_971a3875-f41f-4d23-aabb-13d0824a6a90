import React, { useState } from 'react';
import {
  Star,
  Check,
  X,
  <PERSON><PERSON><PERSON><PERSON>gle,
  BarChart3,
  Briefcase,
  GraduationCap,
  Award,
  Clock,
  ListFilter,
  PieChart,
  Building,
  FileText
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { JobSpecificMatch } from '@/services/cv-processing/jobMatcher';
import JobMatchRankings from './JobMatchRankings';
import JobRecommendations from './JobRecommendations';
import JobMatchRadarChart from './JobMatchRadarChart';
import CoverLetterDisplay from './CoverLetterDisplay';

interface Skill {
  name: string;
  match: number;
  required: boolean;
}

interface Experience {
  title: string;
  company: string;
  duration: string;
  relevance: number;
}

interface Education {
  degree: string;
  institution: string;
  year: string;
  relevance: number;
}

interface CVEvaluationResultsProps {
  candidateName: string;
  position: string;
  overallMatch: number;
  skills: Skill[];
  experience: Experience[];
  education: Education[];
  strengths: string[];
  weaknesses: string[];
  // New props for enhanced evaluation
  jobSpecificMatches?: JobSpecificMatch[];
  topMatchingJobs?: JobSpecificMatch[];
  isCompanyEvaluation?: boolean;
  // Cover letter props
  coverLetterContent?: string | null;
  coverLetterUrl?: string | null;
  coverLetterScore?: number;
  coverLetterAnalysis?: string;
}

const CVEvaluationResults: React.FC<CVEvaluationResultsProps> = ({
  candidateName,
  position,
  overallMatch,
  skills,
  experience,
  education,
  strengths,
  weaknesses,
  jobSpecificMatches,
  topMatchingJobs,
  isCompanyEvaluation,
  coverLetterContent,
  coverLetterUrl,
  coverLetterScore,
  coverLetterAnalysis
}) => {
  const [activeTab, setActiveTab] = useState('summary');
  // Get color based on match score
  const getMatchColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 75) return 'text-blue-500';
    if (score >= 60) return 'text-amber-500';
    return 'text-red-500';
  };

  // Get progress bar color based on match score
  const getProgressColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 75) return 'bg-blue-500';
    if (score >= 60) return 'bg-amber-500';
    return 'bg-red-500';
  };

  // Create radar chart data from top matching jobs
  const radarChartData = topMatchingJobs && topMatchingJobs.length > 0 ? {
    labels: ['Skills', 'Experience', 'Education', 'Location'],
    datasets: topMatchingJobs.slice(0, 3).map((job, index) => ({
      label: job.jobTitle,
      data: [job.skillsScore, job.experienceScore, job.educationScore, job.locationScore],
      backgroundColor: [
        'rgba(54, 162, 235, 0.2)',
        'rgba(255, 99, 132, 0.2)',
        'rgba(75, 192, 192, 0.2)'
      ][index],
      borderColor: [
        'rgb(54, 162, 235)',
        'rgb(255, 99, 132)',
        'rgb(75, 192, 192)'
      ][index],
      borderWidth: 1
    }))
  } : undefined;

  // Check if we have enhanced evaluation data
  const hasEnhancedData =
    ((jobSpecificMatches && jobSpecificMatches.length > 0) ||
     (topMatchingJobs && topMatchingJobs.length > 0));

  // Determine which tabs to show based on the evaluation mode
  const showRankingsTab = jobSpecificMatches && jobSpecificMatches.length > 0;
  const showComparisonTab = isCompanyEvaluation && radarChartData;
  const showRecommendationsTab = isCompanyEvaluation && topMatchingJobs && topMatchingJobs.length > 0;
  const showCoverLetterTab = coverLetterContent || coverLetterUrl;

  // Helper function to determine grid columns based on visible tabs
  const getTabsGridCols = () => {
    let visibleTabs = 1; // Summary tab is always visible
    if (showRankingsTab) visibleTabs++;
    if (showComparisonTab) visibleTabs++;
    if (showRecommendationsTab) visibleTabs++;
    if (showCoverLetterTab) visibleTabs++;

    return `grid-cols-${visibleTabs}`;
  };

  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader>
        <CardTitle className="text-gray-800 text-lg flex justify-between items-center">
          <span>Evaluation Results</span>
          {isCompanyEvaluation && (
            <Badge className="bg-blue-500">Company Evaluation</Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Overall match */}

        {/* Enhanced evaluation tabs */}
        {hasEnhancedData && (
          <Tabs defaultValue="summary" className="w-full" onValueChange={setActiveTab}>
            <TabsList className={`grid ${getTabsGridCols()} mb-4`}>
              <TabsTrigger value="summary" className="text-xs">
                <BarChart3 className="h-4 w-4 mr-1" /> Summary
              </TabsTrigger>
              {showRankingsTab && (
                <TabsTrigger value="rankings" className="text-xs">
                  <ListFilter className="h-4 w-4 mr-1" /> Rankings
                </TabsTrigger>
              )}
              {showComparisonTab && (
                <TabsTrigger value="comparison" className="text-xs">
                  <PieChart className="h-4 w-4 mr-1" /> Comparison
                </TabsTrigger>
              )}
              {showRecommendationsTab && (
                <TabsTrigger value="recommendations" className="text-xs">
                  <Building className="h-4 w-4 mr-1" /> Recommendations
                </TabsTrigger>
              )}
              {showCoverLetterTab && (
                <TabsTrigger value="cover-letter" className="text-xs">
                  <FileText className="h-4 w-4 mr-1" /> Cover Letter
                </TabsTrigger>
              )}
            </TabsList>

            <TabsContent value="summary" className="mt-0">
              {/* Summary tab content will show the default content */}
            </TabsContent>
          </Tabs>
        )}
        <div className="flex flex-col items-center">
          {overallMatch ? (
            <>
              <div className="relative mb-2">
                <div
                  className="w-32 h-32 rounded-full border-8 flex items-center justify-center"
                  style={{
                    borderColor: getProgressColor(overallMatch),
                    background: `conic-gradient(${getProgressColor(overallMatch)} ${overallMatch}%, transparent 0)`
                  }}
                >
                  <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center">
                    <div className="text-center">
                      <div className={`text-3xl font-bold ${getMatchColor(overallMatch)}`}>
                        {overallMatch}%
                      </div>
                      <div className="text-xs text-gray-400">Match</div>
                    </div>
                  </div>
                </div>
                <div className="absolute top-0 right-0">
                  <Star className={`h-8 w-8 ${getMatchColor(overallMatch)}`} fill="currentColor" />
                </div>
              </div>
              <h2 className="text-xl font-bold text-gray-800">{candidateName}</h2>
              <p className="text-gray-500">Match for {position}</p>
            </>
          ) : (
            <>
              <div className="mb-2 p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
                <div className="text-gray-500 mb-2">Not Evaluated</div>
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs border-gray-200 text-gray-700 hover:bg-gray-100"
                >
                  Evaluate for {position}
                </Button>
              </div>
              <h2 className="text-xl font-bold text-gray-800">{candidateName}</h2>
              <p className="text-gray-500">CV Information</p>
            </>
          )}
        </div>

        {/* Skills */}
        <div>
          <h3 className="text-gray-800 font-medium mb-3 flex items-center">
            <Award className="mr-2 h-4 w-4 text-gray-500" /> Skills
          </h3>

          {/* Skills count and filter */}
          {skills.length > 0 && (
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm text-gray-500">{skills.length} skills found</span>
              {skills.length > 10 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs h-7 px-2"
                  onClick={() => window.scrollTo({ top: document.getElementById('skills-grid')?.offsetTop || 0, behavior: 'smooth' })}
                >
                  View as Grid
                </Button>
              )}
            </div>
          )}

          {/* Skills list for evaluated candidates */}
          {overallMatch && skills.length > 0 ? (
            <>
              {/* Top skills with progress bars (max 5) */}
              <div className="space-y-3 mb-4">
                {skills.slice(0, 5).map((skill, index) => (
                  <div key={index}>
                    <div className="flex justify-between mb-1">
                      <div className="flex items-center">
                        <span className="text-gray-700">{skill.name}</span>
                        {skill.required && (
                          <Badge className="ml-2 bg-blue-500/20 text-blue-500 hover:bg-blue-500/30">
                            Required
                          </Badge>
                        )}
                      </div>
                      {skill.match ? (
                        <span className={`${getMatchColor(skill.match)}`}>{skill.match}%</span>
                      ) : null}
                    </div>
                    {skill.match ? (
                      <Progress
                        value={skill.match}
                        className="h-1.5"
                        indicatorClassName={getProgressColor(skill.match)}
                      />
                    ) : (
                      <div className="h-1.5 w-full bg-gray-100 rounded"></div>
                    )}
                  </div>
                ))}
              </div>

              {/* Remaining skills as a grid */}
              {skills.length > 5 && (
                <div id="skills-grid" className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                  {skills.slice(5).map((skill, index) => (
                    <div key={index} className="bg-gray-50 p-2 rounded border border-gray-200 text-sm">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-700 truncate" title={skill.name}>
                          {skill.name}
                        </span>
                        {skill.match ? (
                          <span className={`text-xs font-medium ${getMatchColor(skill.match)}`}>
                            {skill.match}%
                          </span>
                        ) : null}
                      </div>
                      {skill.required && (
                        <Badge className="mt-1 text-xs bg-blue-500/20 text-blue-500 hover:bg-blue-500/30">
                          Required
                        </Badge>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </>
          ) : skills.length > 0 ? (
            // Skills grid for non-evaluated candidates
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
              {skills.map((skill, index) => (
                <div key={index} className="bg-gray-50 p-2 rounded border border-gray-200">
                  <span className="text-gray-700 text-sm">{skill.name}</span>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-gray-500 text-sm">No skills information available</div>
          )}
        </div>

        {/* Experience */}
        <div>
          <h3 className="text-gray-800 font-medium mb-3 flex items-center">
            <Briefcase className="mr-2 h-4 w-4 text-gray-500" /> Experience
          </h3>

          {/* Experience count */}
          {experience.length > 0 && (
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm text-gray-500">{experience.length} positions found</span>
              {experience.length > 5 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs h-7 px-2"
                  onClick={() => {
                    const expElement = document.getElementById('experience-section');
                    if (expElement) {
                      expElement.classList.toggle('max-h-[400px]');
                      expElement.classList.toggle('overflow-y-auto');
                    }
                  }}
                >
                  {document.getElementById('experience-section')?.classList.contains('max-h-[400px]')
                    ? 'Show All'
                    : 'Show Less'}
                </Button>
              )}
            </div>
          )}

          <div id="experience-section" className={`space-y-4 ${experience.length > 5 ? 'max-h-[400px] overflow-y-auto pr-2' : ''}`}>
            {experience.length > 0 ? (
              experience.map((exp, index) => (
                <div key={index} className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                  <div className="flex justify-between">
                    <div>
                      <h4 className="text-gray-800 font-medium">{exp.title || 'Unknown Position'}</h4>
                      <p className="text-gray-500 text-sm">{exp.company || 'Unknown Company'}</p>
                    </div>
                    {overallMatch && exp.relevance ? (
                      <Badge
                        className={`
                          ${exp.relevance >= 80 ? 'bg-green-500/20 text-green-500' :
                            exp.relevance >= 60 ? 'bg-blue-500/20 text-blue-500' :
                            'bg-amber-500/20 text-amber-500'}
                        `}
                      >
                        {exp.relevance >= 80 ? 'Highly Relevant' :
                          exp.relevance >= 60 ? 'Relevant' : 'Somewhat Relevant'}
                      </Badge>
                    ) : null}
                  </div>
                  <div className="flex items-center mt-1 text-sm text-gray-500">
                    <Clock className="mr-1 h-3 w-3" />
                    <span>{exp.duration || 'Duration not specified'}</span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-gray-500 text-sm">No experience information available</div>
            )}
          </div>
        </div>

        {/* Education */}
        <div>
          <h3 className="text-gray-800 font-medium mb-3 flex items-center">
            <GraduationCap className="mr-2 h-4 w-4 text-gray-500" /> Education
          </h3>

          {/* Education count */}
          {education.length > 0 && (
            <div className="flex justify-between items-center mb-3">
              <span className="text-sm text-gray-500">{education.length} qualifications found</span>
              {education.length > 3 && (
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs h-7 px-2"
                  onClick={() => {
                    const eduElement = document.getElementById('education-section');
                    if (eduElement) {
                      eduElement.classList.toggle('max-h-[300px]');
                      eduElement.classList.toggle('overflow-y-auto');
                    }
                  }}
                >
                  {document.getElementById('education-section')?.classList.contains('max-h-[300px]')
                    ? 'Show All'
                    : 'Show Less'}
                </Button>
              )}
            </div>
          )}

          <div id="education-section" className={`space-y-4 ${education.length > 3 ? 'max-h-[300px] overflow-y-auto pr-2' : ''}`}>
            {education.length > 0 ? (
              education.map((edu, index) => (
                <div key={index} className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                  <div className="flex justify-between">
                    <div>
                      <h4 className="text-gray-800 font-medium">{edu.degree || 'Unknown Degree'}</h4>
                      <p className="text-gray-500 text-sm">
                        {edu.institution || 'Unknown Institution'}
                        {edu.year && `, ${edu.year}`}
                      </p>
                    </div>
                    {overallMatch && edu.relevance ? (
                      <Badge
                        className={`
                          ${edu.relevance >= 80 ? 'bg-green-500/20 text-green-500' :
                            edu.relevance >= 60 ? 'bg-blue-500/20 text-blue-500' :
                            'bg-amber-500/20 text-amber-500'}
                        `}
                      >
                        {edu.relevance >= 80 ? 'Highly Relevant' :
                          edu.relevance >= 60 ? 'Relevant' : 'Somewhat Relevant'}
                      </Badge>
                    ) : null}
                  </div>
                </div>
              ))
            ) : (
              <div className="text-gray-500 text-sm">No education information available</div>
            )}
          </div>
        </div>

        {/* Enhanced evaluation tab contents */}
        {hasEnhancedData && (
          <>
            <TabsContent value="rankings" className="mt-0">
              {jobSpecificMatches && jobSpecificMatches.length > 0 ? (
                <JobMatchRankings jobMatches={jobSpecificMatches} />
              ) : (
                <div className="text-center py-6 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">No job rankings available</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="comparison" className="mt-0">
              {radarChartData ? (
                <JobMatchRadarChart chartData={radarChartData} />
              ) : (
                <div className="text-center py-6 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">No comparison data available</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="recommendations" className="mt-0">
              {topMatchingJobs && topMatchingJobs.length > 0 ? (
                <JobRecommendations
                  topJobs={topMatchingJobs}
                  candidateName={candidateName}
                />
              ) : (
                <div className="text-center py-6 bg-gray-50 rounded-lg">
                  <p className="text-gray-500">No job recommendations available</p>
                </div>
              )}
            </TabsContent>

            <TabsContent value="cover-letter" className="mt-0">
              <CoverLetterDisplay
                content={coverLetterContent}
                url={coverLetterUrl}
                score={coverLetterScore}
                analysis={coverLetterAnalysis}
              />
            </TabsContent>
          </>
        )}

        {/* Only show the strengths and weaknesses if we're on the summary tab or don't have enhanced data */}
        {(!hasEnhancedData || activeTab === 'summary') && overallMatch ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Strengths */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h3 className="text-gray-800 font-medium mb-3 flex items-center">
                <Check className="mr-2 h-4 w-4 text-green-500" /> Strengths
              </h3>
              <ul className="space-y-2">
                {strengths && strengths.length > 0 ? (
                  strengths.map((strength, index) => (
                    <li key={index} className="flex items-start">
                      <Check className="h-4 w-4 text-green-500 mr-2 mt-0.5" />
                      <span className="text-gray-700">{strength}</span>
                    </li>
                  ))
                ) : (
                  <li className="text-gray-500 text-sm">No strengths identified yet</li>
                )}
              </ul>
            </div>

            {/* Weaknesses */}
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h3 className="text-gray-800 font-medium mb-3 flex items-center">
                <AlertTriangle className="mr-2 h-4 w-4 text-amber-500" /> Areas for Improvement
              </h3>
              <ul className="space-y-2">
                {weaknesses && weaknesses.length > 0 ? (
                  weaknesses.map((weakness, index) => (
                    <li key={index} className="flex items-start">
                      <X className="h-4 w-4 text-red-500 mr-2 mt-0.5" />
                      <span className="text-gray-700">{weakness}</span>
                    </li>
                  ))
                ) : (
                  <li className="text-gray-500 text-sm">No areas for improvement identified yet</li>
                )}
              </ul>
            </div>
          </div>
        ) : null}
      </CardContent>
    </Card>
  );
};

export default CVEvaluationResults;

