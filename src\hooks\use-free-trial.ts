import { useQuery } from '@tanstack/react-query';
import { hasUsedFreeTrial, markFreeTrialAsUsed } from '@/services/supabase/freeTrialService';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { useMutation, useQueryClient } from '@tanstack/react-query';

/**
 * Hook to check if the user has used their free trial
 */
export function useFreeTrial() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const freeTrialQuery = useQuery({
    queryKey: ['freeTrial', user?.id],
    queryFn: () => hasUsedFreeTrial(user?.id || ''),
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const markUsedMutation = useMutation({
    mutationFn: () => markFreeTrialAsUsed(user?.id || ''),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['freeTrial', user?.id] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: `Failed to update free trial status: ${error}`,
        variant: 'destructive',
      });
    },
  });

  return {
    hasUsedFreeTrial: freeTrialQuery.data || false,
    isLoading: freeTrialQuery.isLoading,
    markAsUsed: markUsedMutation.mutate,
    isMarkingAsUsed: markUsedMutation.isPending,
  };
}
