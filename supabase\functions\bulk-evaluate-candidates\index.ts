import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import { corsHeaders } from '../_shared/cors.ts'
import { rankCandidatesForJob } from '../_shared/ai-operations.ts'

// Initialize Supabase client
const supabaseUrl = Deno.env.get('SUPABASE_URL')!
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!
const supabase = createClient(supabaseUrl, supabaseServiceKey)

// GROQ API configuration
const groqApiKey = Deno.env.get('GROQ_API_KEY')!
const groqModel = Deno.env.get('GROQ_MODEL') || 'llama-3.3-70b-versatile'

// Interface definitions
interface CandidateRanking {
  candidateId: string;
  name: string;
  overallScore: number;
  skillsScore: number;
  experienceScore: number;
  educationScore: number;
  culturalFitScore: number;
  strengths: string[];
  areasForImprovement: string[];
  recommendation: 'Hire' | 'Consider' | 'Reject';
}

interface BulkEvaluationResult {
  jobId: string;
  jobTitle: string;
  totalCandidates: number;
  evaluatedCandidates: number;
  rankings: CandidateRanking[];
  topCandidate?: CandidateRanking;
  evaluationSummary: {
    averageScore: number;
    hireRecommendations: number;
    considerRecommendations: number;
    rejectRecommendations: number;
  };
  errors?: Array<{
    candidateId: string;
    candidateName: string;
    error: string;
  }>;
}

// Note: AI prompts and schemas now centralized in _shared/ai-operations.ts

// Function to call AI API for scoring candidates - now uses centralized operations
async function scoreCandidates(jobDescription: string, candidates: any[]): Promise<any> {
  return rankCandidatesForJob(groqApiKey, groqModel, candidates, jobDescription);
}

// Main bulk evaluation function
async function bulkEvaluateCandidatesForJob(jobId: string): Promise<BulkEvaluationResult> {
  // Get the job data
  const { data: job, error: jobError } = await supabase
    .from('jobs')
    .select('*')
    .eq('id', jobId)
    .single();

  if (jobError) {
    throw new Error(`Failed to fetch job: ${jobError.message}`);
  }

  // Get all candidates for the job
  const { data: candidates, error: candidatesError } = await supabase
    .from('candidates')
    .select('*')
    .eq('job_id', jobId);

  if (candidatesError) {
    throw new Error(`Failed to fetch candidates: ${candidatesError.message}`);
  }

  // If there are no candidates, return empty result
  if (!candidates || candidates.length === 0) {
    return {
      jobId,
      jobTitle: job.title,
      totalCandidates: 0,
      evaluatedCandidates: 0,
      rankings: [],
      evaluationSummary: {
        averageScore: 0,
        hireRecommendations: 0,
        considerRecommendations: 0,
        rejectRecommendations: 0,
      },
    };
  }

  const totalCandidates = candidates.length;
  const errors: Array<{ candidateId: string; candidateName: string; error: string }> = [];
  const validCandidates: any[] = [];

  // Process candidates and collect valid ones with error handling
  for (const candidate of candidates) {
    try {
      // Check if candidate has evaluation summary (parsed CV)
      if (!candidate.evaluation_summary) {
        errors.push({
          candidateId: candidate.id,
          candidateName: candidate.name || 'Unknown',
          error: 'No CV data available - candidate needs to be processed first'
        });
        continue;
      }

      const evaluationSummary = JSON.parse(candidate.evaluation_summary);
      if (!evaluationSummary.parsedCV) {
        errors.push({
          candidateId: candidate.id,
          candidateName: candidate.name || 'Unknown',
          error: 'Invalid CV data - candidate needs to be re-processed'
        });
        continue;
      }

      validCandidates.push({
        id: candidate.id,
        name: candidate.name,
        parsedCV: evaluationSummary.parsedCV
      });
    } catch (parseError) {
      errors.push({
        candidateId: candidate.id,
        candidateName: candidate.name || 'Unknown',
        error: 'Failed to parse candidate data'
      });
    }
  }

  // If no valid candidates, return result with errors
  if (validCandidates.length === 0) {
    return {
      jobId,
      jobTitle: job.title,
      totalCandidates,
      evaluatedCandidates: 0,
      rankings: [],
      evaluationSummary: {
        averageScore: 0,
        hireRecommendations: 0,
        considerRecommendations: 0,
        rejectRecommendations: 0,
      },
      errors,
    };
  }

  // Use the GROQ API to score and rank candidates
  const rankingResult = await scoreCandidates(job.description, validCandidates);

  // Map the ranking result to our interface - updated for centralized schema
  const candidateRankings: CandidateRanking[] = rankingResult.rankings.map((ranking: any) => ({
    candidateId: ranking.candidateId,
    name: ranking.name,
    overallScore: ranking.overallScore,
    skillsScore: ranking.skillsScore,
    experienceScore: ranking.experienceScore,
    educationScore: ranking.educationScore,
    culturalFitScore: ranking.culturalFitScore,
    strengths: ranking.strengths,
    areasForImprovement: ranking.areasForImprovement,
    recommendation: ranking.recommendation
  }));

  // Sort by overall score in descending order
  candidateRankings.sort((a, b) => b.overallScore - a.overallScore);

  // Calculate evaluation summary
  const averageScore = candidateRankings.reduce((sum, candidate) => sum + candidate.overallScore, 0) / candidateRankings.length;
  const hireRecommendations = candidateRankings.filter(c => c.recommendation === 'Hire').length;
  const considerRecommendations = candidateRankings.filter(c => c.recommendation === 'Consider').length;
  const rejectRecommendations = candidateRankings.filter(c => c.recommendation === 'Reject').length;

  const result: BulkEvaluationResult = {
    jobId,
    jobTitle: job.title,
    totalCandidates,
    evaluatedCandidates: candidateRankings.length,
    rankings: candidateRankings,
    topCandidate: candidateRankings.length > 0 ? candidateRankings[0] : undefined,
    evaluationSummary: {
      averageScore: Math.round(averageScore * 100) / 100,
      hireRecommendations,
      considerRecommendations,
      rejectRecommendations,
    },
  };

  // Add errors if any
  if (errors.length > 0) {
    result.errors = errors;
  }

  return result;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Parse the URL to get the job ID
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/');
    const jobId = pathParts[pathParts.length - 1]; // Get the last part as job ID

    if (!jobId || jobId === 'bulk-evaluate-candidates') {
      return new Response(
        JSON.stringify({ error: 'Job ID is required' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Verify authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization header is required' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Verify the user has access to this job
    const token = authHeader.replace('Bearer ', '');
    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication token' }),
        { 
          status: 401, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Check if user has access to the job
    const { data: job, error: jobAccessError } = await supabase
      .from('jobs')
      .select('id, user_id, company_id')
      .eq('id', jobId)
      .single();

    if (jobAccessError || !job) {
      return new Response(
        JSON.stringify({ error: 'Job not found or access denied' }),
        { 
          status: 404, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      );
    }

    // Check if user owns the job or is a team member of the company
    if (job.user_id !== user.id) {
      const { data: teamMember } = await supabase
        .from('team_members')
        .select('id')
        .eq('company_id', job.company_id)
        .eq('user_id', user.id)
        .single();

      if (!teamMember) {
        return new Response(
          JSON.stringify({ error: 'Access denied to this job' }),
          { 
            status: 403, 
            headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
          }
        );
      }
    }

    // Perform bulk evaluation
    const result = await bulkEvaluateCandidatesForJob(jobId);

    return new Response(
      JSON.stringify({ success: true, data: result }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );

  } catch (error) {
    console.error('Bulk evaluation error:', error);
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error', 
        message: error.message 
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    );
  }
})
