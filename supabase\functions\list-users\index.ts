import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  // Enable CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
        }
      }
    )

    // Get the token from the Authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing Authorization header' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    // Verify the user token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized', details: authError }), {
        headers: { 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    // Check if user is admin in profiles table
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('platform_admin')
      .eq('user_id', user.id)
      .single()

    if (profileError || !profile?.platform_admin) {
      return new Response(JSON.stringify({ error: 'Forbidden - Admin access required' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 403,
      })
    }

    // Get users from Auth API
    const { data: authUsers, error: usersError } = await supabaseAdmin.auth.admin.listUsers()

    if (usersError) {
      return new Response(JSON.stringify({ error: 'Failed to fetch users', details: usersError }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      })
    }

    // Get profiles data
    const { data: profiles, error: profilesError } = await supabaseAdmin
      .from('profiles')
      .select('*')

    if (profilesError) {
      return new Response(JSON.stringify({ error: 'Failed to fetch profiles', details: profilesError }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      })
    }

    // Combine data
    const users = authUsers.users.map(authUser => {
      const profile = profiles.find(p => p.user_id === authUser.id) || {}
      return {
        id: authUser.id,
        email: authUser.email || '',
        full_name: profile.full_name || authUser.user_metadata?.full_name || '',
        subscription_tier: profile.subscription_tier || 'starter',
        subscription_status: profile.subscription_status || 'inactive',
        platform_admin: !!profile.platform_admin,
        created_at: authUser.created_at || '',
        last_sign_in_at: authUser.last_sign_in_at || null,
        banned: authUser.banned || false
      }
    })

    return new Response(JSON.stringify({ users }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      status: 200,
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal server error', details: error.message }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      status: 500,
    })
  }
})
