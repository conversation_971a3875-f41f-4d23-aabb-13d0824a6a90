import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

/**
 * Assignment interfaces
 */
export interface AssignmentData {
  jobId: string;
  companyId: string;
  jobTitle: string;
  companyName: string;
  matchScore: number;
}

export interface CandidateAssignment {
  id: string;
  created_at: string;
  candidate_id: string;
  job_id: string;
  company_id: string;
  match_score: number;
  status: 'new' | 'screening' | 'interview' | 'offer' | 'hired' | 'rejected';
  assigned_by: string;
  notes?: string;
}

export interface BulkAssignmentResult {
  successful: number;
  failed: number;
  assignments: CandidateAssignment[];
  errors: Array<{
    jobId: string;
    jobTitle: string;
    error: string;
  }>;
}

/**
 * Assign a candidate to multiple jobs/companies
 */
export const assignCandidateToJobs = async (
  candidateId: string,
  assignments: AssignmentData[]
): Promise<BulkAssignmentResult> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      const result: BulkAssignmentResult = {
        successful: 0,
        failed: 0,
        assignments: [],
        errors: []
      };

      // Process each assignment
      for (const assignment of assignments) {
        try {
          // Check if assignment already exists
          const { data: existingAssignment } = await supabase
            .from('candidate_assignments')
            .select('id')
            .eq('candidate_id', candidateId)
            .eq('job_id', assignment.jobId)
            .single();

          if (existingAssignment) {
            // Update existing assignment
            const { data: updatedAssignment, error: updateError } = await supabase
              .from('candidate_assignments')
              .update({
                match_score: assignment.matchScore,
                assigned_by: userId,
                updated_at: new Date().toISOString()
              })
              .eq('id', existingAssignment.id)
              .select()
              .single();

            if (updateError) throw updateError;
            result.assignments.push(updatedAssignment);
          } else {
            // Create new assignment
            const { data: newAssignment, error: insertError } = await supabase
              .from('candidate_assignments')
              .insert({
                candidate_id: candidateId,
                job_id: assignment.jobId,
                company_id: assignment.companyId,
                match_score: assignment.matchScore,
                status: 'new',
                assigned_by: userId
              })
              .select()
              .single();

            if (insertError) throw insertError;
            result.assignments.push(newAssignment);
          }

          // Update candidate status to 'screening' if currently 'new'
          const { data: candidate } = await supabase
            .from('candidates')
            .select('status')
            .eq('id', candidateId)
            .single();

          if (candidate && candidate.status === 'new') {
            await supabase
              .from('candidates')
              .update({ status: 'screening' })
              .eq('id', candidateId);
          }

          result.successful++;
        } catch (error) {
          console.error(`Failed to assign candidate to job ${assignment.jobId}:`, error);
          result.failed++;
          result.errors.push({
            jobId: assignment.jobId,
            jobTitle: assignment.jobTitle,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return result;
    },
    'Failed to assign candidate to jobs'
  );
};

/**
 * Get candidate assignments
 */
export const getCandidateAssignments = async (candidateId: string): Promise<CandidateAssignment[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('candidate_assignments')
        .select(`
          *,
          jobs:job_id (
            title,
            company_id,
            companies:company_id (
              name
            )
          )
        `)
        .eq('candidate_id', candidateId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    'Failed to fetch candidate assignments',
    []
  );
};

/**
 * Get job assignments (candidates assigned to a specific job)
 */
export const getJobAssignments = async (jobId: string): Promise<CandidateAssignment[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('candidate_assignments')
        .select(`
          *,
          candidates:candidate_id (
            name,
            email,
            cv_url
          )
        `)
        .eq('job_id', jobId)
        .order('match_score', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    'Failed to fetch job assignments',
    []
  );
};

/**
 * Update assignment status
 */
export const updateAssignmentStatus = async (
  assignmentId: string,
  status: CandidateAssignment['status'],
  notes?: string
): Promise<CandidateAssignment | null> => {
  return safeDbOperation(
    async () => {
      const updateData: any = {
        status,
        updated_at: new Date().toISOString()
      };

      if (notes !== undefined) {
        updateData.notes = notes;
      }

      const { data, error } = await supabase
        .from('candidate_assignments')
        .update(updateData)
        .eq('id', assignmentId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    'Failed to update assignment status',
    null
  );
};

/**
 * Remove candidate assignment
 */
export const removeAssignment = async (assignmentId: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('candidate_assignments')
        .delete()
        .eq('id', assignmentId);

      if (error) throw error;
      return true;
    },
    'Failed to remove assignment',
    false
  );
};

/**
 * Get company assignments (all candidates assigned to company jobs)
 */
export const getCompanyAssignments = async (companyId: string): Promise<CandidateAssignment[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('candidate_assignments')
        .select(`
          *,
          candidates:candidate_id (
            name,
            email,
            cv_url
          ),
          jobs:job_id (
            title
          )
        `)
        .eq('company_id', companyId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    'Failed to fetch company assignments',
    []
  );
};

/**
 * Bulk update assignment statuses
 */
export const bulkUpdateAssignmentStatus = async (
  assignmentIds: string[],
  status: CandidateAssignment['status']
): Promise<number> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('candidate_assignments')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .in('id', assignmentIds)
        .select('id');

      if (error) throw error;
      return data?.length || 0;
    },
    'Failed to bulk update assignment statuses',
    0
  );
};

/**
 * Get assignment statistics for a company
 */
export const getAssignmentStats = async (companyId: string) => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('candidate_assignments')
        .select('status')
        .eq('company_id', companyId);

      if (error) throw error;

      const stats = {
        total: data?.length || 0,
        new: 0,
        screening: 0,
        interview: 0,
        offer: 0,
        hired: 0,
        rejected: 0
      };

      data?.forEach(assignment => {
        if (assignment.status in stats) {
          stats[assignment.status as keyof typeof stats]++;
        }
      });

      return stats;
    },
    'Failed to fetch assignment statistics',
    {
      total: 0,
      new: 0,
      screening: 0,
      interview: 0,
      offer: 0,
      hired: 0,
      rejected: 0
    }
  );
};
