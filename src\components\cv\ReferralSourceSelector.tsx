import React, { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Users, Linkedin, Upload, UserPlus } from 'lucide-react';

interface ReferralSourceSelectorProps {
  value: string;
  onChange: (value: string) => void;
  className?: string;
}

const ReferralSourceSelector: React.FC<ReferralSourceSelectorProps> = ({
  value,
  onChange,
  className = ''
}) => {
  const [customReferrer, setCustomReferrer] = useState('');
  const [selectedSource, setSelectedSource] = useState(value || 'Direct Upload');

  const referralSources = [
    { value: 'Direct Upload', label: 'Direct Upload', icon: Upload },
    { value: 'LinkedIn', label: 'LinkedIn', icon: Linkedin },
    { value: 'Employee Referral', label: 'Employee Referral', icon: Users },
    { value: 'Recruitment Agency', label: 'Recruitment Agency', icon: UserPlus },
    { value: 'Job Board', label: 'Job Board', icon: Upload },
    { value: 'Company Website', label: 'Company Website', icon: Upload },
    { value: 'Social Media', label: 'Social Media', icon: Upload },
    { value: 'Career Fair', label: 'Career Fair', icon: Users },
    { value: 'Personal Referral', label: 'Personal Referral', icon: Users },
    { value: 'Other', label: 'Other', icon: Upload }
  ];

  const handleSourceChange = (newSource: string) => {
    setSelectedSource(newSource);
    
    if (newSource === 'Personal Referral' && customReferrer) {
      onChange(`${newSource}: ${customReferrer}`);
    } else if (newSource !== 'Personal Referral') {
      onChange(newSource);
      setCustomReferrer('');
    } else {
      onChange(newSource);
    }
  };

  const handleCustomReferrerChange = (referrer: string) => {
    setCustomReferrer(referrer);
    if (selectedSource === 'Personal Referral' && referrer.trim()) {
      onChange(`${selectedSource}: ${referrer.trim()}`);
    } else if (selectedSource === 'Personal Referral') {
      onChange(selectedSource);
    }
  };

  // Parse existing value to set initial state
  React.useEffect(() => {
    if (value && value.includes('Personal Referral:')) {
      const parts = value.split('Personal Referral:');
      if (parts.length > 1) {
        setSelectedSource('Personal Referral');
        setCustomReferrer(parts[1].trim());
      }
    } else if (value) {
      setSelectedSource(value);
    }
  }, [value]);

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Users className="h-4 w-4" />
          Candidate Referral Source
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="referral-source">How did you find this candidate?</Label>
          <Select value={selectedSource} onValueChange={handleSourceChange}>
            <SelectTrigger id="referral-source">
              <SelectValue placeholder="Select referral source" />
            </SelectTrigger>
            <SelectContent>
              {referralSources.map((source) => {
                const IconComponent = source.icon;
                return (
                  <SelectItem key={source.value} value={source.value}>
                    <div className="flex items-center gap-2">
                      <IconComponent className="h-4 w-4" />
                      {source.label}
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>

        {selectedSource === 'Personal Referral' && (
          <div className="space-y-2">
            <Label htmlFor="referrer-name">Referrer Name</Label>
            <Input
              id="referrer-name"
              placeholder="Enter the name of the person who referred this candidate"
              value={customReferrer}
              onChange={(e) => handleCustomReferrerChange(e.target.value)}
            />
          </div>
        )}

        {selectedSource === 'Other' && (
          <div className="space-y-2">
            <Label htmlFor="other-source">Please specify</Label>
            <Input
              id="other-source"
              placeholder="Enter the specific source"
              value={customReferrer}
              onChange={(e) => handleCustomReferrerChange(e.target.value)}
            />
          </div>
        )}

        <div className="text-xs text-muted-foreground">
          This information helps track the effectiveness of different recruitment channels.
        </div>
      </CardContent>
    </Card>
  );
};

export default ReferralSourceSelector;
