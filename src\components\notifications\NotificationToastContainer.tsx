import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import InAppNotification from './InAppNotification';
import { Notification, useNotifications } from '@/contexts/NotificationContext';

const NotificationToastContainer: React.FC = () => {
  const [activeNotifications, setActiveNotifications] = useState<Notification[]>([]);
  const { notifications, markAsRead } = useNotifications();
  const isInitialMount = useRef(true);

  // Listen for new notifications
  useEffect(() => {
    // Skip the first render to prevent showing notifications on initial load
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Find new unread notifications that aren't already in activeNotifications
    const newNotifications = notifications.filter(
      notification =>
        !notification.read &&
        !activeNotifications.some(active => active.id === notification.id)
    );

    if (newNotifications.length > 0) {
      setActiveNotifications(prev => [...prev, ...newNotifications]);
    }
  }, [notifications, activeNotifications]);

  // Remove a notification from the active list
  const removeNotification = (id: string) => {
    setActiveNotifications(prev => prev.filter(notification => notification.id !== id));
    markAsRead(id);
  };

  // Create a portal for the notifications
  return createPortal(
    <div className="fixed top-4 right-4 z-50 flex flex-col gap-2">
      {activeNotifications.map(notification => (
        <InAppNotification
          key={notification.id}
          notification={notification}
          onClose={() => removeNotification(notification.id)}
        />
      ))}
    </div>,
    document.body
  );
};

export default NotificationToastContainer;
