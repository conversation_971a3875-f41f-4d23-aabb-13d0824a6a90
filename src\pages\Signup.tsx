import React, { useState } from 'react';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { useAuth } from '@/contexts/AuthContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { ArrowRight, Mail, Lock, User, CheckCircle, Info, AlertCircle } from 'lucide-react';

// Define form schema with Zod
const formSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters'),
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  confirmPassword: z.string().min(6, 'Password must be at least 6 characters'),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type FormValues = z.infer<typeof formSchema>;

const Signup = () => {
  const { signup, resendVerificationEmail, checkEmailVerification } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isSignupComplete, setIsSignupComplete] = useState(false);
  const [signupEmail, setSignupEmail] = useState('');
  const [resendCount, setResendCount] = useState(0);
  const [isResending, setIsResending] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [searchParams] = useSearchParams();
  const returnTo = searchParams.get('returnTo') || '/dashboard';

  // Handle resend verification email
  const handleResendVerification = async () => {
    if (isResending) return;

    setIsResending(true);
    try {
      await resendVerificationEmail(signupEmail);
      setResendCount(prev => prev + 1);

      toast({
        title: 'Verification email sent',
        description: `We've sent a verification email to ${signupEmail}. Please check your inbox.`,
      });
    } catch (error) {
      toast({
        title: 'Failed to send verification email',
        description: error instanceof Error ? error.message : 'Please try again later',
        variant: 'destructive',
      });
    } finally {
      setIsResending(false);
    }
  };

  // Handle check verification status
  const handleCheckVerification = async () => {
    if (isChecking) return;

    setIsChecking(true);
    try {
      const isVerified = await checkEmailVerification(signupEmail);

      if (isVerified) {
        toast({
          title: 'Email verified',
          description: 'Your email has been verified successfully.',
        });
        navigate('/login');
      } else {
        toast({
          title: 'Email not verified',
          description: 'Please check your inbox and click the verification link.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Failed to check verification status',
        description: error instanceof Error ? error.message : 'Please try again later',
        variant: 'destructive',
      });
    } finally {
      setIsChecking(false);
    }
  };

  // Initialize form with react-hook-form and zod validation
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: '',
      email: '',
      password: '',
      confirmPassword: '',
    },
  });

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    setIsLoading(true);

    try {
      await signup(data.email, data.password, data.name);
      toast({
        title: 'Account created successfully',
        description: 'Welcome to Sourcio.ai!',
      });
      navigate(returnTo, { replace: true });
    } catch (error) {
      toast({
        title: 'Signup failed',
        description: error instanceof Error ? error.message : 'Please check your information and try again',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Show confirmation screen if signup is complete
  if (isSignupComplete) {
    return (
      <div className="min-h-screen bg-hero-gradient flex flex-col">
        <header className="container mx-auto px-6 py-5">
          <Link to="/" className="flex items-center">
            <img src="/logo.png" alt="RecruitAI Logo" className="h-10" />
          </Link>
        </header>

        <div className="flex-1 flex items-center justify-center px-6 py-12">
          <div className="w-full max-w-4xl bg-card-gradient rounded-xl border border-gray-800 shadow-xl overflow-hidden">
            <div className="flex flex-col md:flex-row">
              {/* Left side - Illustration */}
              <div className="w-full md:w-5/12 bg-gradient-to-br from-blue-900 to-indigo-900 p-8 flex flex-col items-center justify-center relative overflow-hidden">
                {/* Background decorative elements */}
                <div className="absolute top-0 left-0 w-full h-full opacity-10">
                  <div className="absolute top-10 left-10 w-20 h-20 rounded-full bg-blue-400 animate-float-slow"></div>
                  <div className="absolute bottom-10 right-10 w-32 h-32 rounded-full bg-indigo-400 animate-float-medium"></div>
                  <div className="absolute top-1/2 left-1/3 w-16 h-16 rounded-full bg-purple-400 animate-float-fast"></div>
                </div>

                {/* Floating mail icons */}
                <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
                  <div className="absolute top-[15%] right-[20%] text-blue-200 opacity-20 animate-float-slow">
                    <Mail className="h-6 w-6" />
                  </div>
                  <div className="absolute bottom-[25%] left-[15%] text-indigo-200 opacity-20 animate-float-medium">
                    <Mail className="h-8 w-8" />
                  </div>
                  <div className="absolute top-[60%] right-[25%] text-purple-200 opacity-20 animate-float-fast">
                    <Mail className="h-5 w-5" />
                  </div>
                </div>

                <div className="relative z-10 text-center">
                  {/* Email verification illustration with animation */}
                  <div className="w-32 h-32 mx-auto mb-6 bg-blue-500 bg-opacity-20 rounded-full flex items-center justify-center relative overflow-hidden">
                    <div className="absolute w-full h-full">
                      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-blue-400 rounded-full opacity-20 animate-ping"></div>
                    </div>
                    <Mail className="h-16 w-16 text-blue-300 animate-pulse" />
                  </div>

                  <h2 className="text-2xl font-bold text-white mb-4">Check Your Email</h2>
                  <p className="text-blue-200 mb-6">
                    We've sent a verification link to your email address. Click the link to activate your account.
                  </p>

                  <div className="flex items-center justify-center space-x-2 text-blue-200">
                    <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                    <div className="w-24 h-1 bg-blue-400"></div>
                    <div className="w-3 h-3 rounded-full bg-blue-400"></div>
                    <div className="w-24 h-1 bg-blue-400"></div>
                    <div className="w-3 h-3 rounded-full bg-white"></div>
                  </div>
                  <div className="mt-2 text-xs text-blue-200">
                    <span>Sign Up</span>
                    <span className="mx-6">Verification</span>
                    <span>Complete</span>
                  </div>
                </div>
              </div>

              {/* Right side - Account confirmation */}
              <div className="w-full md:w-7/12 p-8">
                <div className="flex flex-col items-center text-center">
                  <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mb-4">
                    <CheckCircle className="h-8 w-8 text-white" />
                  </div>
                  <h1 className="text-3xl font-bold text-white">Account Created</h1>
                  <p className="mt-4 text-gray-300">
                    Your account has been created successfully. Please verify your email to continue.
                  </p>

                  {signupEmail && (
                    <div className="mt-6 w-full">
                      <div className="bg-blue-900/30 border border-blue-800 rounded-lg p-4 mb-6">
                        <div className="flex items-start">
                          <AlertCircle className="h-5 w-5 text-blue-400 mt-0.5 mr-3 flex-shrink-0" />
                          <div>
                            <h3 className="font-medium text-blue-300">Email verification required</h3>
                            <p className="mt-2 text-sm text-blue-200">
                              We've sent a verification email to <strong className="break-all text-white">{signupEmail}</strong>.
                              Please check your inbox and click the verification link to activate your account.
                            </p>

                            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-4">
                              <Button
                                variant="outline"
                                onClick={handleResendVerification}
                                disabled={isResending || isChecking || resendCount >= 3}
                                className="w-full bg-blue-800/50 border-blue-700 text-white hover:bg-blue-800"
                                size="sm"
                              >
                                {isResending ? (
                                  <>
                                    <div className="mr-2 h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                                    Sending...
                                  </>
                                ) : (
                                  <>
                                    <Mail className="mr-2 h-4 w-4" />
                                    Resend verification
                                  </>
                                )}
                              </Button>

                              <Button
                                variant="default"
                                onClick={handleCheckVerification}
                                disabled={isResending || isChecking}
                                className="w-full bg-green-600 hover:bg-green-700"
                                size="sm"
                              >
                                {isChecking ? (
                                  <>
                                    <div className="mr-2 h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                                    Checking...
                                  </>
                                ) : (
                                  <>
                                    <CheckCircle className="mr-2 h-4 w-4" />
                                    I've verified my email
                                  </>
                                )}
                              </Button>
                            </div>

                            {resendCount >= 3 && (
                              <p className="text-sm mt-3 text-blue-300">
                                You've reached the maximum number of resend attempts.
                                Please check your spam folder or contact support if you still haven't received the email.
                              </p>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="bg-gray-800/30 border border-gray-700 rounded-lg p-4 mb-6">
                        <h3 className="font-medium text-gray-300 flex items-center">
                          <Info className="h-4 w-4 mr-2 text-gray-400" />
                          Helpful Tips
                        </h3>
                        <ul className="mt-2 text-sm text-gray-400 space-y-2">
                          <li className="flex items-start">
                            <span className="mr-2">•</span>
                            <span>The verification email should arrive within 5 minutes</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2">•</span>
                            <span>Check your spam or junk folder if you don't see it</span>
                          </li>
                          <li className="flex items-start">
                            <span className="mr-2">•</span>
                            <span>Make sure your email address was entered correctly</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  )}

                  <Button
                    className="mt-2 bg-recruiter-lightblue hover:bg-blue-500"
                    onClick={() => navigate('/login')}
                  >
                    Go to Login
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }



  return (
    <div className="min-h-screen bg-hero-gradient flex flex-col">
      {/* Header with logo */}
      <header className="container mx-auto px-6 py-5">
        <Link to="/" className="flex items-center">
          <img src="/logo.png" alt="RecruitAI Logo" className="h-10" />
        </Link>
      </header>

      {/* Main content */}
      <div className="flex-1 flex items-center justify-center px-6 py-12">
        <div className="w-full max-w-md space-y-8 bg-card-gradient p-8 rounded-xl border border-gray-800 shadow-xl">
          <div className="text-center">
            <h1 className="text-3xl font-bold text-white">Create an account</h1>
            <p className="mt-2 text-gray-400">Sign up to start using Sourcio.ai</p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Full Name</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <User className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          placeholder="John Doe"
                          className="pl-10 bg-[#1a2035] border-gray-700 text-white"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Email</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          placeholder="<EMAIL>"
                          className="pl-10 bg-[#1a2035] border-gray-700 text-white"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          type="password"
                          placeholder="••••••••"
                          className="pl-10 bg-[#1a2035] border-gray-700 text-white"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="confirmPassword"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-white">Confirm Password</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Lock className="absolute left-3 top-3 h-5 w-5 text-gray-400" />
                        <Input
                          type="password"
                          placeholder="••••••••"
                          className="pl-10 bg-[#1a2035] border-gray-700 text-white"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button
                type="submit"
                className="w-full bg-recruiter-lightblue hover:bg-blue-500"
                disabled={isLoading}
              >
                {isLoading ? (
                  <div className="flex items-center">
                    <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                    Creating account...
                  </div>
                ) : (
                  <div className="flex items-center">
                    Create Account <ArrowRight className="ml-2 h-4 w-4" />
                  </div>
                )}
              </Button>
            </form>
          </Form>

          <div className="mt-6 text-center space-y-2">
            <p className="text-gray-400">
              Already have an account?{' '}
              <Link to="/login" className="text-recruiter-blue hover:text-blue-400 transition">
                Sign in
              </Link>
            </p>
            <p className="text-gray-400">
              Looking for a job?{' '}
              <Link to="/candidate-signup" className="text-recruiter-blue hover:text-blue-400 transition">
                Join as a candidate
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;


