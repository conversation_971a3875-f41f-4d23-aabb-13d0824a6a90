# Sourcio.ai Platform - Complete Flow Diagram

This document provides a comprehensive overview of the Sourcio.ai platform's user flows and system architecture.

## Platform Overview

The Sourcio.ai platform is a comprehensive recruitment solution built with Next.js, Supabase, and AI integration (GROQ API). It supports multiple user roles and provides end-to-end recruitment management from CV upload to hiring decisions.

## Complete Platform Flow

```mermaid
flowchart TD
    %% User Authentication & Onboarding
    A[User Registration] --> B{User Type}
    B -->|Company| C[Company Setup Wizard]
    B -->|Candidate| D[Candidate Profile]
    B -->|Admin| E[Platform Admin Access]
    
    C --> C1[Create Company Profile]
    C1 --> C2[Post First Job]
    C2 --> F[Dashboard Access]
    
    D --> D1[Complete Profile]
    D1 --> D2[Browse Jobs]
    D2 --> D3[Apply to Jobs]
    
    E --> E1[AI Model Settings]
    E1 --> E2[Subscription Management]
    E2 --> E3[Platform Analytics]
    
    %% Main Dashboard
    F --> G[Main Dashboard]
    G --> H[Job Management]
    G --> I[CV Evaluation System]
    G --> J[Candidate Pipeline]
    G --> K[Analytics & Reports]
    G --> L[Company Management]
    
    %% Job Management Flow
    H --> H1[Create Job Posting]
    H1 --> H2[Job Details Form]
    H2 --> H3{Save as Draft or Publish}
    H3 -->|Draft| H4[Save Draft]
    H3 -->|Publish| H5[Active Job Posting]
    H5 --> H6[Public Job View]
    H6 --> H7[Candidate Applications]
    
    %% CV Evaluation Flow - Main Focus
    I --> I1[CV Upload Interface]
    I1 --> I2[File Upload]
    I2 --> I3[AI CV Parsing - GROQ API]
    I3 --> I4[Extract Personal Details]
    I4 --> I5[Extract Skills & Experience]
    I5 --> I6[Extract Education]
    I6 --> I7[Store Parsed CV Data]
    
    I7 --> I8{Evaluation Mode}
    I8 -->|Specific Job| I9[Match to Selected Job]
    I8 -->|All Company Jobs| I10[Match to All Company Jobs]
    I8 -->|All Companies| I11[Match to All Platform Jobs]
    
    I9 --> I12[AI Job Matching Analysis]
    I10 --> I12
    I11 --> I12
    
    I12 --> I13[Generate Match Scores]
    I13 --> I14[Skills Analysis]
    I14 --> I15[Experience Evaluation]
    I15 --> I16[Education Assessment]
    I16 --> I17[Cover Letter Analysis]
    I17 --> I18[Overall Match Score]
    
    I18 --> I19[Evaluation Results]
    I19 --> I20[Candidate Profile Creation]
    I20 --> J
    
    %% Candidate Pipeline Management
    J --> J1[Candidate List View]
    J1 --> J2[Individual Candidate Details]
    J2 --> J3[Status Management]
    J3 --> J4{Status Options}
    
    J4 -->|New| J5[Initial Review]
    J4 -->|Screening| J6[Phone/Video Screen]
    J4 -->|Interview| J7[Schedule Interview]
    J4 -->|Offer| J8[Extend Offer]
    J4 -->|Hired| J9[Onboarding Process]
    J4 -->|Rejected| J10[Send Rejection Notice]
    
    %% Interview Scheduling Flow
    J7 --> S1[Interview Scheduling Modal]
    S1 --> S2[Select Date & Time]
    S2 --> S3[Choose Interview Type]
    S3 --> S4[Add Interviewers]
    S4 --> S5[Create Interview]
    S5 --> S6[Send Email Invitations]
    S6 --> S7[Calendar Integration - ICS]
    S7 --> S8[Notification System]
    
    %% Notification System
    S8 --> N1[Email Notifications]
    N1 --> N2[Candidate Invitation]
    N1 --> N3[Interviewer Assignment]
    N1 --> N4[Database Notifications]
    N4 --> N5[In-App Notifications]
    N5 --> N6[Notification Dashboard]
    
    %% Analytics & Reporting
    K --> K1[Dashboard Statistics]
    K1 --> K2[CV Upload Metrics]
    K2 --> K3[Match Score Analytics]
    K3 --> K4[Recruitment Funnel]
    K4 --> K5[Time to Hire Analysis]
    K5 --> K6[Source Effectiveness]
    K6 --> K7[Skills Gap Analysis]
    K7 --> K8[Export Reports]
    
    %% Company Management
    L --> L1[Company Profile]
    L1 --> L2[Team Management]
    L2 --> L3[Role Permissions]
    L3 --> L4[Subscription Status]
    L4 --> L5[Usage Limits]
    L5 --> L6[Billing Management]
    
    %% Admin Features
    E3 --> A1[User Management]
    A1 --> A2[Company Approval]
    A2 --> A3[Subscription Plans]
    A3 --> A4[AI Model Configuration]
    A4 --> A5[Platform Analytics]
    A5 --> A6[System Monitoring]
    
    %% Subscription & Billing
    L6 --> B1[Payment Integration]
    B1 --> B2[PayPal Integration]
    B1 --> B3[Stripe Integration]
    B2 --> B4[Subscription Activation]
    B3 --> B4
    B4 --> B5[Feature Access Control]
    B5 --> B6[Usage Tracking]
    
    %% AI Integration Details
    I3 --> AI1[GROQ API Configuration]
    AI1 --> AI2[Llama 3.3 70B Model]
    AI2 --> AI3[JSON Response Processing]
    AI3 --> AI4[Skill Extraction]
    AI4 --> AI5[Experience Parsing]
    AI5 --> AI6[Education Analysis]
    AI6 --> AI7[Match Score Calculation]
    
    %% Data Storage
    I20 --> DB1[(Supabase Database)]
    DB1 --> DB2[Candidates Table]
    DB1 --> DB3[Jobs Table]
    DB1 --> DB4[Companies Table]
    DB1 --> DB5[Evaluations Table]
    DB1 --> DB6[Interviews Table]
    DB1 --> DB7[Notifications Table]
    DB1 --> DB8[Subscriptions Table]
    
    %% File Storage
    I2 --> FS1[Supabase Storage]
    FS1 --> FS2[CV Files]
    FS1 --> FS3[Company Logos]
    FS1 --> FS4[Profile Images]
    
    %% Security & Permissions
    F --> SEC1[Row Level Security]
    SEC1 --> SEC2[Role-Based Access]
    SEC2 --> SEC3[Company Data Isolation]
    SEC3 --> SEC4[User Permissions]
    
    %% Styling
    classDef userFlow fill:#e1f5fe
    classDef aiProcess fill:#f3e5f5
    classDef dataStorage fill:#e8f5e8
    classDef notification fill:#fff3e0
    classDef admin fill:#ffebee
    
    class A,B,C,D,E userFlow
    class I3,I12,AI1,AI2,AI3,AI4,AI5,AI6,AI7 aiProcess
    class DB1,DB2,DB3,DB4,DB5,DB6,DB7,DB8,FS1,FS2,FS3,FS4 dataStorage
    class N1,N2,N3,N4,N5,N6,S6,S7,S8 notification
    class E1,E2,E3,A1,A2,A3,A4,A5,A6 admin
```

## Key Features & Flows

### 1. CV Evaluation Process (Main Flow)
- **Upload**: Users upload CV files at `/dashboard/cvs/evaluation`
- **AI Parsing**: GROQ API (Llama 3.3) extracts structured data
- **Matching**: Three evaluation modes:
  - Specific Job matching
  - All Company Jobs matching  
  - All Platform Companies matching
- **Scoring**: AI generates match scores for skills, experience, education
- **Results**: Detailed evaluation with strengths, gaps, and recommendations

### 2. Candidate Pipeline Management
- **Status Tracking**: New → Screening → Interview → Offer → Hired/Rejected
- **Interview Scheduling**: Integrated scheduling with email notifications
- **Communication**: Notes, feedback, and timeline tracking
- **Bulk Operations**: Evaluate multiple candidates simultaneously

### 3. Job Management
- **Posting**: Create and publish job listings
- **Applications**: Track candidate applications
- **Limits**: Subscription-based job posting limits
- **Public View**: Candidates can view and apply to jobs

### 4. AI Integration
- **Configurable**: Admin can set AI provider and model
- **GROQ Default**: Uses Llama 3.3 70B Versatile model
- **Fallback**: Environment variable fallback for API keys
- **JSON Processing**: Structured response handling

### 5. Subscription Management
- **Tiered Plans**: Starter, Professional, Enterprise
- **Payment Integration**: PayPal and Stripe support
- **Usage Limits**: Job postings, CV evaluations, team members
- **Admin Control**: Platform admin manages subscription plans

### 6. Notification System
- **Email Integration**: SMTP-based email notifications
- **Interview Invites**: Calendar integration with ICS files
- **In-App Notifications**: Database-stored notifications
- **Real-time Updates**: Toast notifications for user actions

## Technology Stack

- **Frontend**: Next.js (React), TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL), Row Level Security
- **AI**: GROQ API (Llama 3.3), OpenAI, Anthropic (configurable)
- **Authentication**: Supabase Auth with role-based permissions
- **Storage**: Supabase Storage for files
- **Payments**: PayPal and Stripe integration
- **Email**: SMTP with calendar integration

## User Roles & Permissions

1. **Platform Admin**: Full system access, AI settings, subscription management
2. **Company Admin**: Company management, job posting, candidate evaluation
3. **Recruiter**: Candidate evaluation, interview scheduling, pipeline management
4. **Viewer**: Read-only access to company data
5. **Candidate**: Job browsing, application submission, status tracking

This platform provides a complete recruitment solution with AI-powered candidate evaluation, comprehensive pipeline management, and flexible subscription-based access control.
