import React, { useState, useEffect } from 'react';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useStripeScriptContext } from './StripeScriptProvider';
import { STRIPE_SUBSCRIPTION_PLANS, STRIPE_PRICING } from '@/config/stripe';
import { getPriceIdForTier } from '@/services/stripe/priceService';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import * as subscriptionService from '@/services/supabase/subscriptions';

interface DirectStripeButtonProps {
  tier: 'STARTER' | 'GROWTH' | 'PRO';
}

const DirectStripeButton: React.FC<DirectStripeButtonProps> = ({ tier }) => {
  const { isLoaded, failedToLoad, reload, stripe } = useStripeScriptContext();
  const [isProcessing, setIsProcessing] = useState(false);
  const [priceId, setPriceId] = useState<string | null>(null);
  const [isLoadingPrice, setIsLoadingPrice] = useState(true);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();

  const pricing = STRIPE_PRICING[tier];

  // Load price ID dynamically
  useEffect(() => {
    const loadPriceId = async () => {
      setIsLoadingPrice(true);
      try {
        // First try the static configuration
        const staticPriceId = STRIPE_SUBSCRIPTION_PLANS[tier];
        if (staticPriceId && !staticPriceId.includes('replace_me')) {
          setPriceId(staticPriceId);
        } else {
          // Fall back to dynamic loading
          const dynamicPriceId = await getPriceIdForTier(tier);
          setPriceId(dynamicPriceId);
        }
      } catch (error) {
        console.error('Error loading price ID:', error);
        setPriceId(null);
      } finally {
        setIsLoadingPrice(false);
      }
    };

    loadPriceId();
  }, [tier]);

  const handleSubscribe = async () => {
    if (!stripe || !user) {
      toast({
        title: 'Error',
        description: 'Stripe is not loaded or user is not authenticated',
        variant: 'destructive',
      });
      return;
    }

    if (!priceId) {
      toast({
        title: 'Error',
        description: 'Subscription plan not configured. Please contact support.',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Create checkout session via our edge function
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          priceId,
          userId: user.id,
          tier: tier.toLowerCase(),
          successUrl: `${window.location.origin}/dashboard?subscription=success`,
          cancelUrl: `${window.location.origin}/dashboard/pricing?subscription=cancelled`,
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      const { error } = await stripe.redirectToCheckout({
        sessionId: data.sessionId,
      });

      if (error) {
        throw new Error(error.message);
      }
    } catch (error) {
      console.error('Error creating Stripe checkout session:', error);
      toast({
        title: 'Error',
        description: 'Failed to start checkout process. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Show loading state while Stripe or price is loading
  if ((!isLoaded && !failedToLoad) || isLoadingPrice) {
    return (
      <Button disabled className="w-full">
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        {isLoadingPrice ? 'Loading plan...' : 'Loading Stripe...'}
      </Button>
    );
  }

  // Show error state if Stripe failed to load
  if (failedToLoad) {
    return (
      <Button onClick={reload} variant="outline" className="w-full">
        Retry Loading Stripe
      </Button>
    );
  }

  // Show error state if price ID is not configured
  if (!isLoadingPrice && !priceId) {
    return (
      <Button disabled variant="outline" className="w-full">
        Plan not configured
      </Button>
    );
  }

  // Show processing state
  if (isProcessing) {
    return (
      <Button disabled className="w-full">
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        Processing...
      </Button>
    );
  }

  return (
    <Button 
      onClick={handleSubscribe}
      className="w-full bg-[#635bff] hover:bg-[#5a54e6] text-white"
    >
      Subscribe with Stripe - ${pricing.price}/{pricing.period}
    </Button>
  );
};

export default DirectStripeButton;
