import React from 'react';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';

interface StatusSelectorProps {
  status: string;
  onStatusChange: (status: string) => void;
  getStatusColor: (status: string) => string;
}

const StatusSelector: React.FC<StatusSelectorProps> = ({ status, onStatusChange, getStatusColor }) => {
  return (
    <div className="flex items-center">
      <span className="text-gray-500 mr-2">Status:</span>
      <Select value={status} onValueChange={onStatusChange}>
        <SelectTrigger className="w-[180px] bg-white border-gray-200 text-gray-800">
          <SelectValue>
            <div className="flex items-center">
              <Badge className={`mr-2 ${getStatusColor(status)}`}>
                {status.charAt(0).toUpperCase() + status.slice(1)}
              </Badge>
            </div>
          </SelectValue>
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="new">
            <div className="flex items-center">
              <Badge className={`mr-2 ${getStatusColor('new')}`}>New</Badge>
            </div>
          </SelectItem>
          <SelectItem value="screening">
            <div className="flex items-center">
              <Badge className={`mr-2 ${getStatusColor('screening')}`}>Screening</Badge>
            </div>
          </SelectItem>
          <SelectItem value="interview">
            <div className="flex items-center">
              <Badge className={`mr-2 ${getStatusColor('interview')}`}>Interview</Badge>
            </div>
          </SelectItem>
          <SelectItem value="offer">
            <div className="flex items-center">
              <Badge className={`mr-2 ${getStatusColor('offer')}`}>Offer</Badge>
            </div>
          </SelectItem>
          <SelectItem value="hired">
            <div className="flex items-center">
              <Badge className={`mr-2 ${getStatusColor('hired')}`}>Hired</Badge>
            </div>
          </SelectItem>
          <SelectItem value="rejected">
            <div className="flex items-center">
              <Badge className={`mr-2 ${getStatusColor('rejected')}`}>Rejected</Badge>
            </div>
          </SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
};

export default StatusSelector;
