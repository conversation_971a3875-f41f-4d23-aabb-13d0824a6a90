import { useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { setToast } from '@/lib/toast';

/**
 * Component that initializes the global toast utility
 * This allows showing toasts from non-React contexts
 */
const ToastInitializer = () => {
  const { toast } = useToast();
  
  useEffect(() => {
    // Set the toast function to be used by the global toast utility
    setToast(toast);
    
    return () => {
      // Reset the toast function when the component unmounts
      setToast(() => {});
    };
  }, [toast]);
  
  return null;
};

export default ToastInitializer;
