-- Create evaluations table
CREATE TABLE IF NOT EXISTS public.evaluations (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  candidate_id uuid NOT NULL REFERENCES public.candidates(id) ON DELETE CASCADE,
  job_id uuid REFERENCES public.jobs(id) ON DELETE SET NULL,
  company_id uuid REFERENCES public.companies(id) ON DELETE SET NULL,
  evaluation_mode text NOT NULL CHECK (evaluation_mode IN ('specific-job', 'all-company-jobs', 'all-companies')),
  evaluation_score numeric,
  evaluation_summary text,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  PRIMARY KEY (id)
);

-- Add RLS policies for the evaluations table
ALTER TABLE public.evaluations ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view their own evaluations"
  ON public.evaluations FOR SELECT
  USING (
    auth.uid() = user_id OR
    EXISTS (
      SELECT 1 FROM public.candidates
      WHERE evaluations.candidate_id = candidates.id
      AND candidates.user_id = auth.uid()
    ) OR
    EXISTS (
      SELECT 1 FROM public.candidates
      JOIN public.jobs ON candidates.job_id = jobs.id
      JOIN public.team_members ON jobs.company_id = team_members.company_id
      WHERE evaluations.candidate_id = candidates.id
      AND team_members.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can insert their own evaluations"
  ON public.evaluations FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own evaluations"
  ON public.evaluations FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own evaluations"
  ON public.evaluations FOR DELETE
  USING (auth.uid() = user_id);

-- Add index for faster queries
CREATE INDEX IF NOT EXISTS evaluations_candidate_id_idx ON public.evaluations (candidate_id);
CREATE INDEX IF NOT EXISTS evaluations_job_id_idx ON public.evaluations (job_id);
CREATE INDEX IF NOT EXISTS evaluations_company_id_idx ON public.evaluations (company_id);
CREATE INDEX IF NOT EXISTS evaluations_user_id_idx ON public.evaluations (user_id);
