import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanies } from '@/hooks/use-companies';

interface SetupStatus {
  isLoading: boolean;
  needsSetup: boolean;
  hasCompanies: boolean;
}

/**
 * Hook to check if a user needs to complete the initial setup
 * Returns loading state and whether the user needs to complete setup
 */
export function useSetupStatus(): SetupStatus {
  const { user, isLoading: authLoading } = useAuth();
  const { data: companies, isLoading: companiesLoading } = useCompanies();
  const [needsSetup, setNeedsSetup] = useState<boolean>(false);
  
  useEffect(() => {
    // If we have user and companies data, check if setup is needed
    if (!authLoading && !companiesLoading && user) {
      // User needs setup if they have no companies
      const hasNoCompanies = !companies || companies.length === 0;
      setNeedsSetup(hasNoCompanies);
    }
  }, [user, companies, authLoading, companiesLoading]);
  
  return {
    isLoading: authLoading || companiesLoading,
    needsSetup,
    hasCompanies: companies && companies.length > 0
  };
}
