import { supabase } from '@/lib/supabase';
import { safeDbOperation } from './utils';

/**
 * Job-specific match interface
 */
export interface JobSpecificMatch {
  jobId: string;
  jobTitle: string;
  companyId?: string;
  companyName?: string;
  overallScore: number;
  skillsScore: number;
  experienceScore: number;
  educationScore: number;
  locationScore: number;
}

/**
 * Evaluation interface
 */
export interface Evaluation {
  id: string;
  created_at: string;
  candidate_id: string;
  job_id?: string;
  company_id?: string;
  evaluation_mode: 'specific-job' | 'all-company-jobs' | 'all-companies';
  evaluation_score: number;
  evaluation_summary: string;
  user_id: string;
  // Additional fields for UI
  job_title?: string;
  company_name?: string;
  evaluation_date?: string;
  // Parsed data from evaluation_summary
  parsedSummary?: {
    jobSpecificMatches?: JobSpecificMatch[];
    topMatchingJobs?: any[];
    isCompanyEvaluation?: boolean;
  };
}

/**
 * Get all evaluations for a candidate
 */
export const getCandidateEvaluations = async (candidateId: string): Promise<Evaluation[]> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Fetch evaluations for the candidate
      const { data: evaluations, error } = await supabase
        .from('evaluations')
        .select('*')
        .eq('candidate_id', candidateId)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching evaluations:', error);
        throw error;
      }



      if (!evaluations || evaluations.length === 0) {
        return [];
      }

      // Fetch jobs and companies to get their names
      const jobIds = evaluations
        .filter(e => e.job_id)
        .map(e => e.job_id);

      const companyIds = evaluations
        .filter(e => e.company_id)
        .map(e => e.company_id);

      const { data: jobs } = await supabase
        .from('jobs')
        .select('id, title, company_id')
        .in('id', jobIds.length > 0 ? jobIds : ['00000000-0000-0000-0000-000000000000']);

      const { data: companies } = await supabase
        .from('companies')
        .select('id, name')
        .in('id', companyIds.length > 0 ? companyIds : ['00000000-0000-0000-0000-000000000000']);

      // Enhance evaluations with job and company names
      const enhancedEvaluations = evaluations.map(evaluation => {
        const job = jobs?.find(j => j.id === evaluation.job_id);
        const company = companies?.find(c => c.id === evaluation.company_id ||
                                           (job && job.company_id === c.id));

        return {
          ...evaluation,
          job_title: job?.title || 'Unknown Job',
          company_name: company?.name || 'Unknown Company',
          evaluation_date: new Date(evaluation.created_at).toLocaleDateString()
        };
      });

      return enhancedEvaluations;
    },
    `Failed to fetch evaluations for candidate ${candidateId}`,
    []
  );
};

/**
 * Get a specific evaluation by ID
 */
export const getEvaluation = async (evaluationId: string): Promise<Evaluation | null> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Fetch the evaluation
      const { data: evaluation, error } = await supabase
        .from('evaluations')
        .select('*')
        .eq('id', evaluationId)
        .single();

      if (error) {
        throw error;
      }

      if (!evaluation) {
        return null;
      }

      // Fetch job and company to get their names
      let job = null;
      let company = null;

      if (evaluation.job_id) {
        const { data: jobData } = await supabase
          .from('jobs')
          .select('id, title, company_id')
          .eq('id', evaluation.job_id)
          .single();

        job = jobData;

        if (job && job.company_id) {
          const { data: companyData } = await supabase
            .from('companies')
            .select('id, name')
            .eq('id', job.company_id)
            .single();

          company = companyData;
        }
      } else if (evaluation.company_id) {
        const { data: companyData } = await supabase
          .from('companies')
          .select('id, name')
          .eq('id', evaluation.company_id)
          .single();

        company = companyData;
      }

      // Enhance evaluation with job and company names
      return {
        ...evaluation,
        job_title: job?.title || 'Unknown Job',
        company_name: company?.name || 'Unknown Company',
        evaluation_date: new Date(evaluation.created_at).toLocaleDateString()
      };
    },
    `Failed to fetch evaluation ${evaluationId}`,
    null
  );
};
