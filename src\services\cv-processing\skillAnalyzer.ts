import { extractSkills } from '@/lib/ai/operations';
import { supabase } from '@/lib/supabase';

// Interface for skill analysis
export interface SkillAnalysis {
  technical: {
    count: number;
    skills: Array<{
      name: string;
      count: number;
      candidates: string[];
    }>;
  };
  domain: {
    count: number;
    skills: Array<{
      name: string;
      count: number;
      candidates: string[];
    }>;
  };
  soft: {
    count: number;
    skills: Array<{
      name: string;
      count: number;
      candidates: string[];
    }>;
  };
}

/**
 * Extract skills from job description
 */
export async function extractSkillsFromJobDescription(jobId: string) {
  try {
    // Get the job data
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('*')
      .eq('id', jobId)
      .single();
    
    if (jobError) {
      console.error('Error fetching job:', jobError);
      throw jobError;
    }
    
    // Extract skills from the job description
    const extractedSkills = await extractSkills(job.description);
    
    // Update the job record with the extracted skills
    const { error: updateError } = await supabase
      .from('jobs')
      .update({
        requirements: JSON.stringify({
          ...JSON.parse(job.requirements || '{}'),
          extractedSkills
        })
      })
      .eq('id', jobId);
    
    if (updateError) {
      console.error('Error updating job with extracted skills:', updateError);
      throw updateError;
    }
    
    return extractedSkills;
  } catch (error) {
    console.error('Error extracting skills from job description:', error);
    throw error;
  }
}

/**
 * Analyze skills across candidates for a job
 */
export async function analyzeSkillsForJob(jobId: string): Promise<SkillAnalysis> {
  try {
    // Get all candidates for the job
    const { data: candidates, error: candidatesError } = await supabase
      .from('candidates')
      .select('id, name, evaluation_summary')
      .eq('job_id', jobId);
    
    if (candidatesError) {
      console.error('Error fetching candidates:', candidatesError);
      throw candidatesError;
    }
    
    // Initialize skill analysis
    const skillAnalysis: SkillAnalysis = {
      technical: { count: 0, skills: [] },
      domain: { count: 0, skills: [] },
      soft: { count: 0, skills: [] }
    };
    
    // Process each candidate
    candidates.forEach(candidate => {
      try {
        const evaluationSummary = JSON.parse(candidate.evaluation_summary || '{}');
        const extractedSkills = evaluationSummary.extractedSkills;
        
        if (!extractedSkills) return;
        
        // Process technical skills
        extractedSkills.technical?.forEach((skill: any) => {
          const existingSkill = skillAnalysis.technical.skills.find(s => s.name.toLowerCase() === skill.name.toLowerCase());
          
          if (existingSkill) {
            existingSkill.count++;
            if (!existingSkill.candidates.includes(candidate.id)) {
              existingSkill.candidates.push(candidate.id);
            }
          } else {
            skillAnalysis.technical.skills.push({
              name: skill.name,
              count: 1,
              candidates: [candidate.id]
            });
            skillAnalysis.technical.count++;
          }
        });
        
        // Process domain skills
        extractedSkills.domain?.forEach((skill: any) => {
          const existingSkill = skillAnalysis.domain.skills.find(s => s.name.toLowerCase() === skill.name.toLowerCase());
          
          if (existingSkill) {
            existingSkill.count++;
            if (!existingSkill.candidates.includes(candidate.id)) {
              existingSkill.candidates.push(candidate.id);
            }
          } else {
            skillAnalysis.domain.skills.push({
              name: skill.name,
              count: 1,
              candidates: [candidate.id]
            });
            skillAnalysis.domain.count++;
          }
        });
        
        // Process soft skills
        extractedSkills.soft?.forEach((skill: any) => {
          const existingSkill = skillAnalysis.soft.skills.find(s => s.name.toLowerCase() === skill.name.toLowerCase());
          
          if (existingSkill) {
            existingSkill.count++;
            if (!existingSkill.candidates.includes(candidate.id)) {
              existingSkill.candidates.push(candidate.id);
            }
          } else {
            skillAnalysis.soft.skills.push({
              name: skill.name,
              count: 1,
              candidates: [candidate.id]
            });
            skillAnalysis.soft.count++;
          }
        });
      } catch (error) {
        console.error(`Error processing skills for candidate ${candidate.id}:`, error);
      }
    });
    
    // Sort skills by count in descending order
    skillAnalysis.technical.skills.sort((a, b) => b.count - a.count);
    skillAnalysis.domain.skills.sort((a, b) => b.count - a.count);
    skillAnalysis.soft.skills.sort((a, b) => b.count - a.count);
    
    return skillAnalysis;
  } catch (error) {
    console.error('Error analyzing skills for job:', error);
    throw error;
  }
}

/**
 * Get skill gap analysis for a candidate
 */
export async function getSkillGapAnalysis(candidateId: string, jobId: string) {
  try {
    // Get the candidate data
    const { data: candidate, error: candidateError } = await supabase
      .from('candidates')
      .select('*')
      .eq('id', candidateId)
      .single();
    
    if (candidateError) {
      console.error('Error fetching candidate:', candidateError);
      throw candidateError;
    }
    
    // Get the job data
    const { data: job, error: jobError } = await supabase
      .from('jobs')
      .select('*')
      .eq('id', jobId)
      .single();
    
    if (jobError) {
      console.error('Error fetching job:', jobError);
      throw jobError;
    }
    
    // Parse the evaluation summary to get the extracted skills
    let candidateSkills: any;
    try {
      const evaluationSummary = JSON.parse(candidate.evaluation_summary || '{}');
      candidateSkills = evaluationSummary.extractedSkills;
    } catch (error) {
      console.error('Error parsing evaluation summary:', error);
      throw new Error('Invalid evaluation summary format');
    }
    
    // Parse the job requirements to get the required skills
    let requiredSkills: any;
    try {
      const requirements = JSON.parse(job.requirements || '{}');
      requiredSkills = requirements.extractedSkills;
      
      // If required skills are not available, extract them now
      if (!requiredSkills) {
        requiredSkills = await extractSkillsFromJobDescription(jobId);
      }
    } catch (error) {
      console.error('Error parsing job requirements:', error);
      throw new Error('Invalid job requirements format');
    }
    
    // Compare candidate skills with required skills
    const skillGapAnalysis = {
      matchedSkills: {
        technical: [] as string[],
        domain: [] as string[],
        soft: [] as string[]
      },
      missingSkills: {
        technical: [] as string[],
        domain: [] as string[],
        soft: [] as string[]
      }
    };
    
    // Check technical skills
    requiredSkills.technical?.forEach((skill: any) => {
      const hasSkill = candidateSkills.technical?.some((s: any) => 
        s.name.toLowerCase() === skill.name.toLowerCase()
      );
      
      if (hasSkill) {
        skillGapAnalysis.matchedSkills.technical.push(skill.name);
      } else {
        skillGapAnalysis.missingSkills.technical.push(skill.name);
      }
    });
    
    // Check domain skills
    requiredSkills.domain?.forEach((skill: any) => {
      const hasSkill = candidateSkills.domain?.some((s: any) => 
        s.name.toLowerCase() === skill.name.toLowerCase()
      );
      
      if (hasSkill) {
        skillGapAnalysis.matchedSkills.domain.push(skill.name);
      } else {
        skillGapAnalysis.missingSkills.domain.push(skill.name);
      }
    });
    
    // Check soft skills
    requiredSkills.soft?.forEach((skill: any) => {
      const hasSkill = candidateSkills.soft?.some((s: any) => 
        s.name.toLowerCase() === skill.name.toLowerCase()
      );
      
      if (hasSkill) {
        skillGapAnalysis.matchedSkills.soft.push(skill.name);
      } else {
        skillGapAnalysis.missingSkills.soft.push(skill.name);
      }
    });
    
    return skillGapAnalysis;
  } catch (error) {
    console.error('Error getting skill gap analysis:', error);
    throw error;
  }
}
