/**
 * Types for AI Settings configuration
 */

export type AIProvider = 'groq' | 'anthropic' | 'openai' | 'gemini';

export interface AISettings {
  id: string;
  created_at: string;
  updated_at: string;
  provider: AIProvider;
  model: string;
  api_key: string;
  is_active: boolean;
  metadata: Record<string, any>;
  created_by?: string;
  updated_by?: string;
}

export interface AISettingsInsert {
  provider: AIProvider;
  model: string;
  api_key: string;
  is_active?: boolean;
  metadata?: Record<string, any>;
}

export interface AISettingsUpdate {
  provider?: AIProvider;
  model?: string;
  api_key?: string;
  is_active?: boolean;
  metadata?: Record<string, any>;
}

export interface AIProviderConfig {
  name: string;
  description: string;
  models: Array<{
    id: string;
    name: string;
  }>;
}

export interface AISettingsResponse {
  data: AISettings | null;
  error: string | null;
}

export interface AISettingsListResponse {
  data: AISettings[] | null;
  error: string | null;
}

// Available AI providers and their models
export const AI_PROVIDERS: Record<AIProvider, AIProviderConfig> = {
  groq: {
    name: 'GROQ API',
    description: 'High-performance inference for Llama models',
    models: [
      { id: 'llama-3.3-70b-versatile', name: 'Llama 3.3 70B Versatile (Recommended)' },
      { id: 'llama-3.3-8b-versatile', name: 'Llama 3.3 8B Versatile' },
      { id: 'llama-3.1-70b-versatile', name: 'Llama 3.1 70B Versatile' },
      { id: 'llama-3.1-8b-versatile', name: 'Llama 3.1 8B Versatile' },
      { id: 'mixtral-8x7b-32768', name: 'Mixtral 8x7B' },
      { id: 'gemma-7b-it', name: 'Gemma 7B' }
    ]
  },
  anthropic: {
    name: 'Anthropic Claude',
    description: 'Advanced reasoning and analysis capabilities',
    models: [
      { id: 'claude-3-5-sonnet-20241022', name: 'Claude 3.5 Sonnet (Latest)' },
      { id: 'claude-3-5-haiku-20241022', name: 'Claude 3.5 Haiku' },
      { id: 'claude-3-opus-20240229', name: 'Claude 3 Opus' },
      { id: 'claude-3-sonnet-20240229', name: 'Claude 3 Sonnet' },
      { id: 'claude-3-haiku-20240307', name: 'Claude 3 Haiku' }
    ]
  },
  openai: {
    name: 'OpenAI GPT',
    description: 'Versatile language models for various tasks',
    models: [
      { id: 'gpt-4o', name: 'GPT-4o (Latest)' },
      { id: 'gpt-4o-mini', name: 'GPT-4o Mini' },
      { id: 'gpt-4-turbo', name: 'GPT-4 Turbo' },
      { id: 'gpt-4', name: 'GPT-4' },
      { id: 'gpt-3.5-turbo', name: 'GPT-3.5 Turbo' }
    ]
  },
  gemini: {
    name: 'Google Gemini',
    description: 'Advanced multimodal AI with strong reasoning capabilities',
    models: [
      { id: 'gemini-2.5-pro', name: 'Gemini 2.5 Pro (Latest - But slow)' },
      { id: 'gemini-2.5-flash', name: 'Gemini 2.5 Flash (Faster)' },
      { id: 'gemini-2.0-flash-exp', name: 'Gemini 2.0 Flash Experimental' },
      { id: 'gemini-1.5-pro', name: 'Gemini 1.5 Pro' },
      { id: 'gemini-1.5-flash', name: 'Gemini 1.5 Flash' },
      { id: 'gemini-1.0-pro', name: 'Gemini 1.0 Pro' }
    ]
  }
};
