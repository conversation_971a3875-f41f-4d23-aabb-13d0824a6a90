/**
 * React Query hooks for usage tracking
 */

import { useAuth } from '@/contexts/AuthContext';
import { useEnhancedQuery, useEnhancedMutation, queryKeys } from '@/lib/queryUtils';
import * as usageService from '@/services/supabase/usage';
import * as profileService from '@/services/supabase/profiles';
import { UsageType, UsageData, SubscriptionLimits } from '@/services/supabase/usage';

/**
 * Hook to fetch current usage
 */
export function useCurrentUsage() {
  const { user } = useAuth();
  
  return useEnhancedQuery<UsageData | null>(
    queryKeys.usage.current(user?.id),
    () => usageService.getCurrentUsage(user?.id || ''),
    {
      enabled: !!user?.id,
      fallbackData: null,
      errorMessage: 'Failed to load usage data',
    }
  );
}

/**
 * Hook to fetch subscription limits for the user's current tier
 */
export function useSubscriptionLimits() {
  const { user } = useAuth();
  const { data: profile } = useEnhancedQuery(
    queryKeys.profiles.detail(user?.id),
    () => profileService.getProfile(user?.id || ''),
    {
      enabled: !!user?.id,
      fallbackData: null,
      errorMessage: 'Failed to load profile',
    }
  );
  
  const tier = profile?.subscription_tier || 'starter';
  
  return useEnhancedQuery<SubscriptionLimits | null>(
    queryKeys.usage.limits(tier),
    () => usageService.getSubscriptionLimits(tier as 'starter' | 'growth' | 'pro'),
    {
      enabled: !!tier,
      fallbackData: null,
      errorMessage: `Failed to load subscription limits for ${tier} tier`,
    }
  );
}

/**
 * Hook to check if user has reached a specific usage limit
 */
export function useUsageLimit(usageType: UsageType) {
  const { user } = useAuth();
  const { data: profile } = useEnhancedQuery(
    queryKeys.profiles.detail(user?.id),
    () => profileService.getProfile(user?.id || ''),
    {
      enabled: !!user?.id,
      fallbackData: null,
      errorMessage: 'Failed to load profile',
    }
  );
  
  const tier = profile?.subscription_tier || 'starter';
  
  return useEnhancedQuery(
    queryKeys.usage.limit(user?.id, usageType, tier),
    () => usageService.checkUsageLimit(
      user?.id || '', 
      usageType, 
      tier as 'starter' | 'growth' | 'pro'
    ),
    {
      enabled: !!user?.id && !!tier,
      fallbackData: { hasReachedLimit: false, currentUsage: 0, limit: 0 },
      errorMessage: `Failed to check ${usageType} usage limit`,
    }
  );
}

/**
 * Hook to increment usage
 */
export function useIncrementUsage() {
  const { user } = useAuth();
  const queryClient = useEnhancedMutation(
    ({ usageType, incrementBy = 1 }: { usageType: UsageType; incrementBy?: number }) => 
      usageService.incrementUsage(user?.id || '', usageType, incrementBy),
    {
      errorMessage: 'Failed to update usage',
      successMessage: 'Usage updated successfully',
      invalidateQueries: (_, { usageType }) => [
        queryKeys.usage.current(user?.id),
        queryKeys.usage.limit(user?.id, usageType),
      ],
    }
  );
  
  return queryClient;
}

/**
 * Hook to decrement usage
 */
export function useDecrementUsage() {
  const { user } = useAuth();
  const queryClient = useEnhancedMutation(
    ({ usageType, decrementBy = 1 }: { usageType: UsageType; decrementBy?: number }) => 
      usageService.decrementUsage(user?.id || '', usageType, decrementBy),
    {
      errorMessage: 'Failed to update usage',
      successMessage: 'Usage updated successfully',
      invalidateQueries: (_, { usageType }) => [
        queryKeys.usage.current(user?.id),
        queryKeys.usage.limit(user?.id, usageType),
      ],
    }
  );
  
  return queryClient;
}

/**
 * Hook to reset user usage
 */
export function useResetUsage() {
  const { user } = useAuth();
  const queryClient = useEnhancedMutation(
    () => usageService.resetUserUsage(user?.id || ''),
    {
      errorMessage: 'Failed to reset usage',
      successMessage: 'Usage reset successfully',
      invalidateQueries: () => [
        queryKeys.usage.current(user?.id),
        queryKeys.usage.all,
      ],
    }
  );
  
  return queryClient;
}

/**
 * Hook to set specific usage value
 */
export function useSetUsageValue() {
  const { user } = useAuth();
  const queryClient = useEnhancedMutation(
    ({ usageType, value }: { usageType: UsageType; value: number }) => 
      usageService.setUsageValue(user?.id || '', usageType, value),
    {
      errorMessage: 'Failed to set usage value',
      successMessage: 'Usage value set successfully',
      invalidateQueries: (_, { usageType }) => [
        queryKeys.usage.current(user?.id),
        queryKeys.usage.limit(user?.id, usageType),
      ],
    }
  );
  
  return queryClient;
}
