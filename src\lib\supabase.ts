import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import { env } from '@/lib/env';

/**
 * Supabase client for interacting with the database
 * Uses environment variables validated by the env utility
 */
// Create a more stable Supabase client that won't trigger unnecessary auth events
export const supabase = createClient<Database>(env.SUPABASE_URL, env.SUPABASE_ANON_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false, // Prevents auto-refreshing on tab switch
    storageKey: 'supabase-auth', // Use a consistent storage key
  },
});
