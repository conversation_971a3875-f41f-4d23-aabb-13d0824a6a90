import { format } from 'date-fns';

// Import types
import type { ReportType, ReportFormat, ReportData } from './types';

// Import PDF generator functions
import { generatePdfReport, downloadPdfReport } from './generators/pdfGenerator';

// Import Excel generator functions
import { generateExcelReport, downloadExcelReport } from './generators/excelGenerator';

// Import subscription utilities
import {
  canExportReportFormat,
  getAvailableReportFormats,
  getAvailableReportTypes
} from './subscriptionUtils';

// Re-export types and functions for public API
export type { ReportType, ReportFormat, ReportData };
export { generatePdfReport, downloadPdfReport };
export { generateExcelReport, downloadExcelReport };
export { canExportReportFormat, getAvailableReportFormats, getAvailableReportTypes };

/**
 * Export a report in the specified format
 */
export const exportReport = (
  reportData: ReportData,
  reportType: ReportType,
  format: ReportFormat
): void => {
  if (format === 'pdf') {
    downloadPdfReport(reportData, reportType);
  } else if (format === 'excel') {
    downloadExcelReport(reportData, reportType);
  }
};
