import { jsPD<PERSON> } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { Invoice, InvoiceItem } from '@/services/supabase/invoices';
import { format } from 'date-fns';

// Extend jsPDF with autotable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: typeof autoTable;
    lastAutoTable: {
      finalY: number;
    };
  }
}

/**
 * Generate a PDF invoice
 */
export const generateInvoicePdf = (
  invoice: Invoice,
  companyInfo: any,
  userInfo: any
): jsPDF => {
  // Create a new PDF document
  const doc = new jsPDF();
  
  // Set document properties
  doc.setProperties({
    title: `Invoice ${invoice.invoice_number}`,
    subject: 'Invoice',
    author: 'Sourcio.ai',
    keywords: 'invoice, payment',
    creator: 'Sourcio.ai Platform',
  });

  // Add company logo (if available)
  // This would require a base64 encoded image
  // doc.addImage(companyLogo, 'PNG', 15, 15, 50, 15);

  // Add invoice title
  doc.setFontSize(20);
  doc.setTextColor(0, 0, 0);
  doc.text('INVOICE', 105, 20, { align: 'center' });

  // Add invoice number and date
  doc.setFontSize(10);
  doc.text(`Invoice Number: ${invoice.invoice_number}`, 150, 30, { align: 'right' });
  doc.text(`Issue Date: ${format(new Date(invoice.issue_date), 'MMMM d, yyyy')}`, 150, 35, { align: 'right' });
  doc.text(`Due Date: ${format(new Date(invoice.due_date), 'MMMM d, yyyy')}`, 150, 40, { align: 'right' });

  // Add status
  doc.setFontSize(12);
  let statusColor;
  switch (invoice.status) {
    case 'paid':
      statusColor = [0, 128, 0]; // Green
      break;
    case 'overdue':
      statusColor = [255, 0, 0]; // Red
      break;
    case 'void':
      statusColor = [128, 128, 128]; // Gray
      break;
    default:
      statusColor = [0, 0, 0]; // Black
  }
  doc.setTextColor(statusColor[0], statusColor[1], statusColor[2]);
  doc.text(invoice.status.toUpperCase(), 150, 50, { align: 'right' });
  doc.setTextColor(0, 0, 0);

  // Add company information
  doc.setFontSize(10);
  doc.text('From:', 15, 30);
  doc.setFontSize(12);
  doc.text(companyInfo?.name || 'Sourcio.ai', 15, 35);
  doc.setFontSize(10);
  if (companyInfo?.address) {
    const address = companyInfo.address;
    doc.text(address.line1, 15, 40);
    if (address.line2) doc.text(address.line2, 15, 45);
    doc.text(`${address.city}, ${address.state} ${address.postalCode}`, 15, address.line2 ? 50 : 45);
    doc.text(address.country, 15, address.line2 ? 55 : 50);
  } else {
    doc.text('123 Business St', 15, 40);
    doc.text('Suite 100', 15, 45);
    doc.text('Business City, ST 12345', 15, 50);
    doc.text('United States', 15, 55);
  }

  // Add billing information
  doc.setFontSize(10);
  doc.text('Bill To:', 15, 65);
  doc.setFontSize(12);
  doc.text(invoice.billing_info?.company_name || userInfo?.full_name || 'Client', 15, 70);
  doc.setFontSize(10);
  
  if (invoice.billing_info?.address_line1) {
    doc.text(invoice.billing_info.address_line1, 15, 75);
  }
  
  let currentY = 75;
  if (invoice.billing_info?.address_line2) {
    currentY += 5;
    doc.text(invoice.billing_info.address_line2, 15, currentY);
  }
  
  if (invoice.billing_info?.city && invoice.billing_info?.state && invoice.billing_info?.postal_code) {
    currentY += 5;
    doc.text(
      `${invoice.billing_info.city}, ${invoice.billing_info.state} ${invoice.billing_info.postal_code}`,
      15,
      currentY
    );
  }
  
  if (invoice.billing_info?.country) {
    currentY += 5;
    doc.text(invoice.billing_info.country, 15, currentY);
  }
  
  if (invoice.billing_info?.billing_email) {
    currentY += 5;
    doc.text(`Email: ${invoice.billing_info.billing_email}`, 15, currentY);
  }
  
  if (invoice.billing_info?.phone) {
    currentY += 5;
    doc.text(`Phone: ${invoice.billing_info.phone}`, 15, currentY);
  }

  // Add invoice items table
  const startY = currentY + 15;
  
  // Prepare table data
  const tableColumn = ['Description', 'Quantity', 'Unit Price', 'Amount'];
  const tableRows = invoice.invoice_items.map((item: InvoiceItem) => [
    item.description,
    item.quantity.toString(),
    `${invoice.currency} ${item.unit_price.toFixed(2)}`,
    `${invoice.currency} ${item.amount.toFixed(2)}`,
  ]);

  // Add the table
  autoTable(doc, {
    head: [tableColumn],
    body: tableRows,
    startY,
    theme: 'grid',
    styles: { fontSize: 10, cellPadding: 5 },
    headStyles: { fillColor: [41, 128, 185], textColor: 255 },
    columnStyles: {
      0: { cellWidth: 'auto' },
      1: { cellWidth: 30, halign: 'center' },
      2: { cellWidth: 40, halign: 'right' },
      3: { cellWidth: 40, halign: 'right' },
    },
  });

  // Add total
  const finalY = (doc as any).lastAutoTable.finalY + 10;
  doc.setFontSize(12);
  doc.text('Total:', 130, finalY);
  doc.setFontSize(14);
  doc.setFont('helvetica', 'bold');
  doc.text(`${invoice.currency} ${invoice.amount.toFixed(2)}`, 175, finalY, { align: 'right' });
  doc.setFont('helvetica', 'normal');

  // Add payment information
  doc.setFontSize(10);
  doc.text('Payment Information', 15, finalY + 20);
  doc.setFontSize(9);
  doc.text('Payment Method: PayPal', 15, finalY + 25);
  if (invoice.payment_date) {
    doc.text(`Payment Date: ${format(new Date(invoice.payment_date), 'MMMM d, yyyy')}`, 15, finalY + 30);
  }
  if (invoice.payment_id) {
    doc.text(`Payment ID: ${invoice.payment_id}`, 15, finalY + 35);
  }

  // Add footer
  const pageCount = doc.getNumberOfPages();
  for (let i = 1; i <= pageCount; i++) {
    doc.setPage(i);
    doc.setFontSize(8);
    doc.text(
      'This invoice was generated by Sourcio.ai Platform.',
      105,
      doc.internal.pageSize.height - 10,
      { align: 'center' }
    );
    doc.text(
      `Page ${i} of ${pageCount}`,
      doc.internal.pageSize.width - 20,
      doc.internal.pageSize.height - 10
    );
  }

  return doc;
};

/**
 * Download a PDF invoice
 */
export const downloadInvoicePdf = (
  invoice: Invoice,
  companyInfo: any,
  userInfo: any
): void => {
  const doc = generateInvoicePdf(invoice, companyInfo, userInfo);
  doc.save(`Invoice_${invoice.invoice_number}.pdf`);
};



