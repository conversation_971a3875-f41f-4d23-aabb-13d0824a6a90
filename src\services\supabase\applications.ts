import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';
import {
  Application,
  ApplicationInsert,
  ApplicationUpdate,
  ApplicationWithDetails,
  CreateApplicationPayload,
  BulkCreateApplicationPayload,
  ApplicationStatistics,
  JobApplicantSummary,
  ApplicationStatus
} from '@/types/applications';

/**
 * Create a new job application
 */
export const createApplication = async (payload: CreateApplicationPayload): Promise<Application> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      const applicationData: ApplicationInsert = {
        candidate_id: payload.candidate_id,
        job_id: payload.job_id,
        user_id: userId,
        notes: payload.notes || null,
        status: 'applied'
      };

      const { data, error } = await supabase
        .from('applications')
        .insert(applicationData)
        .select()
        .single();

      if (error) {
        console.error('Error creating application:', error);
        throw error;
      }

      return data;
    },
    'Failed to create job application'
  );
};

/**
 * Create multiple job applications (bulk assign candidates to a job)
 */
export const createBulkApplications = async (payload: BulkCreateApplicationPayload): Promise<Application[]> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      const applicationData: ApplicationInsert[] = payload.candidate_ids.map(candidateId => ({
        candidate_id: candidateId,
        job_id: payload.job_id,
        user_id: userId,
        notes: payload.notes || null,
        status: 'applied'
      }));

      const { data, error } = await supabase
        .from('applications')
        .insert(applicationData)
        .select();

      if (error) {
        console.error('Error creating bulk applications:', error);
        throw error;
      }

      return data;
    },
    'Failed to create bulk job applications'
  );
};

/**
 * Get all applications for a specific job
 */
export const getJobApplications = async (jobId: string): Promise<ApplicationWithDetails[]> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // RLS policies will handle access control - no need to filter by user_id
      // This will show applications for jobs the user owns or has access to
      const { data, error } = await supabase
        .from('applications')
        .select(`
          *,
          candidates (
            id,
            name,
            email,
            cv_url,
            status
          ),
          jobs (
            id,
            title,
            company_id,
            companies (
              id,
              name
            )
          )
        `)
        .eq('job_id', jobId)
        .order('applied_at', { ascending: false });

      if (error) {
        console.error('Error fetching job applications:', error);
        throw error;
      }

      return data || [];
    },
    'Failed to fetch job applications'
  );
};

/**
 * Get all applications for a specific candidate
 */
export const getCandidateApplications = async (candidateId: string): Promise<ApplicationWithDetails[]> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Let RLS policies handle access control - this will include:
      // 1. Applications created by the user (user_id = userId)
      // 2. Applications for jobs owned by the user
      // 3. Applications for jobs from companies the user is a team member of
      const { data, error } = await supabase
        .from('applications')
        .select(`
          *,
          candidates (
            id,
            name,
            email,
            cv_url,
            status
          ),
          jobs (
            id,
            title,
            company_id,
            companies (
              id,
              name
            )
          )
        `)
        .eq('candidate_id', candidateId)
        .order('applied_at', { ascending: false });

      if (error) {
        console.error('Error fetching candidate applications:', error);
        throw error;
      }

      return data || [];
    },
    'Failed to fetch candidate applications'
  );
};

/**
 * Update application status
 */
export const updateApplicationStatus = async (
  applicationId: string, 
  status: ApplicationStatus,
  notes?: string
): Promise<Application> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      const updateData: ApplicationUpdate = {
        status,
        notes: notes || undefined
      };

      const { data, error } = await supabase
        .from('applications')
        .update(updateData)
        .eq('id', applicationId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating application status:', error);
        throw error;
      }

      return data;
    },
    'Failed to update application status'
  );
};

/**
 * Delete an application
 */
export const deleteApplication = async (applicationId: string): Promise<void> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      const { error } = await supabase
        .from('applications')
        .delete()
        .eq('id', applicationId)
        .eq('user_id', userId);

      if (error) {
        console.error('Error deleting application:', error);
        throw error;
      }
    },
    'Failed to delete application'
  );
};

/**
 * Get application statistics for a user
 */
export const getApplicationStatistics = async (companyId?: string): Promise<ApplicationStatistics> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      let query = supabase
        .from('applications')
        .select('status')
        .eq('user_id', userId);

      // Filter by company if provided
      if (companyId) {
        query = query.eq('jobs.company_id', companyId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching application statistics:', error);
        throw error;
      }

      const stats: ApplicationStatistics = {
        total: data?.length || 0,
        applied: 0,
        screening: 0,
        interviewed: 0,
        offer: 0,
        hired: 0,
        rejected: 0
      };

      data?.forEach(app => {
        switch (app.status) {
          case 'applied':
            stats.applied++;
            break;
          case 'screening':
            stats.screening++;
            break;
          case 'interviewed':
            stats.interviewed++;
            break;
          case 'offer':
            stats.offer++;
            break;
          case 'hired':
            stats.hired++;
            break;
          case 'rejected':
            stats.rejected++;
            break;
        }
      });

      return stats;
    },
    'Failed to fetch application statistics'
  );
};

/**
 * Get job applicant counts for multiple jobs
 */
export const getJobApplicantCounts = async (jobIds: string[]): Promise<Record<string, number>> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      if (jobIds.length === 0) {
        return {};
      }

      // RLS policies will handle access control - no need to filter by user_id
      // This will count applications for jobs the user owns or has access to
      const { data, error } = await supabase
        .from('applications')
        .select('job_id')
        .in('job_id', jobIds);

      if (error) {
        console.error('Error fetching job applicant counts:', error);
        throw error;
      }

      // Count applications per job
      const counts: Record<string, number> = {};

      // Initialize all jobs with 0
      jobIds.forEach(jobId => {
        counts[jobId] = 0;
      });

      // Count actual applications
      data?.forEach(app => {
        counts[app.job_id] = (counts[app.job_id] || 0) + 1;
      });

      return counts;
    },
    'Failed to fetch job applicant counts'
  );
};
