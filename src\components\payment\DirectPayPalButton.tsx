import React, { useEffect, useRef, useState } from 'react';
import { Loader2 } from 'lucide-react';
import { SUBSCRIPTION_PLANS } from '@/config/paypal';
import { useToast } from '@/hooks/use-toast';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import * as subscriptionService from '@/services/supabase/subscriptions';
import './PayPalButton.css';

interface DirectPayPalButtonProps {
  tier: 'STARTER' | 'GROWTH' | 'PRO';
}

const DirectPayPalButton: React.FC<DirectPayPalButtonProps> = ({ tier }) => {
  const paypalButtonRef = useRef<HTMLDivElement>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();
  const navigate = useNavigate();
  const { user } = useAuth();

  // Check if user is authenticated
  useEffect(() => {
    if (!user) {
      setError('Please log in to subscribe');
      setLoading(false);
      return;
    }

    // Load the PayPal script directly
    // Remove any existing PayPal scripts to avoid conflicts
    document.querySelectorAll('script[src*="paypal.com/sdk/js"]').forEach(script => {
      console.log('Removing existing PayPal script:', script.src);
      script.remove();
    });

    // Clear any existing PayPal global objects
    if (window.paypal) {
      console.log('Clearing existing PayPal global object');
      delete window.paypal;
    }

    // Create a new script element
    const script = document.createElement('script');

    // Use a known working client ID
    const clientId = 'AUO3MAia2PsvyE-zM9xPZ99NUoPFZ-w13A3M_31IWSA1CJzvD3_UDogvtsMhWxtz9JIDmQ24H4Mzo8mE';

    // Set the script source with minimal parameters
    script.src = `https://www.paypal.com/sdk/js?client-id=${clientId}&vault=true&intent=subscription`;
    script.async = true;

    // Add a unique timestamp to prevent caching issues
    script.setAttribute('data-timestamp', Date.now().toString());

    // Handle script load event
    script.onload = () => {
      console.log('PayPal SDK loaded successfully');
      setLoading(false);

      // Check if PayPal object is available
      if (window.paypal) {
        console.log('PayPal object available:', window.paypal);
        renderPayPalButton(window.paypal);
      } else {
        console.error('PayPal object not available after script load');
        setError('PayPal failed to initialize. Please try again later.');
      }
    };

    // Handle script error event
    script.onerror = () => {
      console.error('Failed to load PayPal SDK');
      setError('Failed to load PayPal. Please try again later.');
      setLoading(false);
    };

    // Add the script to the document
    document.body.appendChild(script);

    // Clean up function
    return () => {
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
    };
  }, [user]);

  // Function to render the PayPal button
  const renderPayPalButton = (paypalObj: any) => {
    if (!paypalObj || !paypalObj.Buttons) {
      console.error('PayPal SDK not loaded properly');
      setError('PayPal is not available. Please try again later.');
      return;
    }

    // Clear the container
    if (paypalButtonRef.current) {
      paypalButtonRef.current.innerHTML = '';
    } else {
      console.error('PayPal button container ref is null');
      return;
    }

    try {
      // Get the plan ID based on the tier
      const planId = SUBSCRIPTION_PLANS[tier];
      console.log(`Using plan ID: ${planId} for ${tier} tier`);

      // Create the PayPal button
      const buttons = paypalObj.Buttons({
        style: {
          shape: 'rect',
          color: 'blue',
          layout: 'vertical',
          label: 'subscribe',
          tagline: false,
          height: 45
        },

        // Create subscription when button is clicked
        createSubscription: (_: any, actions: any) => {
          console.log(`Creating subscription with plan ID: ${planId}`);
          return actions.subscription.create({
            plan_id: planId
          });
        },

        // Handle successful subscription
        onApprove: async (data: any) => {
          console.log('Subscription approved:', data);

          try {
            if (!user) {
              throw new Error('User not authenticated');
            }

            // Create subscription record
            await subscriptionService.createSubscription(user.id, {
              subscription_id: data.subscriptionID,
              plan_id: planId,
              tier: tier.toLowerCase(),
              status: 'active',
              payment_method: 'paypal',
              payment_details: data,
            });

            toast({
              title: 'Subscription activated',
              description: `Your ${tier.toLowerCase()} plan has been activated successfully.`,
            });

            // Redirect to dashboard
            navigate('/dashboard');
          } catch (error) {
            console.error('Error creating subscription record:', error);
            toast({
              title: 'Error',
              description: 'There was an error activating your subscription. Please contact support.',
              variant: 'destructive',
            });
          }
        },

        // Handle errors
        onError: (err: any) => {
          console.error('PayPal Error:', err);
          setError('There was an error processing your subscription. Please try again later.');
        },

        // Handle cancellation
        onCancel: () => {
          console.log('Subscription cancelled');
          toast({
            title: 'Subscription cancelled',
            description: 'You can try again when you are ready.',
          });
        }
      });

      // Check if the button can be rendered
      if (buttons.isEligible && buttons.isEligible()) {
        buttons.render(paypalButtonRef.current);
        console.log('PayPal button rendered successfully');
      } else {
        console.error('PayPal button is not eligible for rendering');
        setError('PayPal button cannot be displayed. Please try again later.');
      }
    } catch (err) {
      console.error('Error creating PayPal button:', err);
      setError('Error displaying PayPal button. Please try again later.');
    }
  };

  return (
    <div className="paypal-button-container w-full">
      <div style={{ minHeight: '45px' }} className="w-full">
        {loading && (
          <div className="flex justify-center items-center p-4 border rounded-md bg-gray-50 w-full">
            <Loader2 className="h-5 w-5 animate-spin text-primary mr-2" />
            <span>Loading PayPal...</span>
          </div>
        )}

        {error && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-md mb-4 text-red-700 w-full">
            {error}
          </div>
        )}

        <div ref={paypalButtonRef} className="paypal-button w-full"></div>
      </div>
    </div>
  );
};

export default DirectPayPalButton;
