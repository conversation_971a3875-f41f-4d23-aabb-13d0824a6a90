import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useCreateCompany } from '@/hooks/use-companies';
import { useCreateJob } from '@/hooks/use-jobs';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  Building2, 
  Briefcase, 
  CheckCircle2, 
  ArrowRight, 
  ArrowLeft,
  Upload
} from 'lucide-react';

// Company form schema
const companyFormSchema = z.object({
  name: z.string().min(2, { message: 'Company name must be at least 2 characters' }),
  description: z.string().optional(),
  industry: z.string().min(1, { message: 'Please select an industry' }),
  size: z.string().min(1, { message: 'Please select a company size' }),
  location: z.string().min(2, { message: 'Location must be at least 2 characters' }),
  website: z.string().url({ message: 'Please enter a valid URL' }).optional().or(z.literal('')),
});

// Job form schema
const jobFormSchema = z.object({
  title: z.string().min(2, { message: 'Job title must be at least 2 characters' }),
  department: z.string().min(2, { message: 'Department must be at least 2 characters' }),
  location: z.string().min(2, { message: 'Location must be at least 2 characters' }),
  locationType: z.string().min(1, { message: 'Please select a location type' }),
  type: z.string().min(1, { message: 'Please select a job type' }),
  experience: z.string().min(1, { message: 'Please select an experience level' }),
  description: z.string().min(10, { message: 'Description must be at least 10 characters' }),
});

type CompanyFormValues = z.infer<typeof companyFormSchema>;
type JobFormValues = z.infer<typeof jobFormSchema>;

interface SetupWizardProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SetupWizard: React.FC<SetupWizardProps> = ({ isOpen, onClose }) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();
  const createCompanyMutation = useCreateCompany();
  const createJobMutation = useCreateJob();
  
  const [step, setStep] = useState(1);
  const [createdCompanyId, setCreatedCompanyId] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  
  // Company form
  const companyForm = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      name: '',
      description: '',
      industry: '',
      size: '',
      location: '',
      website: '',
    },
  });
  
  // Job form
  const jobForm = useForm<JobFormValues>({
    resolver: zodResolver(jobFormSchema),
    defaultValues: {
      title: '',
      department: '',
      location: '',
      locationType: 'onsite',
      type: 'full-time',
      experience: 'mid-level',
      description: '',
    },
  });
  
  // Handle logo file change
  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };
  
  // Handle company form submission
  const onCompanySubmit = async (data: CompanyFormValues) => {
    setIsSubmitting(true);
    
    try {
      // Create company
      const newCompany = await createCompanyMutation.mutateAsync({
        name: data.name,
        description: data.description || '',
        industry: data.industry,
        size: data.size,
        location: data.location,
        website: data.website || '',
        user_id: user?.id,
      });
      
      // Store the created company ID for the next step
      if (newCompany?.id) {
        setCreatedCompanyId(newCompany.id);
        
        // If we have a logo file, upload it
        if (logoFile && newCompany.id) {
          // Note: Logo upload would be handled here
          // This would typically call a service like uploadCompanyLogo
        }
        
        toast({
          title: 'Company created',
          description: 'Your company profile has been created successfully.',
        });
        
        // Move to the next step
        setStep(2);
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create company',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle job form submission
  const onJobSubmit = async (data: JobFormValues) => {
    if (!createdCompanyId) {
      toast({
        title: 'Error',
        description: 'Company ID is missing. Please try again.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsSubmitting(true);
    
    try {
      // Create job - transform data to match database schema
      await createJobMutation.mutateAsync({
        title: data.title,
        description: data.description,
        requirements: '', // Empty string for now
        location: `${data.location} (${data.locationType})`,
        salary_min: null,
        salary_max: null,
        job_type: data.type,
        experience_level: data.experience,
        company_id: createdCompanyId,
        user_id: user?.id,
        status: 'active',
      });
      
      toast({
        title: 'Job created',
        description: 'Your job posting has been created successfully.',
      });
      
      // Move to the completion step
      setStep(3);
    } catch (error) {
      toast({
        title: 'Error',
        description: error instanceof Error ? error.message : 'Failed to create job',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Handle completion
  const handleComplete = () => {
    onClose();
    navigate('/dashboard');
  };
  
  // Handle skip job creation
  const handleSkipJob = () => {
    setStep(3);
  };
  
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center">
            {step === 1 && 'Welcome to Sourcio.ai!'}
            {step === 2 && 'Create Your First Job'}
            {step === 3 && 'Setup Complete!'}
          </DialogTitle>
          <DialogDescription className="text-center text-base">
            {step === 1 && 'Let\'s set up your account by creating your first company profile.'}
            {step === 2 && 'Now, let\'s create your first job posting.'}
            {step === 3 && 'You\'re all set to start using Sourcio.ai!'}
          </DialogDescription>
        </DialogHeader>
        
        {/* Progress indicator */}
        <div className="flex justify-center mb-6">
          <div className="flex items-center space-x-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 1 ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              <Building2 size={16} />
            </div>
            <div className={`w-16 h-1 ${step >= 2 ? 'bg-primary' : 'bg-gray-200'}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 2 ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              <Briefcase size={16} />
            </div>
            <div className={`w-16 h-1 ${step >= 3 ? 'bg-primary' : 'bg-gray-200'}`}></div>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${step >= 3 ? 'bg-primary text-white' : 'bg-gray-200'}`}>
              <CheckCircle2 size={16} />
            </div>
          </div>
        </div>
        
        {/* Step 1: Create Company */}
        {step === 1 && (
          <Form {...companyForm}>
            <form onSubmit={companyForm.handleSubmit(onCompanySubmit)} className="space-y-4">
              {/* Company Logo Upload */}
              <div className="space-y-2">
                <FormLabel>Company Logo</FormLabel>
                <div className="flex flex-col items-center p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50">
                  {logoPreview ? (
                    <div className="relative mb-4">
                      <img 
                        src={logoPreview} 
                        alt="Company logo preview" 
                        className="w-24 h-24 object-contain"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="mt-2"
                        onClick={() => {
                          setLogoFile(null);
                          setLogoPreview(null);
                        }}
                      >
                        Remove
                      </Button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <Upload className="h-12 w-12 text-gray-400 mb-2" />
                      <p className="text-sm text-gray-500 mb-2">Upload your company logo</p>
                    </div>
                  )}
                  <Input
                    id="logo"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={handleLogoChange}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => document.getElementById('logo')?.click()}
                  >
                    {logoPreview ? 'Change Logo' : 'Select Logo'}
                  </Button>
                </div>
              </div>
              
              {/* Company Name */}
              <FormField
                control={companyForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Acme Corporation" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Company Description */}
              <FormField
                control={companyForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Description</FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Brief description of your company" 
                        className="resize-none" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Industry */}
              <FormField
                control={companyForm.control}
                name="industry"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Industry <span className="text-red-500">*</span></FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select industry" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="technology">Technology</SelectItem>
                        <SelectItem value="healthcare">Healthcare</SelectItem>
                        <SelectItem value="finance">Finance</SelectItem>
                        <SelectItem value="education">Education</SelectItem>
                        <SelectItem value="retail">Retail</SelectItem>
                        <SelectItem value="manufacturing">Manufacturing</SelectItem>
                        <SelectItem value="services">Services</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Company Size */}
              <FormField
                control={companyForm.control}
                name="size"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Size <span className="text-red-500">*</span></FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select company size" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="1-10">1-10 employees</SelectItem>
                        <SelectItem value="11-50">11-50 employees</SelectItem>
                        <SelectItem value="51-200">51-200 employees</SelectItem>
                        <SelectItem value="201-500">201-500 employees</SelectItem>
                        <SelectItem value="501-1000">501-1000 employees</SelectItem>
                        <SelectItem value="1001+">1001+ employees</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Location */}
              <FormField
                control={companyForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. New York, NY" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Website */}
              <FormField
                control={companyForm.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. https://example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex justify-end pt-4">
                <Button type="submit" disabled={isSubmitting} className="bg-primary-gradient">
                  {isSubmitting ? 'Creating...' : 'Next'}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
            </form>
          </Form>
        )}
        
        {/* Step 2: Create Job */}
        {step === 2 && (
          <Form {...jobForm}>
            <form onSubmit={jobForm.handleSubmit(onJobSubmit)} className="space-y-4">
              {/* Job Title */}
              <FormField
                control={jobForm.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Title <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Frontend Developer" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Department */}
              <FormField
                control={jobForm.control}
                name="department"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Department <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input placeholder="e.g. Engineering" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Location */}
              <FormField
                control={jobForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>
                      Location <span className="text-red-500">*</span>
                      {jobForm.watch('locationType') === 'remote' && (
                        <span className="text-sm text-gray-500 ml-2">(Optional for remote jobs)</span>
                      )}
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder={
                          jobForm.watch('locationType') === 'remote'
                            ? "e.g. Preferred time zone or 'Worldwide'"
                            : "e.g. New York, NY"
                        }
                        {...field}
                      />
                    </FormControl>
                    {jobForm.watch('locationType') === 'remote' && (
                      <p className="text-xs text-gray-500">
                        For remote jobs, you can specify time zone preferences or leave blank for worldwide
                      </p>
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Location Type */}
              <FormField
                control={jobForm.control}
                name="locationType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Location Type <span className="text-red-500">*</span></FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select location type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="onsite">On-site</SelectItem>
                        <SelectItem value="remote">Remote</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Job Type */}
              <FormField
                control={jobForm.control}
                name="type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Type <span className="text-red-500">*</span></FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select job type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="full-time">Full-time</SelectItem>
                        <SelectItem value="part-time">Part-time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="internship">Internship</SelectItem>
                        <SelectItem value="temporary">Temporary</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Experience Level */}
              <FormField
                control={jobForm.control}
                name="experience"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Experience Level <span className="text-red-500">*</span></FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select experience level" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="entry-level">Entry Level</SelectItem>
                        <SelectItem value="mid-level">Mid Level</SelectItem>
                        <SelectItem value="senior">Senior</SelectItem>
                        <SelectItem value="executive">Executive</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Job Description */}
              <FormField
                control={jobForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Job Description <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Textarea 
                        placeholder="Describe the job role, responsibilities, and requirements" 
                        className="resize-none min-h-[100px]" 
                        {...field} 
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <div className="flex justify-between pt-4">
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={handleSkipJob}
                >
                  Skip for now
                </Button>
                <div className="flex space-x-2">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setStep(1)}
                  >
                    <ArrowLeft className="mr-2 h-4 w-4" />
                    Back
                  </Button>
                  <Button type="submit" disabled={isSubmitting} className="bg-primary-gradient">
                    {isSubmitting ? 'Creating...' : 'Next'}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </div>
              </div>
            </form>
          </Form>
        )}
        
        {/* Step 3: Completion */}
        {step === 3 && (
          <div className="space-y-6">
            <div className="flex flex-col items-center justify-center py-6">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
                <CheckCircle2 className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold text-center">Setup Complete!</h3>
              <p className="text-gray-600 text-center mt-2">
                You've successfully set up your account. You can now start using Sourcio.ai to manage your recruitment process.
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center">
                    <Upload className="h-8 w-8 text-primary mb-2" />
                    <h4 className="font-medium">Upload CVs</h4>
                    <p className="text-sm text-gray-500">
                      Start uploading candidate CVs for evaluation
                    </p>
                  </div>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="pt-6">
                  <div className="flex flex-col items-center text-center">
                    <Briefcase className="h-8 w-8 text-primary mb-2" />
                    <h4 className="font-medium">Manage Jobs</h4>
                    <p className="text-sm text-gray-500">
                      Create and manage job postings
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
            
            <div className="flex justify-center pt-4">
              <Button onClick={handleComplete} className="bg-primary-gradient">
                Go to Dashboard
              </Button>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
