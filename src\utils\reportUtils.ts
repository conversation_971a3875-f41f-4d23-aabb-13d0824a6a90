import { ReportData } from '@/services/reports/reportExporter';
import { formatDate } from '@/utils/candidateUtils';

/**
 * Generate report data for a candidate
 */
export const getCandidateReportData = (candidate: any): ReportData => {
  // Extract skills from candidate
  const skills = candidate.skills || [];
  const skillsText = Array.isArray(skills) 
    ? skills.map(skill => typeof skill === 'string' ? skill : JSON.stringify(skill)).join(', ')
    : '';

  // Extract notes from candidate
  const notes = candidate.notes || [];
  const notesText = Array.isArray(notes)
    ? notes.map((note: any) => `${note.author} (${formatDate(note.date)}): ${note.content}`).join('\n\n')
    : '';

  // Create report data
  return {
    title: `Candidate Profile: ${candidate.name}`,
    date: new Date(),
    author: 'Sourcio.ai',
    data: [candidate],
    columns: [
      { header: 'Name', dataKey: 'name' },
      { header: 'Email', dataKey: 'email' },
      { header: 'Phone', dataKey: 'phone' },
      { header: 'Status', dataKey: 'status' },
      { header: 'Match Score', dataKey: 'matchScore' }
    ],
    sections: [
      {
        title: 'Personal Information',
        data: [
          { label: 'Name', value: candidate.name },
          { label: 'Email', value: candidate.email },
          { label: 'Phone', value: candidate.phone },
          { label: 'Location', value: candidate.location || 'Not specified' },
          { label: 'Position', value: candidate.position || 'Not specified' },
          { label: 'Status', value: candidate.status }
        ],
        columns: [
          { header: 'Label', dataKey: 'label' },
          { header: 'Value', dataKey: 'value' }
        ]
      },
      {
        title: 'Skills',
        data: [
          { label: 'Skills', value: skillsText || 'No skills listed' }
        ],
        columns: [
          { header: 'Label', dataKey: 'label' },
          { header: 'Value', dataKey: 'value' }
        ]
      },
      {
        title: 'Evaluation',
        data: [
          { label: 'Match Score', value: candidate.matchScore ? `${candidate.matchScore}%` : 'Not evaluated' },
          { label: 'Evaluation Summary', value: candidate.evaluation_summary || 'No evaluation summary available' }
        ],
        columns: [
          { header: 'Label', dataKey: 'label' },
          { header: 'Value', dataKey: 'value' }
        ]
      },
      {
        title: 'Notes',
        data: [
          { label: 'Notes', value: notesText || 'No notes available' }
        ],
        columns: [
          { header: 'Label', dataKey: 'label' },
          { header: 'Value', dataKey: 'value' }
        ]
      }
    ]
  };
};
