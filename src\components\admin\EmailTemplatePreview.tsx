import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  renderEmailTemplate, 
  type EmailTemplate, 
  type EmailTemplateVariable 
} from '@/services/supabase/emailTemplates';
import { Eye, Code, Mail } from 'lucide-react';

interface EmailTemplatePreviewProps {
  template: EmailTemplate;
  onClose: () => void;
}

export const EmailTemplatePreview: React.FC<EmailTemplatePreviewProps> = ({
  template,
  onClose
}) => {
  const [variables, setVariables] = useState<Record<string, string>>({});
  const [showCode, setShowCode] = useState(false);

  const templateVariables = (template.variables as EmailTemplateVariable[]) || [];

  useEffect(() => {
    // Initialize variables with sample data
    const sampleData: Record<string, string> = {};
    templateVariables.forEach(variable => {
      switch (variable.name) {
        case 'companyName':
          sampleData[variable.name] = 'Acme Corporation';
          break;
        case 'candidateName':
          sampleData[variable.name] = 'John Doe';
          break;
        case 'jobTitle':
          sampleData[variable.name] = 'Senior Software Engineer';
          break;
        case 'interviewDate':
          sampleData[variable.name] = 'Monday, January 15, 2024';
          break;
        case 'interviewTime':
          sampleData[variable.name] = '2:00 PM';
          break;
        case 'duration':
          sampleData[variable.name] = '60';
          break;
        case 'interviewType':
          sampleData[variable.name] = 'Video';
          break;
        case 'location':
          sampleData[variable.name] = 'https://meet.google.com/abc-defg-hij';
          break;
        case 'inviteeName':
          sampleData[variable.name] = 'Jane Smith';
          break;
        case 'inviterName':
          sampleData[variable.name] = 'Alice Johnson';
          break;
        case 'role':
          sampleData[variable.name] = 'Senior Developer';
          break;
        case 'invitationLink':
          sampleData[variable.name] = 'https://example.com/accept-invitation?token=abc123';
          break;
        case 'userName':
          sampleData[variable.name] = 'John Doe';
          break;
        case 'userEmail':
          sampleData[variable.name] = '<EMAIL>';
          break;
        case 'resetLink':
          sampleData[variable.name] = 'https://example.com/reset-password?token=xyz789';
          break;
        case 'verificationLink':
          sampleData[variable.name] = 'https://example.com/verify-email?token=def456';
          break;
        case 'status':
          sampleData[variable.name] = 'Under Review';
          break;
        case 'message':
          sampleData[variable.name] = 'Thank you for your application. We were impressed with your experience.';
          break;
        case 'nextSteps':
          sampleData[variable.name] = 'We will contact you within the next 3-5 business days to schedule an interview.';
          break;
        default:
          sampleData[variable.name] = `Sample ${variable.name}`;
      }
    });
    setVariables(sampleData);
  }, [templateVariables]);

  const handleVariableChange = (name: string, value: string) => {
    setVariables(prev => ({ ...prev, [name]: value }));
  };

  const rendered = renderEmailTemplate(template, variables);

  const getTypeColor = (type: string) => {
    const colors: Record<string, string> = {
      interview: 'bg-blue-100 text-blue-800',
      candidate: 'bg-green-100 text-green-800',
      team: 'bg-purple-100 text-purple-800',
      auth: 'bg-orange-100 text-orange-800',
      system: 'bg-gray-100 text-gray-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="space-y-6">
      {/* Template Info */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center gap-2">
            <h3 className="text-lg font-semibold">{template.display_name}</h3>
            <Badge className={getTypeColor(template.template_type)}>
              {template.template_type}
            </Badge>
            <Badge variant={template.is_active ? 'default' : 'secondary'}>
              {template.is_active ? 'Active' : 'Inactive'}
            </Badge>
          </div>
          {template.description && (
            <p className="text-sm text-gray-600">{template.description}</p>
          )}
        </div>
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowCode(!showCode)}
        >
          {showCode ? <Eye className="h-4 w-4 mr-1" /> : <Code className="h-4 w-4 mr-1" />}
          {showCode ? 'Preview' : 'Code'}
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Variables Panel */}
        {templateVariables.length > 0 && (
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Variables</CardTitle>
                <CardDescription>
                  Customize the values to see how they appear in the template
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4 max-h-96 overflow-y-auto">
                {templateVariables.map((variable) => (
                  <div key={variable.name} className="space-y-1">
                    <Label htmlFor={variable.name} className="text-sm">
                      {variable.name}
                      {variable.required && <span className="text-red-500 ml-1">*</span>}
                    </Label>
                    <Input
                      id={variable.name}
                      value={variables[variable.name] || ''}
                      onChange={(e) => handleVariableChange(variable.name, e.target.value)}
                      placeholder={variable.description}
                      className="text-sm"
                    />
                    {variable.description && (
                      <p className="text-xs text-gray-500">{variable.description}</p>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          </div>
        )}

        {/* Preview Panel */}
        <div className={templateVariables.length > 0 ? 'lg:col-span-2' : 'lg:col-span-3'}>
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Mail className="h-5 w-5" />
                <CardTitle className="text-base">Email Preview</CardTitle>
              </div>
            </CardHeader>
            <CardContent>
              {showCode ? (
                <div className="space-y-4">
                  <div>
                    <Label className="text-sm font-medium">Subject:</Label>
                    <pre className="mt-1 p-3 bg-gray-50 rounded-md border text-sm overflow-x-auto">
                      {template.subject}
                    </pre>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">HTML Content:</Label>
                    <pre className="mt-1 p-3 bg-gray-50 rounded-md border text-xs overflow-x-auto max-h-96 overflow-y-auto">
                      {template.content}
                    </pre>
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  {/* Email Header */}
                  <div className="bg-gray-50 p-4 rounded-md border">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">From:</span>
                        <span className="text-gray-600">
                          {variables.companyName || 'Company Name'} &lt;<EMAIL>&gt;
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">To:</span>
                        <span className="text-gray-600">
                          {variables.candidateName || variables.inviteeName || variables.userName || '<EMAIL>'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">Subject:</span>
                        <span className="text-gray-900 font-medium">{rendered.subject}</span>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  {/* Email Content */}
                  <div className="bg-white border rounded-md">
                    <div 
                      className="p-4"
                      dangerouslySetInnerHTML={{ __html: rendered.content }}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Actions */}
      <div className="flex justify-end pt-4 border-t">
        <Button onClick={onClose}>
          Close
        </Button>
      </div>
    </div>
  );
};
