import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Eye, EyeOff, ExternalLink } from 'lucide-react';

interface CoverLetterDisplayProps {
  content?: string | null;
  url?: string | null;
  score?: number;
  analysis?: string;
  className?: string;
}

const CoverLetterDisplay: React.FC<CoverLetterDisplayProps> = ({
  content,
  url,
  score,
  analysis,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // If no cover letter data is available
  if (!content && !url) {
    return (
      <Card className={`${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-lg">
            <FileText className="h-5 w-5 text-gray-400" />
            Cover Letter
            <Badge variant="secondary" className="ml-auto">
              Not Provided
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 text-sm">
            No cover letter was provided with this application.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <FileText className="h-5 w-5 text-blue-500" />
          Cover Letter
          <div className="ml-auto flex items-center gap-2">
            {score !== undefined && (
              <Badge 
                variant={score >= 70 ? "default" : score >= 50 ? "secondary" : "destructive"}
                className="text-xs"
              >
                {score}% Match
              </Badge>
            )}
            <Badge variant="outline">
              {content ? 'Text' : 'File'}
            </Badge>
          </div>
        </CardTitle>
        {analysis && (
          <p className="text-sm text-gray-600 mt-2">
            {analysis}
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {content && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-gray-700">
                Cover Letter Content
              </p>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(!isExpanded)}
                className="text-xs"
              >
                {isExpanded ? (
                  <>
                    <EyeOff className="h-3 w-3 mr-1" />
                    Collapse
                  </>
                ) : (
                  <>
                    <Eye className="h-3 w-3 mr-1" />
                    Expand
                  </>
                )}
              </Button>
            </div>
            
            <div className={`bg-gray-50 rounded-lg p-4 border ${isExpanded ? '' : 'max-h-32 overflow-hidden relative'}`}>
              <p className="text-sm text-gray-800 whitespace-pre-wrap leading-relaxed">
                {content}
              </p>
              {!isExpanded && content.length > 200 && (
                <div className="absolute bottom-0 left-0 right-0 h-8 bg-gradient-to-t from-gray-50 to-transparent" />
              )}
            </div>
            
            {!isExpanded && content.length > 200 && (
              <p className="text-xs text-gray-500 text-center">
                Click "Expand" to read the full cover letter
              </p>
            )}
          </div>
        )}

        {url && (
          <div className="space-y-3">
            <p className="text-sm font-medium text-gray-700">
              Cover Letter File
            </p>
            <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
              <FileText className="h-8 w-8 text-blue-500" />
              <div className="flex-1">
                <p className="text-sm font-medium text-blue-900">
                  Cover Letter Document
                </p>
                <p className="text-xs text-blue-700">
                  Click to view the uploaded cover letter file
                </p>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.open(url, '_blank')}
                className="text-blue-600 border-blue-300 hover:bg-blue-100"
              >
                <ExternalLink className="h-4 w-4 mr-1" />
                View
              </Button>
            </div>
          </div>
        )}

        {score !== undefined && (
          <div className="pt-3 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-600">Cover Letter Match Score:</span>
              <span className={`font-medium ${
                score >= 70 ? 'text-green-600' : 
                score >= 50 ? 'text-yellow-600' : 
                'text-red-600'
              }`}>
                {score}%
              </span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CoverLetterDisplay;
