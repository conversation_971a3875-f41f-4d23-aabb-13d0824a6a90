import { sendEmail } from './emailService';
import { TemplateService } from './templateService';

/**
 * Send team invitation email
 */
export const sendTeamInvitation = async (
  inviteeEmail: string,
  inviteeName: string,
  inviterName: string,
  companyName: string,
  role: string,
  invitationToken: string
): Promise<void> => {
  try {
    const invitationLink = `${window.location.origin}/accept-invitation?token=${invitationToken}`;
    
    // Try to use database template first, fallback to hardcoded template
    const templateResult = await TemplateService.renderTeamInvitation({
      companyName,
      inviteeName,
      inviterName,
      role,
      invitationLink
    });

    const emailContent = templateResult || {
      subject: `You're invited to join ${companyName} on Sourcio.ai`,
      content: generateTeamInvitationHTML(
        inviteeName,
        inviterName,
        companyName,
        role,
        invitationLink
      )
    };

    await sendEmail({
      to: inviteeEmail,
      subject: emailContent.subject,
      html: emailContent.content,
      companyName: companyName
    });
  } catch (error) {
    console.error('Failed to send team invitation:', error);
    throw error;
  }
};

/**
 * Generate team invitation HTML
 */
const generateTeamInvitationHTML = (
  inviteeName: string,
  inviterName: string,
  companyName: string,
  role: string,
  invitationLink: string
): string => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">${companyName}</h1>
      </div>
      
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2 style="color: #1e293b; margin: 0 0 15px 0;">You're Invited to Join a Team!</h2>
        <p style="color: #475569; margin: 0; line-height: 1.6;">
          Hi ${inviteeName},<br><br>
          ${inviterName} has invited you to join <strong>${companyName}</strong> as a <strong>${role}</strong> on Sourcio.ai.
        </p>
      </div>
      
      <div style="margin: 30px 0; text-align: center;">
        <a href="${invitationLink}" style="background: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 600;">
          Accept Invitation
        </a>
      </div>
      
      <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
        <p style="color: #92400e; margin: 0; font-size: 14px;">
          <strong>Note:</strong> This invitation will expire in 7 days. If you don't have an Sourcio.ai account, you'll be prompted to create one.
        </p>
      </div>
      
      <div style="margin: 20px 0;">
        <p style="color: #475569; margin: 0; line-height: 1.6;">
          If the button above doesn't work, you can copy and paste this link into your browser:
        </p>
        <p style="color: #2563eb; word-break: break-all; margin: 10px 0;">
          ${invitationLink}
        </p>
      </div>
      
      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};