import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { safeDbOperation } from '@/lib/errorHandling';
import { RealtimeChannel } from '@supabase/supabase-js';

export type Job = Database['public']['Tables']['jobs']['Row'];
export type JobInsert = Database['public']['Tables']['jobs']['Insert'];
export type JobUpdate = Database['public']['Tables']['jobs']['Update'];

// Define event types for real-time updates
export type JobRealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE';
export type JobRealtimeCallback = (event: JobRealtimeEvent, job: Job) => void;

/**
 * Get all jobs for the current user (owned + team member companies)
 */
export const getJobs = async (companyId?: string): Promise<Job[]> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      let query = supabase
        .from('jobs')
        .select(`
          *,
          companies (
            id,
            name,
            logo_url
          )
        `)
        .order('created_at', { ascending: false });

      // If companyId is specified, filter by it
      if (companyId) {
        query = query.eq('company_id', companyId);
      }

      const { data, error } = await query;

      if (error) throw error;

      return data || [];
    },
    `Failed to fetch jobs${companyId ? ` for company ${companyId}` : ''}`,
    []
  );
};

/**
 * Get a job by ID
 */
export const getJob = async (id: string): Promise<Job | null> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Always filter by user_id to ensure data isolation between users
      const { data, error } = await supabase
        .from('jobs')
        .select('*')
        .eq('id', id)
        .eq('user_id', userId)
        .single();

      if (error) {
        // If no rows found, return null instead of throwing an error
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      return data;
    },
    `Failed to fetch job with ID ${id}`,
    null // Return null as fallback
  );
};

/**
 * Get a job by ID (public access - no user authentication required)
 */
export const getPublicJob = async (id: string): Promise<Job | null> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          companies (
            id,
            name,
            logo_url
          )
        `)
        .eq('id', id)
        .eq('status', 'active')
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      return {
        ...data,
        company_name: data.companies?.name || data.company_name
      };
    },
    `Failed to fetch public job with ID ${id}`,
    null
  );
};

/**
 * Get all public active jobs (no authentication required)
 */
export const getPublicJobs = async (): Promise<Job[]> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('jobs')
        .select(`
          *,
          companies (
            id,
            name,
            logo_url
          )
        `)
        .eq('status', 'active')
        .order('created_at', { ascending: false });

      if (error) {
        throw error;
      }

      return (data || []).map(job => ({
        ...job,
        company_name: job.companies?.name || job.company_name
      }));
    },
    'Failed to fetch public jobs'
  );
};

/**
 * Create a new job
 */
export const createJob = async (job: JobInsert, checkUsageLimit = true): Promise<Job> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // If usage limit checking is enabled
      if (checkUsageLimit) {
        // Get user's subscription tier
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('subscription_tier, subscription_status')
          .eq('user_id', userId)
          .single();

        if (profileError) throw profileError;

        // Check if subscription is active
        if (profile.subscription_status !== 'active') {
          throw new Error('Your subscription is not active. Please renew your subscription to post jobs.');
        }

        // Get current active jobs count
        const { data: activeJobs, error: jobsError } = await supabase
          .from('jobs')
          .select('id')
          .eq('user_id', userId)
          .eq('status', 'active');

        if (jobsError) throw jobsError;

        // Get subscription limits
        const { data: limits, error: limitsError } = await supabase
          .from('subscription_limits')
          .select('job_posting_limit')
          .eq('tier', profile.subscription_tier)
          .single();

        if (limitsError) throw limitsError;

        // Check if user has reached their limit
        const activeJobsCount = activeJobs?.length || 0;
        if (activeJobsCount >= limits.job_posting_limit) {
          throw new Error(`You have reached your active job posting limit (${limits.job_posting_limit}). Please upgrade your plan or close some existing job postings.`);
        }
      }

      // Create the job
      const { data, error } = await supabase
        .from('jobs')
        .insert(job)
        .select()
        .single();

      if (error) throw error;

      // If usage limit checking is enabled and the job is active, increment the usage counter
      if (checkUsageLimit && job.status === 'active') {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

        try {
          // Increment the active jobs count
          const { error } = await supabase.rpc('increment_usage_counter', {
            p_user_id: userId,
            p_month_year: currentMonth,
            p_counter_name: 'active_jobs_count',
            p_increment_by: 1
          });

          if (error) {
            console.error('Failed to increment usage counter:', error);
            // Don't throw here, we still want to return the job even if tracking fails
          }
        } catch (error) {
          console.error('Failed to increment usage counter:', error);
          // Don't throw here, we still want to return the job even if tracking fails
        }
      }

      return data;
    },
    'Failed to create job'
  );
};

/**
 * Update a job
 */
export const updateJob = async (id: string, job: JobUpdate, checkUsageLimit = true): Promise<Job> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Get the current job to check if status is changing
      const { data: currentJob, error: currentJobError } = await supabase
        .from('jobs')
        .select('status')
        .eq('id', id)
        .single();

      if (currentJobError) throw currentJobError;

      // Check if status is changing to active
      const isChangingToActive = job.status === 'active' && currentJob.status !== 'active';

      // If changing to active and usage limit checking is enabled
      if (isChangingToActive && checkUsageLimit) {
        // Get user's subscription tier
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('subscription_tier, subscription_status')
          .eq('user_id', userId)
          .single();

        if (profileError) throw profileError;

        // Check if subscription is active
        if (profile.subscription_status !== 'active') {
          throw new Error('Your subscription is not active. Please renew your subscription to post jobs.');
        }

        // Get current active jobs count
        const { data: activeJobs, error: jobsError } = await supabase
          .from('jobs')
          .select('id')
          .eq('user_id', userId)
          .eq('status', 'active');

        if (jobsError) throw jobsError;

        // Get subscription limits
        const { data: limits, error: limitsError } = await supabase
          .from('subscription_limits')
          .select('job_posting_limit')
          .eq('tier', profile.subscription_tier)
          .single();

        if (limitsError) throw limitsError;

        // Check if user has reached their limit
        const activeJobsCount = activeJobs?.length || 0;
        if (activeJobsCount >= limits.job_posting_limit) {
          throw new Error(`You have reached your active job posting limit (${limits.job_posting_limit}). Please upgrade your plan or close some existing job postings.`);
        }
      }

      // Update the job
      const { data, error } = await supabase
        .from('jobs')
        .update(job)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // If usage limit checking is enabled and status is changing
      if (checkUsageLimit && job.status) {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

        // If changing to active, increment the active jobs count
        if (isChangingToActive) {
          try {
            const { error } = await supabase.rpc('increment_usage_counter', {
              p_user_id: userId,
              p_month_year: currentMonth,
              p_counter_name: 'active_jobs_count',
              p_increment_by: 1
            });

            if (error) {
              console.error('Failed to increment usage counter:', error);
            }
          } catch (error) {
            console.error('Failed to increment usage counter:', error);
          }
        }
        // If changing from active to something else, decrement the active jobs count
        else if (currentJob.status === 'active' && job.status !== 'active') {
          try {
            const { error } = await supabase.rpc('increment_usage_counter', {
              p_user_id: userId,
              p_month_year: currentMonth,
              p_counter_name: 'active_jobs_count',
              p_increment_by: -1
            });

            if (error) {
              console.error('Failed to decrement usage counter:', error);
            }
          } catch (error) {
            console.error('Failed to decrement usage counter:', error);
          }
        }
      }

      return data;
    },
    `Failed to update job with ID ${id}`
  );
};

/**
 * Delete a job
 */
export const deleteJob = async (id: string, checkUsageLimit = true): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Get the current job to check if it's active
      const { data: currentJob, error: currentJobError } = await supabase
        .from('jobs')
        .select('status')
        .eq('id', id)
        .single();

      if (currentJobError) throw currentJobError;

      // Check if the job is active
      const isActive = currentJob.status === 'active';

      // Delete the job
      const { error } = await supabase
        .from('jobs')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // If usage limit checking is enabled and the job was active
      if (checkUsageLimit && isActive) {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

        // Decrement the active jobs count
        try {
          const { error } = await supabase.rpc('increment_usage_counter', {
            p_user_id: userId,
            p_month_year: currentMonth,
            p_counter_name: 'active_jobs_count',
            p_increment_by: -1
          });

          if (error) {
            console.error('Failed to decrement usage counter:', error);
            // Don't throw here, we still want to return success even if tracking fails
          }
        } catch (error) {
          console.error('Failed to decrement usage counter:', error);
          // Don't throw here, we still want to return success even if tracking fails
        }
      }

      return true;
    },
    `Failed to delete job with ID ${id}`,
    false // Return false as fallback
  );
};

/**
 * Get job statistics
 */
export const getJobStatistics = async (companyId?: string) => {
  return safeDbOperation(
    async () => {
      let query = supabase
        .from('jobs')
        .select('id, status');

      if (companyId) {
        query = query.eq('company_id', companyId);
      }

      const { data, error } = await query;

      if (error) throw error;

      const total = data.length;
      const active = data.filter(job => job.status === 'active').length;
      const draft = data.filter(job => job.status === 'draft').length;
      const closed = data.filter(job => job.status === 'closed').length;

      return {
        total,
        active,
        draft,
        closed
      };
    },
    `Failed to fetch job statistics${companyId ? ` for company ${companyId}` : ''}`,
    { total: 0, active: 0, draft: 0, closed: 0 } // Default fallback
  );
};

/**
 * Subscribe to real-time job updates
 *
 * @param userId User ID to filter jobs by
 * @param companyId Optional company ID to filter jobs by
 * @param callback Function to call when a job is updated
 * @returns Supabase subscription that can be unsubscribed from
 */
export const subscribeToJobs = (
  userId: string,
  companyId?: string,
  callback?: JobRealtimeCallback
): RealtimeChannel => {
  // Create a channel for job updates
  const channel = supabase.channel(`jobs-${userId}-${companyId || 'all'}`);

  // Set up filters for the subscription
  const insertFilter: Record<string, string> = { user_id: `eq.${userId}` };
  const updateFilter: Record<string, string> = { user_id: `eq.${userId}` };
  const deleteFilter: Record<string, string> = { user_id: `eq.${userId}` };

  // Add company filter if provided
  if (companyId) {
    insertFilter.company_id = `eq.${companyId}`;
    updateFilter.company_id = `eq.${companyId}`;
    deleteFilter.company_id = `eq.${companyId}`;
  }

  // Subscribe to INSERT events
  channel.on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'jobs',
      filter: insertFilter
    },
    (payload) => {
      if (callback) {
        callback('INSERT', payload.new as Job);
      }
    }
  );

  // Subscribe to UPDATE events
  channel.on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'jobs',
      filter: updateFilter
    },
    (payload) => {
      if (callback) {
        callback('UPDATE', payload.new as Job);
      }
    }
  );

  // Subscribe to DELETE events
  channel.on(
    'postgres_changes',
    {
      event: 'DELETE',
      schema: 'public',
      table: 'jobs',
      filter: deleteFilter
    },
    (payload) => {
      if (callback) {
        callback('DELETE', payload.old as Job);
      }
    }
  );

  // Start the subscription
  return channel.subscribe();
}



