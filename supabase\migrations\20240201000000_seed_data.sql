-- Seed data for Expert-Recruiter application

-- Create a default company for testing
INSERT INTO public.companies (id, name, description, logo_url, website, industry, size, location, user_id)
VALUES (
  '00000000-0000-0000-0000-000000000001',
  'TechCorp Solutions',
  'TechCorp Solutions is a leading technology company specializing in innovative software solutions for businesses of all sizes. We help companies transform their operations through cutting-edge technology and expert consulting services.',
  'https://via.placeholder.com/150',
  'https://techcorp-example.com',
  'Information Technology',
  '50-200 employees',
  'San Francisco, CA',
  (SELECT id FROM auth.users LIMIT 1)
) ON CONFLICT (id) DO NOTHING;

-- Seed jobs data
INSERT INTO public.jobs (
  id, 
  title, 
  description, 
  requirements, 
  location, 
  salary_min, 
  salary_max, 
  job_type, 
  experience_level, 
  status, 
  company_id, 
  user_id
)
VALUES
(
  '00000000-0000-0000-0000-000000000001',
  'Frontend Developer',
  'We are looking for a skilled Frontend Developer to join our team. You will be responsible for building user interfaces and implementing web designs.',
  'Strong experience with React, TypeScript, and modern JavaScript frameworks. Knowledge of responsive design and cross-browser compatibility.',
  'Remote',
  90000,
  120000,
  'Full-time',
  '3-5 years',
  'active',
  '00000000-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users LIMIT 1)
),
(
  '00000000-0000-0000-0000-000000000002',
  'UX Designer',
  'We are seeking a talented UX Designer to create amazing user experiences. You will work on designing intuitive interfaces for our products.',
  'Experience with Figma, UI Design, User Research, and Prototyping. Strong portfolio demonstrating user-centered design approach.',
  'New York, NY',
  85000,
  110000,
  'Full-time',
  '2-4 years',
  'active',
  '00000000-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users LIMIT 1)
),
(
  '00000000-0000-0000-0000-000000000003',
  'Product Manager',
  'We are looking for a Product Manager to lead our product development efforts. You will be responsible for defining product vision and strategy.',
  'Experience in product management, agile methodologies, and market research. Strong communication and leadership skills.',
  'San Francisco, CA',
  110000,
  150000,
  'Full-time',
  '5-7 years',
  'closed',
  '00000000-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users LIMIT 1)
),
(
  '00000000-0000-0000-0000-000000000004',
  'DevOps Engineer',
  'We are seeking a DevOps Engineer to help us build and maintain our infrastructure. You will be responsible for CI/CD pipelines and cloud infrastructure.',
  'Experience with AWS, Docker, Kubernetes, and CI/CD tools. Knowledge of infrastructure as code and automation.',
  'Remote',
  95000,
  130000,
  'Contract',
  '3-5 years',
  'active',
  '00000000-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users LIMIT 1)
),
(
  '00000000-0000-0000-0000-000000000005',
  'Marketing Specialist',
  'We are looking for a Marketing Specialist to help us grow our brand. You will be responsible for creating and executing marketing campaigns.',
  'Experience in digital marketing, content creation, and social media management. Knowledge of SEO and analytics tools.',
  'London, UK',
  70000,
  90000,
  'Part-time',
  '2-3 years',
  'draft',
  '00000000-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users LIMIT 1)
) ON CONFLICT (id) DO NOTHING;

-- Seed candidates data
INSERT INTO public.candidates (
  id,
  name,
  email,
  phone,
  cv_url,
  status,
  job_id,
  user_id,
  evaluation_score,
  evaluation_summary,
  notes
)
VALUES
(
  '00000000-0000-0000-0000-000000000001',
  'John Doe',
  '<EMAIL>',
  '+****************',
  'https://example.com/cv/john-doe.pdf',
  'new',
  '00000000-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users LIMIT 1),
  85,
  '{"skills": ["React", "TypeScript", "CSS", "Node.js"], "experience": "5 years", "education": "Bachelor in Computer Science"}',
  'Initial screening call went well. Candidate has strong React experience and good communication skills.'
),
(
  '00000000-0000-0000-0000-000000000002',
  'Jane Smith',
  '<EMAIL>',
  '+****************',
  'https://example.com/cv/jane-smith.pdf',
  'interview',
  '00000000-0000-0000-0000-000000000002',
  (SELECT id FROM auth.users LIMIT 1),
  92,
  '{"skills": ["Figma", "UI Design", "User Research", "Prototyping"], "experience": "3 years", "education": "Master in Design"}',
  'Great portfolio and design skills. Scheduled for interview next week.'
),
(
  '00000000-0000-0000-0000-000000000003',
  'Michael Johnson',
  '<EMAIL>',
  '+1 (555) 456-7890',
  'https://example.com/cv/michael-johnson.pdf',
  'offer',
  '00000000-0000-0000-0000-000000000003',
  (SELECT id FROM auth.users LIMIT 1),
  78,
  '{"skills": ["AWS", "Docker", "Kubernetes", "CI/CD", "Terraform"], "experience": "7 years", "education": "Bachelor in Information Technology"}',
  'Strong technical skills but communication could be better. Offering position based on technical expertise.'
),
(
  '00000000-0000-0000-0000-000000000004',
  'Emily Davis',
  '<EMAIL>',
  '+1 (555) 789-0123',
  'https://example.com/cv/emily-davis.pdf',
  'rejected',
  '00000000-0000-0000-0000-000000000004',
  (SELECT id FROM auth.users LIMIT 1),
  65,
  '{"skills": ["Python", "Data Analysis", "SQL", "Tableau"], "experience": "4 years", "education": "Master in Data Science"}',
  'Not enough experience with the specific technologies we use. May be a better fit for a different role.'
),
(
  '00000000-0000-0000-0000-000000000005',
  'David Wilson',
  '<EMAIL>',
  '+1 (555) 234-5678',
  'https://example.com/cv/david-wilson.pdf',
  'new',
  '00000000-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users LIMIT 1),
  88,
  '{"skills": ["JavaScript", "React", "HTML", "CSS"], "experience": "2 years", "education": "Bachelor in Computer Science"}',
  'Good potential but limited experience. Worth considering for junior position.'
) ON CONFLICT (id) DO NOTHING;

-- Seed notifications data
INSERT INTO public.notifications (
  id,
  user_id,
  title,
  message,
  type,
  read,
  link
)
VALUES
(
  '00000000-0000-0000-0000-000000000001',
  (SELECT id FROM auth.users LIMIT 1),
  'New Candidate Application',
  'John Doe has applied for Frontend Developer position',
  'info',
  false,
  '/dashboard/cvs/00000000-0000-0000-0000-000000000001'
),
(
  '00000000-0000-0000-0000-000000000002',
  (SELECT id FROM auth.users LIMIT 1),
  'Interview Scheduled',
  'Interview with Jane Smith scheduled for tomorrow at 2:00 PM',
  'success',
  false,
  '/dashboard/cvs/00000000-0000-0000-0000-000000000002'
),
(
  '00000000-0000-0000-0000-000000000003',
  (SELECT id FROM auth.users LIMIT 1),
  'Subscription Expiring',
  'Your subscription will expire in 3 days. Renew now to avoid service interruption.',
  'warning',
  true,
  '/dashboard/settings'
) ON CONFLICT (id) DO NOTHING;
