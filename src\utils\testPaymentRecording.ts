// Test utility to manually record a payment for testing
import { supabase } from '@/lib/supabase';

export const testPaymentRecording = async (userId: string) => {
  try {
    console.log('Testing payment recording for user:', userId);
    
    // Test recording a payment
    const testPayment = {
      user_id: userId,
      payment_id: 'test_payment_' + Date.now(),
      subscription_id: 'test_sub_' + Date.now(),
      amount: 29.99,
      currency: 'usd',
      status: 'paid',
      payment_method: 'stripe',
      payment_type: 'subscription' as const,
      payment_details: {
        test: true,
        created_at: new Date().toISOString()
      }
    };

    const { data, error } = await supabase
      .from('payments')
      .insert(testPayment)
      .select()
      .single();

    if (error) {
      console.error('Error recording test payment:', error);
      return { success: false, error };
    }

    console.log('Test payment recorded successfully:', data);
    return { success: true, data };
  } catch (error) {
    console.error('Error in testPaymentRecording:', error);
    return { success: false, error };
  }
};

export const getPaymentHistory = async (userId: string) => {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching payments:', error);
      return { success: false, error };
    }

    console.log('Payment history:', data);
    return { success: true, data };
  } catch (error) {
    console.error('Error in getPaymentHistory:', error);
    return { success: false, error };
  }
};
