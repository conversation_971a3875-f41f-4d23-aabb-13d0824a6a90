-- Create function to increment usage counters
CREATE OR REPLACE FUNCTION public.increment_usage_counter(
    p_user_id UUID,
    p_month_year TEXT,
    p_counter_name TEXT,
    p_increment_by INTEGER DEFAULT 1
)
RETURNS void AS $$
DECLARE
    v_record_exists BOOLEAN;
BEGIN
    -- Check if a record exists for this user and month
    SELECT EXISTS(
        SELECT 1 FROM public.usage_tracking
        WHERE user_id = p_user_id AND month_year = p_month_year
    ) INTO v_record_exists;
    
    -- If no record exists, create one with initial values
    IF NOT v_record_exists THEN
        INSERT INTO public.usage_tracking (
            user_id,
            month_year,
            cv_uploads_count,
            active_jobs_count,
            company_profiles_count,
            team_members_count
        ) VALUES (
            p_user_id,
            p_month_year,
            CASE WHEN p_counter_name = 'cv_uploads_count' THEN p_increment_by ELSE 0 END,
            CASE WHEN p_counter_name = 'active_jobs_count' THEN p_increment_by ELSE 0 END,
            CASE WHEN p_counter_name = 'company_profiles_count' THEN p_increment_by ELSE 0 END,
            CASE WHEN p_counter_name = 'team_members_count' THEN p_increment_by ELSE 0 END
        );
    ELSE
        -- Update the existing record
        EXECUTE format('
            UPDATE public.usage_tracking
            SET %I = %I + $1,
                updated_at = now()
            WHERE user_id = $2 AND month_year = $3
        ', p_counter_name, p_counter_name)
        USING p_increment_by, p_user_id, p_month_year;
    END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if a user has reached their usage limit
CREATE OR REPLACE FUNCTION public.check_usage_limit(
    p_user_id UUID,
    p_usage_type TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    v_current_month TEXT;
    v_subscription_tier TEXT;
    v_current_usage INTEGER;
    v_usage_limit INTEGER;
    v_has_reached_limit BOOLEAN;
BEGIN
    -- Get current month in YYYY-MM format
    v_current_month := to_char(now(), 'YYYY-MM');
    
    -- Get user's subscription tier
    SELECT subscription_tier INTO v_subscription_tier
    FROM public.profiles
    WHERE user_id = p_user_id;
    
    IF v_subscription_tier IS NULL THEN
        RAISE EXCEPTION 'User profile not found';
    END IF;
    
    -- Get current usage
    EXECUTE format('
        SELECT COALESCE(%I, 0)
        FROM public.usage_tracking
        WHERE user_id = $1 AND month_year = $2
    ', p_usage_type)
    INTO v_current_usage
    USING p_user_id, v_current_month;
    
    -- If no usage record found, set to 0
    IF v_current_usage IS NULL THEN
        v_current_usage := 0;
    END IF;
    
    -- Get usage limit for the subscription tier
    EXECUTE format('
        SELECT %I
        FROM public.subscription_limits
        WHERE tier = $1
    ', p_usage_type || '_limit')
    INTO v_usage_limit
    USING v_subscription_tier;
    
    IF v_usage_limit IS NULL THEN
        RAISE EXCEPTION 'Subscription limit not found for tier %', v_subscription_tier;
    END IF;
    
    -- Check if user has reached their limit
    v_has_reached_limit := v_current_usage >= v_usage_limit;
    
    RETURN v_has_reached_limit;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comments for documentation
COMMENT ON FUNCTION public.increment_usage_counter(UUID, TEXT, TEXT, INTEGER) IS 'Increments a usage counter for a specific user and month';
COMMENT ON FUNCTION public.check_usage_limit(UUID, TEXT) IS 'Checks if a user has reached their usage limit for a specific type';
