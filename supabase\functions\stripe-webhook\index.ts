import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import { corsHeaders } from '../_shared/cors.ts'

interface WebhookEvent {
  id: string;
  type: string;
  data: {
    object: any;
  };
  created: number;
  livemode: boolean;
}

serve(async (req) => {
  console.log('🚀 Stripe webhook received:', new Date().toISOString())
  
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  // Don't check for Authorization header - <PERSON><PERSON> doesn't send one
  // Create admin client directly
  const supabaseAdmin = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
  )

  try {
    const rawBody = await req.text()
    const signature = req.headers.get('stripe-signature')
    
    console.log('📦 Body length:', rawBody.length)
    console.log('🔐 Signature present:', !!signature)
    
    // Parse the event without signature verification for now
    const event: WebhookEvent = JSON.parse(rawBody)
    console.log('📨 Event type:', event.type)
    
    // Handle different event types
    switch (event.type) {
      case 'customer.subscription.created':
        await handleSubscriptionCreated(event, supabaseAdmin)
        break
      case 'customer.subscription.updated':
        await handleSubscriptionUpdated(event, supabaseAdmin)
        break
      case 'customer.subscription.deleted':
        await handleSubscriptionDeleted(event, supabaseAdmin)
        break
      case 'invoice.payment_succeeded':
        await handlePaymentSucceeded(event, supabaseAdmin)
        break
      case 'invoice.payment_failed':
        await handlePaymentFailed(event, supabaseAdmin)
        break
      default:
        console.log(`Unhandled event type: ${event.type}`)
    }
    
    return new Response(JSON.stringify({ received: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
    
  } catch (error) {
    console.error('❌ Webhook error:', error)
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})

// Handle subscription created event
async function handleSubscriptionCreated(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.data.object
  
  // Get subscription details
  const subscriptionId = subscription.id
  const customerId = subscription.customer
  const status = subscription.status
  
  // Safely handle date conversions with fallbacks
  let currentPeriodStart = null
  let currentPeriodEnd = null
  
  if (subscription.current_period_start && typeof subscription.current_period_start === 'number') {
    try {
      currentPeriodStart = new Date(subscription.current_period_start * 1000).toISOString()
    } catch (error) {
      console.error('Invalid current_period_start:', subscription.current_period_start)
    }
  }
  
  if (subscription.current_period_end && typeof subscription.current_period_end === 'number') {
    try {
      currentPeriodEnd = new Date(subscription.current_period_end * 1000).toISOString()
    } catch (error) {
      console.error('Invalid current_period_end:', subscription.current_period_end)
    }
  }
  
  // Use created timestamp as fallback for start_date if current_period_start is null
  if (!currentPeriodStart && subscription.created) {
    try {
      currentPeriodStart = new Date(subscription.created * 1000).toISOString()
    } catch (error) {
      console.error('Invalid created timestamp:', subscription.created)
      // Final fallback to current time
      currentPeriodStart = new Date().toISOString()
    }
  }
  
  // If still null, use current time as absolute fallback
  if (!currentPeriodStart) {
    currentPeriodStart = new Date().toISOString()
  }
  
  // Get the price ID and product ID from the first item
  const item = subscription.items.data[0]
  const priceId = item.price.id
  const productId = item.price.product
  
  // Get user ID from subscription metadata
  const userId = subscription.metadata?.user_id
  const tier = subscription.metadata?.tier || 'starter'

  if (!userId) {
    console.error('No user ID found in subscription metadata:', subscriptionId)
    return
  }
  
  console.log('Creating subscription:', {
    subscriptionId,
    userId,
    tier,
    status,
    startDate: currentPeriodStart,
    endDate: currentPeriodEnd
  })
  
  // Create or update subscription record using upsert
  const { data, error } = await supabaseAdmin
    .from('subscriptions')
    .upsert({
      user_id: userId,
      subscription_id: subscriptionId,
      plan_id: priceId,
      tier: tier,
      status: status,
      start_date: currentPeriodStart, // Now guaranteed to not be null
      end_date: currentPeriodEnd,
      payment_method: 'stripe',
      payment_details: subscription,
    }, {
      onConflict: 'subscription_id'
    })
  
  if (error) {
    console.error('Error creating subscription record:', error)
    throw error
  }
  
  // Update user profile with subscription info
  const { error: profileError } = await supabaseAdmin
    .from('profiles')
    .update({
      subscription_tier: tier,
      subscription_status: status,
      subscription_end_date: currentPeriodEnd,
    })
    .eq('user_id', userId)
  
  if (profileError) {
    console.error('Error updating user profile:', profileError)
    throw profileError
  }
  
  console.log('Subscription created successfully:', subscriptionId)
}

// Handle subscription updated event
async function handleSubscriptionUpdated(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.data.object
  const subscriptionId = subscription.id
  const status = subscription.status
  
  // Safely handle the date conversion
  let currentPeriodEnd = null
  if (subscription.current_period_end && typeof subscription.current_period_end === 'number') {
    try {
      currentPeriodEnd = new Date(subscription.current_period_end * 1000).toISOString()
    } catch (error) {
      console.error('Invalid current_period_end timestamp:', subscription.current_period_end)
      currentPeriodEnd = null
    }
  }
  
  console.log('Updating subscription:', subscriptionId, 'Status:', status, 'End date:', currentPeriodEnd)
  
  // Check if subscription exists first
  const { data: existingSubscription, error: checkError } = await supabaseAdmin
    .from('subscriptions')
    .select('user_id')
    .eq('subscription_id', subscriptionId)
    .maybeSingle() // Use maybeSingle instead of single to avoid error when no rows
  
  if (checkError) {
    console.error('Error checking subscription:', checkError)
    return
  }
  
  if (!existingSubscription) {
    console.log('Subscription not found, creating it first:', subscriptionId)
    // If subscription doesn't exist, create it first
    await handleSubscriptionCreated(event, supabaseAdmin)
    return
  }
  
  // Update subscription record
  const updateData: any = {
    status: status,
    payment_details: subscription,
  }
  
  // Only update end_date if we have a valid date
  if (currentPeriodEnd) {
    updateData.end_date = currentPeriodEnd
  }
  
  const { error } = await supabaseAdmin
    .from('subscriptions')
    .update(updateData)
    .eq('subscription_id', subscriptionId)
  
  if (error) {
    console.error('Error updating subscription record:', error)
    throw error
  }
  
  // Update user profile
  const profileUpdateData: any = {
    subscription_status: status,
  }
  
  // Only update end_date if we have a valid date
  if (currentPeriodEnd) {
    profileUpdateData.subscription_end_date = currentPeriodEnd
  }
  
  const { error: profileError } = await supabaseAdmin
    .from('profiles')
    .update(profileUpdateData)
    .eq('user_id', existingSubscription.user_id)
  
  if (profileError) {
    console.error('Error updating user profile:', profileError)
    throw profileError
  }
  
  console.log('Subscription updated successfully:', subscriptionId)
}

// Handle subscription deleted event
async function handleSubscriptionDeleted(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.data.object
  const subscriptionId = subscription.id
  
  // Update subscription record
  const { error } = await supabaseAdmin
    .from('subscriptions')
    .update({
      status: 'cancelled',
      end_date: new Date().toISOString(),
      payment_details: subscription,
    })
    .eq('subscription_id', subscriptionId)
  
  if (error) {
    console.error('Error updating subscription record:', error)
    throw error
  }
  
  // Get user ID from subscription
  const { data: subData, error: subError } = await supabaseAdmin
    .from('subscriptions')
    .select('user_id')
    .eq('subscription_id', subscriptionId)
    .single()
  
  if (subError || !subData) {
    console.error('Error finding subscription:', subscriptionId, subError)
    return
  }
  
  // Update user profile
  const { error: profileError } = await supabaseAdmin
    .from('profiles')
    .update({
      subscription_status: 'cancelled',
    })
    .eq('user_id', subData.user_id)
  
  if (profileError) {
    console.error('Error updating user profile:', profileError)
    throw profileError
  }
  
  console.log('Subscription cancelled successfully:', subscriptionId)
}

// Handle payment succeeded event
async function handlePaymentSucceeded(event: WebhookEvent, supabaseAdmin: any) {
  const invoice = event.data.object

  // Try multiple ways to get subscription ID from invoice
  let subscriptionId = invoice.subscription

  // If not found directly, check in parent.subscription_details
  if (!subscriptionId && invoice.parent?.subscription_details?.subscription) {
    subscriptionId = invoice.parent.subscription_details.subscription
  }

  console.log('Invoice subscription ID:', subscriptionId)
  console.log('Invoice object keys:', Object.keys(invoice))

  if (!subscriptionId) {
    console.log('Not a subscription payment, skipping')
    return
  }

  // Get subscription data to find user
  const { data: subscriptionData, error: subError } = await supabaseAdmin
    .from('subscriptions')
    .select('user_id, tier')
    .eq('subscription_id', subscriptionId)
    .single()

  if (subError || !subscriptionData) {
    console.error('Error finding subscription:', subError)
    return
  }

  // Record payment
  const { error } = await supabaseAdmin
    .from('payments')
    .insert({
      user_id: subscriptionData.user_id,
      payment_id: invoice.id,
      subscription_id: subscriptionId,
      amount: invoice.amount_paid / 100, // Convert from cents
      currency: invoice.currency,
      status: invoice.status,
      payment_method: 'stripe',
      payment_type: 'subscription',
      payment_details: invoice,
    })

  if (error) {
    console.error('Error recording payment:', error)
    throw error
  }

  // Create invoice record
  await createInvoiceFromPayment(supabaseAdmin, {
    userId: subscriptionData.user_id,
    paymentId: invoice.id,
    subscriptionId: subscriptionId,
    amount: invoice.amount_paid / 100,
    currency: invoice.currency,
    paymentMethod: 'stripe',
    tier: subscriptionData.tier,
    paymentDate: new Date(invoice.created * 1000).toISOString(),
  })

  console.log('Payment and invoice recorded successfully:', invoice.id)
}

// Handle payment failed event
async function handlePaymentFailed(event: WebhookEvent, supabaseAdmin: any) {
  const invoice = event.data.object
  const subscriptionId = invoice.subscription

  if (!subscriptionId) {
    console.log('Not a subscription payment, skipping')
    return
  }

  // Get subscription data to find user
  const { data: subscriptionData, error: subError } = await supabaseAdmin
    .from('subscriptions')
    .select('user_id')
    .eq('subscription_id', subscriptionId)
    .single()

  if (subError || !subscriptionData) {
    console.error('Error finding subscription for failed payment:', subError)
    return
  }

  // Record failed payment
  const { error } = await supabaseAdmin
    .from('payments')
    .insert({
      user_id: subscriptionData.user_id,
      payment_id: invoice.id,
      subscription_id: subscriptionId,
      amount: invoice.amount_due / 100, // Convert from cents
      currency: invoice.currency,
      status: 'failed',
      payment_method: 'stripe',
      payment_type: 'subscription',
      payment_details: invoice,
    })
  
  if (error) {
    console.error('Error recording failed payment:', error)
    throw error
  }
  
  console.log('Failed payment recorded:', invoice.id)
}

// Helper function to create invoice from payment
async function createInvoiceFromPayment(supabaseAdmin: any, paymentData: {
  userId: string
  paymentId: string
  subscriptionId: string
  amount: number
  currency: string
  paymentMethod: string
  tier: string
  paymentDate: string
}) {
  try {
    // Get user's billing info
    const { data: billingInfo } = await supabaseAdmin
      .from('billing_info')
      .select('*')
      .eq('user_id', paymentData.userId)
      .single()

    // If no billing info, create a basic one from user profile
    let finalBillingInfo = billingInfo
    if (!billingInfo) {
      const { data: profile } = await supabaseAdmin
        .from('profiles')
        .select('full_name, email')
        .eq('user_id', paymentData.userId)
        .single()

      finalBillingInfo = {
        company_name: null,
        address_line1: 'Address not provided',
        address_line2: null,
        city: 'City not provided',
        state: 'State not provided',
        postal_code: '00000',
        country: 'US',
        tax_id: null,
        billing_email: profile?.email || '<EMAIL>',
        phone: null,
      }
    }

    // Generate invoice number
    const year = new Date().getFullYear()
    const { count } = await supabaseAdmin
      .from('invoices')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', `${year}-01-01`)
      .lt('created_at', `${year + 1}-01-01`)

    const invoiceCount = (count || 0) + 1
    const invoiceNumber = `INV-${year}-${invoiceCount.toString().padStart(5, '0')}`

    // Create invoice items
    const invoiceItems = [{
      description: `${paymentData.tier.charAt(0).toUpperCase() + paymentData.tier.slice(1)} Plan Subscription`,
      quantity: 1,
      unit_price: paymentData.amount,
      amount: paymentData.amount,
    }]

    // Create invoice
    const { error: invoiceError } = await supabaseAdmin
      .from('invoices')
      .insert({
        user_id: paymentData.userId,
        invoice_number: invoiceNumber,
        amount: paymentData.amount,
        currency: paymentData.currency,
        status: 'paid',
        issue_date: paymentData.paymentDate,
        due_date: paymentData.paymentDate,
        payment_date: paymentData.paymentDate,
        subscription_id: paymentData.subscriptionId,
        payment_id: paymentData.paymentId,
        invoice_items: invoiceItems,
        billing_info: finalBillingInfo,
      })

    if (invoiceError) {
      console.error('Error creating invoice:', invoiceError)
    } else {
      console.log('Invoice created successfully:', invoiceNumber)
    }
  } catch (error) {
    console.error('Error in createInvoiceFromPayment:', error)
  }
}





