import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { supabase } from '@/lib/supabase';
import * as authService from '@/services/supabase/auth';
import * as profileService from '@/services/supabase/profiles';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';

// Define user type
export interface User {
  id: string;
  email: string;
  name: string;
  role: 'admin' | 'recruiter' | 'viewer' | 'candidate';
  isPlatformAdmin: boolean;
  subscriptionTier: string;
  avatarUrl?: string;
  companyId?: string;
  userType?: 'recruiter' | 'candidate';
}

// Define auth context type
interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (email: string, password: string, fullName: string, userType?: 'recruiter' | 'candidate') => Promise<void>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (password: string) => Promise<void>;
  updateProfile: (profile: { fullName?: string; avatarUrl?: string }) => Promise<void>;
  resendVerificationEmail: (email: string) => Promise<void>;
  checkEmailVerification: (email: string) => Promise<boolean>;
}

// Create the auth context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const { toast } = useToast();

  // Convert Supabase user to our User type - SIMPLIFIED NO DATABASE CALLS
  const formatUser = async (supabaseUser: SupabaseUser): Promise<User> => {
    console.log('SIMPLIFIED: Creating user from session data only - NO DATABASE CALLS');

    const userType = supabaseUser.user_metadata?.user_type || 'recruiter';

    // Create basic user from session data only - no database calls during auth
    return {
      id: supabaseUser.id,
      email: supabaseUser.email || '',
      name: supabaseUser.user_metadata?.full_name || 'User',
      role: userType === 'candidate' ? 'viewer' : 'viewer', // Default role, will be updated by PermissionsContext
      isPlatformAdmin: false, // Default, will be updated by PermissionsContext
      subscriptionTier: userType === 'candidate' ? 'free' : 'starter', // Candidates get free tier
      avatarUrl: supabaseUser.user_metadata?.avatar_url,
      companyId: undefined, // Will be loaded separately
      userType: userType
    };
  };

  // Track if the page is visible and the last visibility state
  const [isPageVisible, setIsPageVisible] = useState<boolean>(true);
  const lastVisibilityState = useRef<boolean>(true);
  const ignoreNextAuthChange = useRef<boolean>(false);

  // Get access to the React Query client
  const queryClient = useQueryClient();

  // Handle visibility change events
  useEffect(() => {
    const handleVisibilityChange = () => {
      const isVisible = !document.hidden;

      // If we're going from hidden to visible, we should ignore the next auth change
      if (!lastVisibilityState.current && isVisible) {
        console.log('Tab becoming visible - will ignore next auth change');
        ignoreNextAuthChange.current = true;
      }

      lastVisibilityState.current = isVisible;
      setIsPageVisible(isVisible);
      console.log(`Page visibility changed: ${document.hidden ? 'hidden' : 'visible'}`);
    };

    // Add event listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Clean up
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Check if user is already logged in
  useEffect(() => {
    let retryCount = 0;
    let retryTimeout: NodeJS.Timeout;
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 1500; // 1.5 seconds

    const checkAuth = async () => {
      try {
        setIsLoading(true);
        console.log('Checking authentication state...');

        // Get the current session
        const session = await authService.getCurrentSession();

        if (session) {
          console.log('Session found, using session user data directly...');
          try {
            // SIMPLIFIED: Use session.user directly instead of making another API call
            const formattedUser = await formatUser(session.user);
            setUser(formattedUser);
            console.log('User authenticated successfully');
          } catch (formatError) {
            console.error('Error formatting user:', formatError);

            // If we've reached max retries, set loading to false
            if (retryCount >= MAX_RETRIES) {
              console.warn(`Max retries (${MAX_RETRIES}) reached, authentication failed`);
              setIsLoading(false);
            } else {
              // Otherwise, retry after a delay
              retryCount++;
              console.log(`Retrying authentication (${retryCount}/${MAX_RETRIES})...`);
              retryTimeout = setTimeout(checkAuth, RETRY_DELAY);
              return; // Don't set isLoading to false yet
            }
          }
        } else {
          console.log('No active session found');
        }
      } catch (error) {
        console.error('Auth check error:', error);

        // If we've reached max retries, set loading to false
        if (retryCount >= MAX_RETRIES) {
          console.warn(`Max retries (${MAX_RETRIES}) reached after error`);
        } else {
          // Otherwise, retry after a delay
          retryCount++;
          console.log(`Retrying authentication (${retryCount}/${MAX_RETRIES})...`);
          retryTimeout = setTimeout(checkAuth, RETRY_DELAY);
          return; // Don't set isLoading to false yet
        }
      } finally {
        if (retryCount >= MAX_RETRIES || !retryTimeout) {
          setIsLoading(false);
        }
      }
    };

    // Initial auth check
    checkAuth();

    // Set up auth state change listener with debouncing to prevent duplicate events
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event);

      // If we should ignore this auth change (due to tab visibility change)
      if (ignoreNextAuthChange.current) {
        console.log(`Ignoring ${event} event due to tab visibility change`);
        ignoreNextAuthChange.current = false;
        return;
      }

      // Handle specific auth events
      switch (event) {
        case 'SIGNED_IN':
          if (session) {
            // SIMPLIFIED: Use session.user directly instead of making another API call
            if (!user) {
              console.log('Processing initial SIGNED_IN event');
              setIsLoading(true);
              try {
                // Use the user data from the session directly - no need for another API call
                const formattedUser = await formatUser(session.user);
                setUser(formattedUser);
                console.log('User signed in successfully');
              } catch (error) {
                console.error('Error during sign in:', error);
              } finally {
                setIsLoading(false);
              }
            } else {
              console.log('Ignoring duplicate SIGNED_IN event - user already signed in');
            }
          }
          break;

        case 'SIGNED_OUT':
          setUser(null);
          // Clear the entire React Query cache to prevent data leakage between users
          console.log('User signed out - clearing React Query cache');
          queryClient.clear();
          break;

        case 'TOKEN_REFRESHED':
          // Just log the token refresh but don't trigger a full re-render
          console.log('Auth token refreshed - no state update needed');
          break;

        case 'USER_UPDATED':
          console.log('User updated - no state update needed');
          break;

        default:
          console.log(`Unhandled auth event: ${event}`);
      }
    });

    // Cleanup subscription and any pending retries on unmount
    return () => {
      subscription.unsubscribe();
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
    };
  }, [queryClient]);

  // Login function
  const login = async (email: string, password: string) => {
    setIsLoading(true);

    try {
      const { user: supabaseUser } = await authService.signIn({ email, password });

      if (supabaseUser) {
        // Check if email is confirmed
        if (!supabaseUser.email_confirmed_at) {
          throw new Error('Please confirm your email address before logging in. Check your inbox for a confirmation link.');
        }

        const formattedUser = await formatUser(supabaseUser);
        setUser(formattedUser);
      }
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Signup function
  const signup = async (email: string, password: string, fullName: string, userType: 'recruiter' | 'candidate' = 'recruiter') => {
    setIsLoading(true);

    try {
      const { user: supabaseUser } = await authService.signUp({
        email,
        password,
        fullName,
        userType
      });

      // In Supabase, the user won't be fully authenticated until they confirm their email
      // We'll show a success message but not log them in automatically
      toast({
        title: 'Verification email sent',
        description: 'Please check your email to verify your account before logging in.',
      });

      // We don't set the user here, as they need to verify their email first
    } catch (error) {
      console.error('Signup error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };



  // Logout function
  const logout = async () => {
    setIsLoading(true);

    try {
      await authService.signOut();
      setUser(null);

      // Clear the entire React Query cache to prevent data leakage between users
      console.log('Clearing React Query cache on logout');
      queryClient.clear();
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Forgot password function
  const forgotPassword = async (email: string) => {
    setIsLoading(true);

    try {
      await authService.resetPassword({ email });
    } catch (error) {
      console.error('Forgot password error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Reset password function
  const resetPassword = async (password: string) => {
    setIsLoading(true);

    try {
      await authService.updatePassword({ password });
    } catch (error) {
      console.error('Reset password error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Update profile function
  const updateProfile = async (profile: { fullName?: string; avatarUrl?: string }) => {
    setIsLoading(true);

    try {
      await authService.updateProfile(profile);

      // Update the local user state
      if (user) {
        setUser({
          ...user,
          name: profile.fullName || user.name,
          avatarUrl: profile.avatarUrl || user.avatarUrl
        });
      }
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Resend verification email function
  const resendVerificationEmail = async (email: string) => {
    setIsLoading(true);

    try {
      await authService.resendEmailVerification(email);
    } catch (error) {
      console.error('Resend verification email error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // We use a flag to track if this is the initial visibility change
  const isInitialVisibilityChange = useRef(true);

  // This effect handles what happens when the page becomes visible again
  // We want to silently check the session without triggering a full re-render
  useEffect(() => {
    // Skip the first run (initial mount)
    if (isInitialVisibilityChange.current) {
      isInitialVisibilityChange.current = false;
      return;
    }

    // Only do something when the page becomes visible AND we have a user
    if (isPageVisible && user) {
      console.log('Page became visible again, checking session status silently');

      // We don't want to trigger any state updates unless absolutely necessary
      // Just verify the session is still valid without updating any state
      authService.getCurrentSession()
        .then(session => {
          if (!session) {
            console.warn('Session expired while page was inactive - logging out');
            setUser(null);
            // Clear the entire React Query cache to prevent data leakage between users
            console.log('Clearing React Query cache due to session expiration');
            queryClient.clear();
          } else {
            console.log('Session is still valid, no refresh needed');
            // Important: We're NOT updating any state here to prevent re-renders
          }
        })
        .catch(error => {
          console.error('Error checking session on visibility change:', error);
        });
    }
  }, [isPageVisible, user, queryClient]);

  // Check email verification status
  const checkEmailVerification = async (email: string): Promise<boolean> => {
    try {
      // Try to get the user by email (this is a workaround since we can't directly check verification status)
      const { data, error } = await supabase.auth.signInWithOtp({
        email,
        options: {
          shouldCreateUser: false,
        }
      });

      if (error) {
        // If the error is about the user not being confirmed, then the email is not verified
        if (error.message.includes('Email not confirmed')) {
          return false;
        }
        throw error;
      }

      // If we got here, the email is verified
      return true;
    } catch (error) {
      console.error('Check email verification error:', error);
      return false;
    }
  };

  const value = {
    user,
    isAuthenticated: !!user,
    isLoading,
    login,
    signup,
    logout,
    forgotPassword,
    resetPassword,
    updateProfile,
    resendVerificationEmail,
    checkEmailVerification
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

// Custom hook to use the auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
