import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { AuthProvider } from "@/contexts/AuthContext";
import { NotificationProvider } from "@/contexts/NotificationContext";
import { PermissionsProvider } from "@/contexts/PermissionsContext";
import { CompanyProvider } from "@/contexts/CompanyContext";
import { UsageProvider } from "@/contexts/UsageContext";
import NotificationToastContainer from "@/components/notifications/NotificationToastContainer";
import ToastInitializer from "@/components/ToastInitializer";
import ProtectedRoute from "@/components/ProtectedRoute";
import PermissionGuard from "@/components/PermissionGuard";
import { PublicJobView } from '@/pages/PublicJobView';
import { HelmetProvider } from 'react-helmet-async';

// Pages
import Index from "./pages/Index";
import Login from "./pages/Login";
import Signup from "./pages/Signup";
import ForgotPassword from "./pages/ForgotPassword";
// import Dashboard from "./pages/Dashboard"; // Using EnhancedDashboard instead
import EnhancedDashboard from "./pages/EnhancedDashboard";
import JobListings from "./pages/JobListings";
import JobPostingForm from "./pages/JobPostingForm";
import JobDetails from "./pages/JobDetails";
import JobEdit from "./pages/JobEdit";
import CandidateManagement from "./pages/CandidateManagement";
import CVEvaluation from "./pages/CVEvaluation";
import CandidateStatus from "./pages/CandidateStatus";
import UserProfile from "./pages/UserProfile";
import Settings from "./pages/Settings";
import TeamManagement from "./pages/TeamManagement";
import RolePermissions from "./pages/RolePermissions";
import NotificationPreferences from "./pages/NotificationPreferences";
import Notifications from "./pages/Notifications";
import Analytics from "./pages/Analytics";
import Help from "./pages/Help";
import CompanyProfile from "./pages/CompanyProfile";
import CompanyManagement from "./pages/CompanyManagement";
import PlatformAdmin from "./pages/PlatformAdmin";
import Pricing from "./pages/Pricing";
import BillingSubscriptionManagement from "./pages/BillingSubscriptionManagement";
import SubscriptionPlansAdmin from "./pages/SubscriptionPlansAdmin";
import PlanFeaturesAdmin from "./pages/PlanFeaturesAdmin";
import AIModelSettings from "./pages/AIModelSettings";
import EmailTemplates from "./pages/admin/EmailTemplates";
import InterviewScheduling from "./pages/InterviewScheduling";
import InterviewDetailsPage from "./components/interviews/InterviewDetailsPage";
import AssessmentTests from "./pages/AssessmentTests";
import AIInterviewPrep from "./pages/AIInterviewPrep";
import ReferenceChecks from "./pages/ReferenceChecks";
import AcceptInvitation from './pages/AcceptInvitation';

import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";
import PublicJobsListing from "./pages/PublicJobsListing";
import CandidateSignup from "./pages/CandidateSignup";
import CandidateDashboard from "./pages/CandidateDashboard";

// Configure QueryClient with stricter caching to prevent data leakage between users
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Reduce stale time to minimize risk of showing stale data
      staleTime: 1000 * 60 * 5, // 5 minutes
      // Reduce cache time to minimize risk of showing cached data from previous sessions
      gcTime: 1000 * 60 * 10, // 10 minutes
      // Don't retry failed queries to prevent showing stale data
      retry: false,
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <PermissionsProvider>
        <UsageProvider>
          <NotificationProvider>
            <CompanyProvider>
              <TooltipProvider>
              <Toaster />
              <Sonner />
              <NotificationToastContainer />
              <ToastInitializer />
              <BrowserRouter>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<Index />} />
            <Route path="/login" element={<Login />} />
            <Route path="/signup" element={<Signup />} />
            <Route path="/forgot-password" element={<ForgotPassword />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/accept-invitation" element={<AcceptInvitation />} />
            <Route
              path="/dashboard/pricing"
              element={
                <ProtectedRoute>
                  <Pricing />
                </ProtectedRoute>
              }
            />
            <Route path="/jobs/:jobId" element={<PublicJobView />} />

            {/* Candidate Portal Routes */}
            <Route path="/jobs" element={<PublicJobsListing />} />
            <Route path="/candidate-signup" element={<CandidateSignup />} />
            <Route
              path="/candidate-dashboard"
              element={
                <ProtectedRoute>
                  <CandidateDashboard />
                </ProtectedRoute>
              }
            />

            {/* Protected routes */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <EnhancedDashboard />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/jobs"
              element={
                <ProtectedRoute>
                  <JobListings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/jobs/new"
              element={
                <ProtectedRoute resource="jobs" action="create">
                  <JobPostingForm />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/jobs/:id"
              element={
                <ProtectedRoute>
                  <JobDetails />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/jobs/edit/:id"
              element={
                <ProtectedRoute resource="jobs" action="edit">
                  <JobEdit />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/cvs"
              element={
                <ProtectedRoute>
                  <CandidateManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/cvs/evaluation"
              element={
                <ProtectedRoute>
                  <CVEvaluation />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/cvs/:id"
              element={
                <ProtectedRoute>
                  <CandidateStatus />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/analytics"
              element={
                <ProtectedRoute>
                  <Analytics />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/companies"
              element={
                <ProtectedRoute>
                  <CompanyManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/company/:id"
              element={
                <ProtectedRoute>
                  <CompanyProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/profile"
              element={
                <ProtectedRoute>
                  <UserProfile />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/settings"
              element={
                <ProtectedRoute>
                  <Settings />
                </ProtectedRoute>
              }
            />

            <Route
              path="/dashboard/notifications"
              element={
                <ProtectedRoute>
                  <Notifications />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/notifications/preferences"
              element={
                <ProtectedRoute>
                  <NotificationPreferences />
                </ProtectedRoute>
              }
            />
            {/* Unified Billing & Subscription page */}
            <Route
              path="/dashboard/billing"
              element={
                <ProtectedRoute>
                  <BillingSubscriptionManagement />
                </ProtectedRoute>
              }
            />
            {/* Redirect old subscription route to billing */}
            <Route
              path="/dashboard/subscription"
              element={
                <ProtectedRoute>
                  <BillingSubscriptionManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/team"
              element={
                <ProtectedRoute>
                  <TeamManagement />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/roles"
              element={
                <ProtectedRoute requiredRoles={['admin', 'platform_admin']}>
                  <RolePermissions />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/admin"
              element={
                <ProtectedRoute requiredRoles={['platform_admin']}>
                  <PlatformAdmin />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/admin/subscription-plans"
              element={
                <ProtectedRoute requiredRoles={['platform_admin']}>
                  <SubscriptionPlansAdmin />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/admin/ai-model-settings"
              element={
                <ProtectedRoute requiredRoles={['platform_admin']}>
                  <AIModelSettings />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/admin/plan-features"
              element={
                <ProtectedRoute requiredRoles={['platform_admin']}>
                  <PlanFeaturesAdmin />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/admin/email-templates"
              element={
                <ProtectedRoute requiredRoles={['platform_admin']}>
                  <EmailTemplates />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/help"
              element={
                <ProtectedRoute>
                  <Help />
                </ProtectedRoute>
              }
            />

            {/* Advanced Features (Pro) */}
            <Route
              path="/dashboard/interviews"
              element={
                <ProtectedRoute>
                  <InterviewScheduling />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/interviews/:interviewId"
              element={
                <ProtectedRoute>
                  <InterviewDetailsPage />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/assessments"
              element={
                <ProtectedRoute>
                  <AssessmentTests />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/interview-prep"
              element={
                <ProtectedRoute>
                  <AIInterviewPrep />
                </ProtectedRoute>
              }
            />
            <Route
              path="/dashboard/references"
              element={
                <ProtectedRoute>
                  <ReferenceChecks />
                </ProtectedRoute>
              }
            />

            {/* Catch-all route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
              </TooltipProvider>
            </CompanyProvider>
          </NotificationProvider>
        </UsageProvider>
      </PermissionsProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
