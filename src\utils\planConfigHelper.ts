import { FEATURES, PLAN_FEATURES } from '@/config/paypal';

/**
 * Utility functions to help manage plan feature configurations
 */

/**
 * Helper to quickly disable a feature for specific plans
 * @param feature - The feature to disable
 * @param plans - Array of plans to disable the feature for
 * @returns Updated plan features configuration
 */
export const disableFeatureForPlans = (
  feature: keyof typeof FEATURES,
  plans: Array<keyof typeof PLAN_FEATURES>
) => {
  const featureKey = FEATURES[feature];
  const updatedConfig = { ...PLAN_FEATURES };
  
  plans.forEach(plan => {
    if (updatedConfig[plan]) {
      updatedConfig[plan] = {
        ...updatedConfig[plan],
        [featureKey]: false
      };
    }
  });
  
  return updatedConfig;
};

/**
 * Helper to quickly enable a feature for specific plans
 * @param feature - The feature to enable
 * @param plans - Array of plans to enable the feature for
 * @returns Updated plan features configuration
 */
export const enableFeatureForPlans = (
  feature: keyof typeof FEATURES,
  plans: Array<keyof typeof PLAN_FEATURES>
) => {
  const featureKey = FEATURES[feature];
  const updatedConfig = { ...PLAN_FEATURES };
  
  plans.forEach(plan => {
    if (updatedConfig[plan]) {
      updatedConfig[plan] = {
        ...updatedConfig[plan],
        [featureKey]: true
      };
    }
  });
  
  return updatedConfig;
};

/**
 * Helper to set a feature to be available only for specific plans
 * @param feature - The feature to configure
 * @param allowedPlans - Array of plans that should have access
 * @returns Updated plan features configuration
 */
export const setFeatureForPlansOnly = (
  feature: keyof typeof FEATURES,
  allowedPlans: Array<keyof typeof PLAN_FEATURES>
) => {
  const featureKey = FEATURES[feature];
  const updatedConfig = { ...PLAN_FEATURES };
  const allPlans = Object.keys(PLAN_FEATURES) as Array<keyof typeof PLAN_FEATURES>;
  
  allPlans.forEach(plan => {
    if (updatedConfig[plan]) {
      updatedConfig[plan] = {
        ...updatedConfig[plan],
        [featureKey]: allowedPlans.includes(plan)
      };
    }
  });
  
  return updatedConfig;
};

/**
 * Get a summary of which plans have access to a specific feature
 * @param feature - The feature to check
 * @returns Object with plan access information
 */
export const getFeatureAccessSummary = (feature: keyof typeof FEATURES) => {
  const featureKey = FEATURES[feature];
  const summary = {
    feature: feature,
    featureKey: featureKey,
    accessByPlan: {} as Record<string, boolean>,
    allowedPlans: [] as string[],
    restrictedPlans: [] as string[]
  };
  
  Object.entries(PLAN_FEATURES).forEach(([plan, features]) => {
    const hasAccess = features[featureKey] || false;
    summary.accessByPlan[plan] = hasAccess;
    
    if (hasAccess) {
      summary.allowedPlans.push(plan);
    } else {
      summary.restrictedPlans.push(plan);
    }
  });
  
  return summary;
};

/**
 * Get all features and their plan availability
 * @returns Array of feature summaries
 */
export const getAllFeaturesSummary = () => {
  return Object.keys(FEATURES).map(feature => 
    getFeatureAccessSummary(feature as keyof typeof FEATURES)
  );
};

/**
 * Example configurations for common scenarios
 */
export const EXAMPLE_CONFIGURATIONS = {
  // Make interview scheduling available only to Growth and Pro plans
  interviewSchedulingGrowthPlus: () => 
    setFeatureForPlansOnly('INTERVIEW_SCHEDULING', ['GROWTH', 'PRO']),
  
  // Make team management Pro-only
  teamManagementProOnly: () => 
    setFeatureForPlansOnly('TEAM_MANAGEMENT', ['PRO']),
  
  // Disable interview scheduling for all plans (maintenance mode)
  disableInterviewScheduling: () => 
    disableFeatureForPlans('INTERVIEW_SCHEDULING', ['STARTER', 'GROWTH', 'PRO']),
  
  // Enable a new feature for all plans
  enableNewFeatureForAll: (feature: keyof typeof FEATURES) => 
    enableFeatureForPlans(feature, ['STARTER', 'GROWTH', 'PRO'])
};

/**
 * Validate plan configuration to ensure it makes sense
 * @param config - Plan features configuration to validate
 * @returns Validation result with any issues found
 */
export const validatePlanConfiguration = (config: typeof PLAN_FEATURES) => {
  const issues: string[] = [];
  const warnings: string[] = [];
  
  // Check that higher tier plans have at least as many features as lower tiers
  const plans = ['STARTER', 'GROWTH', 'PRO'] as const;
  const features = Object.values(FEATURES);
  
  features.forEach(featureKey => {
    const starterHas = config.STARTER[featureKey];
    const growthHas = config.GROWTH[featureKey];
    const proHas = config.PRO[featureKey];
    
    // Starter has feature but Growth doesn't
    if (starterHas && !growthHas) {
      warnings.push(`Feature ${featureKey} is available in STARTER but not GROWTH`);
    }
    
    // Growth has feature but Pro doesn't
    if (growthHas && !proHas) {
      warnings.push(`Feature ${featureKey} is available in GROWTH but not PRO`);
    }
    
    // Starter has feature but Pro doesn't
    if (starterHas && !proHas) {
      issues.push(`Feature ${featureKey} is available in STARTER but not PRO`);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues,
    warnings
  };
};

// Example usage:
// console.log('Interview Scheduling Access:', getFeatureAccessSummary('INTERVIEW_SCHEDULING'));
// console.log('All Features:', getAllFeaturesSummary());
// console.log('Validation:', validatePlanConfiguration(PLAN_FEATURES));
