import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import { corsHeaders } from '../_shared/cors.ts'

interface WebhookEvent {
  id: string;
  event_type: string;
  resource: any;
  create_time: string;
  resource_type: string;
  summary: string;
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
    )

    // Get the request body
    const event: WebhookEvent = await req.json()
    const eventType = event.event_type
    
    console.log('Received PayPal webhook event:', eventType)
    
    // Verify webhook signature (simplified for now)
    // In production, you should implement proper signature verification
    // using the PayPal-Transmission-Id, PayPal-Transmission-Sig, etc. headers
    
    // Handle different event types
    switch (eventType) {
      case 'BILLING.SUBSCRIPTION.CREATED':
        await handleSubscriptionCreated(event, supabaseAdmin)
        break
      case 'BILLING.SUBSCRIPTION.ACTIVATED':
        await handleSubscriptionActivated(event, supabaseAdmin)
        break
      case 'BILLING.SUBSCRIPTION.UPDATED':
        await handleSubscriptionUpdated(event, supabaseAdmin)
        break
      case 'BILLING.SUBSCRIPTION.CANCELLED':
        await handleSubscriptionCancelled(event, supabaseAdmin)
        break
      case 'BILLING.SUBSCRIPTION.SUSPENDED':
        await handleSubscriptionSuspended(event, supabaseAdmin)
        break
      case 'BILLING.SUBSCRIPTION.PAYMENT.FAILED':
        await handleSubscriptionPaymentFailed(event, supabaseAdmin)
        break
      case 'PAYMENT.SALE.COMPLETED':
        await handlePaymentCompleted(event, supabaseAdmin)
        break
      default:
        console.log('Unhandled webhook event type:', eventType)
    }
    
    return new Response(JSON.stringify({ status: 'success' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Error processing webhook:', error)
    
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    })
  }
})

// Handle subscription created event
async function handleSubscriptionCreated(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.resource
  const subscriptionId = subscription.id
  const planId = subscription.plan_id
  const status = subscription.status.toLowerCase()
  const startDate = subscription.start_time
  
  // Find the custom ID (user ID) from the subscription
  const customId = subscription.custom_id
  
  if (!customId) {
    console.error('No custom ID found in subscription:', subscriptionId)
    return
  }
  
  try {
    // Check if subscription already exists
    const { data: existingSubscription } = await supabaseAdmin
      .from('subscriptions')
      .select('id')
      .eq('subscription_id', subscriptionId)
      .single()
    
    if (existingSubscription) {
      console.log('Subscription already exists:', subscriptionId)
      return
    }
    
    // Determine subscription tier from plan ID
    let tier = 'starter'
    if (planId.includes('GROWTH')) {
      tier = 'growth'
    } else if (planId.includes('PRO')) {
      tier = 'pro'
    }
    
    // Create subscription record
    const { error } = await supabaseAdmin
      .from('subscriptions')
      .insert({
        user_id: customId,
        subscription_id: subscriptionId,
        plan_id: planId,
        tier,
        status,
        start_date: startDate,
        payment_method: 'paypal',
        payment_details: subscription
      })
    
    if (error) {
      console.error('Error creating subscription record:', error)
    }
    
    // Update user profile with subscription info
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_tier: tier,
        subscription_status: status,
        subscription_end_date: null
      })
      .eq('user_id', customId)
    
    if (profileError) {
      console.error('Error updating user profile:', profileError)
    }
  } catch (error) {
    console.error('Error handling subscription created event:', error)
  }
}

// Handle subscription activated event
async function handleSubscriptionActivated(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.resource
  const subscriptionId = subscription.id
  
  try {
    // Update subscription status
    const { error } = await supabaseAdmin
      .from('subscriptions')
      .update({
        status: 'active',
        payment_details: subscription
      })
      .eq('subscription_id', subscriptionId)
    
    if (error) {
      console.error('Error updating subscription status:', error)
      return
    }
    
    // Get user ID from subscription
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .select('user_id, tier')
      .eq('subscription_id', subscriptionId)
      .single()
    
    if (subscriptionError || !subscriptionData) {
      console.error('Error getting subscription data:', subscriptionError)
      return
    }
    
    // Update user profile
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_status: 'active',
        subscription_tier: subscriptionData.tier
      })
      .eq('user_id', subscriptionData.user_id)
    
    if (profileError) {
      console.error('Error updating user profile:', profileError)
    }
  } catch (error) {
    console.error('Error handling subscription activated event:', error)
  }
}

// Handle subscription updated event
async function handleSubscriptionUpdated(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.resource
  const subscriptionId = subscription.id
  const status = subscription.status.toLowerCase()
  
  try {
    // Update subscription record
    const { error } = await supabaseAdmin
      .from('subscriptions')
      .update({
        status,
        payment_details: subscription
      })
      .eq('subscription_id', subscriptionId)
    
    if (error) {
      console.error('Error updating subscription record:', error)
      return
    }
    
    // Get user ID from subscription
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .select('user_id')
      .eq('subscription_id', subscriptionId)
      .single()
    
    if (subscriptionError || !subscriptionData) {
      console.error('Error getting subscription data:', subscriptionError)
      return
    }
    
    // Update user profile
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_status: status
      })
      .eq('user_id', subscriptionData.user_id)
    
    if (profileError) {
      console.error('Error updating user profile:', profileError)
    }
  } catch (error) {
    console.error('Error handling subscription updated event:', error)
  }
}

// Handle subscription cancelled event
async function handleSubscriptionCancelled(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.resource
  const subscriptionId = subscription.id
  
  try {
    // Update subscription record
    const { error } = await supabaseAdmin
      .from('subscriptions')
      .update({
        status: 'cancelled',
        end_date: new Date().toISOString(),
        payment_details: subscription
      })
      .eq('subscription_id', subscriptionId)
    
    if (error) {
      console.error('Error updating subscription record:', error)
      return
    }
    
    // Get user ID from subscription
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .select('user_id')
      .eq('subscription_id', subscriptionId)
      .single()
    
    if (subscriptionError || !subscriptionData) {
      console.error('Error getting subscription data:', subscriptionError)
      return
    }
    
    // Update user profile
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_status: 'cancelled',
        subscription_end_date: new Date().toISOString()
      })
      .eq('user_id', subscriptionData.user_id)
    
    if (profileError) {
      console.error('Error updating user profile:', profileError)
    }
  } catch (error) {
    console.error('Error handling subscription cancelled event:', error)
  }
}

// Handle subscription suspended event
async function handleSubscriptionSuspended(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.resource
  const subscriptionId = subscription.id
  
  try {
    // Update subscription record
    const { error } = await supabaseAdmin
      .from('subscriptions')
      .update({
        status: 'suspended',
        payment_details: subscription
      })
      .eq('subscription_id', subscriptionId)
    
    if (error) {
      console.error('Error updating subscription record:', error)
      return
    }
    
    // Get user ID from subscription
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .select('user_id')
      .eq('subscription_id', subscriptionId)
      .single()
    
    if (subscriptionError || !subscriptionData) {
      console.error('Error getting subscription data:', subscriptionError)
      return
    }
    
    // Update user profile
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_status: 'suspended'
      })
      .eq('user_id', subscriptionData.user_id)
    
    if (profileError) {
      console.error('Error updating user profile:', profileError)
    }
  } catch (error) {
    console.error('Error handling subscription suspended event:', error)
  }
}

// Handle subscription payment failed event
async function handleSubscriptionPaymentFailed(event: WebhookEvent, supabaseAdmin: any) {
  const subscription = event.resource
  const subscriptionId = subscription.id
  
  try {
    // Update subscription record
    const { error } = await supabaseAdmin
      .from('subscriptions')
      .update({
        status: 'payment_failed',
        payment_details: subscription
      })
      .eq('subscription_id', subscriptionId)
    
    if (error) {
      console.error('Error updating subscription record:', error)
      return
    }
    
    // Get user ID from subscription
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .select('user_id')
      .eq('subscription_id', subscriptionId)
      .single()
    
    if (subscriptionError || !subscriptionData) {
      console.error('Error getting subscription data:', subscriptionError)
      return
    }
    
    // Update user profile
    const { error: profileError } = await supabaseAdmin
      .from('profiles')
      .update({
        subscription_status: 'payment_failed'
      })
      .eq('user_id', subscriptionData.user_id)
    
    if (profileError) {
      console.error('Error updating user profile:', profileError)
    }
    
    // TODO: Send notification to user about payment failure
  } catch (error) {
    console.error('Error handling subscription payment failed event:', error)
  }
}

// Handle payment completed event
async function handlePaymentCompleted(event: WebhookEvent, supabaseAdmin: any) {
  const payment = event.resource
  const paymentId = payment.id
  const amount = payment.amount.total
  const currency = payment.amount.currency
  
  // Get billing agreement ID (subscription ID)
  const billingAgreementId = payment.billing_agreement_id
  
  if (!billingAgreementId) {
    console.log('Not a subscription payment, skipping')
    return
  }
  
  try {
    // Get subscription data
    const { data: subscriptionData, error: subscriptionError } = await supabaseAdmin
      .from('subscriptions')
      .select('user_id, tier')
      .eq('subscription_id', billingAgreementId)
      .single()

    if (subscriptionError || !subscriptionData) {
      console.error('Error getting subscription data:', subscriptionError)
      return
    }

    // Record payment
    const { error } = await supabaseAdmin
      .from('payments')
      .insert({
        user_id: subscriptionData.user_id,
        subscription_id: billingAgreementId,
        payment_id: paymentId,
        amount: parseFloat(amount),
        currency,
        status: 'completed',
        payment_method: 'paypal',
        payment_type: 'subscription',
        payment_details: payment
      })

    if (error) {
      console.error('Error recording payment:', error)
      return
    }

    // Create invoice record
    await createInvoiceFromPayment(supabaseAdmin, {
      userId: subscriptionData.user_id,
      paymentId: paymentId,
      subscriptionId: billingAgreementId,
      amount: parseFloat(amount),
      currency: currency,
      paymentMethod: 'paypal',
      tier: subscriptionData.tier,
      paymentDate: payment.create_time || new Date().toISOString(),
    })

    console.log('Payment and invoice recorded successfully:', paymentId)
  } catch (error) {
    console.error('Error handling payment completed event:', error)
  }
}

// Helper function to create invoice from payment
async function createInvoiceFromPayment(supabaseAdmin: any, paymentData: {
  userId: string
  paymentId: string
  subscriptionId: string
  amount: number
  currency: string
  paymentMethod: string
  tier: string
  paymentDate: string
}) {
  try {
    // Get user's billing info
    const { data: billingInfo } = await supabaseAdmin
      .from('billing_info')
      .select('*')
      .eq('user_id', paymentData.userId)
      .single()

    // If no billing info, create a basic one from user profile
    let finalBillingInfo = billingInfo
    if (!billingInfo) {
      const { data: profile } = await supabaseAdmin
        .from('profiles')
        .select('full_name, email')
        .eq('user_id', paymentData.userId)
        .single()

      finalBillingInfo = {
        company_name: null,
        address_line1: 'Address not provided',
        address_line2: null,
        city: 'City not provided',
        state: 'State not provided',
        postal_code: '00000',
        country: 'US',
        tax_id: null,
        billing_email: profile?.email || '<EMAIL>',
        phone: null,
      }
    }

    // Generate invoice number
    const year = new Date().getFullYear()
    const { count } = await supabaseAdmin
      .from('invoices')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', `${year}-01-01`)
      .lt('created_at', `${year + 1}-01-01`)

    const invoiceCount = (count || 0) + 1
    const invoiceNumber = `INV-${year}-${invoiceCount.toString().padStart(5, '0')}`

    // Create invoice items
    const invoiceItems = [{
      description: `${paymentData.tier.charAt(0).toUpperCase() + paymentData.tier.slice(1)} Plan Subscription`,
      quantity: 1,
      unit_price: paymentData.amount,
      amount: paymentData.amount,
    }]

    // Create invoice
    const { error: invoiceError } = await supabaseAdmin
      .from('invoices')
      .insert({
        user_id: paymentData.userId,
        invoice_number: invoiceNumber,
        amount: paymentData.amount,
        currency: paymentData.currency,
        status: 'paid',
        issue_date: paymentData.paymentDate,
        due_date: paymentData.paymentDate,
        payment_date: paymentData.paymentDate,
        subscription_id: paymentData.subscriptionId,
        payment_id: paymentData.paymentId,
        invoice_items: invoiceItems,
        billing_info: finalBillingInfo,
      })

    if (invoiceError) {
      console.error('Error creating invoice:', invoiceError)
    } else {
      console.log('Invoice created successfully:', invoiceNumber)
    }
  } catch (error) {
    console.error('Error in createInvoiceFromPayment:', error)
  }
}
