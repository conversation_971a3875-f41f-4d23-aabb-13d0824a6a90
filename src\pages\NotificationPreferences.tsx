import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  Bell,
  Mail,
  MessageSquare,
  Calendar,
  User,
  Briefcase,
  FileText,
  Save,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import * as profileService from '@/services/supabase/profiles';

// Mock notification preferences
interface NotificationChannel {
  email: boolean;
  inApp: boolean;
}

interface NotificationPreference {
  id: string;
  category: string;
  description: string;
  icon: React.ReactNode;
  channels: NotificationChannel;
}

const NotificationPreferences = () => {
  const { toast } = useToast();
  const { user: authUser } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initial notification preferences
  const [preferences, setPreferences] = useState<NotificationPreference[]>([
    {
      id: 'new_candidates',
      category: 'New Candidates',
      description: 'Notifications when new candidates apply for your job postings',
      icon: <User className="h-5 w-5 text-blue-500" />,
      channels: { email: true, inApp: true }
    },
    {
      id: 'job_updates',
      category: 'Job Updates',
      description: 'Notifications about changes to your job postings',
      icon: <Briefcase className="h-5 w-5 text-green-500" />,
      channels: { email: true, inApp: true }
    },
    {
      id: 'interviews',
      category: 'Interviews',
      description: 'Reminders about upcoming interviews and schedule changes',
      icon: <Calendar className="h-5 w-5 text-purple-500" />,
      channels: { email: true, inApp: true }
    },
    {
      id: 'messages',
      category: 'Messages',
      description: 'Notifications about new messages and replies',
      icon: <MessageSquare className="h-5 w-5 text-amber-500" />,
      channels: { email: false, inApp: true }
    },
    {
      id: 'cv_evaluations',
      category: 'CV Evaluations',
      description: 'Notifications when CV evaluations are completed',
      icon: <FileText className="h-5 w-5 text-red-500" />,
      channels: { email: true, inApp: true }
    },
    {
      id: 'system',
      category: 'System Notifications',
      description: 'Important system updates and announcements',
      icon: <Bell className="h-5 w-5 text-gray-500" />,
      channels: { email: true, inApp: true }
    }
  ]);

  // Toggle notification channel
  const toggleChannel = (id: string, channel: keyof NotificationChannel) => {
    setPreferences(prev =>
      prev.map(pref =>
        pref.id === id
          ? {
              ...pref,
              channels: {
                ...pref.channels,
                [channel]: !pref.channels[channel]
              }
            }
          : pref
      )
    );
  };

  // Load user preferences from profile
  useEffect(() => {
    const loadUserPreferences = async () => {
      if (!authUser) return;

      try {
        setIsLoading(true);
        const profile = await profileService.getProfile(authUser.id);

        if (profile && profile.metadata) {
          // Parse metadata from profile
          const metadata = typeof profile.metadata === 'string'
            ? JSON.parse(profile.metadata)
            : profile.metadata;

          // Get notification preferences from metadata
          if (metadata.notificationPreferences) {
            // Update notification channels
            if (metadata.notificationPreferences.channels) {
              setPreferences(prev =>
                prev.map(pref => {
                  const savedPref = metadata.notificationPreferences.channels[pref.id];
                  if (savedPref) {
                    return {
                      ...pref,
                      channels: savedPref
                    };
                  }
                  return pref;
                })
              );
            }
          }
        }
      } catch (error) {
        console.error('Error loading notification preferences:', error);
        toast({
          title: 'Error',
          description: 'Failed to load notification preferences. Using defaults.',
          variant: 'destructive',
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadUserPreferences();
  }, [authUser, toast]);

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!authUser) return;

    setIsSubmitting(true);

    try {
      // Get current profile
      const profile = await profileService.getProfile(authUser.id);

      if (!profile) {
        throw new Error('Profile not found');
      }

      // Prepare notification preferences data
      const notificationChannels: Record<string, NotificationChannel> = {};
      preferences.forEach(pref => {
        notificationChannels[pref.id] = pref.channels;
      });

      // Parse existing metadata or create new object
      const metadata = profile.metadata
        ? (typeof profile.metadata === 'string' ? JSON.parse(profile.metadata) : profile.metadata)
        : {};

      // Update notification preferences in metadata
      metadata.notificationPreferences = {
        channels: notificationChannels
      };

      // Update profile with new metadata
      await profileService.updateProfile({
        user_id: authUser.id,
        metadata: JSON.stringify(metadata)
      });

      toast({
        title: 'Preferences updated',
        description: 'Your notification preferences have been updated successfully.',
      });
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      toast({
        title: 'Error',
        description: 'There was an error updating your preferences. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-[60vh]">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-lg text-gray-600">Loading preferences...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-2xl font-bold text-gray-800">Notification Preferences</h1>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-6">


            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Notification Channels</CardTitle>
                <CardDescription className="text-gray-500">
                  Choose which notifications you want to receive and how
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 text-gray-600 font-medium">Notification Type</th>
                        <th className="text-center py-3 px-4 text-gray-600 font-medium">
                          <div className="flex items-center justify-center">
                            <Mail className="h-4 w-4 mr-2" />
                            <span>Email</span>
                          </div>
                        </th>
                        <th className="text-center py-3 px-4 text-gray-600 font-medium">
                          <div className="flex items-center justify-center">
                            <Bell className="h-4 w-4 mr-2" />
                            <span>In-App</span>
                          </div>
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {preferences.map(pref => (
                        <tr key={pref.id} className="border-b border-gray-200">
                          <td className="py-4 px-4">
                            <div className="flex items-center">
                              <div className="mr-3">
                                {pref.icon}
                              </div>
                              <div>
                                <h4 className="text-gray-800 font-medium">{pref.category}</h4>
                                <p className="text-gray-500 text-sm">{pref.description}</p>
                              </div>
                            </div>
                          </td>
                          <td className="py-4 px-4 text-center">
                            <div className="flex justify-center">
                              <Switch
                                checked={pref.channels.email}
                                onCheckedChange={() => toggleChannel(pref.id, 'email')}
                                className="data-[state=checked]:bg-recruiter-lightblue"
                              />
                            </div>
                          </td>
                          <td className="py-4 px-4 text-center">
                            <div className="flex justify-center">
                              <Switch
                                checked={pref.channels.inApp}
                                onCheckedChange={() => toggleChannel(pref.id, 'inApp')}
                                className="data-[state=checked]:bg-recruiter-lightblue"
                              />
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>


              </CardContent>
            </Card>

            <div className="flex justify-end">
              <Button
                type="submit"
                className="bg-recruiter-lightblue hover:bg-blue-500"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" /> Save Preferences
                  </>
                )}
              </Button>
            </div>
          </div>
        </form>
      </div>
    </DashboardLayout>
  );
};

export default NotificationPreferences;
