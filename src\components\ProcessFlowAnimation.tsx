import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  FileText, Upload, Building, BarChart2, CheckCircle,
  ChevronRight, Users, Database, Zap, PieChart,
  File, FileText as FileTextIcon, FolderPlus,
  UserPlus, ListFilter, ArrowUpCircle, Plus, Briefcase,
  Play, Pause
} from 'lucide-react';

// Upload Illustration Component
const UploadIllustration: React.FC<{ subStep: number }> = ({ subStep }) => {
  return (
    <div className="h-full flex flex-col items-center justify-center">
      {subStep === 0 && (
        <div className="text-center">
          <div className="bg-blue-100 w-24 h-24 rounded-full flex items-center justify-center mx-auto mb-4">
            <ArrowUpCircle size={48} className="text-blue-600" />
          </div>
          <h3 className="text-lg font-medium text-gray-800 mb-2">Drag & Drop Upload</h3>
          <p className="text-gray-600 text-sm">
            Simply drag and drop your CV files or use the file browser
          </p>

          <div className="mt-4 border-2 border-dashed border-blue-300 rounded-lg p-6 bg-blue-50">
            <div className="flex items-center justify-center space-x-2">
              <FileTextIcon size={24} className="text-red-500" />
              <FileTextIcon size={24} className="text-blue-500" />
              <File size={24} className="text-gray-500" />
            </div>
            <p className="text-sm text-blue-600 mt-2">Drop files here or click to browse</p>
          </div>
        </div>
      )}

      {subStep === 1 && (
        <div className="w-full">
          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
            <div className="flex items-start">
              <div className="bg-blue-100 p-2 rounded mr-3">
                <FileTextIcon size={24} className="text-blue-600" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-gray-800">John_Smith_CV.pdf</h3>
                <div className="mt-2 space-y-2">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{ duration: 1 }}
                    className="h-1 bg-blue-500 rounded-full"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>Parsing CV...</span>
                    <span>100%</span>
                  </div>
                </div>

                <div className="mt-3 grid grid-cols-2 gap-2">
                  <div className="bg-gray-100 p-2 rounded">
                    <p className="text-xs text-gray-500">Name</p>
                    <p className="text-sm font-medium">John Smith</p>
                  </div>
                  <div className="bg-gray-100 p-2 rounded">
                    <p className="text-xs text-gray-500">Email</p>
                    <p className="text-sm font-medium"><EMAIL></p>
                  </div>
                  <div className="bg-gray-100 p-2 rounded">
                    <p className="text-xs text-gray-500">Skills</p>
                    <div className="flex flex-wrap gap-1 mt-1">
                      <span className="px-1 py-0.5 bg-blue-100 text-blue-700 rounded text-xs">React</span>
                      <span className="px-1 py-0.5 bg-green-100 text-green-700 rounded text-xs">Node.js</span>
                    </div>
                  </div>
                  <div className="bg-gray-100 p-2 rounded">
                    <p className="text-xs text-gray-500">Experience</p>
                    <p className="text-sm font-medium">5 years</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {subStep === 2 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Talent Pools</h3>
          <div className="grid grid-cols-2 gap-3">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="flex items-center mb-2">
                <Users size={18} className="text-blue-600 mr-2" />
                <h4 className="font-medium text-gray-800">Developers</h4>
              </div>
              <p className="text-xs text-gray-600 mb-2">28 candidates</p>
              <div className="flex -space-x-2">
                <div className="w-6 h-6 rounded-full bg-blue-500 border-2 border-white flex items-center justify-center">
                  <span className="text-white text-xs">JS</span>
                </div>
                <div className="w-6 h-6 rounded-full bg-green-500 border-2 border-white flex items-center justify-center">
                  <span className="text-white text-xs">AK</span>
                </div>
                <div className="w-6 h-6 rounded-full bg-purple-500 border-2 border-white flex items-center justify-center">
                  <span className="text-white text-xs">TM</span>
                </div>
                <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                  <span className="text-gray-600 text-xs">+25</span>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
              <div className="flex items-center mb-2">
                <Users size={18} className="text-purple-600 mr-2" />
                <h4 className="font-medium text-gray-800">Designers</h4>
              </div>
              <p className="text-xs text-gray-600 mb-2">14 candidates</p>
              <div className="flex -space-x-2">
                <div className="w-6 h-6 rounded-full bg-yellow-500 border-2 border-white flex items-center justify-center">
                  <span className="text-white text-xs">SJ</span>
                </div>
                <div className="w-6 h-6 rounded-full bg-red-500 border-2 border-white flex items-center justify-center">
                  <span className="text-white text-xs">LR</span>
                </div>
                <div className="w-6 h-6 rounded-full bg-gray-200 border-2 border-white flex items-center justify-center">
                  <span className="text-gray-600 text-xs">+12</span>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-3 flex justify-end">
            <button className="flex items-center text-sm text-blue-600">
              <FolderPlus size={16} className="mr-1" />
              Create New Pool
            </button>
          </div>
        </div>
      )}

      {subStep === 3 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Bulk Upload</h3>

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-3">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <Database size={18} className="text-blue-600 mr-2" />
                <span className="font-medium text-gray-800">Batch #1204</span>
              </div>
              <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">Processing</span>
            </div>

            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Total CVs:</span>
                <span className="font-medium">24</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Processed:</span>
                <span className="font-medium">16</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Remaining:</span>
                <span className="font-medium">8</span>
              </div>
            </div>

            <div className="mt-3">
              <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: '67%' }}
                  transition={{ duration: 1 }}
                  className="h-full bg-blue-600 rounded-full"
                />
              </div>
              <div className="flex justify-between mt-1 text-xs text-gray-500">
                <span>Processing batch</span>
                <span>67%</span>
              </div>
            </div>
          </div>

          <div className="flex justify-between">
            <button className="text-sm text-gray-600 flex items-center">
              <ListFilter size={16} className="mr-1" />
              View All Batches
            </button>
            <button className="text-sm text-blue-600 flex items-center">
              <Upload size={16} className="mr-1" />
              New Batch Upload
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Companies Illustration Component
const CompaniesIllustration: React.FC<{ subStep: number }> = ({ subStep }) => {
  return (
    <div className="h-full flex flex-col items-center justify-center">
      {subStep === 0 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Company Profiles</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-3">
            <div className="flex items-center mb-3">
              <div className="w-12 h-12 rounded-lg bg-purple-100 flex items-center justify-center mr-3">
                <Building size={24} className="text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-800">TechCorp Inc.</h4>
                <p className="text-sm text-gray-600">Software Development</p>
              </div>
              <div className="ml-auto bg-green-100 px-2 py-1 rounded text-xs text-green-700">
                Active
              </div>
            </div>

            <div className="flex justify-between text-sm mb-3">
              <div>
                <span className="text-gray-600">Open Positions:</span>
                <span className="font-medium ml-1">4</span>
              </div>
              <div>
                <span className="text-gray-600">Candidates:</span>
                <span className="font-medium ml-1">28</span>
              </div>
              <div>
                <span className="text-gray-600">Matches:</span>
                <span className="font-medium ml-1">12</span>
              </div>
            </div>

            <button className="text-sm text-purple-600 flex items-center">
              <ChevronRight size={16} className="mr-1" />
              View Profile
            </button>
          </div>

          <button className="w-full py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors flex items-center justify-center">
            <Plus size={16} className="mr-2" />
            Add New Company
          </button>
        </div>
      )}

      {subStep === 1 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Define Requirements</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center mr-3">
                <Building size={20} className="text-purple-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-800">TechCorp Inc.</h4>
                <p className="text-xs text-gray-600">Frontend Developer Position</p>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">Required Skills</h5>
                <div className="flex flex-wrap gap-2">
                  <div className="flex items-center bg-purple-50 px-2 py-1 rounded text-sm">
                    <span className="text-purple-700">React</span>
                    <div className="ml-1 px-1.5 bg-purple-200 rounded text-xs text-purple-800">
                      Must-have
                    </div>
                  </div>
                  <div className="flex items-center bg-purple-50 px-2 py-1 rounded text-sm">
                    <span className="text-purple-700">TypeScript</span>
                    <div className="ml-1 px-1.5 bg-purple-200 rounded text-xs text-purple-800">
                      Must-have
                    </div>
                  </div>
                  <div className="flex items-center bg-purple-50 px-2 py-1 rounded text-sm">
                    <span className="text-purple-700">CSS</span>
                    <div className="ml-1 px-1.5 bg-gray-200 rounded text-xs text-gray-800">
                      Nice-to-have
                    </div>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Experience Level</h5>
                  <div className="bg-gray-100 p-2 rounded">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Minimum:</span>
                      <span className="font-medium">3 years</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Preferred:</span>
                      <span className="font-medium">5+ years</span>
                    </div>
                  </div>
                </div>

                <div>
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Education</h5>
                  <div className="bg-gray-100 p-2 rounded">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Minimum:</span>
                      <span className="font-medium">Bachelor's</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Field:</span>
                      <span className="font-medium">Computer Science</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {subStep === 2 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Company Branding</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-4">
            <div className="flex items-start">
              <div className="mr-4">
                <div className="w-16 h-16 rounded-lg bg-purple-100 flex items-center justify-center">
                  <Building size={32} className="text-purple-600" />
                </div>
                <button className="mt-2 text-xs text-purple-600 flex items-center justify-center w-full">
                  <Upload size={12} className="mr-1" />
                  Upload Logo
                </button>
              </div>

              <div className="flex-1">
                <div className="mb-3">
                  <label className="block text-xs text-gray-600 mb-1">Company Name</label>
                  <input
                    type="text"
                    value="TechCorp Inc."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    readOnly
                  />
                </div>

                <div className="mb-3">
                  <label className="block text-xs text-gray-600 mb-1">Industry</label>
                  <input
                    type="text"
                    value="Software Development"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    readOnly
                  />
                </div>

                <div>
                  <label className="block text-xs text-gray-600 mb-1">Company Description</label>
                  <textarea
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    rows={2}
                    value="Leading software development company specializing in web and mobile applications."
                    readOnly
                  ></textarea>
                </div>
              </div>
            </div>
          </div>

          <div className="flex justify-end">
            <button className="px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors text-sm">
              Save Changes
            </button>
          </div>
        </div>
      )}

      {subStep === 3 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Multi-Company Dashboard</h3>

          <div className="grid grid-cols-2 gap-3 mb-4">
            <div className="bg-white rounded-lg border border-gray-200 p-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center mr-2">
                  <Building size={20} className="text-purple-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-800">TechCorp Inc.</h4>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="bg-green-100 text-green-700 px-1.5 py-0.5 rounded mr-1">Active</span>
                    <span>4 open positions</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center mr-2">
                  <Building size={20} className="text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-800">InnovateSoft</h4>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="bg-green-100 text-green-700 px-1.5 py-0.5 rounded mr-1">Active</span>
                    <span>2 open positions</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-3">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center mr-2">
                  <Building size={20} className="text-green-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-800">GreenTech Solutions</h4>
                  <div className="flex items-center text-xs text-gray-500">
                    <span className="bg-green-100 text-green-700 px-1.5 py-0.5 rounded mr-1">Active</span>
                    <span>1 open position</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg border border-gray-200 p-3 border-dashed flex items-center justify-center">
              <button className="text-sm text-purple-600 flex items-center">
                <Plus size={16} className="mr-1" />
                Add Company
              </button>
            </div>
          </div>

          <div className="bg-purple-50 border border-purple-200 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-gray-800">Pro Plan Features</h4>
              <span className="text-xs bg-purple-200 text-purple-800 px-2 py-1 rounded">Unlimited Companies</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Manage unlimited company profiles with our Pro plan. Perfect for recruitment agencies working with multiple clients.
            </p>
            <button className="text-sm text-purple-600 flex items-center">
              <ChevronRight size={16} className="mr-1" />
              View Pricing Plans
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Evaluation Illustration Component
const EvaluationIllustration: React.FC<{ subStep: number }> = ({ subStep }) => {
  return (
    <div className="h-full flex flex-col items-center justify-center">
      {subStep === 0 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">AI Analysis</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-3">
            <div className="flex items-center mb-3">
              <div className="w-12 h-12 rounded-lg bg-green-100 flex items-center justify-center mr-3">
                <Zap size={24} className="text-green-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-800">CV Analysis in Progress</h4>
                <p className="text-sm text-gray-600">Analyzing John Smith's CV against TechCorp requirements</p>
              </div>
            </div>

            <div className="space-y-3">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Analyzing skills...</span>
                  <span className="text-green-600">Complete</span>
                </div>
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-green-500 rounded-full" style={{ width: '100%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Analyzing experience...</span>
                  <span className="text-green-600">Complete</span>
                </div>
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-green-500 rounded-full" style={{ width: '100%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <span className="text-gray-600">Calculating match score...</span>
                  <motion.span
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    className="text-green-600"
                  >
                    Complete
                  </motion.span>
                </div>
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                  <motion.div
                    initial={{ width: 0 }}
                    animate={{ width: '100%' }}
                    transition={{ duration: 1 }}
                    className="h-full bg-green-500 rounded-full"
                  ></motion.div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center text-green-800">
              <CheckCircle size={16} className="mr-2" />
              <span className="font-medium">Analysis complete!</span>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Our AI has analyzed the CV against the company requirements and generated a match score.
            </p>
          </div>
        </div>
      )}

      {subStep === 1 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Skills Matching</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-indigo-500 flex items-center justify-center mr-3">
                  <span className="text-white text-sm font-medium">JS</span>
                </div>
                <div>
                  <h4 className="font-medium text-gray-800">John Smith</h4>
                  <p className="text-xs text-gray-600">Senior Developer</p>
                </div>
              </div>

              <div>
                <span className="text-2xl font-bold text-green-600">85%</span>
                <span className="text-xs text-gray-500 block text-right">Match Score</span>
              </div>
            </div>

            <h5 className="text-sm font-medium text-gray-700 mb-2">Skills Analysis</h5>
            <div className="space-y-2 mb-4">
              <div>
                <div className="flex justify-between text-sm mb-1">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    <span className="text-gray-700">React</span>
                  </div>
                  <span className="text-green-600 font-medium">95%</span>
                </div>
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-green-500 rounded-full" style={{ width: '95%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-2"></span>
                    <span className="text-gray-700">TypeScript</span>
                  </div>
                  <span className="text-green-600 font-medium">90%</span>
                </div>
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-green-500 rounded-full" style={{ width: '90%' }}></div>
                </div>
              </div>

              <div>
                <div className="flex justify-between text-sm mb-1">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-2"></span>
                    <span className="text-gray-700">CSS</span>
                  </div>
                  <span className="text-yellow-600 font-medium">70%</span>
                </div>
                <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div className="h-full bg-yellow-500 rounded-full" style={{ width: '70%' }}></div>
                </div>
              </div>
            </div>

            <div className="bg-green-50 border border-green-200 rounded p-2 text-sm text-gray-700">
              <span className="font-medium text-green-800">AI Insight:</span> Candidate has strong React and TypeScript skills that match the requirements. CSS skills are adequate but could be stronger.
            </div>
          </div>
        </div>
      )}

      {subStep === 2 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Experience Evaluation</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center mr-3">
                <Briefcase className="text-green-600" size={20} />
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Experience Analysis</h4>
                <p className="text-xs text-gray-600">Comparing candidate experience with job requirements</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                <div className="flex justify-between mb-2">
                  <h5 className="text-sm font-medium text-gray-700">TechCorp Requirements</h5>
                  <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">Frontend Developer</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Minimum Experience:</span>
                    <span className="font-medium">3 years</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Preferred Experience:</span>
                    <span className="font-medium">5+ years</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Required Technologies:</span>
                    <span className="font-medium">React, TypeScript</span>
                  </div>
                </div>
              </div>

              <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                <div className="flex justify-between mb-2">
                  <h5 className="text-sm font-medium text-gray-700">John Smith's Experience</h5>
                  <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded">Senior Developer</span>
                </div>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total Experience:</span>
                    <span className="font-medium">5 years</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">React Experience:</span>
                    <span className="font-medium">4 years</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">TypeScript Experience:</span>
                    <span className="font-medium">3 years</span>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 p-3 rounded-lg border border-green-200">
                <h5 className="text-sm font-medium text-green-800 mb-2">Match Analysis</h5>
                <div className="flex items-center mb-2">
                  <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden mr-3">
                    <div className="h-full bg-green-500 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                  <span className="text-green-600 font-medium whitespace-nowrap">85% Match</span>
                </div>
                <p className="text-sm text-gray-700">
                  Candidate exceeds minimum requirements and meets preferred experience levels. Strong match for the position.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {subStep === 3 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Multi-Company Evaluation</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4 mb-3">
            <div className="flex items-center mb-3">
              <div className="w-10 h-10 rounded-full bg-indigo-500 flex items-center justify-center mr-3">
                <span className="text-white text-sm font-medium">JS</span>
              </div>
              <div>
                <h4 className="font-medium text-gray-800">John Smith</h4>
                <p className="text-xs text-gray-600">Evaluated against multiple companies</p>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center p-2 bg-gray-50 rounded-lg border border-gray-200">
                <div className="w-8 h-8 rounded-lg bg-purple-100 flex items-center justify-center mr-2">
                  <Building size={16} className="text-purple-600" />
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-800">TechCorp Inc.</span>
                    <span className="text-sm font-medium text-green-600">85%</span>
                  </div>
                  <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden mt-1">
                    <div className="h-full bg-green-500 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                </div>
              </div>

              <div className="flex items-center p-2 bg-gray-50 rounded-lg border border-gray-200">
                <div className="w-8 h-8 rounded-lg bg-blue-100 flex items-center justify-center mr-2">
                  <Building size={16} className="text-blue-600" />
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-800">InnovateSoft</span>
                    <span className="text-sm font-medium text-green-600">78%</span>
                  </div>
                  <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden mt-1">
                    <div className="h-full bg-green-500 rounded-full" style={{ width: '78%' }}></div>
                  </div>
                </div>
              </div>

              <div className="flex items-center p-2 bg-gray-50 rounded-lg border border-gray-200">
                <div className="w-8 h-8 rounded-lg bg-green-100 flex items-center justify-center mr-2">
                  <Building size={16} className="text-green-600" />
                </div>
                <div className="flex-1">
                  <div className="flex justify-between">
                    <span className="text-sm font-medium text-gray-800">GreenTech Solutions</span>
                    <span className="text-sm font-medium text-yellow-600">62%</span>
                  </div>
                  <div className="w-full h-1.5 bg-gray-200 rounded-full overflow-hidden mt-1">
                    <div className="h-full bg-yellow-500 rounded-full" style={{ width: '62%' }}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-3">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-gray-800">AI Recommendation</h4>
              <span className="text-xs bg-green-200 text-green-800 px-2 py-1 rounded">Best Match: TechCorp</span>
            </div>
            <p className="text-sm text-gray-600 mb-2">
              Based on skills, experience, and company requirements, this candidate is best suited for TechCorp Inc.
            </p>
            <button className="text-sm text-green-600 flex items-center">
              <ChevronRight size={16} className="mr-1" />
              View Detailed Report
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

// Reports Illustration Component
const ReportsIllustration: React.FC<{ subStep: number }> = ({ subStep }) => {
  return (
    <div className="h-full flex flex-col items-center justify-center">
      {subStep === 0 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Evaluation Dashboard</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-lg bg-amber-100 flex items-center justify-center mr-3">
                <BarChart2 size={20} className="text-amber-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Evaluation Results</h4>
                <p className="text-xs text-gray-600">Visual overview of candidate evaluations</p>
              </div>
            </div>

            <div className="grid grid-cols-3 gap-3 mb-4">
              <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 text-center">
                <div className="text-2xl font-bold text-amber-600">128</div>
                <div className="text-xs text-gray-600">Total Candidates</div>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 text-center">
                <div className="text-2xl font-bold text-green-600">85</div>
                <div className="text-xs text-gray-600">Evaluated</div>
              </div>
              <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 text-center">
                <div className="text-2xl font-bold text-blue-600">43</div>
                <div className="text-xs text-gray-600">Pending</div>
              </div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg border border-gray-200 mb-3">
              <h5 className="text-sm font-medium text-gray-700 mb-2">Match Score Distribution</h5>
              <div className="h-24 flex items-end space-x-1">
                <div className="flex-1 bg-red-500 h-[15%]" title="0-20%"></div>
                <div className="flex-1 bg-orange-500 h-[25%]" title="21-40%"></div>
                <div className="flex-1 bg-yellow-500 h-[35%]" title="41-60%"></div>
                <div className="flex-1 bg-blue-500 h-[65%]" title="61-80%"></div>
                <div className="flex-1 bg-green-500 h-[90%]" title="81-100%"></div>
              </div>
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0-20%</span>
                <span>21-40%</span>
                <span>41-60%</span>
                <span>61-80%</span>
                <span>81-100%</span>
              </div>
            </div>

            <button className="w-full py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-lg transition-colors text-sm flex items-center justify-center">
              <BarChart2 size={16} className="mr-2" />
              View Full Dashboard
            </button>
          </div>
        </div>
      )}

      {subStep === 1 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Match Quality Metrics</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-lg bg-amber-100 flex items-center justify-center mr-3">
                  <PieChart size={20} className="text-amber-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-800">TechCorp Inc.</h4>
                  <p className="text-xs text-gray-600">Frontend Developer Position</p>
                </div>
              </div>

              <div className="px-2 py-1 bg-amber-100 text-amber-700 rounded text-xs">
                12 Candidates Evaluated
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">Match Score Distribution</h5>
                <div className="w-full h-4 bg-gray-200 rounded-full overflow-hidden">
                  <div className="flex h-full">
                    <div className="bg-green-500 h-full" style={{ width: '45%' }}></div>
                    <div className="bg-blue-500 h-full" style={{ width: '30%' }}></div>
                    <div className="bg-yellow-500 h-full" style={{ width: '15%' }}></div>
                    <div className="bg-orange-500 h-full" style={{ width: '7%' }}></div>
                    <div className="bg-red-500 h-full" style={{ width: '3%' }}></div>
                  </div>
                </div>
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-green-500 rounded-full mr-1"></span>
                    <span>80-100% (5)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-1"></span>
                    <span>60-79% (4)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></span>
                    <span>40-59% (2)</span>
                  </div>
                  <div className="flex items-center">
                    <span className="w-2 h-2 bg-orange-500 rounded-full mr-1"></span>
                    <span>20-39% (1)</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Top Candidates</h5>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-indigo-500 flex items-center justify-center mr-2">
                          <span className="text-white text-xs">JS</span>
                        </div>
                        <span className="text-sm">John Smith</span>
                      </div>
                      <span className="text-sm font-medium text-green-600">85%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center mr-2">
                          <span className="text-white text-xs">AK</span>
                        </div>
                        <span className="text-sm">Anna Kim</span>
                      </div>
                      <span className="text-sm font-medium text-green-600">82%</span>
                    </div>
                  </div>
                </div>

                <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                  <h5 className="text-sm font-medium text-gray-700 mb-2">Average Scores</h5>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Skills Match:</span>
                      <span className="font-medium">76%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Experience Match:</span>
                      <span className="font-medium">68%</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Education Match:</span>
                      <span className="font-medium">72%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {subStep === 2 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Skill Distribution Analysis</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center mb-4">
              <div className="w-10 h-10 rounded-lg bg-amber-100 flex items-center justify-center mr-3">
                <PieChart size={20} className="text-amber-600" />
              </div>
              <div>
                <h4 className="font-medium text-gray-800">Candidate Skill Analysis</h4>
                <p className="text-xs text-gray-600">Overview of skills in your candidate pool</p>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <h5 className="text-sm font-medium text-gray-700 mb-2">Top Skills in Candidate Pool</h5>
                <div className="space-y-2">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-700">React</span>
                      <span className="text-gray-600">68 candidates</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-blue-500 rounded-full" style={{ width: '85%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-700">TypeScript</span>
                      <span className="text-gray-600">52 candidates</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-blue-500 rounded-full" style={{ width: '65%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-700">Node.js</span>
                      <span className="text-gray-600">45 candidates</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-blue-500 rounded-full" style={{ width: '56%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-700">CSS/SCSS</span>
                      <span className="text-gray-600">38 candidates</span>
                    </div>
                    <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div className="h-full bg-blue-500 rounded-full" style={{ width: '48%' }}></div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                <h5 className="text-sm font-medium text-amber-800 mb-2">Skill Gap Analysis</h5>
                <p className="text-sm text-gray-700 mb-2">
                  Based on your company requirements, we've identified the following skill gaps in your candidate pool:
                </p>
                <div className="space-y-1">
                  <div className="flex items-start">
                    <span className="w-2 h-2 bg-red-500 rounded-full mt-1.5 mr-2"></span>
                    <span className="text-sm text-gray-700">GraphQL expertise (only 12% of candidates)</span>
                  </div>
                  <div className="flex items-start">
                    <span className="w-2 h-2 bg-red-500 rounded-full mt-1.5 mr-2"></span>
                    <span className="text-sm text-gray-700">AWS experience (only 18% of candidates)</span>
                  </div>
                  <div className="flex items-start">
                    <span className="w-2 h-2 bg-yellow-500 rounded-full mt-1.5 mr-2"></span>
                    <span className="text-sm text-gray-700">Testing frameworks (only 35% of candidates)</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {subStep === 3 && (
        <div className="w-full">
          <h3 className="text-lg font-medium text-gray-800 mb-3">Custom Reports</h3>

          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-lg bg-amber-100 flex items-center justify-center mr-3">
                  <FileText size={20} className="text-amber-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-800">Report Generator</h4>
                  <p className="text-xs text-gray-600">Create custom reports for clients and management</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div className="bg-gray-50 p-3 rounded-lg border border-gray-200">
                <h5 className="text-sm font-medium text-gray-700 mb-3">Available Report Templates</h5>
                <div className="space-y-2">
                  <div className="flex items-center p-2 bg-white rounded border border-gray-200">
                    <div className="w-8 h-8 rounded bg-blue-100 flex items-center justify-center mr-2">
                      <FileText size={16} className="text-blue-600" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-800">Client Summary Report</div>
                      <div className="text-xs text-gray-600">Overview of candidates evaluated for a specific client</div>
                    </div>
                    <button className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                      Generate
                    </button>
                  </div>

                  <div className="flex items-center p-2 bg-white rounded border border-gray-200">
                    <div className="w-8 h-8 rounded bg-green-100 flex items-center justify-center mr-2">
                      <FileText size={16} className="text-green-600" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-800">Candidate Pipeline Report</div>
                      <div className="text-xs text-gray-600">Status of all candidates in your recruitment pipeline</div>
                    </div>
                    <button className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                      Generate
                    </button>
                  </div>

                  <div className="flex items-center p-2 bg-white rounded border border-gray-200">
                    <div className="w-8 h-8 rounded bg-purple-100 flex items-center justify-center mr-2">
                      <FileText size={16} className="text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-gray-800">Skills Gap Analysis</div>
                      <div className="text-xs text-gray-600">Detailed analysis of skill gaps in your candidate pool</div>
                    </div>
                    <button className="text-xs bg-purple-100 text-purple-700 px-2 py-1 rounded">
                      Generate
                    </button>
                  </div>
                </div>
              </div>

              <div className="bg-amber-50 border border-amber-200 rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <h5 className="text-sm font-medium text-gray-800">Custom Report Builder</h5>
                  <span className="text-xs bg-amber-200 text-amber-800 px-2 py-1 rounded">Pro Feature</span>
                </div>
                <p className="text-sm text-gray-600 mb-3">
                  Create fully customized reports with your own branding, metrics, and visualizations.
                </p>
                <button className="w-full py-2 bg-amber-600 hover:bg-amber-700 text-white rounded-lg transition-colors text-sm">
                  Create Custom Report
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// Step interface
interface ProcessStep {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  subSteps: {
    number: number;
    text: string;
  }[];
}

// Process steps data
const processSteps: ProcessStep[] = [
  {
    id: 'upload',
    title: 'Upload CVs',
    description: 'Easily upload candidate CVs in multiple formats including PDF, DOCX, and TXT. Our system automatically parses and extracts key information for your agency.',
    icon: <Upload size={24} />,
    color: 'blue',
    subSteps: [
      { number: 1, text: 'Drag and drop multiple files or use the file browser' },
      { number: 2, text: 'Automatic parsing of contact details, skills, experience, and education' },
      { number: 3, text: 'Organize candidates into talent pools for better management' },
      { number: 4, text: 'Bulk upload CVs to process multiple candidates at once' }
    ]
  },
  {
    id: 'companies',
    title: 'Manage Companies',
    description: 'Create and manage company profiles with detailed requirements, skills, and qualifications needed for their open positions.',
    icon: <Building size={24} />,
    color: 'purple',
    subSteps: [
      { number: 1, text: 'Add multiple company profiles to your agency account' },
      { number: 2, text: 'Define required skills, experience levels, and qualifications for each company' },
      { number: 3, text: 'Upload company logos and customize company details' },
      { number: 4, text: 'Manage multiple companies from a single dashboard' }
    ]
  },
  {
    id: 'evaluation',
    title: 'Automatic Evaluation',
    description: 'Our AI-powered system automatically evaluates candidates against company requirements, helping agencies match the right talent to the right opportunities.',
    icon: <Zap size={24} />,
    color: 'green',
    subSteps: [
      { number: 1, text: 'AI analyzes CV content against company job requirements' },
      { number: 2, text: 'Skills matching with percentage-based scoring for each company' },
      { number: 3, text: 'Experience evaluation based on relevance to company needs' },
      { number: 4, text: 'Evaluate candidates against multiple companies simultaneously' }
    ]
  },
  {
    id: 'reports',
    title: 'View Reports',
    description: 'Generate comprehensive reports on your CV evaluation process, match quality, and candidate skills to optimize your agency operations.',
    icon: <BarChart2 size={24} />,
    color: 'amber',
    subSteps: [
      { number: 1, text: 'Visual dashboards showing candidate evaluation results' },
      { number: 2, text: 'Match quality metrics across companies and positions' },
      { number: 3, text: 'Skill distribution analysis in your candidate pool' },
      { number: 4, text: 'Custom reports for clients and agency management' }
    ]
  }
];

// Progress bar component
const ProgressBar: React.FC<{ currentStep: number; totalSteps: number }> = ({
  currentStep,
  totalSteps
}) => {
  return (
    <div className="w-full bg-gray-200 h-2 rounded-full overflow-hidden">
      <motion.div
        className="h-full bg-blue-600"
        initial={{ width: 0 }}
        animate={{ width: `${(currentStep / totalSteps) * 100}%` }}
        transition={{ duration: 0.5 }}
      />
    </div>
  );
};

// Step indicator component
const StepIndicator: React.FC<{
  steps: ProcessStep[];
  currentStep: number;
  onStepClick: (index: number) => void;
}> = ({ steps, currentStep, onStepClick }) => {
  return (
    <div className="flex justify-between items-center w-full mb-8 text-base">
      {steps.map((step, index) => {
        const isActive = index === currentStep;
        const isPast = index < currentStep;
        const colorClasses = {
          blue: 'bg-recruiter-blue',
          purple: 'bg-purple-600',
          green: 'bg-green-500',
          amber: 'bg-amber-500'
        };

        return (
          <div key={step.id} className="flex flex-col items-center relative">
            {/* Connector line */}
            {index > 0 && (
              <div className="absolute h-1 bg-gray-700 top-5 -left-full w-full z-0">
                <motion.div
                  className={`h-full ${colorClasses[step.color as keyof typeof colorClasses]}`}
                  initial={{ width: 0 }}
                  animate={{ width: isPast ? '100%' : isActive ? '50%' : '0%' }}
                  transition={{ duration: 0.5 }}
                />
              </div>
            )}

            {/* Step circle */}
            <motion.button
              className={`w-10 h-10 rounded-full flex items-center justify-center z-10 border-2 ${
                isActive || isPast
                  ? `${colorClasses[step.color as keyof typeof colorClasses]} text-white border-transparent`
                  : 'bg-gray-800 text-gray-400 border-gray-700'
              }`}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => onStepClick(index)}
            >
              {isPast ? <CheckCircle size={20} /> : (index + 1)}
            </motion.button>

            {/* Step label */}
            <span className={`text-sm mt-2 font-medium ${
              isActive || isPast ? 'text-white' : 'text-gray-400'
            }`}>
              {step.title}
            </span>
          </div>
        );
      })}
    </div>
  );
};

// Main component
export const ProcessFlowAnimation: React.FC = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [currentSubStep, setCurrentSubStep] = useState(0);
  const [isAutoPlaying, setIsAutoPlaying] = useState(true);
  const timeoutsRef = useRef<NodeJS.Timeout[]>([]);

  // Function to handle step change
  const handleStepChange = (index: number) => {
    // Clear all timeouts when manually changing steps
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current = [];

    setCurrentStep(index);
    setCurrentSubStep(0);

    // If auto-play is running, restart it from the new step
    if (isAutoPlaying) {
      startAutoPlay(index, 0);
    }
  };

  // Function to toggle auto-play
  const toggleAutoPlay = () => {
    if (isAutoPlaying) {
      // Stop auto-play
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
      timeoutsRef.current = [];
      setIsAutoPlaying(false);
    } else {
      // Start auto-play from the current step and sub-step
      setIsAutoPlaying(true);
      startAutoPlay(currentStep, currentSubStep);
    }
  };

  // Function to start auto-play sequence
  const startAutoPlay = (stepIndex: number, subStepIndex: number) => {
    // Clear existing timeouts
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    timeoutsRef.current = [];

    const newTimeouts: NodeJS.Timeout[] = [];

    // Calculate total number of steps and substeps
    const totalStepsCount = processSteps.reduce((acc, step) => acc + step.subSteps.length, 0);

    // Create a flat sequence of all steps and substeps
    const flatSequence: {step: number, subStep: number}[] = [];

    // Start with current position
    flatSequence.push({step: stepIndex, subStep: subStepIndex});

    // Add next 20 steps to the sequence (to avoid infinite loop)
    let currentStep = stepIndex;
    let currentSubStep = subStepIndex;

    for (let i = 0; i < totalStepsCount; i++) {
      // Calculate next position
      if (currentSubStep < processSteps[currentStep].subSteps.length - 1) {
        // Move to next sub-step
        currentSubStep++;
      } else if (currentStep < processSteps.length - 1) {
        // Move to next step
        currentStep++;
        currentSubStep = 0;
      } else {
        // Restart from beginning
        currentStep = 0;
        currentSubStep = 0;
      }

      // Add to sequence
      flatSequence.push({step: currentStep, subStep: currentSubStep});
    }

    // Create timeouts for each step in the sequence
    flatSequence.forEach((position, index) => {
      if (index === 0) return; // Skip the first one (current position)

      const timeout = setTimeout(() => {
        setCurrentStep(position.step);
        setCurrentSubStep(position.subStep);
      }, 3000 * index); // 3 seconds per step

      newTimeouts.push(timeout);
    });

    // Store the timeouts
    timeoutsRef.current = newTimeouts;
  };

  // Start auto-play on mount
  useEffect(() => {
    startAutoPlay(0, 0);

    return () => {
      // Clean up timeouts on unmount
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  // Get current step data
  const currentStepData = processSteps[currentStep];

  return (
    <section className="bg-recruiter-navy pt-16">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">How Our CV Evaluation Process Works</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our AI-powered platform helps recruitment agencies evaluate CVs against company requirements to find the best matches.
          </p>
        </div>

        {/* Container with fixed height to prevent layout shifts */}
        <div className="relative h-[680px] overflow-hidden">
          {/* Apply scaling to the entire component */}
          <div className="max-w-5xl mx-auto absolute top-0 left-0 right-0">
          {/* Removed auto-play control from here - moved to header */}

          {/* Step indicators */}
          <StepIndicator
            steps={processSteps}
            currentStep={currentStep}
            onStepClick={handleStepChange}
          />

          {/* Main content area - scaled to 67% */}
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden mb-8 mx-auto relative"
               style={{ transform: 'scale(0.67)', transformOrigin: 'top center', width: '150%', marginLeft: '-25%', marginBottom: '4rem' }}>

            {/* Play button overlay - only shows when animation is paused */}
            {!isAutoPlaying && (
              <div className="absolute inset-0 bg-black/40 z-10 flex items-center justify-center">
                <motion.button
                  className="w-20 h-20 bg-gradient-to-r from-recruiter-blue to-recruiter-lightblue rounded-full flex items-center justify-center text-white"
                  onClick={toggleAutoPlay}
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  <Play size={36} fill="white" />
                </motion.button>
              </div>
            )}

            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center mr-4 ${
                    currentStepData.color === 'blue' ? 'bg-blue-100 text-blue-600' :
                    currentStepData.color === 'purple' ? 'bg-purple-100 text-purple-600' :
                    currentStepData.color === 'green' ? 'bg-green-100 text-green-600' :
                    'bg-amber-100 text-amber-600'
                  }`}>
                    {currentStepData.icon}
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">{currentStepData.title}</h3>
                    <p className="text-gray-600">{currentStepData.description}</p>
                  </div>
                </div>

                {/* Auto-play control moved to header */}
                <button
                  onClick={toggleAutoPlay}
                  className={`flex items-center px-4 py-2 rounded-lg text-sm font-medium transition-all ${
                    isAutoPlaying
                      ? 'bg-blue-100 text-blue-600 hover:bg-blue-200'
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }`}
                >
                  {isAutoPlaying ? (
                    <>
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                        className="w-3 h-3 border-2 border-blue-500 border-t-transparent rounded-full mr-2"
                      />
                      <Pause size={16} className="mr-1" />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play size={16} className="mr-1" />
                      Play
                    </>
                  )}
                </button>
              </div>
            </div>

            <div className="p-6">
              <h4 className="text-lg font-medium text-gray-800 mb-4 flex items-center">
                <span className="mr-2">How it works:</span>
                <span className="text-xs text-gray-500">(Click on steps to navigate)</span>
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Left side: Steps */}
                <div className="space-y-4">
                  {currentStepData.subSteps.map((subStep, index) => (
                    <motion.button
                      key={index}
                      onClick={() => {
                        // If auto-play is running, restart it from this step
                        if (isAutoPlaying) {
                          timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
                          timeoutsRef.current = [];
                          setCurrentSubStep(index);
                          // Restart auto-play from the current step and selected sub-step
                          startAutoPlay(currentStep, index);
                        } else {
                          // Just set the current sub-step without starting auto-play
                          setCurrentSubStep(index);
                        }
                      }}
                      className={`flex items-start p-4 rounded-lg w-full text-left cursor-pointer transition-all hover:shadow-md ${
                        index === currentSubStep
                          ? currentStepData.color === 'blue' ? 'bg-blue-50 border border-blue-200' :
                            currentStepData.color === 'purple' ? 'bg-purple-50 border border-purple-200' :
                            currentStepData.color === 'green' ? 'bg-green-50 border border-green-200' :
                            'bg-amber-50 border border-amber-200'
                          : 'bg-gray-50 border border-gray-200 hover:bg-gray-100'
                      }`}
                      animate={{
                        scale: index === currentSubStep ? 1.02 : 1,
                        opacity: index <= currentSubStep ? 1 : 0.5
                      }}
                      transition={{ duration: 0.3 }}
                      whileHover={{ scale: 1.01 }}
                      whileTap={{ scale: 0.99 }}
                    >
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 flex-shrink-0 ${
                        index <= currentSubStep
                          ? currentStepData.color === 'blue' ? 'bg-blue-600 text-white' :
                            currentStepData.color === 'purple' ? 'bg-purple-600 text-white' :
                            currentStepData.color === 'green' ? 'bg-green-600 text-white' :
                            'bg-amber-600 text-white'
                          : 'bg-gray-300 text-white'
                      }`}>
                        {subStep.number}
                      </div>
                      <div className="text-gray-700">{subStep.text}</div>
                    </motion.button>
                  ))}
                </div>

                {/* Right side: Visual illustration */}
                <div className="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={`${currentStep}-${currentSubStep}`}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{ duration: 0.5 }}
                      className="p-4 h-full"
                    >
                      {currentStepData.id === 'upload' && (
                        <UploadIllustration subStep={currentSubStep} />
                      )}

                      {currentStepData.id === 'companies' && (
                        <CompaniesIllustration subStep={currentSubStep} />
                      )}

                      {currentStepData.id === 'evaluation' && (
                        <EvaluationIllustration subStep={currentSubStep} />
                      )}

                      {currentStepData.id === 'reports' && (
                        <ReportsIllustration subStep={currentSubStep} />
                      )}
                    </motion.div>
                  </AnimatePresence>
                </div>
              </div>
            </div>

            {/* Progress bar - moved closer to main content */}
            <div className="flex items-center justify-between mt-4 mb-2 px-6 pt-4 border-t border-gray-200">
              <div className="text-sm text-gray-600">
                Step {currentStep + 1} of {processSteps.length}, Substep {currentSubStep + 1} of {currentStepData.subSteps.length}
              </div>
              <div className="w-64">
                <ProgressBar
                  currentStep={(currentStep * currentStepData.subSteps.length + currentSubStep + 1)}
                  totalSteps={processSteps.reduce((acc, step) => acc + step.subSteps.length, 0)}
                />
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>
    </section>
  );
};
