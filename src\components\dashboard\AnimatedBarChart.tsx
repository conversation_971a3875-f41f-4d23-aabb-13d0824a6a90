import React, { useState, useEffect } from 'react';
import { cn } from '@/lib/utils';

interface BarChartItem {
  label: string;
  value: number;
  color: string;
}

interface AnimatedBarChartProps {
  data: BarChartItem[];
  className?: string;
  maxValue?: number;
}

export const AnimatedBarChart: React.FC<AnimatedBarChartProps> = ({
  data,
  className,
  maxValue: customMaxValue,
}) => {
  const [animated, setAnimated] = useState(false);
  
  // Calculate the max value for the chart
  const maxValue = customMaxValue || Math.max(...data.map(item => item.value)) * 1.2;
  
  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setAnimated(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);
  
  return (
    <div className={cn("space-y-4", className)}>
      {data.map((item, index) => (
        <div key={index} className="space-y-1">
          <div className="flex justify-between text-sm">
            <span className="font-medium">{item.label}</span>
            <span className="text-gray-500">{item.value}</span>
          </div>
          <div className="h-2 bg-gray-100 rounded-full overflow-hidden">
            <div 
              className={cn(
                "h-full rounded-full transition-all duration-1000 ease-out",
                item.color
              )}
              style={{ 
                width: animated ? `${(item.value / maxValue) * 100}%` : '0%',
                transitionDelay: `${index * 100}ms`
              }}
            />
          </div>
        </div>
      ))}
    </div>
  );
};
