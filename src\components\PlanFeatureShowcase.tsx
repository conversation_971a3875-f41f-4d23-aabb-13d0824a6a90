import React from 'react';
import { usePlanFeatures } from '@/hooks/use-plan-features';
import PlanGuard from '@/components/guards/PlanGuard';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CalendarClock, 
  Users, 
  BrainCircuit, 
  BarChart3, 
  Settings,
  CheckCircle,
  XCircle,
  ArrowRight
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

/**
 * Component to showcase plan-based features and their availability
 */
const PlanFeatureShowcase: React.FC = () => {
  const { 
    currentPlan, 
    hasFeature, 
    getMinimumPlan, 
    getAvailableFeatures, 
    getDisabledFeatures,
    canUpgradeForFeature 
  } = usePlanFeatures();
  const navigate = useNavigate();

  const features = [
    {
      key: 'INTERVIEW_SCHEDULING' as const,
      name: 'Interview Scheduling',
      description: 'Schedule and manage candidate interviews',
      icon: CalendarClock,
      color: 'text-green-600'
    },
    {
      key: 'TEAM_MANAGEMENT' as const,
      name: 'Team Management',
      description: 'Invite and manage team members',
      icon: Users,
      color: 'text-blue-600'
    },
    {
      key: 'CUSTOM_SCORING' as const,
      name: 'Custom Scoring',
      description: 'Create custom candidate scoring rules',
      icon: BrainCircuit,
      color: 'text-purple-600'
    },
    {
      key: 'ADVANCED_ANALYTICS' as const,
      name: 'Advanced Analytics',
      description: 'Detailed hiring insights and reports',
      icon: BarChart3,
      color: 'text-orange-600'
    }
  ];

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Plan Features</h2>
        <p className="text-gray-600 mt-2">
          You're currently on the <Badge variant="outline" className="mx-1 capitalize">{currentPlan}</Badge> plan
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {features.map((feature) => {
          const hasAccess = hasFeature(feature.key);
          const minimumPlan = getMinimumPlan(feature.key);
          const canUpgrade = canUpgradeForFeature(feature.key);
          const Icon = feature.icon;

          return (
            <Card key={feature.key} className={`border-2 ${hasAccess ? 'border-green-200 bg-green-50' : 'border-gray-200'}`}>
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Icon className={`h-6 w-6 ${feature.color}`} />
                    <div>
                      <CardTitle className="text-lg">{feature.name}</CardTitle>
                      <CardDescription className="text-sm">
                        {feature.description}
                      </CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {hasAccess ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-gray-400" />
                    )}
                    <Badge variant={hasAccess ? 'default' : 'secondary'} className="text-xs">
                      {hasAccess ? 'Available' : `${minimumPlan.toLowerCase()}+`}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {hasAccess ? (
                  <div className="text-sm text-green-700">
                    ✓ This feature is available on your current plan
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="text-sm text-gray-600">
                      Requires {minimumPlan.toLowerCase()} plan or higher
                    </div>
                    {canUpgrade && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => navigate('/dashboard/pricing')}
                        className="gap-1"
                      >
                        <ArrowRight className="h-3 w-3" />
                        Upgrade to {minimumPlan.toLowerCase()}
                      </Button>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Example of using PlanGuard for specific features */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Feature Examples</h3>
        
        {/* Interview Scheduling Example */}
        <PlanGuard feature="INTERVIEW_SCHEDULING" featureName="Interview Scheduling" compact>
          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <CalendarClock className="h-5 w-5 text-green-600" />
                <div>
                  <h4 className="font-medium">Interview Scheduling Available!</h4>
                  <p className="text-sm text-gray-600">You can now schedule and manage interviews.</p>
                </div>
                <Button size="sm" onClick={() => navigate('/dashboard/interviews')}>
                  Go to Interviews
                </Button>
              </div>
            </CardContent>
          </Card>
        </PlanGuard>

        {/* Team Management Example */}
        <PlanGuard feature="TEAM_MANAGEMENT" featureName="Team Management" compact>
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Users className="h-5 w-5 text-blue-600" />
                <div>
                  <h4 className="font-medium">Team Management Available!</h4>
                  <p className="text-sm text-gray-600">Invite team members and manage permissions.</p>
                </div>
                <Button size="sm" onClick={() => navigate('/dashboard/team')}>
                  Manage Team
                </Button>
              </div>
            </CardContent>
          </Card>
        </PlanGuard>

        {/* Custom Scoring Example */}
        <PlanGuard feature="CUSTOM_SCORING" featureName="Custom Scoring" compact>
          <Card className="border-purple-200 bg-purple-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <BrainCircuit className="h-5 w-5 text-purple-600" />
                <div>
                  <h4 className="font-medium">Custom Scoring Available!</h4>
                  <p className="text-sm text-gray-600">Create custom rules for candidate evaluation.</p>
                </div>
                <Button size="sm" onClick={() => navigate('/dashboard/settings')}>
                  Configure Scoring
                </Button>
              </div>
            </CardContent>
          </Card>
        </PlanGuard>
      </div>

      {/* Debug Information (only in development) */}
      {process.env.NODE_ENV === 'development' && (
        <Card className="border-gray-300 bg-gray-50">
          <CardHeader>
            <CardTitle className="text-sm">Debug Information</CardTitle>
          </CardHeader>
          <CardContent className="text-xs space-y-2">
            <div><strong>Current Plan:</strong> {currentPlan}</div>
            <div><strong>Available Features:</strong> {getAvailableFeatures().join(', ')}</div>
            <div><strong>Disabled Features:</strong> {getDisabledFeatures().join(', ')}</div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PlanFeatureShowcase;
