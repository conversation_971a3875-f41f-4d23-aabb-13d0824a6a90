import React, { useEffect, useRef } from 'react';
import { Check, Sparkles } from 'lucide-react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';

interface PricingFeature {
  text: string;
  highlighted?: boolean;
}

interface AnimatedPricingCardProps {
  title: string;
  icon: React.ReactNode;
  price: string;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
  color: string;
  delay?: number;
}

export const AnimatedPricingCard: React.FC<AnimatedPricingCardProps> = ({
  title,
  icon,
  price,
  description,
  features,
  isPopular = false,
  color,
  delay = 0
}) => {
  const cardRef = useRef<HTMLDivElement>(null);
  const shineRef = useRef<HTMLDivElement>(null);

  // Shine effect on hover
  useEffect(() => {
    if (!cardRef.current || !shineRef.current) return;
    
    const card = cardRef.current;
    const shine = shineRef.current;
    
    const handleMouseMove = (e: MouseEvent) => {
      const { left, top, width, height } = card.getBoundingClientRect();
      const x = (e.clientX - left) / width;
      const y = (e.clientY - top) / height;
      
      gsap.to(shine, {
        opacity: 0.2,
        x: x * width,
        y: y * height,
        duration: 0.3
      });
    };
    
    const handleMouseLeave = () => {
      gsap.to(shine, {
        opacity: 0,
        duration: 0.3
      });
    };
    
    card.addEventListener('mousemove', handleMouseMove);
    card.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      card.removeEventListener('mousemove', handleMouseMove);
      card.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: delay * 0.2 }}
      className={`relative bg-card-gradient rounded-xl p-6 flex flex-col h-full overflow-hidden ${
        isPopular ? 'border-2 border-' + color : 'border-2 border-transparent'
      }`}
    >
      {/* Shine effect overlay */}
      <div 
        ref={shineRef}
        className="absolute w-40 h-40 rounded-full bg-white opacity-0 blur-xl pointer-events-none"
        style={{ transform: 'translate(-50%, -50%)' }}
      />
      
      {/* Popular badge */}
      {isPopular && (
        <div className="absolute -right-12 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs font-bold py-1 px-12 transform rotate-45 shadow-lg">
          MOST POPULAR
        </div>
      )}
      
      <div className="mb-6 relative z-10">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <div className={`w-12 h-12 rounded-full flex items-center justify-center bg-${color}/10 mr-3`}>
              {icon}
            </div>
            <h3 className="text-white text-xl font-bold">{title}</h3>
          </div>
        </div>
        
        <div className="mb-3">
          <div className="flex items-baseline">
            <span className="text-white text-4xl font-bold">{price}</span>
            <span className="text-gray-400 ml-2">/month</span>
          </div>
          <p className="text-gray-300 mt-2">{description}</p>
        </div>
      </div>
      
      <div className="mb-6 flex-grow relative z-10">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <motion.li 
              key={index} 
              className="flex items-start"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
            >
              <div className={`mt-1 mr-3 flex-shrink-0 w-5 h-5 rounded-full bg-${color}/20 flex items-center justify-center`}>
                <Check size={12} className={`text-${color}`} />
              </div>
              <span className={`text-gray-300 ${feature.highlighted ? 'font-semibold' : ''}`}>
                {feature.text}
                {feature.highlighted && (
                  <Sparkles size={12} className="inline-block ml-1 text-yellow-400" />
                )}
              </span>
            </motion.li>
          ))}
        </ul>
      </div>
      
      <motion.div
        whileHover={{ scale: 1.03 }}
        whileTap={{ scale: 0.98 }}
        className="relative z-10"
      >
        <Link 
          to="/signup" 
          className={`w-full py-3 rounded-lg text-center font-medium transition-all flex items-center justify-center
            ${isPopular 
              ? `bg-gradient-to-r from-${color}-500 to-${color}-600 hover:from-${color}-600 hover:to-${color}-700 text-white shadow-lg shadow-${color}/20` 
              : 'bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white'}`}
        >
          Get Started
        </Link>
      </motion.div>
    </motion.div>
  );
};
