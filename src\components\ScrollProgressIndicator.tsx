import React, { useEffect, useRef } from 'react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

export const ScrollProgressIndicator: React.FC = () => {
  const progressRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const progressBar = progressRef.current;
    const wrapper = wrapperRef.current;
    
    if (!progressBar || !wrapper) return;
    
    gsap.to(progressBar, {
      width: '100%',
      ease: 'none',
      scrollTrigger: {
        trigger: document.documentElement,
        start: 'top top',
        end: 'bottom bottom',
        scrub: 0.3,
      }
    });
    
    // Create scroll-triggered animations for the indicator
    gsap.fromTo(wrapper, 
      { opacity: 0, y: 20 },
      { 
        opacity: 1, 
        y: 0, 
        duration: 0.5,
        scrollTrigger: {
          trigger: document.documentElement,
          start: 'top top+=100',
          toggleActions: 'play none none reverse'
        }
      }
    );
    
    return () => {
      ScrollTrigger.getAll().forEach(trigger => trigger.kill());
    };
  }, []);

  return (
    <div 
      ref={wrapperRef}
      className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 w-[200px] h-2 bg-gray-800/50 rounded-full overflow-hidden backdrop-blur-sm"
    >
      <div 
        ref={progressRef}
        className="h-full w-0 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-full"
      />
    </div>
  );
};
