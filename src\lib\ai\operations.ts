/**
 * Unified AI Operations Service
 * 
 * This service provides a centralized interface for all AI operations.
 * It uses the centralized prompts and schemas to ensure consistent behavior
 * regardless of which AI model is selected in the admin settings.
 */

import OpenAI from 'openai';
import { Type } from '@google/genai';
import { getAIConfig, getProviderBaseURL } from '@/lib/aiConfig';
import { withRetry, withGeminiRetry, getUserFriendlyErrorMessage } from '@/lib/apiRetry';
import { AI_PROMPTS, getPrompt } from './prompts';
import { AI_SCHEMAS } from './schemas';

// ============================================================================
// TYPES
// ============================================================================

export interface CVParsingResult {
  personalDetails: {
    name: string;
    email: string;
    phone?: string;
    location?: string;
    linkedIn?: string;
    website?: string;
  };
  professionalSummary?: string;
  workExperience: Array<{
    company: string;
    title: string;
    startDate: string;
    endDate?: string;
    current?: boolean;
    responsibilities: string[];
    achievements?: string[];
  }>;
  education: Array<{
    institution: string;
    degree: string;
    field?: string;
    startDate: string;
    endDate?: string;
    current?: boolean;
    gpa?: string;
    achievements?: string[];
  }>;
  skills: {
    technical: string[];
    soft?: string[];
    languages?: string[];
  };
  languages: Array<{
    name: string;
    proficiency: string;
  }>;
  certifications: Array<{
    name: string;
    issuer: string;
    date?: string;
    expiryDate?: string;
  }>;
  projects: Array<{
    name: string;
    description: string;
    technologies?: string[];
    url?: string;
  }>;
}

export interface JobMatchingResult {
  overallScore: number;
  skillsMatch: {
    score: number;
    analysis: string;
    skills?: Array<{
      name: string;
      match: number;
      required: boolean;
    }>;
  };
  experienceMatch: {
    score: number;
    analysis: string;
    details?: Array<{
      title: string;
      company: string;
      duration: string;
      relevance: number;
    }>;
  };
  educationMatch: {
    score: number;
    analysis: string;
    details?: Array<{
      degree: string;
      institution: string;
      year: string;
      relevance: number;
    }>;
  };
  locationMatch: {
    score: number;
    analysis: string;
  };
  strengths: string[];
  gaps: string[];
  recommendation: string;
}

export interface SkillExtractionResult {
  technical: string[];
  domain: string[];
  soft: string[];
}

export interface CandidateRankingResult {
  rankings: Array<{
    candidateId: string;
    name: string;
    overallScore: number;
    skillsScore: number;
    experienceScore: number;
    educationScore: number;
    culturalFitScore: number;
    strengths: string[];
    areasForImprovement: string[];
    recommendation: 'Hire' | 'Consider' | 'Reject';
  }>;
}

// ============================================================================
// CORE AI CLIENT
// ============================================================================

/**
 * Convert our JSON schema to Gemini schema format
 */
function convertToGeminiSchema(jsonSchema: any): any {
  const convertType = (type: string) => {
    switch (type) {
      case 'string': return Type.STRING;
      case 'number': return Type.NUMBER;
      case 'integer': return Type.INTEGER;
      case 'boolean': return Type.BOOLEAN;
      case 'array': return Type.ARRAY;
      case 'object': return Type.OBJECT;
      default: return Type.STRING;
    }
  };

  const convertSchema = (schema: any): any => {
    const result: any = {
      type: convertType(schema.type)
    };

    if (schema.required) {
      result.required = schema.required;
    }

    if (schema.properties) {
      result.properties = {};
      for (const [key, value] of Object.entries(schema.properties)) {
        result.properties[key] = convertSchema(value as any);
      }
    }

    if (schema.items) {
      result.items = convertSchema(schema.items);
    }

    if (schema.minimum !== undefined) {
      result.minimum = schema.minimum;
    }

    if (schema.maximum !== undefined) {
      result.maximum = schema.maximum;
    }

    if (schema.enum) {
      result.enum = schema.enum;
    }

    return result;
  };

  return convertSchema(jsonSchema);
}

/**
 * Create AI client based on current configuration
 */
async function createAIClient(): Promise<{ client: OpenAI | null; model: string; provider: string }> {
  const config = await getAIConfig();

  if (config.provider === 'gemini') {
    // For Gemini, we use direct fetch API, so no client needed
    return { client: null, model: config.model, provider: 'gemini' };
  } else {
    const client = new OpenAI({
      apiKey: config.apiKey,
      baseURL: getProviderBaseURL(config.provider),
      dangerouslyAllowBrowser: true,
    });
    return { client, model: config.model, provider: config.provider };
  }
}

/**
 * Make AI API call with provider-specific retry logic
 */
async function makeAICall(prompt: string, userMessage: string, schema?: any): Promise<string> {
  const { client, model, provider } = await createAIClient();

  if (provider === 'gemini') {
    // For Gemini, we'll use direct fetch API with enhanced retry logic
    const { apiKey } = await getAIConfig();

    // Debug logging
    console.log('Gemini API Key length:', apiKey?.length);
    console.log('Gemini API Key starts with:', apiKey?.substring(0, 10));
    console.log('Gemini API Key ends with:', apiKey?.substring(-10));

    if (!apiKey || apiKey === 'PLACEHOLDER_API_KEY' || apiKey.includes('••••')) {
      throw new Error('Invalid or missing Gemini API key. Please configure it in admin settings.');
    }

    const requestBody: any = {
      contents: [
        {
          parts: [
            {
              text: `${prompt}\n\n${userMessage}`
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.1,
        topP: 1,
        maxOutputTokens: 32768,
        responseMimeType: 'application/json'
      }
    };

    // Add schema if provided
    if (schema) {
      requestBody.generationConfig.responseSchema = convertToGeminiSchema(schema);
    }

    // Use Gemini-specific retry logic
    const response = await withGeminiRetry(async () => {
      console.log(`Making Gemini API call to model: ${model}`);
      
      const res = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${encodeURIComponent(apiKey)}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      });

      if (!res.ok) {
        const errorData = await res.json().catch(() => ({}));
        const errorMessage = errorData.error?.message || `HTTP ${res.status}: ${res.statusText}`;
        
        // Create a structured error for better retry logic
        const error = new Error(`Gemini API error: ${errorMessage}`);
        (error as any).status = res.status;
        (error as any).code = res.status;
        (error as any).type = res.status === 429 ? 'rate_limit_exceeded' : 
                              res.status >= 500 ? 'server_error' : 'client_error';
        
        throw error;
      }

      return await res.json();
    });

    const content = response.candidates?.[0]?.content?.parts?.[0]?.text;
    if (!content) {
      throw new Error('Empty response from Gemini API');
    }

    return content;
  } else {
    // OpenAI-compatible providers (GROQ, OpenAI, Anthropic) - use standard retry
    const openaiClient = client as OpenAI;

    const response = await withRetry(async () => {
      return await openaiClient.chat.completions.create({
        model: model,
        max_completion_tokens: 32768,
        temperature: 0.1,
        top_p: 1,
        stream: false,
        response_format: {
          type: "json_object"
        },
        stop: null,
        messages: [
          {
            role: 'system',
            content: prompt
          },
          {
            role: 'user',
            content: userMessage
          }
        ],
      });
    });

    const content = response.choices[0].message.content;
    if (!content) {
      throw new Error('Empty response from AI API');
    }

    return content;
  }
}

// ============================================================================
// AI OPERATIONS
// ============================================================================

/**
 * Parse CV text and extract structured information
 */
export async function parseCV(cvText: string): Promise<CVParsingResult> {
  try {
    const prompt = getPrompt('CV_PARSING');
    const userMessage = `Parse the following CV and extract structured information as JSON:\n\n${cvText}`;

    const content = await makeAICall(prompt, userMessage, AI_SCHEMAS.CV_PARSING);
    const parsedData = JSON.parse(content);

    return parsedData as CVParsingResult;
  } catch (error) {
    console.error('Error parsing CV:', error);
    throw new Error(getUserFriendlyErrorMessage(error));
  }
}

/**
 * Match candidate profile to job description
 */
export async function matchCandidateToJob(
  candidateProfile: any,
  jobDescription: string,
  jobDetails?: any,
  coverLetterContent?: string
): Promise<JobMatchingResult> {
  try {
    const prompt = getPrompt('JOB_MATCHING');
    const candidateProfileStr = JSON.stringify(candidateProfile);
    
    // Create comprehensive job information string
    let jobInfo = `Job Description:\n${jobDescription}`;
    
    if (jobDetails) {
      jobInfo = `Job Title: ${jobDetails.title || 'Not specified'}
Department: ${jobDetails.department || 'Not specified'}
Location: ${jobDetails.location || 'Not specified'}
Employment Type: ${jobDetails.employment_type || 'Not specified'}
Experience Level: ${jobDetails.experience_level || 'Not specified'}
Salary Range: ${jobDetails.salary_range || 'Not specified'}
Application Deadline: ${jobDetails.application_deadline || 'Not specified'}

Requirements:
${jobDetails.requirements || 'Not specified'}

Responsibilities:
${jobDetails.responsibilities || jobDescription}

Benefits:
${jobDetails.benefits || 'Not specified'}

Additional Job Description:
${jobDescription}`;
    }
    
    const userMessage = `Match the following candidate profile to the job information as JSON:\n\nCandidate Profile:\n${candidateProfileStr}\n\n${jobInfo}${coverLetterContent ? `\n\nCover Letter:\n${coverLetterContent}` : ''}`;

    const content = await makeAICall(prompt, userMessage, AI_SCHEMAS.JOB_MATCHING);
    const matchResult = JSON.parse(content);
    
    return matchResult;
  } catch (error) {
    console.error('Error in matchCandidateToJob:', error);
    throw error;
  }
}

/**
 * Extract skills from text
 */
export async function extractSkills(text: string): Promise<SkillExtractionResult> {
  try {
    const prompt = getPrompt('SKILL_EXTRACTION');
    const userMessage = `Extract skills from the following text as JSON:\n\n${text}`;

    const content = await makeAICall(prompt, userMessage, AI_SCHEMAS.SKILL_EXTRACTION);
    const skillsData = JSON.parse(content);

    return skillsData as SkillExtractionResult;
  } catch (error) {
    console.error('Error extracting skills:', error);
    throw new Error(getUserFriendlyErrorMessage(error));
  }
}

/**
 * Score and rank candidates for a job
 */
export async function rankCandidatesForJob(
  candidates: any[],
  jobDescription: string
): Promise<CandidateRankingResult> {
  try {
    const prompt = getPrompt('CANDIDATE_RANKING');
    const candidatesStr = JSON.stringify(candidates);
    const userMessage = `Score and rank the following candidates for this job description as JSON:\n\nJob Description:\n${jobDescription}\n\nCandidates:\n${candidatesStr}`;

    const content = await makeAICall(prompt, userMessage, AI_SCHEMAS.CANDIDATE_RANKING);
    const rankingResult = JSON.parse(content);

    return rankingResult as CandidateRankingResult;
  } catch (error) {
    console.error('Error ranking candidates:', error);
    throw new Error(getUserFriendlyErrorMessage(error));
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export {
  AI_PROMPTS,
  AI_SCHEMAS,
  getPrompt
};


