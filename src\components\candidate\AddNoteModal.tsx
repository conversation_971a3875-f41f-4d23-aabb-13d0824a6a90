import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { updateCandidate } from '@/services/supabase/candidates';
import { supabase } from '@/lib/supabase';

interface AddNoteModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidateId: string;
  candidateName: string;
  onSuccess?: () => void;
}

const AddNoteModal: React.FC<AddNoteModalProps> = ({
  isOpen,
  onClose,
  candidateId,
  candidateName,
  onSuccess
}) => {
  const [note, setNote] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async () => {
    if (!note.trim()) {
      toast({
        title: 'Note required',
        description: 'Please enter a note before submitting.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Get user's name from localStorage or use default
      let currentUser = 'Recruiter';
      try {
        const userDataStr = localStorage.getItem('userData');
        if (userDataStr) {
          const userData = JSON.parse(userDataStr);
          currentUser = userData.full_name || userData.email || 'Recruiter';
        }
      } catch (e) {
        console.error('Error getting user data from localStorage:', e);
      }

      // Get existing candidate data
      const { data: candidateData } = await supabase
        .from('candidates')
        .select('notes')
        .eq('id', candidateId)
        .single();

      // Parse existing notes or create empty array
      let existingNotes = [];
      if (candidateData?.notes) {
        try {
          existingNotes = JSON.parse(candidateData.notes);
          if (!Array.isArray(existingNotes)) {
            existingNotes = [];
          }
        } catch (e) {
          console.error('Error parsing notes:', e);
        }
      }

      // Create a new note
      const newNote = {
        id: `note_${Date.now()}`,
        date: new Date().toISOString(),
        author: currentUser,
        authorAvatar: `https://i.pravatar.cc/150?u=${candidateId}_${existingNotes.length + 1}`,
        content: note,
        status: 'note'
      };

      // Add the note to the candidate's notes array
      const updatedNotes = [newNote, ...existingNotes];

      // Store the notes as a JSON string in the notes field
      const notesJson = JSON.stringify(updatedNotes);

      // Update the candidate in the database
      await updateCandidate(candidateId, {
        notes: notesJson
      });

      toast({
        title: 'Note added',
        description: 'Your note has been added successfully.',
      });

      // Reset form and close modal
      setNote('');
      if (onSuccess) {
        onSuccess();
      }
      onClose();
    } catch (error) {
      console.error('Error adding note:', error);
      toast({
        title: 'Error',
        description: 'Failed to add note. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Note</DialogTitle>
          <DialogDescription>
            Add a note for {candidateName}. This will be visible to all team members.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <Textarea
            value={note}
            onChange={(e) => setNote(e.target.value)}
            placeholder="Enter your note here..."
            className="bg-white border-gray-200 text-gray-800"
            rows={5}
          />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isSubmitting}>
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Adding...
              </>
            ) : (
              'Add Note'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddNoteModal;
