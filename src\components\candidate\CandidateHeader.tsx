import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ArrowLeft, Mail, Phone, MapPin, MessageSquare, Users } from 'lucide-react';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface CandidateHeaderProps {
  candidate: any;
  status: string;
  handleStatusChange: (newStatus: string) => void;
  getStatusColor: (status: string) => string;
}

const CandidateHeader: React.FC<CandidateHeaderProps> = ({
  candidate,
  status,
  handleStatusChange,
  getStatusColor
}) => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="icon"
          className="border-gray-200 text-gray-700 hover:bg-gray-100"
          onClick={() => navigate('/dashboard/cvs')}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <Avatar className="h-16 w-16">
          <AvatarImage src="https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png" alt={candidate.name} />
          <AvatarFallback>{candidate.name ? candidate.name.charAt(0) : 'C'}</AvatarFallback>
        </Avatar>
        <div>
          <div className="flex items-center gap-2">
            <h1 className="text-2xl font-bold text-gray-800">{candidate.name}</h1>
            <Badge className={getStatusColor(status)}>
              {status.charAt(0).toUpperCase() + status.slice(1) || 'New'}
            </Badge>
          </div>
          <p className="text-gray-500">{candidate.position || 'Candidate'}</p>
          <div className="flex flex-wrap gap-3 mt-2">
            {candidate.email && (
              <div className="flex items-center text-gray-600 text-sm">
                <Mail className="mr-1 h-4 w-4 text-gray-500" />
                <a href={`mailto:${candidate.email}`} className="hover:text-blue-500">
                  {candidate.email}
                </a>
              </div>
            )}
            {candidate.phone && (
              <div className="flex items-center text-gray-600 text-sm">
                <Phone className="mr-1 h-4 w-4 text-gray-500" />
                <a href={`tel:${candidate.phone}`} className="hover:text-blue-500">
                  {candidate.phone}
                </a>
              </div>
            )}
            {candidate.source && (
              <div className="flex items-center text-gray-600 text-sm">
                <Users className="mr-1 h-4 w-4 text-gray-500" />
                <span>{candidate.source}</span>
              </div>
            )}
            {candidate.location && (
              <div className="flex items-center text-gray-600 text-sm">
                <MapPin className="mr-1 h-4 w-4 text-gray-500" />
                <span>{candidate.location}</span>
              </div>
            )}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-3">
        <div className="flex items-center">
          <span className="text-gray-500 mr-2">Status:</span>
          <Select value={status} onValueChange={handleStatusChange}>
            <SelectTrigger className="w-[180px] bg-white border-gray-200 text-gray-800">
              <SelectValue>
                <div className="flex items-center">
                  <Badge className={`mr-2 ${getStatusColor(status)}`}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </Badge>
                </div>
              </SelectValue>
            </SelectTrigger>
            <SelectContent className="bg-white border-gray-200 text-gray-800">
              <SelectItem value="new">
                <Badge className={getStatusColor('new')}>New</Badge>
              </SelectItem>
              <SelectItem value="screening">
                <Badge className={getStatusColor('screening')}>Screening</Badge>
              </SelectItem>
              <SelectItem value="interview">
                <Badge className={getStatusColor('interview')}>Interview</Badge>
              </SelectItem>
              <SelectItem value="offer">
                <Badge className={getStatusColor('offer')}>Offer</Badge>
              </SelectItem>
              <SelectItem value="hired">
                <Badge className={getStatusColor('hired')}>Hired</Badge>
              </SelectItem>
              <SelectItem value="rejected">
                <Badge className={getStatusColor('rejected')}>Rejected</Badge>
              </SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button
          variant="outline"
          className="border-gray-200 text-gray-700 hover:bg-gray-100"
        >
          <MessageSquare className="mr-2 h-4 w-4" /> Contact
        </Button>
      </div>
    </div>
  );
};

export default CandidateHeader;

