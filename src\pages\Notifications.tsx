import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  Bell,
  Check,
  Trash2,
  Info,
  AlertCircle,
  CheckCircle,
  X,
  Settings,
  MarkAsRead,
  Archive
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Link } from 'react-router-dom';
import { format } from 'date-fns';
import {
  useNotifications,
  useMarkNotificationAsRead,
  useMarkAllNotificationsAsRead,
  useDeleteNotification
} from '@/hooks/use-notifications';

const Notifications = () => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('all');
  
  // Use real database hooks
  const { data: notifications = [], isLoading } = useNotifications();
  const markAsReadMutation = useMarkNotificationAsRead();
  const markAllAsReadMutation = useMarkAllNotificationsAsRead();
  const deleteNotificationMutation = useDeleteNotification();

  // Get notification icon based on type
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <X className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  // Handle notification click
  const handleNotificationClick = async (notification: any) => {
    if (!notification.read) {
      try {
        await markAsReadMutation.mutateAsync(notification.id);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }
  };

  // Handle mark as read
  const handleMarkAsRead = async (id: string) => {
    try {
      await markAsReadMutation.mutateAsync(id);
      toast({
        title: 'Notification marked as read',
        description: 'The notification has been marked as read.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to mark notification as read.',
        variant: 'destructive',
      });
    }
  };

  // Handle mark all as read
  const handleMarkAllAsRead = async () => {
    try {
      await markAllAsReadMutation.mutateAsync();
      toast({
        title: 'All notifications marked as read',
        description: 'All notifications have been marked as read.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to mark all notifications as read.',
        variant: 'destructive',
      });
    }
  };

  // Handle delete notification
  const handleDeleteNotification = async (id: string) => {
    try {
      await deleteNotificationMutation.mutateAsync(id);
      toast({
        title: 'Notification deleted',
        description: 'The notification has been deleted.',
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to delete notification.',
        variant: 'destructive',
      });
    }
  };

  // Filter notifications
  const unreadNotifications = notifications.filter(notification => !notification.read);
  const readNotifications = notifications.filter(notification => notification.read);

  const renderNotification = (notification: any) => (
    <div
      key={notification.id}
      className={`p-4 border-b border-gray-200 hover:bg-gray-50 transition-colors ${!notification.read ? 'bg-blue-50' : ''}`}
    >
      <div className="flex gap-3">
        <div className="flex-shrink-0 mt-1">
          {getNotificationIcon(notification.type)}
        </div>
        <div className="flex-1 min-w-0">
          {notification.link ? (
            <Link
              to={notification.link}
              className="block"
              onClick={() => handleNotificationClick(notification)}
            >
              <h4 className="font-medium text-gray-800 hover:text-blue-600">
                {notification.title}
              </h4>
              <p className="text-gray-600 text-sm mt-1">{notification.message}</p>
              <p className="text-gray-400 text-xs mt-2">
                {format(new Date(notification.created_at), 'MMM d, yyyy h:mm a')}
              </p>
            </Link>
          ) : (
            <div>
              <h4 className="font-medium text-gray-800">{notification.title}</h4>
              <p className="text-gray-600 text-sm mt-1">{notification.message}</p>
              <p className="text-gray-400 text-xs mt-2">
                {format(new Date(notification.created_at), 'MMM d, yyyy h:mm a')}
              </p>
            </div>
          )}
        </div>
        <div className="flex-shrink-0 flex gap-2">
          {!notification.read && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleMarkAsRead(notification.id)}
              className="text-blue-600 hover:text-blue-800"
            >
              <Check className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteNotification(notification.id)}
            className="text-red-600 hover:text-red-800"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-500">Loading notifications...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Notifications</h1>
            <p className="text-gray-500">Stay updated with your latest activities</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleMarkAllAsRead}
              disabled={unreadNotifications.length === 0}
            >
              <Check className="h-4 w-4 mr-2" />
              Mark All Read
            </Button>
            <Button variant="outline" asChild>
              <Link to="/dashboard/notifications/preferences">
                <Settings className="h-4 w-4 mr-2" />
                Preferences
              </Link>
            </Button>
          </div>
        </div>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Your Notifications
              {unreadNotifications.length > 0 && (
                <Badge className="bg-blue-600">
                  {unreadNotifications.length} unread
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="w-full justify-start border-b rounded-none">
                <TabsTrigger value="all">
                  All ({notifications.length})
                </TabsTrigger>
                <TabsTrigger value="unread">
                  Unread ({unreadNotifications.length})
                </TabsTrigger>
                <TabsTrigger value="read">
                  Read ({readNotifications.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="all" className="mt-0">
                {notifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <Bell className="h-12 w-12 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-800 mb-2">No notifications</h3>
                    <p className="text-gray-500">You're all caught up! Check back later for updates.</p>
                  </div>
                ) : (
                  <div className="max-h-[600px] overflow-y-auto">
                    {notifications.map(renderNotification)}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="unread" className="mt-0">
                {unreadNotifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <CheckCircle className="h-12 w-12 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-800 mb-2">No unread notifications</h3>
                    <p className="text-gray-500">You're all caught up!</p>
                  </div>
                ) : (
                  <div className="max-h-[600px] overflow-y-auto">
                    {unreadNotifications.map(renderNotification)}
                  </div>
                )}
              </TabsContent>

              <TabsContent value="read" className="mt-0">
                {readNotifications.length === 0 ? (
                  <div className="flex flex-col items-center justify-center py-12">
                    <Archive className="h-12 w-12 text-gray-300 mb-4" />
                    <h3 className="text-lg font-medium text-gray-800 mb-2">No read notifications</h3>
                    <p className="text-gray-500">Read notifications will appear here.</p>
                  </div>
                ) : (
                  <div className="max-h-[600px] overflow-y-auto">
                    {readNotifications.map(renderNotification)}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Notifications;
