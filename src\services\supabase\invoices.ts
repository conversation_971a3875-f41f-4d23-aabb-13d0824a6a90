import { supabase } from '@/lib/supabase';
import * as billingService from './billing';

/**
 * Invoice status type
 */
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'void' | 'overdue';

/**
 * Invoice item interface
 */
export interface InvoiceItem {
  description: string;
  quantity: number;
  unit_price: number;
  amount: number;
  tax_rate?: number;
  tax_amount?: number;
}

/**
 * Invoice interface
 */
export interface Invoice {
  id: string;
  user_id: string;
  invoice_number: string;
  amount: number;
  currency: string;
  status: InvoiceStatus;
  issue_date: string;
  due_date: string;
  payment_date: string | null;
  subscription_id: string | null;
  payment_id: string | null;
  invoice_items: InvoiceItem[];
  billing_info: any;
  created_at: string;
  updated_at: string;
}

/**
 * Get invoices for a user
 */
export const getUserInvoices = async (userId: string): Promise<Invoice[]> => {
  try {
    const { data, error } = await supabase
      .from('invoices')
      .select('*')
      .eq('user_id', userId)
      .order('issue_date', { ascending: false });

    if (error) {
      console.error('Error fetching invoices:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getUserInvoices:', error);
    return [];
  }
};

/**
 * Get a specific invoice by ID
 */
export const getInvoiceById = async (
  invoiceId: string,
  userId: string
): Promise<Invoice | null> => {
  try {
    const { data, error } = await supabase
      .from('invoices')
      .select('*')
      .eq('id', invoiceId)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching invoice:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getInvoiceById:', error);
    return null;
  }
};

/**
 * Get a specific invoice by invoice number
 */
export const getInvoiceByNumber = async (
  invoiceNumber: string,
  userId: string
): Promise<Invoice | null> => {
  try {
    const { data, error } = await supabase
      .from('invoices')
      .select('*')
      .eq('invoice_number', invoiceNumber)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching invoice:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getInvoiceByNumber:', error);
    return null;
  }
};

/**
 * Generate a new invoice number
 */
export const generateInvoiceNumber = async (): Promise<string> => {
  try {
    // Get the current year
    const year = new Date().getFullYear();
    
    // Get the count of invoices for the current year
    const { count, error } = await supabase
      .from('invoices')
      .select('id', { count: 'exact', head: true })
      .like('invoice_number', `INV-${year}-%`);

    if (error) {
      console.error('Error counting invoices:', error);
      throw error;
    }

    // Generate the invoice number
    const invoiceCount = (count || 0) + 1;
    const invoiceNumber = `INV-${year}-${invoiceCount.toString().padStart(5, '0')}`;

    return invoiceNumber;
  } catch (error) {
    console.error('Error in generateInvoiceNumber:', error);
    // Fallback to a timestamp-based invoice number
    const timestamp = Date.now();
    return `INV-${timestamp}`;
  }
};

/**
 * Create a new invoice
 */
export const createInvoice = async (
  userId: string,
  invoiceData: {
    amount: number;
    currency: string;
    status: InvoiceStatus;
    issue_date: string;
    due_date: string;
    payment_date?: string;
    subscription_id?: string;
    payment_id?: string;
    invoice_items: InvoiceItem[];
  }
): Promise<Invoice | null> => {
  try {
    // Get the user's billing info
    const billingInfo = await billingService.getBillingInfo(userId);
    
    if (!billingInfo) {
      throw new Error('Billing information is required to create an invoice');
    }

    // Generate a new invoice number
    const invoiceNumber = await generateInvoiceNumber();

    // Create the invoice
    const { data, error } = await supabase
      .from('invoices')
      .insert({
        user_id: userId,
        invoice_number: invoiceNumber,
        amount: invoiceData.amount,
        currency: invoiceData.currency,
        status: invoiceData.status,
        issue_date: invoiceData.issue_date,
        due_date: invoiceData.due_date,
        payment_date: invoiceData.payment_date || null,
        subscription_id: invoiceData.subscription_id || null,
        payment_id: invoiceData.payment_id || null,
        invoice_items: invoiceData.invoice_items,
        billing_info: billingInfo,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating invoice:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in createInvoice:', error);
    throw error;
  }
};

/**
 * Update invoice status
 */
export const updateInvoiceStatus = async (
  invoiceId: string,
  status: InvoiceStatus,
  userId: string,
  paymentDate?: string
): Promise<boolean> => {
  try {
    const updateData: any = { status };
    
    // If the status is 'paid', update the payment date
    if (status === 'paid' && paymentDate) {
      updateData.payment_date = paymentDate;
    }

    const { error } = await supabase
      .from('invoices')
      .update(updateData)
      .eq('id', invoiceId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating invoice status:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in updateInvoiceStatus:', error);
    throw error;
  }
};

/**
 * Generate PDF invoice data
 */
export const generateInvoicePdfData = async (
  invoiceId: string,
  userId: string
): Promise<any> => {
  try {
    // Get the invoice
    const invoice = await getInvoiceById(invoiceId, userId);
    
    if (!invoice) {
      throw new Error('Invoice not found');
    }

    // Get the user's profile
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('full_name, email, company_id')
      .eq('user_id', userId)
      .single();

    if (profileError) {
      console.error('Error fetching profile:', profileError);
      throw profileError;
    }

    // Get the company information if available
    let company = null;
    if (profile.company_id) {
      const { data: companyData, error: companyError } = await supabase
        .from('companies')
        .select('*')
        .eq('id', profile.company_id)
        .single();

      if (!companyError) {
        company = companyData;
      }
    }

    // Prepare the invoice data for PDF generation
    return {
      invoice,
      profile,
      company,
    };
  } catch (error) {
    console.error('Error in generateInvoicePdfData:', error);
    throw error;
  }
};
