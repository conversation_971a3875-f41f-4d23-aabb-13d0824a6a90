import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import { corsHeaders } from '../_shared/cors.ts'

// Helper function to get PayPal access token
async function getPayPalAccessToken() {
  try {
    const clientId = Deno.env.get('PAYPAL_CLIENT_ID')
    const clientSecret = Deno.env.get('PAYPAL_CLIENT_SECRET')
    
    if (!clientId || !clientSecret) {
      throw new Error('PayPal credentials are not configured')
    }
    
    const auth = btoa(`${clientId}:${clientSecret}`)
    const paypalEnvironment = Deno.env.get('PAYPAL_ENVIRONMENT') || 'sandbox'
    const url = paypalEnvironment === 'production'
      ? 'https://api.paypal.com/v1/oauth2/token'
      : 'https://api.sandbox.paypal.com/v1/oauth2/token'
    
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: 'grant_type=client_credentials'
    })
    
    const data = await response.json()
    return data.access_token
  } catch (error) {
    console.error('Error getting PayPal access token:', error)
    throw new Error('Failed to authenticate with PayPal')
  }
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
  
  // Create a Supabase client with the service role key
  const supabaseAdmin = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )
  
  // Get the URL path
  const url = new URL(req.url)
  const path = url.pathname.split('/').filter(Boolean)
  const endpoint = path[path.length - 1]
  
  try {
    // Get PayPal access token
    const accessToken = await getPayPalAccessToken()
    const paypalEnvironment = Deno.env.get('PAYPAL_ENVIRONMENT') || 'sandbox'
    const baseUrl = paypalEnvironment === 'production'
      ? 'https://api.paypal.com/v1'
      : 'https://api.sandbox.paypal.com/v1'
    
    // Handle different endpoints
    if (req.method === 'GET') {
      // Get all plans
      if (endpoint === 'plans') {
        const response = await fetch(`${baseUrl}/billing/plans`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        })

        const data = await response.json()

        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      // Get a specific plan
      if (endpoint.startsWith('plan-')) {
        const planId = endpoint.replace('plan-', '')

        const response = await fetch(`${baseUrl}/billing/plans/${planId}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        })

        if (!response.ok) {
          const errorData = await response.json()
          return new Response(JSON.stringify({
            status: 'error',
            message: 'Failed to fetch plan details',
            error: errorData
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: response.status,
          })
        }

        const data = await response.json()

        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      
      // Get all products
      if (endpoint === 'products') {
        const response = await fetch(`${baseUrl}/catalogs/products`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        })
        
        const data = await response.json()
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
    }
    
    if (req.method === 'POST') {
      const body = await req.json()
      
      // Create a new plan
      if (endpoint === 'plans') {
        const response = await fetch(`${baseUrl}/billing/plans`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        })
        
        const data = await response.json()
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 201,
        })
      }
      
      // Create a new product
      if (endpoint === 'products') {
        const response = await fetch(`${baseUrl}/catalogs/products`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        })
        
        const data = await response.json()
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 201,
        })
      }
      
      // Activate a plan
      if (endpoint.startsWith('activate-')) {
        const planId = endpoint.replace('activate-', '')
        
        const response = await fetch(`${baseUrl}/billing/plans/${planId}/activate`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        })
        
        return new Response(JSON.stringify({ status: 'success', message: 'Plan activated successfully' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      
      // Deactivate a plan
      if (endpoint.startsWith('deactivate-')) {
        const planId = endpoint.replace('deactivate-', '')

        const response = await fetch(`${baseUrl}/billing/plans/${planId}/deactivate`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          }
        })

        return new Response(JSON.stringify({ status: 'success', message: 'Plan deactivated successfully' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }

      // Update pricing schemes for a plan
      if (endpoint.startsWith('update-pricing-')) {
        const planId = endpoint.replace('update-pricing-', '')

        console.log(`Updating pricing for plan ${planId} with data:`, JSON.stringify(body))

        const response = await fetch(`${baseUrl}/billing/plans/${planId}/update-pricing-schemes`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        })

        // Check if the request was successful
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
          console.error(`Error updating pricing for plan ${planId}:`, JSON.stringify(errorData))

          return new Response(JSON.stringify({
            status: 'error',
            message: 'Failed to update plan pricing',
            error: errorData
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: response.status,
          })
        }

        // Return success response
        return new Response(JSON.stringify({
          status: 'success',
          message: 'Plan pricing updated successfully',
          planId
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
    }
    
    if (req.method === 'PATCH') {
      const body = await req.json()

      // Update a plan
      if (endpoint.startsWith('plan-')) {
        const planId = endpoint.replace('plan-', '')

        console.log(`Updating plan ${planId} with data:`, JSON.stringify(body))

        const response = await fetch(`${baseUrl}/billing/plans/${planId}`, {
          method: 'PATCH',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(body)
        })

        // Check if the request was successful
        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: 'Unknown error' }))
          console.error(`Error updating plan ${planId}:`, JSON.stringify(errorData))

          return new Response(JSON.stringify({
            status: 'error',
            message: 'Failed to update plan',
            error: errorData
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
            status: response.status,
          })
        }

        // If response is 204 No Content (common for PATCH operations), or other success code
        return new Response(JSON.stringify({
          status: 'success',
          message: 'Plan updated successfully',
          planId
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
    }
    // If no endpoint matched
    return new Response(JSON.stringify({ error: 'Endpoint not found' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 404,
    })
  } catch (error) {
    console.error('Error processing request:', error)
    
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})
