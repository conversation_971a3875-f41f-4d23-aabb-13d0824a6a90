import { supabase } from '@/lib/supabase';
import {
  AISettings,
  AISettingsInsert,
  AISettingsUpdate,
  AISettingsResponse,
  AISettingsListResponse,
  AIProvider
} from '@/types/aiSettings';
import { clearAIConfigCache } from '@/lib/aiConfig';

/**
 * Simple encryption/decryption for API keys
 * Note: In production, use a more robust encryption method
 */
const ENCRYPTION_KEY = 'ai-settings-key-2025'; // In production, use environment variable

function encryptApiKey(apiKey: string): string {
  // Simple base64 encoding with key prefix (not secure for production)
  return btoa(`${ENCRYPTION_KEY}:${apiKey}`);
}

function decryptApiKey(encryptedKey: string): string {
  try {
    const decoded = atob(encryptedKey);
    const [key, apiKey] = decoded.split(':');
    if (key === ENCRYPTION_KEY) {
      return apiKey;
    }
    return encryptedKey; // Return as-is if decryption fails
  } catch {
    return encryptedKey; // Return as-is if decryption fails
  }
}

/**
 * Get all AI settings
 */
export async function getAISettings(): Promise<AISettingsListResponse> {
  try {
    const { data, error } = await supabase
      .from('ai_settings')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching AI settings:', error);
      return { data: null, error: error.message };
    }

    // Decrypt API keys for display (but mask them)
    const decryptedData = data?.map(setting => ({
      ...setting,
      api_key: setting.api_key === 'PLACEHOLDER_API_KEY' ? '' : '••••••••••••••••••••••'
    })) || [];

    return { data: decryptedData, error: null };
  } catch (error) {
    console.error('Error in getAISettings:', error);
    return { data: null, error: 'Failed to fetch AI settings' };
  }
}

/**
 * Get all active AI settings (one per provider)
 */
export async function getAllActiveAISettings(): Promise<AISettingsListResponse> {
  try {
    const { data, error } = await supabase
      .from('ai_settings')
      .select('*')
      .eq('is_active', true)
      .order('updated_at', { ascending: false });

    if (error) {
      console.error('Error fetching all active AI settings:', error);
      return { data: null, error: error.message };
    }

    // Decrypt API keys
    const decryptedData = data?.map(setting => ({
      ...setting,
      api_key: decryptApiKey(setting.api_key)
    })) || [];

    return { data: decryptedData, error: null };
  } catch (error) {
    console.error('Error in getAllActiveAISettings:', error);
    return { data: null, error: 'Failed to fetch all active AI settings' };
  }
}

/**
 * Get active AI settings for a specific provider
 */
export async function getActiveAISettings(provider: AIProvider): Promise<AISettingsResponse> {
  try {
    const { data, error } = await supabase
      .from('ai_settings')
      .select('*')
      .eq('provider', provider)
      .eq('is_active', true)
      .single();

    if (error) {
      console.error('Error fetching active AI settings:', error);
      return { data: null, error: error.message };
    }

    // Decrypt API key
    const decryptedData = data ? {
      ...data,
      api_key: decryptApiKey(data.api_key)
    } : null;

    return { data: decryptedData, error: null };
  } catch (error) {
    console.error('Error in getActiveAISettings:', error);
    return { data: null, error: 'Failed to fetch active AI settings' };
  }
}

/**
 * Create or update AI settings
 */
export async function upsertAISettings(
  provider: AIProvider,
  settings: Omit<AISettingsInsert, 'provider'>
): Promise<AISettingsResponse> {
  try {
    // First, deactivate ALL existing active settings (only one provider should be active at a time)
    await supabase
      .from('ai_settings')
      .update({ is_active: false })
      .eq('is_active', true);

    // Encrypt the API key
    const encryptedApiKey = encryptApiKey(settings.api_key);

    // Insert new settings
    const { data, error } = await supabase
      .from('ai_settings')
      .insert({
        provider,
        model: settings.model,
        api_key: encryptedApiKey,
        is_active: true,
        metadata: settings.metadata || {}
      })
      .select()
      .single();

    if (error) {
      console.error('Error upserting AI settings:', error);
      return { data: null, error: error.message };
    }

    // Clear the AI config cache so the new settings are used immediately
    clearAIConfigCache();

    return { data, error: null };
  } catch (error) {
    console.error('Error in upsertAISettings:', error);
    return { data: null, error: 'Failed to save AI settings' };
  }
}

/**
 * Update existing AI settings
 */
export async function updateAISettings(
  id: string,
  updates: AISettingsUpdate
): Promise<AISettingsResponse> {
  try {
    const updateData: any = { ...updates };

    // Encrypt API key if provided
    if (updates.api_key) {
      updateData.api_key = encryptApiKey(updates.api_key);
    }

    const { data, error } = await supabase
      .from('ai_settings')
      .update(updateData)
      .eq('id', id)
      .select()
      .single();

    if (error) {
      console.error('Error updating AI settings:', error);
      return { data: null, error: error.message };
    }

    // Clear the AI config cache so the new settings are used immediately
    clearAIConfigCache();

    return { data, error: null };
  } catch (error) {
    console.error('Error in updateAISettings:', error);
    return { data: null, error: 'Failed to update AI settings' };
  }
}

/**
 * Delete AI settings
 */
export async function deleteAISettings(id: string): Promise<{ error: string | null }> {
  try {
    const { error } = await supabase
      .from('ai_settings')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting AI settings:', error);
      return { error: error.message };
    }

    return { error: null };
  } catch (error) {
    console.error('Error in deleteAISettings:', error);
    return { error: 'Failed to delete AI settings' };
  }
}

/**
 * Test AI settings by making a simple API call
 */
export async function testAISettings(provider: AIProvider, apiKey: string, model: string): Promise<{ success: boolean; error?: string }> {
  try {
    // This is a simplified test - in a real implementation, you'd make an actual API call
    // to the provider to validate the credentials

    if (!apiKey || apiKey.trim() === '') {
      return { success: false, error: 'API key is required' };
    }

    if (!model || model.trim() === '') {
      return { success: false, error: 'Model is required' };
    }

    // For now, just validate the format
    if (provider === 'groq' && !apiKey.startsWith('gsk_')) {
      return { success: false, error: 'GROQ API key should start with "gsk_"' };
    }

    if (provider === 'anthropic' && !apiKey.startsWith('sk-ant-')) {
      return { success: false, error: 'Anthropic API key should start with "sk-ant-"' };
    }

    if (provider === 'openai' && !apiKey.startsWith('sk-')) {
      return { success: false, error: 'OpenAI API key should start with "sk-"' };
    }

    return { success: true };
  } catch (error) {
    console.error('Error testing AI settings:', error);
    return { success: false, error: 'Failed to test AI settings' };
  }
}
