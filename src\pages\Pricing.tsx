import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Check, X, Loader2, Database, Zap } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/contexts/AuthContext';
import { useSubscription } from '@/hooks/use-profile';
import DirectPayPalButton from '@/components/payment/DirectPayPalButton';
import DirectStripeButton from '@/components/payment/DirectStripeButton';
import StripeScriptProvider from '@/components/payment/StripeScriptProvider';
import { PRICING, SUBSCRIPTION_PLANS } from '@/config/paypal';
import { STRIPE_PRICING } from '@/config/stripe';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useDynamicPricing } from '@/hooks/use-dynamic-pricing';

const Pricing = () => {
  const { user, isAuthenticated } = useAuth();
  const { data: subscription } = useSubscription();
  const navigate = useNavigate();
  const [billingPeriod, setBillingPeriod] = useState<'month' | 'year'>('month');
  const [isProcessing, setIsProcessing] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<'paypal' | 'stripe'>('paypal');

  // Get current subscription tier and convert to uppercase for comparison
  const currentTier = subscription?.tier?.toUpperCase() || null;

  // Get dynamic pricing and features from database
  const { pricingTiers, isLoading: isPricingLoading, hasDbFeatures } = useDynamicPricing();

  // Render feature item with dynamic styling
  const FeatureItem = ({ included, text, highlighted }: { included: boolean; text: string; highlighted?: boolean }) => (
    <div className="flex items-start space-x-2 py-1">
      {included ? (
        <Check className="h-5 w-5 text-green-500 mt-0.5" />
      ) : (
        <X className="h-5 w-5 text-gray-300 mt-0.5" />
      )}
      <span className={`${included ? 'text-gray-700' : 'text-gray-400 line-through'} ${highlighted ? 'font-semibold' : ''}`}>
        {text}
      </span>
    </div>
  );

  // Get dynamic features for a specific plan - only show features enabled on at least one plan
  const getDynamicFeaturesForPlan = (planKey: string) => {
    const tier = pricingTiers.find(t => t.planKey === planKey);
    if (!tier || !pricingTiers.length) return [];

    const featureDisplayMap: Record<string, { text: string; highlighted?: boolean }> = {
      'interview_scheduling': { text: "Interview scheduling", highlighted: true },
      'team_management': { text: "Team collaboration" },
      'custom_scoring': { text: "Custom rules for candidate scoring", highlighted: true },
      'advanced_analytics': { text: "Advanced analytics & insights" },
      'api_access': { text: "API access", highlighted: true },
      'white_label': { text: "White-label solution" },
      'priority_support': { text: "Priority support" }
    };

    // Get all feature keys that exist in the database
    const allFeatureKeys = Object.keys(tier.enabledFeatures || {});

    // Filter out features that are disabled on ALL plans
    const featuresEnabledOnAtLeastOnePlan = allFeatureKeys.filter(featureKey => {
      return pricingTiers.some(t => t.enabledFeatures?.[featureKey] === true);
    });

    // Return only features that are enabled on at least one plan
    return featuresEnabledOnAtLeastOnePlan
      .map(featureKey => {
        const display = featureDisplayMap[featureKey];
        const enabled = tier.enabledFeatures?.[featureKey] || false;

        return display ? {
          text: display.text,
          included: enabled,
          highlighted: display.highlighted
        } : null;
      })
      .filter(Boolean);
  };

  // Render subscription button based on current plan
  const renderSubscriptionButton = (planTier: 'STARTER' | 'GROWTH' | 'PRO') => {
    const isCurrentPlan = currentTier === planTier;

    if (isCurrentPlan) {
      return (
        <Button disabled className="w-full" variant="secondary">
          Current Plan
        </Button>
      );
    }

    if (isProcessing && selectedPlan === planTier) {
      return (
        <Button disabled className="w-full">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Processing...
        </Button>
      );
    }

    if (selectedPlan === planTier) {
      return (
        <div className="w-full space-y-2">
          {paymentMethod === 'paypal' ? (
            <DirectPayPalButton tier={planTier} />
          ) : (
            <DirectStripeButton tier={planTier} />
          )}
        </div>
      );
    }

    return (
      <Button
        onClick={() => setSelectedPlan(planTier)}
        className="w-full"
        variant={planTier === 'GROWTH' ? 'default' : 'outline'}
      >
        Subscribe Now
      </Button>
    );
  };

  return (
    <DashboardLayout>
    <StripeScriptProvider>
    <div className="container mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <div className="text-center mb-12">
        <h1 className="text-3xl font-bold text-gray-900 sm:text-4xl">
          Choose the Right Plan for Your Recruitment Needs
        </h1>
        <p className="mt-4 text-xl text-gray-600 max-w-2xl mx-auto">
          Select a plan that works best for your business. All plans include our core features.
        </p>

    
      </div>

      <div className="flex justify-center mb-8">
        <Tabs
          defaultValue="paypal"
          value={paymentMethod}
          onValueChange={(value) => setPaymentMethod(value as 'paypal' | 'stripe')}
          className="w-full max-w-md"
        >
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="paypal">Pay with PayPal</TabsTrigger>
            <TabsTrigger value="stripe">Pay with Stripe</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      <div className="grid gap-8 md:grid-cols-3">
        {/* Starter Plan */}
        <Card className={`flex flex-col border-2 transition-colors ${
          currentTier === 'STARTER'
            ? 'border-green-500 bg-green-50'
            : 'border-gray-200 hover:border-primary'
        }`}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-2xl">Starter</CardTitle>
                <CardDescription>Perfect for small businesses</CardDescription>
              </div>
              {currentTier === 'STARTER' && (
                <span className="bg-green-500 text-white px-2 py-1 text-xs font-medium rounded">
                  Current Plan
                </span>
              )}
            </div>
            <div className="mt-4">
              <span className="text-4xl font-bold">${PRICING.STARTER.price}</span>
              <span className="text-gray-500">/{billingPeriod}</span>
            </div>
          </CardHeader>
          <CardContent className="flex-grow">
            <div className="space-y-2">
              {/* Static features from config */}
              {PRICING.STARTER.features.map((feature, index) => (
                <FeatureItem key={index} included={true} text={feature} />
              ))}
              {/* Dynamic features from database */}
              {getDynamicFeaturesForPlan('STARTER').map((feature: any, index) => (
                <FeatureItem
                  key={`dynamic-${index}`}
                  included={feature.included}
                  text={feature.text}
                  highlighted={feature.highlighted}
                />
              ))}
            </div>
          </CardContent>
          <CardFooter>
            {renderSubscriptionButton('STARTER')}
          </CardFooter>
        </Card>

        {/* Growth Plan */}
        <Card className={`flex flex-col border-2 relative transition-colors ${
          currentTier === 'GROWTH'
            ? 'border-green-500 bg-green-50'
            : 'border-primary'
        }`}>
          {currentTier === 'GROWTH' ? (
            <div className="absolute top-0 right-0 bg-green-500 text-white px-3 py-1 text-sm font-medium rounded-bl-lg rounded-tr-lg">
              Current Plan
            </div>
          ) : (
            <div className="absolute top-0 right-0 bg-primary text-white px-3 py-1 text-sm font-medium rounded-bl-lg rounded-tr-lg">
              Popular
            </div>
          )}
          <CardHeader>
            <CardTitle className="text-2xl">Growth</CardTitle>
            <CardDescription>For growing recruitment teams</CardDescription>
            <div className="mt-4">
              <span className="text-4xl font-bold">${PRICING.GROWTH.price}</span>
              <span className="text-gray-500">/{billingPeriod}</span>
            </div>
          </CardHeader>
          <CardContent className="flex-grow">
            <div className="space-y-2">
              {/* Static features from config */}
              {PRICING.GROWTH.features.map((feature, index) => (
                <FeatureItem key={index} included={true} text={feature} />
              ))}
              {/* Dynamic features from database */}
              {getDynamicFeaturesForPlan('GROWTH').map((feature: any, index) => (
                <FeatureItem
                  key={`dynamic-${index}`}
                  included={feature.included}
                  text={feature.text}
                  highlighted={feature.highlighted}
                />
              ))}
            </div>
          </CardContent>
          <CardFooter>
            {renderSubscriptionButton('GROWTH')}
          </CardFooter>
        </Card>

        {/* Pro Plan */}
        <Card className={`flex flex-col border-2 transition-colors ${
          currentTier === 'PRO'
            ? 'border-green-500 bg-green-50'
            : 'border-gray-200 hover:border-primary'
        }`}>
          <CardHeader>
            <div className="flex justify-between items-start">
              <div>
                <CardTitle className="text-2xl">Pro</CardTitle>
                <CardDescription>For professional recruitment agencies</CardDescription>
              </div>
              {currentTier === 'PRO' && (
                <span className="bg-green-500 text-white px-2 py-1 text-xs font-medium rounded">
                  Current Plan
                </span>
              )}
            </div>
            <div className="mt-4">
              <span className="text-4xl font-bold">${PRICING.PRO.price}</span>
              <span className="text-gray-500">/{billingPeriod}</span>
            </div>
          </CardHeader>
          <CardContent className="flex-grow">
            <div className="space-y-2">
              {/* Static features from config */}
              {PRICING.PRO.features.map((feature, index) => (
                <FeatureItem key={index} included={true} text={feature} />
              ))}
              {/* Dynamic features from database */}
              {getDynamicFeaturesForPlan('PRO').map((feature: any, index) => (
                <FeatureItem
                  key={`dynamic-${index}`}
                  included={feature.included}
                  text={feature.text}
                  highlighted={feature.highlighted}
                />
              ))}
            </div>
          </CardContent>
          <CardFooter>
            {renderSubscriptionButton('PRO')}
          </CardFooter>
        </Card>
      </div>



      <div className="mt-16 text-center">
        <h2 className="text-2xl font-bold text-gray-900">Frequently Asked Questions</h2>
        <div className="mt-8 max-w-3xl mx-auto grid gap-6">
          <div className="text-left">
            <h3 className="text-lg font-medium text-gray-900">Can I change plans later?</h3>
            <p className="mt-2 text-gray-600">
              Yes, you can upgrade or downgrade your plan at any time. Changes will be applied at the start of your next billing cycle.
            </p>
          </div>
          <div className="text-left">
            <h3 className="text-lg font-medium text-gray-900">Can I try before subscribing?</h3>
            <p className="mt-2 text-gray-600">
              The Starter plan includes 1 free CV evaluation to test our platform. Upgrade to access more features.
            </p>
          </div>
          <div className="text-left">
            <h3 className="text-lg font-medium text-gray-900">What payment methods do you accept?</h3>
            <p className="mt-2 text-gray-600">
              We accept all major credit cards through Stripe and PayPal for both monthly and annual subscriptions.
            </p>
          </div>
          <div className="text-left">
            <h3 className="text-lg font-medium text-gray-900">Can I cancel my subscription?</h3>
            <p className="mt-2 text-gray-600">
              Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your current billing period.
            </p>
          </div>
        </div>
      </div>
    </div>
    </StripeScriptProvider>
    </DashboardLayout>
  );
};

export default Pricing;
