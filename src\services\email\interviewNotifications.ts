import { sendEmail } from './emailService';
import { Interview } from '@/services/supabase/interviews';
import { InterviewParticipant } from '@/services/supabase/interview-participants';
import { generateInterviewICS } from '@/services/calendar/icsGenerator';
import { format } from 'date-fns';
import { TemplateService } from './templateService';

interface EmailRecipient {
  email: string;
  name: string;
  role: 'candidate' | 'interviewer' | 'organizer';
}

/**
 * Send interview invitation email with calendar invite
 */
export const sendInterviewInvitation = async (
  interview: Interview,
  participants: InterviewParticipant[],
  candidate: { email: string; name: string },
  organizer: { email: string; name: string }
): Promise<void> => {
  try {
    console.log('📧 sendInterviewInvitation called with:', {
      interviewId: interview.id,
      candidateEmail: candidate.email,
      organizerEmail: organizer.email,
      participantsCount: participants.length
    });

    // Generate ICS file
    console.log('📅 Generating ICS file...');
    const icsContent = generateInterviewICS(
      interview,
      participants,
      candidate.email,
      organizer.email
    );
    console.log('✅ ICS file generated successfully');

    const interviewDate = format(new Date(interview.scheduled_at), 'EEEE, MMMM d, yyyy');
    const interviewTime = format(new Date(interview.scheduled_at), 'h:mm a');

    // Email to candidate
    console.log('📤 Sending email to candidate:', candidate.email);

    // Try to use database template first, fallback to hardcoded template
    const templateResult = await TemplateService.renderInterviewInvitationCandidate({
      companyName: interview.company_name || 'Sourcio.ai',
      candidateName: candidate.name,
      jobTitle: interview.job_title || 'Unknown Position',
      interviewDate,
      interviewTime,
      duration: interview.duration_minutes?.toString() || '60',
      interviewType: interview.interview_type || 'video',
      location: interview.location || 'TBD'
    });

    const emailContent = templateResult || {
      subject: `Interview Scheduled: ${interview.job_title}`,
      content: generateCandidateInvitationHTML(interview, interviewDate, interviewTime)
    };

    await sendEmail({
      to: candidate.email,
      subject: emailContent.subject,
      html: emailContent.content,
      companyName: interview.company_name,
      attachments: [
        {
          filename: 'interview.ics',
          content: btoa(icsContent), // Use browser-compatible base64 encoding
          contentType: 'text/calendar'
        }
      ]
    });
    console.log('✅ Candidate email sent successfully');

    // Email to interviewers
    for (const participant of participants) {
      if (participant.email && participant.role === 'interviewer') {
        await sendEmail({
          to: participant.email,
          subject: `Interview Assignment: ${interview.candidate_name} - ${interview.job_title}`,
          html: generateInterviewerInvitationHTML(interview, candidate, interviewDate, interviewTime),
          companyName: interview.company_name,
          attachments: [
            {
              filename: 'interview.ics',
              content: btoa(icsContent), // Use browser-compatible base64 encoding
              contentType: 'text/calendar'
            }
          ]
        });
      }
    }
  } catch (error) {
    console.error('Failed to send interview invitation:', error);
    throw error;
  }
};

/**
 * Send interview reminder email
 */
export const sendInterviewReminder = async (
  interview: Interview,
  recipient: EmailRecipient,
  reminderType: '24h' | '1h'
): Promise<void> => {
  try {
    const timeUntil = reminderType === '24h' ? '24 hours' : '1 hour';
    const interviewDate = format(new Date(interview.scheduled_at), 'EEEE, MMMM d, yyyy');
    const interviewTime = format(new Date(interview.scheduled_at), 'h:mm a');

    await sendEmail({
      to: recipient.email,
      subject: `Interview Reminder: ${timeUntil} until your interview`,
      html: generateReminderHTML(interview, recipient, interviewDate, interviewTime, timeUntil)
    });
  } catch (error) {
    console.error('Failed to send interview reminder:', error);
    throw error;
  }
};

/**
 * Send interview status update email
 */
export const sendInterviewStatusUpdate = async (
  interview: Interview,
  recipient: EmailRecipient,
  newStatus: string,
  message?: string
): Promise<void> => {
  try {
    const interviewDate = format(new Date(interview.scheduled_at), 'EEEE, MMMM d, yyyy');
    const interviewTime = format(new Date(interview.scheduled_at), 'h:mm a');

    await sendEmail({
      to: recipient.email,
      subject: `Interview ${newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}: ${interview.job_title}`,
      html: generateStatusUpdateHTML(interview, recipient, newStatus, interviewDate, interviewTime, message)
    });
  } catch (error) {
    console.error('Failed to send interview status update:', error);
    throw error;
  }
};

/**
 * Generate candidate invitation HTML
 */
const generateCandidateInvitationHTML = (
  interview: Interview,
  date: string,
  time: string
): string => {
  const companyName = interview.company_name || 'Sourcio.ai';
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">${companyName}</h1>
      </div>
      
      <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">Interview Scheduled</h2>
      
      <p style="font-size: 16px; line-height: 1.6;">Dear ${interview.candidate_name},</p>
      
      <p style="font-size: 16px; line-height: 1.6;">
        We're pleased to inform you that an interview has been scheduled for the <strong>${interview.job_title}</strong> position.
      </p>
      
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
        <h3 style="margin-top: 0; color: #1f2937;">Interview Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Date:</td>
            <td style="padding: 8px 0; color: #1f2937;">${date}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Time:</td>
            <td style="padding: 8px 0; color: #1f2937;">${time}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Duration:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.duration_minutes} minutes</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Type:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.interview_type.charAt(0).toUpperCase() + interview.interview_type.slice(1)}</td>
          </tr>
          ${interview.location ? `
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Location:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.location}</td>
          </tr>
          ` : ''}
        </table>
        ${interview.notes ? `
        <div style="margin-top: 15px;">
          <p style="font-weight: bold; color: #374151; margin-bottom: 5px;">Additional Notes:</p>
          <p style="color: #1f2937; margin: 0;">${interview.notes}</p>
        </div>
        ` : ''}
      </div>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Please find the calendar invitation attached to this email. We look forward to speaking with you!
      </p>
      
      <div style="background: #fef3c7; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="margin: 0; color: #92400e; font-size: 14px;">
          <strong>Tip:</strong> Please arrive 5 minutes early and ensure you have a stable internet connection if this is a video interview.
        </p>
      </div>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Best regards,<br>
        <strong>The Recruitment Team</strong><br>
        ${companyName}
      </p>

      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};

/**
 * Generate interviewer invitation HTML
 */
const generateInterviewerInvitationHTML = (
  interview: Interview,
  candidate: { email: string; name: string },
  date: string,
  time: string
): string => {
  const companyName = interview.company_name || 'Sourcio.ai';
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">${companyName}</h1>
      </div>
      
      <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">Interview Assignment</h2>
      
      <p style="font-size: 16px; line-height: 1.6;">Hello,</p>
      
      <p style="font-size: 16px; line-height: 1.6;">
        You have been assigned as an interviewer for <strong>${candidate.name}</strong> applying for the <strong>${interview.job_title}</strong> position.
      </p>
      
      <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #2563eb;">
        <h3 style="margin-top: 0; color: #1f2937;">Interview Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Candidate:</td>
            <td style="padding: 8px 0; color: #1f2937;">${candidate.name}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Position:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.job_title}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Date:</td>
            <td style="padding: 8px 0; color: #1f2937;">${date}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Time:</td>
            <td style="padding: 8px 0; color: #1f2937;">${time}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Duration:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.duration_minutes} minutes</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Type:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.interview_type.charAt(0).toUpperCase() + interview.interview_type.slice(1)}</td>
          </tr>
          ${interview.location ? `
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Location:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.location}</td>
          </tr>
          ` : ''}
        </table>
        ${interview.notes ? `
        <div style="margin-top: 15px;">
          <p style="font-weight: bold; color: #374151; margin-bottom: 5px;">Interview Notes:</p>
          <p style="color: #1f2937; margin: 0;">${interview.notes}</p>
        </div>
        ` : ''}
      </div>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Please find the calendar invitation attached. Make sure to prepare any necessary materials for the interview.
      </p>
      
      <div style="background: #dbeafe; padding: 15px; border-radius: 8px; margin: 20px 0;">
        <p style="margin: 0; color: #1e40af; font-size: 14px;">
          <strong>Reminder:</strong> Please review the candidate's profile and prepare your interview questions in advance.
        </p>
      </div>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Best regards,<br>
        <strong>The Recruitment Team</strong><br>
        ${companyName}
      </p>

      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};

/**
 * Generate reminder HTML
 */
const generateReminderHTML = (
  interview: Interview,
  recipient: EmailRecipient,
  date: string,
  time: string,
  timeUntil: string
): string => {
  const isCandidate = recipient.role === 'candidate';
  const companyName = interview.company_name || 'Sourcio.ai';

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #dc2626; margin: 0;">${companyName}</h1>
      </div>
      
      <h2 style="color: #dc2626; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">Interview Reminder</h2>
      
      <p style="font-size: 16px; line-height: 1.6;">Dear ${recipient.name},</p>
      
      <p style="font-size: 16px; line-height: 1.6;">
        This is a reminder that you have an interview scheduled in <strong style="color: #dc2626;">${timeUntil}</strong>.
      </p>
      
      <div style="background: #fef2f2; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626;">
        <h3 style="margin-top: 0; color: #1f2937;">Interview Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">${isCandidate ? 'Position:' : 'Candidate:'}</td>
            <td style="padding: 8px 0; color: #1f2937;">${isCandidate ? interview.job_title : interview.candidate_name}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Date:</td>
            <td style="padding: 8px 0; color: #1f2937;">${date}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Time:</td>
            <td style="padding: 8px 0; color: #1f2937;">${time}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Duration:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.duration_minutes} minutes</td>
          </tr>
          ${interview.location ? `
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Location:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.location}</td>
          </tr>
          ` : ''}
        </table>
      </div>
      
      ${isCandidate ? 
        '<p style="font-size: 16px; line-height: 1.6;">Please ensure you are prepared and arrive on time. Good luck!</p>' : 
        '<p style="font-size: 16px; line-height: 1.6;">Please review the candidate\'s profile and prepare your interview questions.</p>'
      }
      
      <p style="font-size: 16px; line-height: 1.6;">
        Best regards,<br>
        <strong>The Recruitment Team</strong><br>
        ${companyName}
      </p>

      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};

/**
 * Generate status update HTML
 */
const generateStatusUpdateHTML = (
  interview: Interview,
  recipient: EmailRecipient,
  newStatus: string,
  date: string,
  time: string,
  message?: string
): string => {
  const statusColors = {
    cancelled: '#dc2626',
    rescheduled: '#d97706',
    completed: '#059669',
    confirmed: '#2563eb'
  };

  const statusColor = statusColors[newStatus as keyof typeof statusColors] || '#6b7280';
  const companyName = interview.company_name || 'Sourcio.ai';

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: ${statusColor}; margin: 0;">${companyName}</h1>
      </div>
      
      <h2 style="color: ${statusColor}; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
        Interview ${newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}
      </h2>
      
      <p style="font-size: 16px; line-height: 1.6;">Dear ${recipient.name},</p>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Your interview for the <strong>${interview.job_title}</strong> position has been <strong>${newStatus}</strong>.
      </p>
      
      <div style="background: #f9fafb; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid ${statusColor};">
        <h3 style="margin-top: 0; color: #1f2937;">Interview Details</h3>
        <table style="width: 100%; border-collapse: collapse;">
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Position:</td>
            <td style="padding: 8px 0; color: #1f2937;">${interview.job_title}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Original Date:</td>
            <td style="padding: 8px 0; color: #1f2937;">${date}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Original Time:</td>
            <td style="padding: 8px 0; color: #1f2937;">${time}</td>
          </tr>
          <tr>
            <td style="padding: 8px 0; font-weight: bold; color: #374151;">Status:</td>
            <td style="padding: 8px 0; color: ${statusColor}; font-weight: bold;">${newStatus.charAt(0).toUpperCase() + newStatus.slice(1)}</td>
          </tr>
        </table>
        ${message ? `
        <div style="margin-top: 15px;">
          <p style="font-weight: bold; color: #374151; margin-bottom: 5px;">Message:</p>
          <p style="color: #1f2937; margin: 0;">${message}</p>
        </div>
        ` : ''}
      </div>
      
      <p style="font-size: 16px; line-height: 1.6;">
        If you have any questions, please don't hesitate to contact us.
      </p>
      
      <p style="font-size: 16px; line-height: 1.6;">
        Best regards,<br>
        <strong>The Recruitment Team</strong><br>
        ${companyName}
      </p>

      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};


