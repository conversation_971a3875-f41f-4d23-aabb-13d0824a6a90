import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Chart as ChartJS, RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend } from 'chart.js';
import { Radar } from 'react-chartjs-2';

// Register Chart.js components
ChartJS.register(RadialLinearScale, PointElement, LineElement, Filler, Tooltip, Legend);

interface JobMatchRadarChartProps {
  chartData: {
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      backgroundColor: string;
      borderColor: string;
      borderWidth: number;
    }>;
  };
}

const JobMatchRadarChart: React.FC<JobMatchRadarChartProps> = ({ chartData }) => {
  // Chart options
  const options = {
    scales: {
      r: {
        angleLines: {
          display: true
        },
        suggestedMin: 0,
        suggestedMax: 100
      }
    },
    plugins: {
      legend: {
        position: 'bottom' as const,
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${context.raw}%`;
          }
        }
      }
    },
    maintainAspectRatio: false
  };

  return (
    <div className="space-y-4">
      <p className="text-sm text-gray-500 mb-4">
        This chart compares how the candidate's skills, experience, education, and location match with the top job positions.
      </p>
      <div className="h-80">
        <Radar data={chartData} options={options} />
      </div>
    </div>
  );
};

export default JobMatchRadarChart;
