import React, { useEffect } from 'react';
import { <PERSON> } from 'react-router-dom';
import { NavBar } from '@/components/NavBar';
import { Mail, Building, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { motion } from 'framer-motion';

const Contact = () => {
  useEffect(() => {
    document.body.className = 'bg-recruiter-navy';
  }, []);

  return (
    <div className="min-h-screen bg-hero-gradient overflow-hidden relative">
      {/* NavBar is positioned absolutely by the component itself */}
      <NavBar />

      <div className="container mx-auto px-6 pt-32 pb-24">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="max-w-3xl mx-auto"
        >
          <Link 
            to="/" 
            className="inline-flex items-center text-white hover:text-recruiter-blue transition mb-6"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Home
          </Link>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-4xl font-bold text-white mb-6"
          >
            Contact Us
          </motion.h1>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-card-gradient rounded-xl p-8 border border-gray-800 mb-8"
          >
            <h2 className="text-2xl font-bold text-white mb-4">Custom Enterprise Solutions</h2>
            <p className="text-gray-300 mb-6">
              We offer white-label solutions and custom enterprise plans for larger recruitment agencies with specific needs. 
              Our team can work with you to create a tailored solution that meets your unique requirements.
            </p>
            
            <div className="space-y-6">
              <div className="flex items-start">
                <Building className="h-6 w-6 text-recruiter-blue mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-semibold text-white mb-2">White-Label Solutions</h3>
                  <p className="text-gray-300">
                    Offer our powerful CV evaluation platform under your own brand. We provide complete customization 
                    of the interface, features, and reporting to match your agency's identity.
                  </p>
                </div>
              </div>
              
              <div className="flex items-start">
                <Building className="h-6 w-6 text-recruiter-blue mr-4 mt-1 flex-shrink-0" />
                <div>
                  <h3 className="text-xl font-semibold text-white mb-2">Custom Enterprise Plans</h3>
                  <p className="text-gray-300">
                    For larger recruitment agencies with specific needs, we offer custom enterprise plans with 
                    dedicated support, advanced features, and integration with your existing systems.
                  </p>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-card-gradient rounded-xl p-8 border border-gray-800"
          >
            <h2 className="text-2xl font-bold text-white mb-4">Get in Touch</h2>
            <p className="text-gray-300 mb-6">
              For inquiries about our custom solutions or enterprise plans, please contact us via email. 
              Our team will get back to you within 1-2 business days.
            </p>
            
            <div className="flex items-center bg-[#1a2035] p-4 rounded-lg border border-gray-700">
              <Mail className="h-6 w-6 text-recruiter-blue mr-4 flex-shrink-0" />
              <div>
                <p className="text-white font-medium">Email us at:</p>
                <a 
                  href="mailto:<EMAIL>" 
                  className="text-recruiter-blue hover:underline"
                >
                  <EMAIL>
                </a>
              </div>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default Contact;
