import React from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ClipboardCheck, Lock, UserRound } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import ProPlanGuard from '@/components/guards/ProPlanGuard';
import CandidateAssessmentView from '@/components/assessments/CandidateAssessmentView';

const AssessmentTestsContent = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { hasRole } = usePermissions();

  // Check if user is admin or has Pro plan
  const hasAccess = hasRole(['admin', 'platform_admin']);

  if (hasAccess) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Assessment Tests</h1>
            <p className="text-gray-500">Create and manage candidate skills assessments</p>
          </div>
        </div>

        <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-4">
          <p className="text-blue-800 flex items-center">
            <UserRound className="h-4 w-4 mr-2" />
            <span>Assessment interface preview for presentation purposes.</span>
          </p>
        </div>

        <CandidateAssessmentView />
      </div>
    );
  }

  // Default view for non-Pro users
  return (
    <div className="space-y-8">
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardHeader className="text-center">
          <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 mb-4">
            <ClipboardCheck className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl">Assessment Tests</CardTitle>
          <CardDescription className="text-lg">
            This feature is coming soon to Pro plan subscribers
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <div className="space-y-2">
            <p className="text-gray-600">
              The Assessment Tests feature will allow you to:
            </p>
            <ul className="text-left mx-auto max-w-md space-y-2 text-gray-600">
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Create custom skills assessment tests for candidates</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Choose from a library of pre-built test templates</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Automatically score and evaluate test results</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Compare candidate performance against job requirements</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Generate detailed assessment reports</span>
              </li>
            </ul>
          </div>

          <div className="flex justify-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard/pricing')}
              className="gap-2"
            >
              <Lock className="h-4 w-4" />
              Upgrade to Pro
            </Button>
            <Button
              variant="default"
              onClick={() => navigate('/dashboard')}
            >
              Return to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Wrap the component with the DashboardLayout
const AssessmentTestsWithLayout = () => (
  <DashboardLayout>
    <AssessmentTestsContent />
  </DashboardLayout>
);

export default AssessmentTestsWithLayout;
