import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { notificationProcessor } from '@/services/notifications/notificationProcessor';
import { env } from '@/lib/env';

/**
 * Hook to manage the notification processor
 */
export const useNotificationProcessor = () => {
  const { user } = useAuth();
  const [isRunning, setIsRunning] = useState(false);
  const [stats, setStats] = useState({
    total: 0,
    processed: 0,
    pending: 0,
    failed: 0
  });

  /**
   * Start the notification processor
   */
  const start = () => {
    if (!env.ENABLE_EMAIL_NOTIFICATIONS) {
      console.log('Email notifications disabled, not starting processor');
      return;
    }

    notificationProcessor.start(30000); // 30 second interval
    setIsRunning(true);
  };

  /**
   * Stop the notification processor
   */
  const stop = () => {
    notificationProcessor.stop();
    setIsRunning(false);
  };

  /**
   * Manually trigger processing
   */
  const triggerProcessing = async () => {
    await notificationProcessor.triggerProcessing();
    await refreshStats();
  };

  /**
   * Refresh statistics
   */
  const refreshStats = async () => {
    try {
      const newStats = await notificationProcessor.getStats();
      setStats(newStats);
    } catch (error) {
      console.error('Failed to refresh notification stats:', error);
    }
  };

  /**
   * Auto-start processor when user is authenticated and email notifications are enabled
   */
  useEffect(() => {
    if (user && env.ENABLE_EMAIL_NOTIFICATIONS && !isRunning) {
      start();
    }

    return () => {
      if (isRunning) {
        stop();
      }
    };
  }, [user]);

  /**
   * Refresh stats periodically
   */
  useEffect(() => {
    if (isRunning) {
      refreshStats();
      const interval = setInterval(refreshStats, 60000); // Every minute
      return () => clearInterval(interval);
    }
  }, [isRunning]);

  return {
    isRunning,
    stats,
    start,
    stop,
    triggerProcessing,
    refreshStats,
    isEnabled: env.ENABLE_EMAIL_NOTIFICATIONS
  };
};

/**
 * Hook to test candidate notifications
 */
export const useTestNotifications = () => {
  const [isTesting, setIsTesting] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const testNotifications = async (email: string) => {
    setIsTesting(true);
    setTestResult(null);

    try {
      const { candidateNotificationOrchestrator } = await import('@/services/notifications/candidateNotificationOrchestrator');
      await candidateNotificationOrchestrator.testNotifications(email);
      
      setTestResult({
        success: true,
        message: `Test notification sent successfully to ${email}`
      });
    } catch (error) {
      setTestResult({
        success: false,
        message: `Failed to send test notification: ${error instanceof Error ? error.message : 'Unknown error'}`
      });
    } finally {
      setIsTesting(false);
    }
  };

  return {
    testNotifications,
    isTesting,
    testResult,
    clearResult: () => setTestResult(null)
  };
};
