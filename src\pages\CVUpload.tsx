import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { 
  Upload, 
  File, 
  X, 
  Check, 
  Trash2, 
  FileText,
  ArrowLeft,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';

// Mock job positions for dropdown
const mockPositions = [
  { id: '1', title: 'Frontend Developer' },
  { id: '2', title: 'UX Designer' },
  { id: '3', title: 'Product Manager' },
  { id: '4', title: 'DevOps Engineer' },
  { id: '5', title: 'Marketing Specialist' },
];

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
  url?: string;
}

const CVUpload = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  
  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      addFiles(Array.from(files));
    }
  };
  
  // Handle drag events
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files) {
      addFiles(Array.from(files));
    }
  };
  
  // Add files to the upload list
  const addFiles = (files: File[]) => {
    // Filter for PDF, DOC, DOCX files
    const validFiles = files.filter(file => {
      const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
      return validTypes.includes(file.type);
    });
    
    if (validFiles.length !== files.length) {
      toast({
        title: 'Invalid file type',
        description: 'Only PDF, DOC, and DOCX files are allowed.',
        variant: 'destructive',
      });
    }
    
    // Add valid files to the list
    const newFiles = validFiles.map(file => ({
      id: Math.random().toString(36).substring(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      progress: 0,
      status: 'uploading' as const,
    }));
    
    setUploadedFiles(prev => [...prev, ...newFiles]);
    
    // Simulate upload progress for each file
    newFiles.forEach(file => {
      simulateFileUpload(file.id);
    });
  };
  
  // Simulate file upload progress
  const simulateFileUpload = (fileId: string) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.floor(Math.random() * 10) + 5;
      
      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);
        
        setUploadedFiles(prev => 
          prev.map(file => 
            file.id === fileId 
              ? { ...file, progress: 100, status: 'success', url: `/mock-cv-${fileId}.pdf` } 
              : file
          )
        );
      } else {
        setUploadedFiles(prev => 
          prev.map(file => 
            file.id === fileId 
              ? { ...file, progress } 
              : file
          )
        );
      }
    }, 300);
  };
  
  // Remove file from the list
  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };
  
  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  // Process CVs
  const processCVs = async () => {
    if (!selectedPosition) {
      toast({
        title: 'Position required',
        description: 'Please select a position for these CVs.',
        variant: 'destructive',
      });
      return;
    }
    
    if (uploadedFiles.length === 0) {
      toast({
        title: 'No files uploaded',
        description: 'Please upload at least one CV to process.',
        variant: 'destructive',
      });
      return;
    }
    
    // Check if all files are uploaded successfully
    const allUploaded = uploadedFiles.every(file => file.status === 'success');
    if (!allUploaded) {
      toast({
        title: 'Upload in progress',
        description: 'Please wait for all files to finish uploading.',
        variant: 'destructive',
      });
      return;
    }
    
    setIsProcessing(true);
    
    try {
      // Simulate processing delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      toast({
        title: 'CVs processed successfully',
        description: `${uploadedFiles.length} CV${uploadedFiles.length > 1 ? 's' : ''} processed for ${mockPositions.find(p => p.id === selectedPosition)?.title}.`,
      });
      
      // Navigate to CV evaluation results
      navigate('/dashboard/cvs/evaluation');
    } catch (error) {
      toast({
        title: 'Processing failed',
        description: 'There was an error processing the CVs. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            <Button 
              variant="outline" 
              size="icon"
              className="border-gray-700 text-white hover:bg-[#2a2f3d]"
              onClick={() => navigate('/dashboard/cvs')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold text-white">Upload CVs</h1>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Upload Area */}
          <div className="lg:col-span-2">
            <Card className="bg-[#1a2035] border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white text-lg">Upload CVs</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Position selection */}
                <div className="space-y-2">
                  <Label htmlFor="position" className="text-white">Select Position <span className="text-red-500">*</span></Label>
                  <Select value={selectedPosition} onValueChange={setSelectedPosition}>
                    <SelectTrigger className="bg-[#141b2d] border-gray-700 text-white">
                      <SelectValue placeholder="Select a position" />
                    </SelectTrigger>
                    <SelectContent className="bg-[#1a2035] border-gray-700 text-white">
                      {mockPositions.map(position => (
                        <SelectItem key={position.id} value={position.id}>
                          {position.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                {/* Drag and drop area */}
                <div 
                  className={`border-2 border-dashed rounded-lg p-8 text-center ${
                    isDragging ? 'border-recruiter-blue bg-recruiter-blue/10' : 'border-gray-700'
                  }`}
                  onDragOver={handleDragOver}
                  onDragLeave={handleDragLeave}
                  onDrop={handleDrop}
                >
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-500" />
                  <h3 className="text-lg font-medium text-white mb-2">
                    Drag and drop your CV files here
                  </h3>
                  <p className="text-gray-400 mb-4">
                    Supports PDF, DOC, DOCX (Max 10MB per file)
                  </p>
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleFileSelect}
                    className="hidden"
                    multiple
                    accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                  />
                  <Button 
                    variant="outline" 
                    className="border-gray-700 text-white hover:bg-[#2a2f3d]"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mr-2 h-4 w-4" /> Browse Files
                  </Button>
                </div>
                
                {/* Uploaded files list */}
                {uploadedFiles.length > 0 && (
                  <div className="space-y-4">
                    <h3 className="text-white font-medium">Uploaded Files ({uploadedFiles.length})</h3>
                    <div className="space-y-3">
                      {uploadedFiles.map(file => (
                        <div 
                          key={file.id} 
                          className="bg-[#141b2d] border border-gray-800 rounded-lg p-4 flex items-center"
                        >
                          <div className="mr-3">
                            <File className="h-8 w-8 text-gray-500" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex justify-between">
                              <p className="text-white font-medium truncate">{file.name}</p>
                              <span className="text-gray-400 text-sm">{formatFileSize(file.size)}</span>
                            </div>
                            <div className="mt-1">
                              <Progress 
                                value={file.progress} 
                                className="h-1.5" 
                                indicatorClassName={
                                  file.status === 'error' 
                                    ? 'bg-red-500' 
                                    : file.progress === 100 
                                      ? 'bg-green-500' 
                                      : 'bg-recruiter-blue'
                                }
                              />
                            </div>
                            <div className="flex justify-between mt-1">
                              <span className="text-xs text-gray-400">
                                {file.status === 'uploading' && `Uploading... ${file.progress}%`}
                                {file.status === 'success' && 'Upload complete'}
                                {file.status === 'error' && file.error}
                              </span>
                              {file.status === 'success' && (
                                <span className="text-xs text-green-500 flex items-center">
                                  <Check className="h-3 w-3 mr-1" /> Done
                                </span>
                              )}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="ml-2 text-gray-400 hover:text-white hover:bg-[#2a2f3d]"
                            onClick={() => removeFile(file.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                
                {/* Process button */}
                <Button 
                  className="w-full bg-recruiter-lightblue hover:bg-blue-500"
                  disabled={isProcessing || uploadedFiles.length === 0 || !selectedPosition}
                  onClick={processCVs}
                >
                  {isProcessing ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing CVs...
                    </>
                  ) : (
                    <>
                      <FileText className="mr-2 h-4 w-4" /> Process CVs
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          </div>
          
          {/* Instructions */}
          <div className="lg:col-span-1">
            <Card className="bg-[#1a2035] border-gray-800 shadow-lg">
              <CardHeader>
                <CardTitle className="text-white text-lg">Instructions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <h3 className="text-white font-medium">How it works</h3>
                  <ol className="list-decimal list-inside text-gray-300 space-y-2">
                    <li>Select the job position for these CVs</li>
                    <li>Upload CV files (PDF, DOC, DOCX)</li>
                    <li>Click "Process CVs" to analyze them</li>
                    <li>View the evaluation results and candidate matches</li>
                  </ol>
                </div>
                
                <div className="space-y-2">
                  <h3 className="text-white font-medium">Tips</h3>
                  <ul className="list-disc list-inside text-gray-300 space-y-2">
                    <li>Upload multiple CVs at once for batch processing</li>
                    <li>Make sure CVs are in a standard format for best results</li>
                    <li>The system works best with clearly structured CVs</li>
                    <li>Processing may take a few minutes for multiple files</li>
                  </ul>
                </div>
                
                <div className="bg-blue-500/10 border border-blue-500/30 rounded-lg p-4">
                  <p className="text-blue-400 text-sm">
                    <strong>Note:</strong> Our AI will analyze the CVs and match them against the selected job position requirements. You'll get a detailed evaluation report with match scores and candidate comparisons.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
};

export default CVUpload;
