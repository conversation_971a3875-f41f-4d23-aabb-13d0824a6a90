import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>ontent,
  Card<PERSON><PERSON><PERSON>,
  CardTitle,
} from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import {
  Loader2,
  Users,
  Brain,
  CheckCircle,
  AlertCircle,
  X,
} from 'lucide-react';

interface BulkEvaluationProgressProps {
  isEvaluating: boolean;
  jobTitle: string;
  totalCandidates: number;
  currentStep: string;
  progress: number;
  onCancel?: () => void;
  canCancel?: boolean;
}

const BulkEvaluationProgress: React.FC<BulkEvaluationProgressProps> = ({
  isEvaluating,
  jobTitle,
  totalCandidates,
  currentStep,
  progress,
  onCancel,
  canCancel = true,
}) => {
  const getStepIcon = () => {
    if (progress < 25) {
      return <Users className="w-5 h-5 text-blue-500" />;
    } else if (progress < 75) {
      return <Brain className="w-5 h-5 text-purple-500" />;
    } else if (progress < 100) {
      return <Loader2 className="w-5 h-5 text-orange-500 animate-spin" />;
    } else {
      return <CheckCircle className="w-5 h-5 text-green-500" />;
    }
  };

  const getProgressColor = () => {
    if (progress < 25) return 'bg-blue-500';
    if (progress < 75) return 'bg-purple-500';
    if (progress < 100) return 'bg-orange-500';
    return 'bg-green-500';
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
            {getStepIcon()}
            <span className="ml-2">
              {progress >= 100 ? 'Evaluation Complete' : 'Evaluating Candidates'}
            </span>
          </CardTitle>
          {canCancel && onCancel && progress < 100 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="text-gray-500 hover:text-gray-700"
            >
              <X className="w-4 h-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Job Information */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-1">Job Position</h3>
          <p className="text-gray-700">{jobTitle}</p>
          <p className="text-sm text-gray-500 mt-1">
            {totalCandidates} candidate{totalCandidates !== 1 ? 's' : ''} to evaluate
          </p>
        </div>

        {/* Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm text-gray-500">{Math.round(progress)}%</span>
          </div>
          <div className="relative">
            <Progress 
              value={progress} 
              className="h-3"
            />
            <div 
              className={`absolute top-0 left-0 h-3 rounded-full transition-all duration-300 ${getProgressColor()}`}
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Current Step */}
        <div className="space-y-3">
          <div className="flex items-center space-x-3">
            {isEvaluating && progress < 100 ? (
              <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
            ) : progress >= 100 ? (
              <CheckCircle className="w-4 h-4 text-green-500" />
            ) : (
              <AlertCircle className="w-4 h-4 text-gray-400" />
            )}
            <span className="text-sm text-gray-700">{currentStep}</span>
          </div>

          {/* Step Details */}
          <div className="ml-7 space-y-2">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-xs text-gray-500">
              <div className={`flex items-center space-x-2 ${progress >= 25 ? 'text-green-600' : ''}`}>
                {progress >= 25 ? (
                  <CheckCircle className="w-3 h-3" />
                ) : progress >= 10 ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : (
                  <div className="w-3 h-3 rounded-full border border-gray-300" />
                )}
                <span>Fetching candidates</span>
              </div>
              <div className={`flex items-center space-x-2 ${progress >= 75 ? 'text-green-600' : progress >= 25 ? 'text-blue-600' : ''}`}>
                {progress >= 75 ? (
                  <CheckCircle className="w-3 h-3" />
                ) : progress >= 25 ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : (
                  <div className="w-3 h-3 rounded-full border border-gray-300" />
                )}
                <span>AI evaluation</span>
              </div>
              <div className={`flex items-center space-x-2 ${progress >= 100 ? 'text-green-600' : progress >= 75 ? 'text-orange-600' : ''}`}>
                {progress >= 100 ? (
                  <CheckCircle className="w-3 h-3" />
                ) : progress >= 75 ? (
                  <Loader2 className="w-3 h-3 animate-spin" />
                ) : (
                  <div className="w-3 h-3 rounded-full border border-gray-300" />
                )}
                <span>Generating results</span>
              </div>
            </div>
          </div>
        </div>

        {/* Completion Message */}
        {progress >= 100 && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <h4 className="font-medium text-green-800">Evaluation Complete!</h4>
                <p className="text-sm text-green-700 mt-1">
                  All candidates have been successfully evaluated and ranked.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Tips while waiting */}
        {isEvaluating && progress < 100 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-start space-x-2">
              <Brain className="w-5 h-5 text-blue-600 mt-0.5" />
              <div>
                <h4 className="font-medium text-blue-800">AI Evaluation in Progress</h4>
                <p className="text-sm text-blue-700 mt-1">
                  Our AI is analyzing each candidate's CV against the job requirements, 
                  evaluating skills, experience, and cultural fit. This may take a few moments.
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BulkEvaluationProgress;
