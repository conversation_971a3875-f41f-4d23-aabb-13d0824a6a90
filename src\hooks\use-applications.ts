import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import {
  createApplication,
  createBulkApplications,
  getJobApplications,
  getCandidateApplications,
  updateApplicationStatus,
  deleteApplication,
  getApplicationStatistics,
  getJobApplicantCounts
} from '@/services/supabase/applications';
import {
  Application,
  ApplicationWithDetails,
  CreateApplicationPayload,
  BulkCreateApplicationPayload,
  ApplicationStatistics,
  ApplicationStatus
} from '@/types/applications';

/**
 * Hook to create a new job application
 */
export function useCreateApplication() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (payload: CreateApplicationPayload) => createApplication(payload),
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['applications'] });
      queryClient.invalidateQueries({ queryKey: ['job-applications', data.job_id] });
      queryClient.invalidateQueries({ queryKey: ['candidate-applications', data.candidate_id] });
      queryClient.invalidateQueries({ queryKey: ['job-applicant-counts'] });
      
      toast({
        title: 'Success',
        description: 'Candidate assigned to job successfully',
      });
    },
    onError: (error) => {
      console.error('Error creating application:', error);
      toast({
        title: 'Error',
        description: 'Failed to assign candidate to job',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to create multiple job applications (bulk assign)
 */
export function useCreateBulkApplications() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (payload: BulkCreateApplicationPayload) => createBulkApplications(payload),
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['applications'] });
      queryClient.invalidateQueries({ queryKey: ['job-applications', variables.job_id] });
      queryClient.invalidateQueries({ queryKey: ['job-applicant-counts'] });
      
      // Invalidate candidate applications for all affected candidates
      variables.candidate_ids.forEach(candidateId => {
        queryClient.invalidateQueries({ queryKey: ['candidate-applications', candidateId] });
      });
      
      toast({
        title: 'Success',
        description: `${data.length} candidate${data.length > 1 ? 's' : ''} assigned to job successfully`,
      });
    },
    onError: (error) => {
      console.error('Error creating bulk applications:', error);
      toast({
        title: 'Error',
        description: 'Failed to assign candidates to job',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to fetch applications for a specific job
 */
export function useJobApplications(jobId: string) {
  return useQuery({
    queryKey: ['job-applications', jobId],
    queryFn: () => getJobApplications(jobId),
    enabled: !!jobId,
  });
}

/**
 * Hook to fetch applications for a specific candidate
 */
export function useCandidateApplications(candidateId: string) {
  return useQuery({
    queryKey: ['candidate-applications', candidateId],
    queryFn: () => getCandidateApplications(candidateId),
    enabled: !!candidateId,
  });
}

/**
 * Hook to update application status
 */
export function useUpdateApplicationStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: ({ 
      applicationId, 
      status, 
      notes 
    }: { 
      applicationId: string; 
      status: ApplicationStatus; 
      notes?: string; 
    }) => updateApplicationStatus(applicationId, status, notes),
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['applications'] });
      queryClient.invalidateQueries({ queryKey: ['job-applications', data.job_id] });
      queryClient.invalidateQueries({ queryKey: ['candidate-applications', data.candidate_id] });
      
      toast({
        title: 'Success',
        description: 'Application status updated successfully',
      });
    },
    onError: (error) => {
      console.error('Error updating application status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update application status',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to delete an application
 */
export function useDeleteApplication() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: (applicationId: string) => deleteApplication(applicationId),
    onSuccess: () => {
      // Invalidate all application-related queries
      queryClient.invalidateQueries({ queryKey: ['applications'] });
      queryClient.invalidateQueries({ queryKey: ['job-applications'] });
      queryClient.invalidateQueries({ queryKey: ['candidate-applications'] });
      queryClient.invalidateQueries({ queryKey: ['job-applicant-counts'] });
      
      toast({
        title: 'Success',
        description: 'Application removed successfully',
      });
    },
    onError: (error) => {
      console.error('Error deleting application:', error);
      toast({
        title: 'Error',
        description: 'Failed to remove application',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to fetch application statistics
 */
export function useApplicationStatistics(companyId?: string) {
  return useQuery({
    queryKey: ['application-statistics', companyId],
    queryFn: () => getApplicationStatistics(companyId),
  });
}

/**
 * Hook to fetch job applicant counts for multiple jobs
 */
export function useJobApplicantCounts(jobIds: string[]) {
  return useQuery({
    queryKey: ['job-applicant-counts', jobIds],
    queryFn: () => getJobApplicantCounts(jobIds),
    enabled: jobIds.length > 0,
  });
}
