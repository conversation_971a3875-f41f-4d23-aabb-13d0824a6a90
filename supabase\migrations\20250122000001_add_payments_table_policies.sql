-- Add RLS policies for payments table
-- Enable RLS on payments table
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Users can view their own payments
CREATE POLICY "Users can view their own payments"
ON public.payments
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Service role can manage all payments (for webhooks)
CREATE POLICY "Service role can manage payments"
ON public.payments
FOR ALL
TO service_role
USING (true);

-- Add comment for documentation
COMMENT ON TABLE public.payments IS 'Stores payment records from Stripe and PayPal webhooks. Users can view their own payments, service role can manage all.';
