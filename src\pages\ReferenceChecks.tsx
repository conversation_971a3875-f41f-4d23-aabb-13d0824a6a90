import React from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CheckSquare, Lock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const ReferenceChecks = () => {
  const navigate = useNavigate();

  return (
    <div className="space-y-8">
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardHeader className="text-center">
          <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 mb-4">
            <CheckSquare className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl">Automated Reference Checks</CardTitle>
          <CardDescription className="text-lg">
            This feature is coming soon to Pro plan subscribers
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <div className="space-y-2">
            <p className="text-gray-600">
              The Automated Reference Checks feature will allow you to:
            </p>
            <ul className="text-left mx-auto max-w-md space-y-2 text-gray-600">
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Send automated reference check requests to candidate references</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Collect standardized feedback through customizable forms</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Track reference completion status in real-time</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Generate comprehensive reference summary reports</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Integrate reference feedback with candidate profiles</span>
              </li>
            </ul>
          </div>

          <div className="flex justify-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard/pricing')}
              className="gap-2"
            >
              <Lock className="h-4 w-4" />
              Upgrade to Pro
            </Button>
            <Button
              variant="default"
              onClick={() => navigate('/dashboard')}
            >
              Return to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Wrap the component with the DashboardLayout
const ReferenceChecksWithLayout = () => (
  <DashboardLayout>
    <ReferenceChecks />
  </DashboardLayout>
);

export default ReferenceChecksWithLayout;
