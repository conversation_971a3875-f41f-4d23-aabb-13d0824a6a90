import React, { useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import { useGSAP } from '@gsap/react';
import gsap from 'gsap';
import { FileText, Building, CheckCircle, BarChart2, ArrowRight, ArrowLeft } from 'lucide-react';

// Interactive CV Upload Demo
const CVUploadDemo: React.FC = () => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploaded, setIsUploaded] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [isComplete, setIsComplete] = useState(false);
  const progressRef = useRef<HTMLDivElement>(null);
  
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };
  
  const handleDragLeave = () => {
    setIsDragging(false);
  };
  
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    setIsUploaded(true);
    
    // Simulate processing
    setIsProcessing(true);
    
    // Animate progress bar
    if (progressRef.current) {
      gsap.fromTo(
        progressRef.current,
        { width: '0%' },
        { 
          width: '100%', 
          duration: 3,
          ease: 'power2.inOut',
          onComplete: () => {
            setIsProcessing(false);
            setIsComplete(true);
          }
        }
      );
    }
  };
  
  const resetDemo = () => {
    setIsUploaded(false);
    setIsProcessing(false);
    setIsComplete(false);
  };
  
  return (
    <div className="bg-card-gradient rounded-xl p-8 border border-gray-800 h-full">
      <div className="flex items-center mb-6">
        <div className="w-12 h-12 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
          <FileText className="h-6 w-6 text-blue-400" />
        </div>
        <h3 className="text-2xl font-bold text-white">CV Upload & Processing</h3>
      </div>
      
      <div className="mb-6">
        <p className="text-gray-300 mb-4">
          Upload CVs in multiple formats (PDF, DOCX, TXT) and our AI automatically extracts key information including skills, experience, and qualifications.
        </p>
      </div>
      
      <div className="mt-6">
        {!isUploaded ? (
          <div 
            className={`border-2 border-dashed ${isDragging ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'} rounded-lg p-8 text-center transition-colors duration-200`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-300 mb-2">Drag & drop your CV here</p>
            <p className="text-gray-500 text-sm">Supports PDF, DOCX, and TXT</p>
          </div>
        ) : (
          <div className="rounded-lg p-6 bg-gray-800/50">
            {isProcessing ? (
              <div>
                <div className="flex items-center justify-between mb-4">
                  <span className="text-gray-300">Processing CV...</span>
                  <span className="text-blue-400">Extracting data</span>
                </div>
                <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
                  <div ref={progressRef} className="h-full bg-blue-500 rounded-full" style={{ width: '0%' }}></div>
                </div>
              </div>
            ) : (
              <div>
                <div className="flex items-center mb-4">
                  <CheckCircle className="h-6 w-6 text-green-500 mr-2" />
                  <span className="text-green-400">CV processed successfully!</span>
                </div>
                
                <div className="bg-gray-900 rounded-lg p-4 mb-4">
                  <h4 className="text-white font-medium mb-2">Extracted Information:</h4>
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <p className="text-gray-500 text-xs">Name</p>
                      <p className="text-gray-300">John Smith</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-xs">Position</p>
                      <p className="text-gray-300">Senior Developer</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-xs">Experience</p>
                      <p className="text-gray-300">8 years</p>
                    </div>
                    <div>
                      <p className="text-gray-500 text-xs">Education</p>
                      <p className="text-gray-300">MSc Computer Science</p>
                    </div>
                  </div>
                  
                  <div className="mt-3">
                    <p className="text-gray-500 text-xs mb-1">Skills</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs">React</span>
                      <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">Node.js</span>
                      <span className="px-2 py-1 bg-purple-500/20 text-purple-400 rounded text-xs">TypeScript</span>
                      <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded text-xs">AWS</span>
                      <span className="px-2 py-1 bg-pink-500/20 text-pink-400 rounded text-xs">GraphQL</span>
                    </div>
                  </div>
                </div>
                
                <button 
                  onClick={resetDemo}
                  className="w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
                >
                  Try Again
                </button>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

// Interactive Evaluation Demo
const EvaluationDemo: React.FC = () => {
  const [step, setStep] = useState(0);
  const progressRef = useRef<HTMLDivElement>(null);
  const matchScoreRef = useRef<HTMLDivElement>(null);
  
  useGSAP(() => {
    if (step === 1 && progressRef.current) {
      gsap.fromTo(
        progressRef.current,
        { width: '0%' },
        { 
          width: '100%', 
          duration: 3,
          ease: 'power2.inOut',
          onComplete: () => {
            setStep(2);
          }
        }
      );
    }
    
    if (step === 2 && matchScoreRef.current) {
      gsap.fromTo(
        matchScoreRef.current,
        { height: '0%' },
        { 
          height: '85%', 
          duration: 1,
          ease: 'power2.out',
        }
      );
    }
  }, [step]);
  
  const startEvaluation = () => {
    setStep(1);
  };
  
  const resetDemo = () => {
    setStep(0);
  };
  
  return (
    <div className="bg-card-gradient rounded-xl p-8 border border-gray-800 h-full">
      <div className="flex items-center mb-6">
        <div className="w-12 h-12 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
          <CheckCircle className="h-6 w-6 text-green-400" />
        </div>
        <h3 className="text-2xl font-bold text-white">AI-Powered Evaluation</h3>
      </div>
      
      <div className="mb-6">
        <p className="text-gray-300 mb-4">
          Our advanced AI evaluates candidates against company requirements, providing percentage-based matching scores and detailed skill analysis.
        </p>
      </div>
      
      <div className="mt-6">
        {step === 0 && (
          <div className="rounded-lg p-6 bg-gray-800/50">
            <div className="flex mb-4">
              <div className="w-12 h-12 rounded-full bg-blue-500/20 flex-shrink-0 mr-3"></div>
              <div>
                <h4 className="text-white font-medium">John Smith</h4>
                <p className="text-gray-400 text-sm">Senior Developer</p>
              </div>
            </div>
            
            <div className="flex mb-4">
              <div className="w-12 h-12 rounded-full bg-purple-500/20 flex-shrink-0 mr-3"></div>
              <div>
                <h4 className="text-white font-medium">TechCorp Inc.</h4>
                <p className="text-gray-400 text-sm">Frontend Lead Position</p>
              </div>
            </div>
            
            <button 
              onClick={startEvaluation}
              className="w-full py-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white rounded-lg transition-colors"
            >
              Start Evaluation
            </button>
          </div>
        )}
        
        {step === 1 && (
          <div className="rounded-lg p-6 bg-gray-800/50">
            <div className="flex items-center justify-between mb-4">
              <span className="text-gray-300">Evaluating candidate...</span>
              <span className="text-green-400">AI processing</span>
            </div>
            <div className="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
              <div ref={progressRef} className="h-full bg-green-500 rounded-full" style={{ width: '0%' }}></div>
            </div>
            
            <div className="mt-4 space-y-2">
              <div className="flex items-center text-gray-400 text-sm">
                <div className="w-4 h-4 rounded-full border border-gray-600 flex items-center justify-center mr-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                </div>
                <span>Analyzing CV content</span>
              </div>
              <div className="flex items-center text-gray-400 text-sm">
                <div className="w-4 h-4 rounded-full border border-gray-600 flex items-center justify-center mr-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                </div>
                <span>Extracting skills and experience</span>
              </div>
              <div className="flex items-center text-gray-400 text-sm">
                <div className="w-4 h-4 rounded-full border border-gray-600 flex items-center justify-center mr-2">
                  <div className="w-2 h-2 rounded-full bg-green-500"></div>
                </div>
                <span>Comparing with job requirements</span>
              </div>
              <div className="flex items-center text-gray-400 text-sm">
                <div className="w-4 h-4 rounded-full border border-gray-600 flex items-center justify-center mr-2">
                  <div className="w-2 h-2 rounded-full bg-blue-500 animate-pulse"></div>
                </div>
                <span>Generating match score</span>
              </div>
            </div>
          </div>
        )}
        
        {step === 2 && (
          <div className="rounded-lg p-6 bg-gray-800/50">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h4 className="text-white font-medium">Match Score</h4>
                <p className="text-gray-400 text-sm">John Smith → TechCorp Inc.</p>
              </div>
              <div className="w-16 h-16 rounded-full bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center text-white font-bold">
                85%
              </div>
            </div>
            
            <div className="mb-6">
              <h5 className="text-white text-sm mb-2">Skills Match</h5>
              <div className="flex h-24 space-x-3">
                <div className="w-1/5 bg-gray-700 rounded-lg relative overflow-hidden">
                  <div ref={matchScoreRef} className="absolute bottom-0 w-full bg-blue-500 rounded-b-lg" style={{ height: '0%' }}></div>
                  <div className="absolute inset-0 flex flex-col items-center justify-between p-2">
                    <span className="text-white text-xs">React</span>
                    <span className="text-white text-xs font-bold">85%</span>
                  </div>
                </div>
                <div className="w-1/5 bg-gray-700 rounded-lg relative overflow-hidden">
                  <div className="absolute bottom-0 w-full bg-green-500 rounded-b-lg" style={{ height: '90%' }}></div>
                  <div className="absolute inset-0 flex flex-col items-center justify-between p-2">
                    <span className="text-white text-xs">TypeScript</span>
                    <span className="text-white text-xs font-bold">90%</span>
                  </div>
                </div>
                <div className="w-1/5 bg-gray-700 rounded-lg relative overflow-hidden">
                  <div className="absolute bottom-0 w-full bg-yellow-500 rounded-b-lg" style={{ height: '75%' }}></div>
                  <div className="absolute inset-0 flex flex-col items-center justify-between p-2">
                    <span className="text-white text-xs">Node.js</span>
                    <span className="text-white text-xs font-bold">75%</span>
                  </div>
                </div>
                <div className="w-1/5 bg-gray-700 rounded-lg relative overflow-hidden">
                  <div className="absolute bottom-0 w-full bg-purple-500 rounded-b-lg" style={{ height: '60%' }}></div>
                  <div className="absolute inset-0 flex flex-col items-center justify-between p-2">
                    <span className="text-white text-xs">GraphQL</span>
                    <span className="text-white text-xs font-bold">60%</span>
                  </div>
                </div>
                <div className="w-1/5 bg-gray-700 rounded-lg relative overflow-hidden">
                  <div className="absolute bottom-0 w-full bg-pink-500 rounded-b-lg" style={{ height: '80%' }}></div>
                  <div className="absolute inset-0 flex flex-col items-center justify-between p-2">
                    <span className="text-white text-xs">AWS</span>
                    <span className="text-white text-xs font-bold">80%</span>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="mb-4">
              <h5 className="text-white text-sm mb-2">AI Insights</h5>
              <div className="bg-gray-900 rounded-lg p-3 text-gray-300 text-sm">
                <p>Strong match for the Frontend Lead position. Candidate has excellent React and TypeScript skills with good experience in GraphQL. Consider discussing AWS deployment experience during the interview.</p>
              </div>
            </div>
            
            <button 
              onClick={resetDemo}
              className="w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            >
              Try Again
            </button>
          </div>
        )}
      </div>
    </div>
  );
};

// Main Interactive Feature Showcase Component
export const InteractiveFeatureShowcase: React.FC = () => {
  const [activeDemo, setActiveDemo] = useState<'upload' | 'evaluation'>('upload');
  const containerRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(containerRef, { once: true, amount: 0.2 });
  
  return (
    <section className="py-24 bg-recruiter-darknavy overflow-hidden">
      <div className="container mx-auto px-6">
        <motion.div 
          ref={containerRef}
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-white mb-4">
            See How It Works
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our platform streamlines the recruitment process with powerful AI-driven tools.
            Try the interactive demos below to see it in action.
          </p>
        </motion.div>
        
        <div className="flex flex-col lg:flex-row gap-8 items-stretch">
          <motion.div 
            initial={{ opacity: 0, x: -50 }}
            animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="lg:w-1/3"
          >
            <div className="bg-card-gradient rounded-xl p-6 border border-gray-800 h-full">
              <h3 className="text-2xl font-bold text-white mb-6">Interactive Demos</h3>
              
              <div className="space-y-4">
                <button
                  onClick={() => setActiveDemo('upload')}
                  className={`w-full p-4 rounded-lg flex items-center text-left transition-colors ${
                    activeDemo === 'upload' 
                      ? 'bg-blue-500/20 border border-blue-500/50' 
                      : 'bg-gray-800/50 hover:bg-gray-700/50'
                  }`}
                >
                  <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-4">
                    <FileText className="h-5 w-5 text-blue-400" />
                  </div>
                  <div>
                    <h4 className="text-white font-medium">CV Upload & Processing</h4>
                    <p className="text-gray-400 text-sm">See how our AI extracts CV data</p>
                  </div>
                </button>
                
                <button
                  onClick={() => setActiveDemo('evaluation')}
                  className={`w-full p-4 rounded-lg flex items-center text-left transition-colors ${
                    activeDemo === 'evaluation' 
                      ? 'bg-green-500/20 border border-green-500/50' 
                      : 'bg-gray-800/50 hover:bg-gray-700/50'
                  }`}
                >
                  <div className="w-10 h-10 rounded-full bg-green-500/20 flex items-center justify-center mr-4">
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  </div>
                  <div>
                    <h4 className="text-white font-medium">AI-Powered Evaluation</h4>
                    <p className="text-gray-400 text-sm">Experience our matching algorithm</p>
                  </div>
                </button>
              </div>
              
              <div className="mt-8">
                <p className="text-gray-300 text-sm mb-4">
                  Our platform uses advanced AI to analyze CVs and match candidates to job requirements with high accuracy.
                </p>
                <div className="flex items-center text-blue-400 text-sm">
                  <span>Learn more about our technology</span>
                  <ArrowRight className="h-4 w-4 ml-2" />
                </div>
              </div>
            </div>
          </motion.div>
          
          <motion.div 
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 50 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="lg:w-2/3"
          >
            {activeDemo === 'upload' ? <CVUploadDemo /> : <EvaluationDemo />}
          </motion.div>
        </div>
      </div>
    </section>
  );
};
