import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate, useLocation } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useToast } from '@/hooks/use-toast';
import { showRateLimitToast, isRateLimitError, getErrorMessage } from '@/components/ui/rate-limit-toast';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from '@/components/ui/select';
import EvaluateModal from '@/components/cv/EvaluateModal';
import { getCandidate, updateCandidate } from '@/services/supabase/candidates';
import { getCandidateEvaluations, getEvaluation } from '@/services/supabase/evaluations';
import { supabase } from '@/lib/supabase';

// Import components
import CandidateHeader from '@/components/candidate/CandidateHeader';
import CandidateTabs from '@/components/candidate/CandidateTabs';
import LoadingState from '@/components/candidate/LoadingState';
import NotFoundState from '@/components/candidate/NotFoundState';
import StatusSelector from '@/components/candidate/StatusSelector';
import ScheduleInterviewModal from '@/components/interviews/ScheduleInterviewModal';

// Import helper functions
import {
  generateCandidateNotes,
  formatDate,
  getStatusColor,
  getProgressColor,
  getMatchScoreColor,
  extractSkillNamesFromText
} from '@/utils/candidateUtils';

// Import evaluation utilities
import { generateEvaluationData } from '@/utils/evaluationUtils';

// Mock data for fallback
const mockCandidate = {
  id: 'mock-id',
  name: 'John Doe',
  email: '<EMAIL>',
  phone: '+****************',
  location: 'New York, NY',
  position: 'Software Developer',
  status: 'new',
  created_at: new Date().toISOString(),
  avatar: 'https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png',
  notes: []
};

const mockEvaluationData = {
  candidateName: 'John Doe',
  position: 'Software Developer',
  overallMatch: 75,
  skills: [
    { name: 'JavaScript', match: 85, required: true },
    { name: 'React', match: 80, required: true },
    { name: 'TypeScript', match: 70, required: false }
  ],
  experience: [
    { title: 'Frontend Developer', company: 'Tech Co', duration: '2 years', relevance: 85 }
  ],
  education: [
    { degree: 'BS Computer Science', institution: 'University', year: '2020', relevance: 90 }
  ],
  strengths: ['Strong frontend skills', 'Good problem solver'],
  weaknesses: ['Limited backend experience']
};













const CandidateStatus = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const location = useLocation();
  const { toast } = useToast();

  // State for candidate data
  const [candidate, setCandidate] = useState<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [status, setStatus] = useState<string>('new');
  const [parsedCV, setParsedCV] = useState<any>(null);

  // State for evaluations
  const [evaluations, setEvaluations] = useState<any[]>([]);
  const [selectedEvaluation, setSelectedEvaluation] = useState<any>(null);
  const [isLoadingEvaluations, setIsLoadingEvaluations] = useState<boolean>(false);
  const [evaluationData, setEvaluationData] = useState<any>(null);

  // State for notes
  const [newNote, setNewNote] = useState<string>('');
  const [isSubmittingNote, setIsSubmittingNote] = useState<boolean>(false);

  // Parse tab from URL query parameters
  const queryParams = new URLSearchParams(location.search);
  const tabParam = queryParams.get('tab');

  // State for tabs - default to 'overview' or use the tab from URL if valid
  const [activeTab, setActiveTab] = useState<string>(
    ['overview', 'cv-details', 'evaluation', 'notes'].includes(tabParam || '')
      ? tabParam!
      : 'overview'
  );

  // State for evaluate modal
  const [isEvaluateModalOpen, setIsEvaluateModalOpen] = useState<boolean>(false);

  // State for schedule interview modal
  const [isScheduleInterviewModalOpen, setIsScheduleInterviewModalOpen] = useState<boolean>(false);

  // Function to fetch evaluations
  const fetchEvaluations = async () => {
    if (!id) return;

    setIsLoadingEvaluations(true);
    try {
      const evaluationsData = await getCandidateEvaluations(id);
      setEvaluations(evaluationsData);

      // If there are evaluations, select the most recent one by default
      if (evaluationsData.length > 0) {
        setSelectedEvaluation(evaluationsData[0]);
      }
    } catch (error) {
      console.error('Error fetching evaluations:', error);
      toast({
        title: 'Error',
        description: 'Failed to load evaluation history.',
        variant: 'destructive'
      });
    } finally {
      setIsLoadingEvaluations(false);
    }
  };

  // Function to handle evaluation selection
  const handleEvaluationSelect = async (evaluationId: string) => {
    try {
      const evaluation = await getEvaluation(evaluationId);
      if (evaluation) {
        // Parse the evaluation summary to get the match result
        try {
          const summary = JSON.parse(evaluation.evaluation_summary);
          const matchResult = summary.matchResult;

          if (matchResult) {
            // Extract job-specific matches if available
            const jobSpecificMatches = matchResult.jobSpecificMatches || [];
            const topMatchingJobs = matchResult.topMatchingJobs || [];
            const isCompanyEvaluation =
              evaluation.evaluation_mode === 'all-company-jobs' ||
              evaluation.evaluation_mode === 'all-companies';

            // Add parsed summary to the evaluation object
            const enhancedEvaluation = {
              ...evaluation,
              parsedSummary: {
                jobSpecificMatches,
                topMatchingJobs,
                isCompanyEvaluation
              }
            };

            setSelectedEvaluation(enhancedEvaluation);

            // Generate evaluation data for the CV evaluation component
            const directEvaluationData = {
              candidateName: candidate.name,
              position: evaluation.job_title || 'Position Not Specified',
              overallMatch: matchResult.overallScore || evaluation.evaluation_score,
              skills: matchResult.skillsMatch?.skills || [],
              experience: matchResult.experienceMatch?.details || [],
              education: matchResult.educationMatch?.details || [],
              strengths: matchResult.strengths || [],
              weaknesses: matchResult.gaps || [],
              isCompanyEvaluation,
              jobSpecificMatches,
              topMatchingJobs
            };

            setEvaluationData(directEvaluationData);
          } else {
            setSelectedEvaluation(evaluation);
          }
        } catch (error) {
          console.error('Error parsing evaluation summary:', error);
          setSelectedEvaluation(evaluation);
        }
      }
    } catch (error) {
      console.error('Error fetching evaluation:', error);

      // Check if it's a rate limiting error
      const rateLimit = isRateLimitError(error);
      const errorMessage = getErrorMessage(error);

      if (rateLimit) {
        showRateLimitToast({
          message: errorMessage,
          onRetry: () => handleEvaluationSelect(evaluationId)
        });
      } else {
        toast({
          title: 'Error',
          description: errorMessage,
          variant: 'destructive'
        });
      }
    }
  };

  // Fetch candidate data
  useEffect(() => {
    const fetchCandidate = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const candidateData = await getCandidate(id);

        if (!candidateData) {
          toast({
            title: 'Error',
            description: 'Candidate not found. Using mock data instead.',
            variant: 'destructive'
          });
          setCandidate(mockCandidate);
          setStatus(mockCandidate.status);
          setEvaluationData(mockEvaluationData);
          return;
        }

        // Process the candidate data
        let parsedCV = null;
        let extractedSkills = null;
        let position = '';
        let location = '';
        let matchScore = candidateData.evaluation_score || null;

        if (candidateData.evaluation_summary) {
          try {
            const summary = JSON.parse(candidateData.evaluation_summary);
            console.log('Parsed evaluation summary:', summary);

            if (summary.parsedCV) {
              parsedCV = summary.parsedCV;
              console.log('Work Experience data:', parsedCV.workExperience);
              console.log('Education data:', parsedCV.education);

              // Extract position from work experience if available
              if (parsedCV.workExperience && parsedCV.workExperience.length > 0) {
                // Check for different property names
                const firstExp = parsedCV.workExperience[0];
                position = firstExp.title || firstExp.jobTitle || '';
                console.log('Extracted position:', position);
              }

              // Extract location
              if (parsedCV.personalDetails && parsedCV.personalDetails.location) {
                location = parsedCV.personalDetails.location;
              }
            }

            if (summary.extractedSkills) {
              extractedSkills = summary.extractedSkills;
            }

            // Create evaluation data for the CV evaluation component
            // Check if we have a matchResult in the evaluation summary
            let matchResult = null;

            // First, check if matchResult is directly in the summary
            if (summary.matchResult) {
              matchResult = summary.matchResult;
              console.log("Found match result in evaluation summary.matchResult:", matchResult);
            }
            // If not, check if the summary itself is the match result (has the expected properties)
            else if (summary.overallScore !== undefined &&
                    (summary.skillsMatch || summary.strengths || summary.gaps)) {
              matchResult = summary;
              console.log("Using summary itself as match result:", matchResult);
            }
            // If still not found, check if parsedCV exists and try to extract from there
            else if (summary.parsedCV) {
              parsedCV = summary.parsedCV;
              console.log("Found parsedCV in evaluation summary:", parsedCV);

              // If we have both parsedCV and matchResult in the same object
              if (summary.parsedCV && summary.matchResult) {
                matchResult = summary.matchResult;
                console.log("Found match result alongside parsedCV:", matchResult);
              }
            }

            if (matchResult) {
              // If we have a match result, use it directly to create evaluation data
              console.log("Creating evaluation data from match result:", matchResult);

              // Try to extract position from job title if available
              if (!position && candidateData.job_id) {
                try {
                  const { data: job } = await supabase
                    .from('jobs')
                    .select('title')
                    .eq('id', candidateData.job_id)
                    .single();

                  if (job && job.title) {
                    position = job.title;
                  }
                } catch (e) {
                  console.error('Error fetching job title:', e);
                }
              }

              const directEvaluationData = {
                candidateName: candidateData.name,
                position: position || 'Position Not Specified',
                overallMatch: matchResult.overallScore || matchScore,

                // Use skills directly from the match result if available
                skills: (() => {
                  console.log("Processing skills from matchResult:", matchResult);

                  // First, check if we have skills in the structured format
                  if (matchResult.skillsMatch && matchResult.skillsMatch.skills && matchResult.skillsMatch.skills.length > 0) {
                    console.log("Using structured skills from matchResult.skillsMatch.skills:", matchResult.skillsMatch.skills);
                    return matchResult.skillsMatch.skills.map((skill: any) => ({
                      name: skill.name,
                      match: skill.match || skill.score || 0,
                      required: skill.required || false
                    }));
                  }

                  // If no structured skills, try to extract from analysis
                  if (matchResult.skillsMatch && matchResult.skillsMatch.analysis) {
                    const skillNames = extractSkillNamesFromText(matchResult.skillsMatch.analysis);
                    if (skillNames.length > 0) {
                      return skillNames.map(name => ({
                        name,
                        match: matchResult.skillsMatch.score || 70,
                        required: false
                      }));
                    }
                  }

                  // If still no skills, use extracted skills from the CV
                  if (parsedCV && parsedCV.skills && parsedCV.skills.technical) {
                    return parsedCV.skills.technical.map((skill: any) => ({
                      name: typeof skill === 'string' ? skill : skill.name,
                      match: matchResult.overallScore ? Math.round(matchResult.overallScore * 0.9) : 0,
                      required: false
                    }));
                  }

                  // Last resort: check if extractedSkills exists
                  if (extractedSkills && extractedSkills.technical) {
                    return extractedSkills.technical.map((skill: any) => ({
                      name: typeof skill === 'string' ? skill : skill.name,
                      match: matchResult.overallScore ? Math.round(matchResult.overallScore * 0.9) : 0,
                      required: false
                    }));
                  }

                  return [];
                })(),

                // Use experience details directly from the match result if available
                experience: (() => {
                  console.log("Processing experience from matchResult:", matchResult.experienceMatch);

                  // First, check if we have experience details in the structured format
                  if (matchResult.experienceMatch && matchResult.experienceMatch.details && matchResult.experienceMatch.details.length > 0) {
                    console.log("Using structured experience from matchResult.experienceMatch.details:", matchResult.experienceMatch.details);
                    return matchResult.experienceMatch.details.map((exp: any) => ({
                      title: exp.title || 'Unknown Position',
                      company: exp.company || 'Unknown Company',
                      duration: exp.duration || 'Unknown Duration',
                      relevance: exp.relevance || 0
                    }));
                  }

                  // If no structured experience, use from parsedCV
                  if (parsedCV && parsedCV.workExperience) {
                    return parsedCV.workExperience.map((exp: any) => {
                      // Handle different property names in the work experience data
                      const title = exp.title || exp.jobTitle || 'Unknown Position';
                      const company = exp.company || 'Unknown Company';

                      // Calculate duration based on different date formats
                      let duration = '';
                      if (exp.startDate && exp.endDate) {
                        duration = `${exp.startDate} - ${exp.endDate}`;
                      } else if (exp.dates) {
                        duration = exp.dates;
                      } else if (exp.startDate) {
                        duration = `${exp.startDate} - Present`;
                      } else {
                        duration = 'Unknown duration';
                      }

                      // Calculate relevance based on match result
                      let relevance = matchResult.experienceMatch ? matchResult.experienceMatch.score : 70;

                      return {
                        title,
                        company,
                        duration,
                        relevance
                      };
                    });
                  }

                  return [];
                })(),

                // Use education details directly from the match result if available
                education: (() => {
                  console.log("Processing education from matchResult:", matchResult.educationMatch);

                  // First, check if we have education details in the structured format
                  if (matchResult.educationMatch && matchResult.educationMatch.details && matchResult.educationMatch.details.length > 0) {
                    console.log("Using structured education from matchResult.educationMatch.details:", matchResult.educationMatch.details);
                    return matchResult.educationMatch.details.map((edu: any) => ({
                      degree: edu.degree || 'Unknown Degree',
                      institution: edu.institution || 'Unknown Institution',
                      year: edu.year || 'Unknown Year',
                      relevance: edu.relevance || 0
                    }));
                  }

                  // If no structured education, use from parsedCV
                  if (parsedCV && parsedCV.education) {
                    return parsedCV.education.map((edu: any) => {
                      // Handle different property names in the education data
                      const degree = edu.degree || edu.qualification || 'Unknown Degree';
                      const institution = edu.institution || edu.school || edu.university || 'Unknown Institution';

                      // Handle different date formats
                      let year = '';
                      if (edu.dates) {
                        year = edu.dates;
                      } else if (edu.endDate) {
                        year = edu.endDate;
                      } else if (edu.graduationYear) {
                        year = edu.graduationYear.toString();
                      } else if (edu.startDate) {
                        year = `${edu.startDate} - Present`;
                      } else {
                        year = 'N/A';
                      }

                      // Calculate relevance based on match result
                      let relevance = matchResult.educationMatch ? matchResult.educationMatch.score : 70;

                      return {
                        degree,
                        institution,
                        year,
                        relevance
                      };
                    });
                  }

                  return [];
                })(),

                // Use strengths directly from match result
                strengths: (() => {
                  console.log("Processing strengths from matchResult:", matchResult.strengths);
                  if (Array.isArray(matchResult.strengths) && matchResult.strengths.length > 0) {
                    return matchResult.strengths;
                  }
                  // Fallback: try to extract strengths from recommendation or analysis
                  if (matchResult.recommendation) {
                    const strengthsFromRec = matchResult.recommendation.match(/strengths include ([^\.]+)/i);
                    if (strengthsFromRec && strengthsFromRec[1]) {
                      return strengthsFromRec[1].split(/,\s*|\s+and\s+/).map(s => s.trim());
                    }
                  }
                  return [];
                })(),

                // Use gaps as weaknesses
                weaknesses: (() => {
                  console.log("Processing gaps from matchResult:", matchResult.gaps);
                  if (Array.isArray(matchResult.gaps) && matchResult.gaps.length > 0) {
                    return matchResult.gaps;
                  }
                  // Fallback: try to extract weaknesses from recommendation or analysis
                  if (matchResult.recommendation) {
                    const weaknessesFromRec = matchResult.recommendation.match(/areas for improvement include ([^\.]+)/i);
                    if (weaknessesFromRec && weaknessesFromRec[1]) {
                      return weaknessesFromRec[1].split(/,\s*|\s+and\s+/).map(s => s.trim());
                    }
                  }
                  return [];
                })(),

                // Add enhanced evaluation properties
                isCompanyEvaluation: matchResult.isCompanyEvaluation || false,
                jobSpecificMatches: matchResult.jobSpecificMatches || [],
                topMatchingJobs: matchResult.topMatchingJobs || []
              };

              console.log("Generated direct evaluation data from match result:", directEvaluationData);
              setEvaluationData(directEvaluationData);
            } else if (parsedCV && extractedSkills) {
              // Generate real evaluation data based on the candidate's information
              const evaluationData = generateEvaluationData(candidateData, parsedCV, extractedSkills, position, matchScore);
              console.log("Generated real evaluation data:", evaluationData);
              setEvaluationData(evaluationData);
            } else if (matchScore) {
              // If we have a match score but no parsed CV or extracted skills, create minimal evaluation data
              // Try to extract data from the raw evaluation summary string
              let extractedData = {
                skills: [],
                experience: [],
                education: [],
                strengths: [],
                weaknesses: []
              };

              try {
                // Try to extract structured data from the raw evaluation summary
                if (candidateData.evaluation_summary) {
                  const rawSummary = candidateData.evaluation_summary;

                  // Look for skills section
                  const skillsMatch = rawSummary.match(/skills[:\s]+(.*?)(?=experience|education|strengths|gaps|recommendation|$)/is);
                  if (skillsMatch && skillsMatch[1]) {
                    const skillNames = extractSkillNamesFromText(skillsMatch[1]);
                    if (skillNames.length > 0) {
                      extractedData.skills = skillNames.map(name => ({
                        name,
                        match: matchScore || 70,
                        required: false
                      }));
                    }
                  }

                  // Look for strengths
                  const strengthsMatch = rawSummary.match(/strengths[:\s]+(.*?)(?=gaps|weaknesses|recommendation|$)/is);
                  if (strengthsMatch && strengthsMatch[1]) {
                    extractedData.strengths = strengthsMatch[1]
                      .split(/[\n\r]+|,\s*|\s+and\s+/)
                      .map(s => s.trim())
                      .filter(s => s.length > 0);
                  }

                  // Look for gaps/weaknesses
                  const gapsMatch = rawSummary.match(/(?:gaps|weaknesses|areas for improvement)[:\s]+(.*?)(?=strengths|recommendation|$)/is);
                  if (gapsMatch && gapsMatch[1]) {
                    extractedData.weaknesses = gapsMatch[1]
                      .split(/[\n\r]+|,\s*|\s+and\s+/)
                      .map(s => s.trim())
                      .filter(s => s.length > 0);
                  }
                }
              } catch (e) {
                console.error('Error extracting data from raw evaluation summary:', e);
              }

              const minimalEvaluationData = {
                candidateName: candidateData.name,
                position: position || 'Position Not Specified',
                overallMatch: matchScore,
                skills: extractedData.skills.length > 0 ? extractedData.skills : [],
                experience: extractedData.experience.length > 0 ? extractedData.experience : [],
                education: extractedData.education.length > 0 ? extractedData.education : [],
                strengths: extractedData.strengths.length > 0 ? extractedData.strengths : [],
                weaknesses: extractedData.weaknesses.length > 0 ? extractedData.weaknesses : []
              };
              console.log("Generated minimal evaluation data:", minimalEvaluationData);
              setEvaluationData(minimalEvaluationData);
            } else {
              // Only use mock data as a last resort
              console.log("No evaluation data available, not using mock data");
              setEvaluationData(null);
            }
          } catch (e) {
            console.error('Error parsing evaluation summary:', e);
            console.error('Raw evaluation summary:', candidateData.evaluation_summary);
            setEvaluationData(mockEvaluationData);
          }
        } else {
          setEvaluationData(mockEvaluationData);
        }

        // Try to load notes from the database first, or generate them if not available
        let notes = [];

        try {
          // Check if notes field exists and is not empty
          if (candidateData.notes) {
            try {
              // Parse the notes from the JSON string
              const parsedNotes = JSON.parse(candidateData.notes);

              // Validate that the parsed notes is an array
              if (Array.isArray(parsedNotes)) {
                notes = parsedNotes;
                console.log('Loaded notes from database:', notes);
              } else {
                console.warn('Notes field is not an array, generating new notes');
                notes = generateCandidateNotes(candidateData, parsedCV);
              }
            } catch (parseError) {
              console.error('Error parsing notes JSON:', parseError);

              // If the notes field is a string but not JSON, try to use it as a single note
              if (typeof candidateData.notes === 'string' && candidateData.notes.trim()) {
                const singleNote = {
                  id: `note_legacy_${Date.now()}`,
                  date: candidateData.created_at,
                  author: 'System',
                  authorAvatar: `https://i.pravatar.cc/150?u=${candidateData.id}_legacy`,
                  content: candidateData.notes,
                  status: candidateData.status
                };
                notes = [singleNote];
                console.log('Created single note from notes field:', notes);
              } else {
                // Generate new notes if parsing fails
                notes = generateCandidateNotes(candidateData, parsedCV);
                console.log('Generated new notes due to parsing error:', notes);
              }
            }
          } else {
            // Generate notes if none exist in the database
            notes = generateCandidateNotes(candidateData, parsedCV);
            console.log('Generated notes (no existing notes):', notes);
          }
        } catch (error) {
          console.error('Error handling candidate notes:', error);
          // Fallback to generated notes
          notes = generateCandidateNotes(candidateData, parsedCV);
        }

        // Enhance candidate data with additional fields
        const enhancedCandidate = {
          ...candidateData,
          position: position || 'Position Not Specified',
          location: location || 'Location Not Specified',
          matchScore: matchScore,
          avatar: "https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png",
          notes: notes
        };

        setCandidate(enhancedCandidate);
        setStatus(candidateData.status);
        setParsedCV(parsedCV); // Set the parsed CV data

        // Fetch evaluations after candidate data is loaded
        fetchEvaluations();
      } catch (error) {
        console.error('Error fetching candidate:', error);
        toast({
          title: 'Error',
          description: 'Failed to load candidate data. Using mock data instead.',
          variant: 'destructive'
        });

        // Use mock candidate notes
        const notes = mockCandidate.notes;

        // Use enhanced mock candidate with generated data
        const enhancedMockCandidate = {
          ...mockCandidate,
          notes: notes
        };

        setCandidate(enhancedMockCandidate);
        setStatus(mockCandidate.status);
        setEvaluationData(mockEvaluationData);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCandidate();
  }, [id, toast]);

  // Handle status change
  const handleStatusChange = async (newStatus: string) => {
    if (!candidate || !id) return;

    try {
      setStatus(newStatus);

      // Update the candidate status in the database
      await updateCandidate(id, { status: newStatus });

      toast({
        title: 'Status updated',
        description: `Candidate status has been changed to ${newStatus}.`,
      });
    } catch (error) {
      console.error('Error updating candidate status:', error);
      toast({
        title: 'Error',
        description: 'Failed to update candidate status. Please try again.',
        variant: 'destructive'
      });

      // Revert to the previous status
      setStatus(candidate.status);
    }
  };

  // Handle adding a new note
  const handleAddNote = async () => {
    if (!newNote.trim() || !candidate || !id) {
      toast({
        title: 'Note required',
        description: 'Please enter a note before submitting.',
        variant: 'destructive',
      });
      return;
    }

    setIsSubmittingNote(true);

    try {
      // Get user's name from localStorage or use default
      let currentUser = 'Recruiter';
      try {
        const userDataStr = localStorage.getItem('userData');
        if (userDataStr) {
          const userData = JSON.parse(userDataStr);
          currentUser = userData.full_name || userData.email || 'Recruiter';
        }
      } catch (e) {
        console.error('Error getting user data from localStorage:', e);
      }

      // Create a new note
      const newNoteObj = {
        id: `note_${Date.now()}`,
        date: new Date().toISOString(),
        author: currentUser,
        authorAvatar: `https://i.pravatar.cc/150?u=${candidate.id}_${candidate.notes.length + 1}`,
        content: newNote,
        status: status
      };

      // Add the note to the candidate's notes array
      const updatedNotes = [newNoteObj, ...(candidate.notes || [])];

      // Update the candidate in the local state
      const updatedCandidate = {
        ...candidate,
        notes: updatedNotes
      };

      setCandidate(updatedCandidate);

      // Store the notes as a JSON string in the notes field
      // This is a workaround since we don't have a dedicated notes table
      const notesJson = JSON.stringify(updatedNotes);

      // Update the candidate in the database
      await updateCandidate(id, {
        notes: notesJson
      });

      toast({
        title: 'Note added',
        description: 'Your note has been added successfully.',
      });
    } catch (error) {
      console.error('Error adding note:', error);
      toast({
        title: 'Error',
        description: 'Failed to add note. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setNewNote('');
      setIsSubmittingNote(false);
    }
  };

  // Handle successful evaluation
  const handleEvaluationSuccess = (score: number) => {
    // Update the candidate's match score
    if (candidate) {
      const updatedCandidate = {
        ...candidate,
        matchScore: score
      };
      setCandidate(updatedCandidate);

      // Refresh the evaluations list
      fetchEvaluations();

      // Switch to the Evaluation tab and update URL
      setActiveTab('evaluation');
      navigate(`/dashboard/cvs/${id}?tab=evaluation`, { replace: true });
    }
  };

  // Get match score color
  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 75) return 'text-blue-500';
    if (score >= 60) return 'text-amber-500';
    return 'text-red-500';
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {isLoading ? (
          <LoadingState />
        ) : !candidate ? (
          <NotFoundState />
        ) : (
          <>
            <CandidateHeader
              candidate={candidate}
              status={status}
              handleStatusChange={handleStatusChange}
              setIsEvaluateModalOpen={setIsEvaluateModalOpen}
              getStatusColor={getStatusColor}
              getMatchScoreColor={getMatchScoreColor}
            />

            <CandidateTabs
              activeTab={activeTab}
              setActiveTab={(tab) => {
                setActiveTab(tab);
                // Update URL with the new tab
                navigate(`/dashboard/cvs/${id}?tab=${tab}`, { replace: true });
              }}
              candidate={candidate}
              evaluations={evaluations}
              selectedEvaluation={selectedEvaluation}
              evaluationData={evaluationData}
              isLoadingEvaluations={isLoadingEvaluations}
              handleEvaluationSelect={handleEvaluationSelect}
              handleAddNote={handleAddNote}
              newNote={newNote}
              setNewNote={setNewNote}
              isSubmittingNote={isSubmittingNote}
              setIsEvaluateModalOpen={setIsEvaluateModalOpen}
              setIsScheduleInterviewModalOpen={setIsScheduleInterviewModalOpen}
              status={status}
              getStatusColor={getStatusColor}
              getProgressColor={getProgressColor}
              getMatchScoreColor={getMatchScoreColor}
              parsedCV={parsedCV}
            />

            <EvaluateModal
              isOpen={isEvaluateModalOpen}
              onClose={() => setIsEvaluateModalOpen(false)}
              candidateId={id || ''}
              candidateName={candidate?.name || 'Candidate'}
              onSuccess={handleEvaluationSuccess}
            />

            <ScheduleInterviewModal
              isOpen={isScheduleInterviewModalOpen}
              onClose={() => setIsScheduleInterviewModalOpen(false)}
              preselectedCandidateId={id || ''}
              candidateName={candidate?.name || 'Candidate'}
            />
          </>
        )}
      </div>
    </DashboardLayout>
  );
}

export default CandidateStatus;
