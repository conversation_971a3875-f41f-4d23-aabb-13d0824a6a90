import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

export type Company = Database['public']['Tables']['companies']['Row'];
export type CompanyInsert = Database['public']['Tables']['companies']['Insert'];
export type CompanyUpdate = Database['public']['Tables']['companies']['Update'];

/**
 * Get all companies for the current user (owned + team member)
 */
export const getCompanies = async (): Promise<Company[]> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // First get companies owned by the user
      const { data: ownedCompanies, error: ownedError } = await supabase
        .from('companies')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (ownedError) throw ownedError;

      // Then get companies where user is a team member
      const { data: teamMemberCompanies, error: teamError } = await supabase
        .from('team_members')
        .select(`
          companies (
            id,
            created_at,
            name,
            description,
            logo_url,
            website,
            industry,
            size,
            location,
            user_id
          )
        `)
        .eq('user_id', userId)
        .eq('status', 'active');

      if (teamError) throw teamError;

      // Combine and deduplicate companies
      const allCompanies = [...(ownedCompanies || [])];
      
      if (teamMemberCompanies) {
        teamMemberCompanies.forEach(tm => {
          if (tm.companies && !allCompanies.find(c => c.id === tm.companies.id)) {
            allCompanies.push(tm.companies);
          }
        });
      }

      return allCompanies.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
    },
    'Failed to fetch companies',
    [] // Return empty array as fallback
  );
};

/**
 * Get a company by ID
 */
export const getCompany = async (id: string): Promise<Company | null> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Get company - RLS policies will handle access control
      const { data, error } = await supabase
        .from('companies')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        // If no rows found, return null instead of throwing an error
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      return data;
    },
    `Failed to fetch company with ID ${id}`,
    null // Return null as fallback
  );
};

/**
 * Create a new company
 */
export const createCompany = async (company: CompanyInsert, checkUsageLimit = true): Promise<Company> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // If usage limit checking is enabled
      if (checkUsageLimit) {
        // Get user's subscription tier
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('subscription_tier, subscription_status')
          .eq('user_id', userId)
          .single();

        if (profileError) throw profileError;

        // Check if subscription is active
        if (profile.subscription_status !== 'active') {
          throw new Error('Your subscription is not active. Please renew your subscription to create company profiles.');
        }

        // Get current company profiles count
        const { data: companies, error: companiesError } = await supabase
          .from('companies')
          .select('id')
          .eq('user_id', userId);

        if (companiesError) throw companiesError;

        // Get subscription limits
        const { data: limits, error: limitsError } = await supabase
          .from('subscription_limits')
          .select('company_profile_limit')
          .eq('tier', profile.subscription_tier)
          .single();

        if (limitsError) throw limitsError;

        // Check if user has reached their limit
        const companyCount = companies?.length || 0;
        if (companyCount >= limits.company_profile_limit) {
          throw new Error(`You have reached your company profile limit (${limits.company_profile_limit}). Please upgrade your plan to create more company profiles.`);
        }
      }

      // Create the company
      const { data, error } = await supabase
        .from('companies')
        .insert(company)
        .select()
        .single();

      if (error) throw error;

      // If usage limit checking is enabled, increment the usage counter
      if (checkUsageLimit) {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

        try {
          // Get current usage
          const { data: usage, error: usageError } = await supabase
            .from('usage_tracking')
            .select('*')
            .eq('user_id', userId)
            .eq('month_year', currentMonth)
            .single();

          if (usageError) {
            if (usageError.code === 'PGRST116') {
              // Record doesn't exist, create a new one with the correct count
              await supabase
                .from('usage_tracking')
                .insert({
                  user_id: userId,
                  month_year: currentMonth,
                  cv_uploads_count: 0,
                  active_jobs_count: 0,
                  company_profiles_count: 1, // Start with 1 for the new company
                  team_members_count: 0,
                });
            } else {
              console.error('Error fetching usage data:', usageError);
            }
          } else if (usage) {
            // Increment the company profiles count
            await supabase
              .from('usage_tracking')
              .update({
                company_profiles_count: usage.company_profiles_count + 1,
                updated_at: new Date().toISOString(),
              })
              .eq('id', usage.id);
          }
        } catch (error) {
          console.error('Failed to increment usage counter:', error);
          // Don't throw here, we still want to return the company even if tracking fails
        }
      }

      return data;
    },
    'Failed to create company'
  );
};

/**
 * Update a company
 */
export const updateCompany = async (id: string, company: CompanyUpdate): Promise<Company> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('companies')
        .update(company)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      return data;
    },
    `Failed to update company with ID ${id}`
  );
};

/**
 * Delete a company
 */
export const deleteCompany = async (id: string, checkUsageLimit = true): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Delete the company
      const { error } = await supabase
        .from('companies')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // If usage limit checking is enabled, decrement the usage counter
      if (checkUsageLimit) {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

        try {
          // Get current usage
          const { data: usage, error: usageError } = await supabase
            .from('usage_tracking')
            .select('*')
            .eq('user_id', userId)
            .eq('month_year', currentMonth)
            .single();

          if (usageError) {
            console.error('Error fetching usage data:', usageError);
          } else if (usage) {
            // Decrement the company profiles count, ensuring it doesn't go below 0
            const newCount = Math.max(0, usage.company_profiles_count - 1);

            await supabase
              .from('usage_tracking')
              .update({
                company_profiles_count: newCount,
                updated_at: new Date().toISOString(),
              })
              .eq('id', usage.id);
          }
        } catch (error) {
          console.error('Failed to decrement usage counter:', error);
          // Don't throw here, we still want to return success even if tracking fails
        }
      }

      return true;
    },
    `Failed to delete company with ID ${id}`,
    false // Return false as fallback
  );
};

/**
 * Upload a company logo
 */
export const uploadCompanyLogo = async (companyId: string, file: File): Promise<string> => {
  return safeDbOperation(
    async () => {
      const fileExt = file.name.split('.').pop();
      const fileName = `${companyId}-${Math.random()}.${fileExt}`;
      const filePath = `companies/${fileName}`;

      const { error } = await supabase.storage
        .from('companies')
        .upload(filePath, file, { upsert: true });

      if (error) throw error;

      const { data } = supabase.storage.from('companies').getPublicUrl(filePath);
      const logoUrl = data.publicUrl;

      // Update the company with the new logo URL
      const { error: updateError } = await supabase
        .from('companies')
        .update({ logo_url: logoUrl })
        .eq('id', companyId);

      if (updateError) {
        console.error('Error updating company with logo URL:', updateError);
        // We still return the URL even if the update fails
      }

      return logoUrl;
    },
    `Failed to upload logo for company ${companyId}`
  );
};




