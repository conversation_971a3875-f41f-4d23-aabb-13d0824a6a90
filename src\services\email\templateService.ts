import { 
  getEmailTemplateByName, 
  renderEmailTemplate, 
  type EmailTemplate 
} from '@/services/supabase/emailTemplates';

/**
 * Template rendering service that fetches templates from database and renders them
 */
export class TemplateService {
  private static templateCache = new Map<string, EmailTemplate>();
  private static cacheExpiry = new Map<string, number>();
  private static readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  /**
   * Get template from cache or database
   */
  private static async getTemplate(templateName: string): Promise<EmailTemplate | null> {
    const now = Date.now();
    const cached = this.templateCache.get(templateName);
    const expiry = this.cacheExpiry.get(templateName);

    // Return cached template if it exists and hasn't expired
    if (cached && expiry && now < expiry) {
      return cached;
    }

    // Fetch from database
    try {
      const template = await getEmailTemplateByName(templateName);
      if (template) {
        this.templateCache.set(templateName, template);
        this.cacheExpiry.set(templateName, now + this.CACHE_DURATION);
      }
      return template;
    } catch (error) {
      console.error(`Failed to fetch template ${templateName}:`, error);
      return null;
    }
  }

  /**
   * Clear template cache
   */
  static clearCache(): void {
    this.templateCache.clear();
    this.cacheExpiry.clear();
  }

  /**
   * Render interview invitation template for candidates
   */
  static async renderInterviewInvitationCandidate(variables: {
    companyName: string;
    candidateName: string;
    jobTitle: string;
    interviewDate: string;
    interviewTime: string;
    duration: string;
    interviewType: string;
    location: string;
  }): Promise<{ subject: string; content: string } | null> {
    const template = await this.getTemplate('interview_invitation_candidate');
    if (!template) {
      console.warn('Interview invitation candidate template not found, using fallback');
      return this.getFallbackInterviewInvitation(variables);
    }

    return renderEmailTemplate(template, variables);
  }

  /**
   * Render application received template
   */
  static async renderApplicationReceived(variables: {
    companyName: string;
    candidateName: string;
    jobTitle: string;
  }): Promise<{ subject: string; content: string } | null> {
    const template = await this.getTemplate('application_received');
    if (!template) {
      console.warn('Application received template not found, using fallback');
      return this.getFallbackApplicationReceived(variables);
    }

    return renderEmailTemplate(template, variables);
  }

  /**
   * Render status update template
   */
  static async renderStatusUpdate(variables: {
    companyName: string;
    candidateName: string;
    jobTitle: string;
    status: string;
    message?: string;
    nextSteps?: string;
  }): Promise<{ subject: string; content: string } | null> {
    const template = await this.getTemplate('status_update');
    if (!template) {
      console.warn('Status update template not found, using fallback');
      return this.getFallbackStatusUpdate(variables);
    }

    return renderEmailTemplate(template, variables);
  }

  /**
   * Render team invitation template
   */
  static async renderTeamInvitation(variables: {
    companyName: string;
    inviteeName: string;
    inviterName: string;
    role: string;
    invitationLink: string;
  }): Promise<{ subject: string; content: string } | null> {
    const template = await this.getTemplate('team_invitation');
    if (!template) {
      console.warn('Team invitation template not found, using fallback');
      return this.getFallbackTeamInvitation(variables);
    }

    return renderEmailTemplate(template, variables);
  }

  /**
   * Render job offer template
   */
  static async renderJobOffer(variables: {
    companyName: string;
    candidateName: string;
    jobTitle: string;
    message?: string;
    nextSteps?: string;
  }): Promise<{ subject: string; content: string } | null> {
    const template = await this.getTemplate('job_offer');
    if (!template) {
      console.warn('Job offer template not found, using fallback');
      return this.getFallbackJobOffer(variables);
    }

    return renderEmailTemplate(template, variables);
  }

  /**
   * Fallback templates for when database templates are not available
   */
  private static getFallbackInterviewInvitation(variables: any): { subject: string; content: string } {
    return {
      subject: `Interview Scheduled: ${variables.jobTitle}`,
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">${variables.companyName}</h1>
          </div>
          
          <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
            Interview Scheduled
          </h2>
          
          <p style="font-size: 16px; line-height: 1.6;">Dear ${variables.candidateName},</p>
          
          <p style="font-size: 16px; line-height: 1.6;">
            We're pleased to inform you that an interview has been scheduled for the <strong>${variables.jobTitle}</strong> position.
          </p>
          
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h3 style="margin-top: 0; color: #1f2937;">Interview Details</h3>
            <p><strong>Date:</strong> ${variables.interviewDate}</p>
            <p><strong>Time:</strong> ${variables.interviewTime}</p>
            <p><strong>Duration:</strong> ${variables.duration} minutes</p>
            <p><strong>Type:</strong> ${variables.interviewType}</p>
            <p><strong>Location:</strong> ${variables.location}</p>
          </div>
          
          <p style="font-size: 16px; line-height: 1.6;">
            Best regards,<br>
            <strong>The Recruitment Team</strong><br>
            ${variables.companyName}
          </p>
          
          <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
              This is an automated message from ${variables.companyName}. Please do not reply to this email.
            </p>
          </div>
        </div>
      `
    };
  }

  private static getFallbackApplicationReceived(variables: any): { subject: string; content: string } {
    return {
      subject: `Application Received: ${variables.jobTitle} at ${variables.companyName}`,
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">${variables.companyName}</h1>
          </div>

          <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
            Application Received
          </h2>

          <p style="font-size: 16px; line-height: 1.6;">Dear ${variables.candidateName},</p>

          <p style="font-size: 16px; line-height: 1.6;">
            Thank you for your interest in the <strong>${variables.jobTitle}</strong> position at <strong>${variables.companyName}</strong>.
          </p>

          <p style="font-size: 16px; line-height: 1.6;">
            We have received your application and our recruitment team will review it carefully.
          </p>

          <p style="font-size: 16px; line-height: 1.6;">
            Best regards,<br>
            <strong>The Recruitment Team</strong><br>
            ${variables.companyName}
          </p>

          <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
              This is an automated message from ${variables.companyName}. Please do not reply to this email.
            </p>
          </div>
        </div>
      `
    };
  }

  private static getFallbackStatusUpdate(variables: any): { subject: string; content: string } {
    return {
      subject: `Application Update: ${variables.jobTitle} at ${variables.companyName}`,
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">${variables.companyName}</h1>
          </div>

          <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
            Application Update
          </h2>

          <p style="font-size: 16px; line-height: 1.6;">Dear ${variables.candidateName},</p>

          <p style="font-size: 16px; line-height: 1.6;">
            We have an update regarding your application for the <strong>${variables.jobTitle}</strong> position.
          </p>

          <p style="font-size: 16px; line-height: 1.6;">
            Status: <strong>${variables.status}</strong>
          </p>

          ${variables.message ? `<p style="font-size: 16px; line-height: 1.6;">${variables.message}</p>` : ''}

          <p style="font-size: 16px; line-height: 1.6;">
            Best regards,<br>
            <strong>The Recruitment Team</strong><br>
            ${variables.companyName}
          </p>

          <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
              This is an automated message from ${variables.companyName}. Please do not reply to this email.
            </p>
          </div>
        </div>
      `
    };
  }

  private static getFallbackTeamInvitation(variables: any): { subject: string; content: string } {
    return {
      subject: `You're invited to join ${variables.companyName} on Sourcio.ai`,
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #2563eb; margin: 0;">${variables.companyName}</h1>
          </div>
          
          <h2 style="color: #1e293b; margin: 0 0 15px 0;">You're Invited to Join a Team!</h2>
          <p style="color: #475569; margin: 0; line-height: 1.6;">
            Hi ${variables.inviteeName},<br><br>
            ${variables.inviterName} has invited you to join <strong>${variables.companyName}</strong> as a <strong>${variables.role}</strong> on Sourcio.ai.
          </p>
          
          <div style="margin: 30px 0; text-align: center;">
            <a href="${variables.invitationLink}" style="background: #2563eb; color: white; padding: 12px 30px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: 600;">
              Accept Invitation
            </a>
          </div>
          
          <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
              This is an automated message from ${variables.companyName}. Please do not reply to this email.
            </p>
          </div>
        </div>
      `
    };
  }

  private static getFallbackJobOffer(variables: any): { subject: string; content: string } {
    return {
      subject: `Great News! Job Offer: ${variables.jobTitle} at ${variables.companyName}`,
      content: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #059669; margin: 0;">${variables.companyName}</h1>
          </div>

          <h2 style="color: #059669; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
            🎉 Congratulations! Job Offer
          </h2>

          <p style="font-size: 16px; line-height: 1.6;">Dear ${variables.candidateName},</p>

          <p style="font-size: 16px; line-height: 1.6;">
            We are delighted to offer you the position of <strong>${variables.jobTitle}</strong> at ${variables.companyName}.
          </p>

          ${variables.message ? `<p style="font-size: 16px; line-height: 1.6;">${variables.message}</p>` : ''}

          <p style="font-size: 16px; line-height: 1.6;">
            Best regards,<br>
            <strong>The Recruitment Team</strong><br>
            ${variables.companyName}
          </p>

          <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
              This is an automated message from ${variables.companyName}. Please do not reply to this email.
            </p>
          </div>
        </div>
      `
    };
  }
}
