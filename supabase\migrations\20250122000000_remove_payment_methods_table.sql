-- Remove payment_methods table as it's no longer used
-- Payment methods are now handled directly by Stripe and PayPal

-- Drop the table and all its policies
DROP TABLE IF EXISTS public.payment_methods CASCADE;

-- Remove the service role policies migration file reference
-- (The policies will be automatically dropped with the table)

-- Add comment for documentation
COMMENT ON SCHEMA public IS 'Payment methods table removed - payment processing now handled directly by Stripe and PayPal';
