import { supabase } from '@/lib/supabase';
import * as profileService from './profiles';

/**
 * Subscription status types
 */
export type SubscriptionStatus = 'active' | 'cancelled' | 'suspended' | 'expired' | 'pending';

/**
 * Subscription tier types
 */
export type SubscriptionTier = 'starter' | 'growth' | 'pro';

/**
 * Subscription data interface
 */
export interface SubscriptionData {
  id: string;
  user_id: string;
  subscription_id: string;
  plan_id: string;
  status: SubscriptionStatus;
  tier: SubscriptionTier;
  start_date: string;
  end_date: string | null;
  created_at: string;
  updated_at: string;
  payment_method: string;
  payment_details: any;
}

/**
 * Create a new subscription
 */
export const createSubscription = async (
  userId: string,
  subscriptionData: {
    subscription_id: string;
    plan_id: string;
    tier: SubscriptionTier;
    status: SubscriptionStatus;
    payment_method: string;
    payment_details?: any;
  }
): Promise<SubscriptionData | null> => {
  try {
    // Create subscription record
    const { data, error } = await supabase
      .from('subscriptions')
      .insert({
        user_id: userId,
        subscription_id: subscriptionData.subscription_id,
        plan_id: subscriptionData.plan_id,
        tier: subscriptionData.tier,
        status: subscriptionData.status,
        start_date: new Date().toISOString(),
        payment_method: subscriptionData.payment_method,
        payment_details: subscriptionData.payment_details || {},
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating subscription:', error);
      throw error;
    }

    // Update user profile with subscription information
    await profileService.updateProfile({
      user_id: userId,
      subscription_tier: subscriptionData.tier,
      subscription_status: subscriptionData.status,
      subscription_end_date: null, // Active subscription
    });

    return data;
  } catch (error) {
    console.error('Error in createSubscription:', error);
    throw error;
  }
};

/**
 * Get a user's active subscription
 */
export const getActiveSubscription = async (userId: string): Promise<SubscriptionData | null> => {
  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      // If no active subscription found, return null
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error('Error fetching active subscription:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getActiveSubscription:', error);
    return null;
  }
};

/**
 * Get a user's subscription history
 */
export const getSubscriptionHistory = async (userId: string): Promise<SubscriptionData[]> => {
  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching subscription history:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getSubscriptionHistory:', error);
    return [];
  }
};

/**
 * Cancel a subscription
 */
export const cancelSubscription = async (
  subscriptionId: string,
  userId: string
): Promise<boolean> => {
  try {
    // Update subscription record
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status: 'cancelled',
        end_date: new Date().toISOString(),
      })
      .eq('subscription_id', subscriptionId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error cancelling subscription:', error);
      throw error;
    }

    // Update user profile
    await profileService.updateProfile({
      user_id: userId,
      subscription_status: 'cancelled',
      subscription_end_date: new Date().toISOString(),
    });

    return true;
  } catch (error) {
    console.error('Error in cancelSubscription:', error);
    throw error;
  }
};

/**
 * Update subscription status
 */
export const updateSubscriptionStatus = async (
  subscriptionId: string,
  status: SubscriptionStatus,
  userId: string
): Promise<boolean> => {
  try {
    // Update subscription record
    const { error } = await supabase
      .from('subscriptions')
      .update({
        status,
        updated_at: new Date().toISOString(),
        ...(status === 'expired' || status === 'cancelled' ? { end_date: new Date().toISOString() } : {}),
      })
      .eq('subscription_id', subscriptionId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating subscription status:', error);
      throw error;
    }

    // Update user profile
    await profileService.updateProfile({
      user_id: userId,
      subscription_status: status,
      ...(status === 'expired' || status === 'cancelled' ? { subscription_end_date: new Date().toISOString() } : {}),
    });

    return true;
  } catch (error) {
    console.error('Error in updateSubscriptionStatus:', error);
    throw error;
  }
};

/**
 * Check if a user has an active subscription
 */
export const hasActiveSubscription = async (userId: string): Promise<boolean> => {
  try {
    const subscription = await getActiveSubscription(userId);
    return !!subscription;
  } catch (error) {
    console.error('Error checking active subscription:', error);
    return false;
  }
};
