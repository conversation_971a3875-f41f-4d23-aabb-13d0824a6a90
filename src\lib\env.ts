/**
 * Environment variable validation utility
 *
 * This utility ensures that all required environment variables are set
 * and provides helpful error messages when they are missing.
 */

// Define required environment variables
const requiredEnvVars = [
  'VITE_SUPABASE_URL',
  'VITE_SUPABASE_ANON_KEY',
  'VITE_GROQ_API_KEY',
];

// Define optional environment variables with default values
const optionalEnvVars: Record<string, string> = {
  'VITE_GROQ_MODEL': 'llama-3.3-70b-versatile',
  'VITE_ENABLE_PAYPAL': 'false',
  'VITE_ENABLE_REAL_DATA': 'true',
  'VITE_ENABLE_EMAIL_NOTIFICATIONS': 'true',
  'VITE_APP_URL': window.location.origin,
  'VITE_API_URL': `${window.location.origin}/api`,
  'VITE_STORAGE_BUCKET': 'cvs',
};

/**
 * Validate environment variables
 * @returns {boolean} True if all required environment variables are set
 * @throws {Error} If any required environment variables are missing
 */
export function validateEnv(): boolean {
  const missingVars: string[] = [];

  // Check required environment variables
  for (const envVar of requiredEnvVars) {
    if (!import.meta.env[envVar]) {
      missingVars.push(envVar);
    }
  }

  // If any required variables are missing, throw an error
  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}. ` +
      `Please check your .env file and make sure all required variables are set.`
    );
  }

  // Set default values for optional environment variables
  for (const [envVar, defaultValue] of Object.entries(optionalEnvVars)) {
    if (!import.meta.env[envVar]) {
      (import.meta.env as any)[envVar] = defaultValue;
    }
  }

  return true;
}

/**
 * Get an environment variable
 * @param {string} name - The name of the environment variable
 * @param {string} defaultValue - The default value to use if the environment variable is not set
 * @returns {string} The value of the environment variable
 */
export function getEnv(name: string, defaultValue: string = ''): string {
  return import.meta.env[name] || defaultValue;
}

/**
 * Check if a feature flag is enabled
 * @param {string} flag - The name of the feature flag
 * @returns {boolean} True if the feature flag is enabled
 */
export function isFeatureEnabled(flag: string): boolean {
  const envVar = `VITE_ENABLE_${flag.toUpperCase()}`;
  return import.meta.env[envVar] === 'true';
}

// Validate environment variables on import
validateEnv();

// Export environment variables
export const env = {
  SUPABASE_URL: import.meta.env.VITE_SUPABASE_URL as string,
  SUPABASE_ANON_KEY: import.meta.env.VITE_SUPABASE_ANON_KEY as string,
  GROQ_API_KEY: import.meta.env.VITE_GROQ_API_KEY as string,
  GROQ_MODEL: import.meta.env.VITE_GROQ_MODEL as string,
  PAYPAL_CLIENT_ID: import.meta.env.VITE_PAYPAL_CLIENT_ID as string,
  PAYPAL_ENVIRONMENT: import.meta.env.VITE_PAYPAL_ENVIRONMENT as string,
  APP_URL: import.meta.env.VITE_APP_URL as string,
  API_URL: import.meta.env.VITE_API_URL as string,
  STORAGE_BUCKET: import.meta.env.VITE_STORAGE_BUCKET as string,
  
  // SMTP Configuration
  SMTP_HOST: import.meta.env.VITE_SMTP_HOST as string,
  SMTP_PORT: parseInt(import.meta.env.VITE_SMTP_PORT || '587'),
  SMTP_SECURE: import.meta.env.VITE_SMTP_SECURE === 'true',
  SMTP_USER: import.meta.env.VITE_SMTP_USER as string,
  SMTP_PASSWORD: import.meta.env.VITE_SMTP_PASSWORD as string,
  SMTP_FROM: import.meta.env.VITE_SMTP_FROM as string,
  
  ENABLE_PAYPAL: isFeatureEnabled('PAYPAL'),
  ENABLE_REAL_DATA: isFeatureEnabled('REAL_DATA'),
  ENABLE_EMAIL_NOTIFICATIONS: isFeatureEnabled('EMAIL_NOTIFICATIONS'),
};

