import React, { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { DashboardOverview } from '@/components/dashboard/DashboardOverview';
import UsageDashboard from '@/components/dashboard/UsageDashboard';
import { Loader2, RefreshCw } from 'lucide-react';

const Dashboard = () => {
  const { user, isLoading: authLoading } = useAuth();
  const { isLoading: permissionsLoading } = usePermissions();
  const [showError, setShowError] = useState(false);

  const isLoading = authLoading || permissionsLoading;

  // Handle page refresh - force reload if stuck
  useEffect(() => {
    let errorTimeoutId: NodeJS.Timeout;
    let reloadTimeoutId: NodeJS.Timeout;

    if (isLoading) {
      // Show error after 10 seconds of loading
      errorTimeoutId = setTimeout(() => {
        setShowError(true);
      }, 10000);

      // Force reload after 30 seconds if still loading
      reloadTimeoutId = setTimeout(() => {
        console.log('Dashboard still loading after 30s, forcing reload');
        window.location.reload();
      }, 30000);
    } else {
      setShowError(false);
    }

    return () => {
      if (errorTimeoutId) clearTimeout(errorTimeoutId);
      if (reloadTimeoutId) clearTimeout(reloadTimeoutId);
    };
  }, [isLoading]);

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-[60vh]">
        <div className="flex flex-col items-center space-y-4">
          {showError ? (
            <>
              <div className="p-4 rounded-full bg-red-100">
                <RefreshCw className="h-12 w-12 text-red-500" />
              </div>
              <h2 className="text-xl font-bold text-gray-800">Still Loading...</h2>
              <p className="text-gray-600 text-center max-w-md">
                It's taking longer than expected to load your dashboard. You can wait or try refreshing the page.
              </p>
              <div className="flex space-x-4 mt-2">
                <button
                  onClick={() => window.location.reload()}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors flex items-center"
                >
                  <RefreshCw className="mr-2 h-4 w-4" /> Refresh Page
                </button>
              </div>
            </>
          ) : (
            <>
              <Loader2 className="h-12 w-12 animate-spin text-primary" />
              <p className="text-lg text-gray-600">Loading dashboard...</p>
            </>
          )}
        </div>
      </div>
    );
  }

  // Handle case where user is null
  if (!user) {
    return (
      <div className="space-y-8">
        <div className="bg-primary-gradient rounded-xl p-6 shadow-lg">
          <h1 className="text-2xl font-bold text-white">Welcome to Sourcio.ai</h1>
          <p className="text-white/80 mt-2">
            Loading your personalized dashboard...
          </p>
        </div>
        <DashboardOverview />
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome section */}
      <div className="bg-primary-gradient rounded-xl p-6 shadow-lg">
        <h1 className="text-2xl font-bold text-white">Welcome back, {user.name}!</h1>
        <p className="text-white/80 mt-2">
          Here's what's happening with your recruitment activities today.
        </p>
      </div>

      {/* Usage Dashboard */}
      <UsageDashboard />

      {/* Dashboard Overview */}
      <DashboardOverview />
    </div>
  );
};

// Wrap the Dashboard component with the DashboardLayout
const DashboardWithLayout = () => (
  <DashboardLayout>
    <Dashboard />
  </DashboardLayout>
);

export default DashboardWithLayout;
