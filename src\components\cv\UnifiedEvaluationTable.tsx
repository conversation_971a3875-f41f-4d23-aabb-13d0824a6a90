import React, { useState, useMemo } from 'react';
import {
  Star,
  ChevronDown,
  ChevronRight,
  Eye,
  Calendar,
  Building,
  Filter,
  Plus,
  Loader2,
  ClipboardList
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Evaluation } from '@/services/supabase/evaluations';
import { JobSpecificMatch } from '@/services/cv-processing/jobMatcher';

interface UnifiedEvaluationTableProps {
  evaluations: Evaluation[];
  onSelectEvaluation: (evaluationId: string) => void;
  onViewFullEvaluation: (evaluationId: string) => void;
  isLoading: boolean;
  onNewEvaluation: () => void;
}

// Helper function to get color based on match score
const getMatchScoreColor = (score: number) => {
  if (score >= 90) return 'text-green-500';
  if (score >= 75) return 'text-blue-500';
  if (score >= 60) return 'text-amber-500';
  return 'text-red-500';
};

const UnifiedEvaluationTable: React.FC<UnifiedEvaluationTableProps> = ({
  evaluations,
  onSelectEvaluation,
  onViewFullEvaluation,
  isLoading,
  onNewEvaluation
}) => {
  // State for expanded rows
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // State for filters
  const [companyFilter, setCompanyFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date-desc');

  // Extract unique companies for filter dropdown
  const companies = useMemo(() => {
    const uniqueCompanies = new Set<string>();
    evaluations.forEach(evaluation => {
      if (evaluation.company_name) {
        uniqueCompanies.add(evaluation.company_name);
      }
    });
    return Array.from(uniqueCompanies);
  }, [evaluations]);

  // Filter and sort evaluations
  const filteredEvaluations = useMemo(() => {
    let filtered = [...evaluations];

    // Apply company filter
    if (companyFilter !== 'all') {
      filtered = filtered.filter(evaluation =>
        evaluation.company_name === companyFilter
      );
    }

    // Apply date filter
    if (dateFilter === 'last-week') {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);
      filtered = filtered.filter(evaluation =>
        new Date(evaluation.evaluation_date) >= oneWeekAgo
      );
    } else if (dateFilter === 'last-month') {
      const oneMonthAgo = new Date();
      oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
      filtered = filtered.filter(evaluation =>
        new Date(evaluation.evaluation_date) >= oneMonthAgo
      );
    }

    // Apply sorting
    if (sortBy === 'date-desc') {
      filtered.sort((a, b) =>
        new Date(b.evaluation_date).getTime() - new Date(a.evaluation_date).getTime()
      );
    } else if (sortBy === 'date-asc') {
      filtered.sort((a, b) =>
        new Date(a.evaluation_date).getTime() - new Date(b.evaluation_date).getTime()
      );
    } else if (sortBy === 'score-desc') {
      filtered.sort((a, b) => b.evaluation_score - a.evaluation_score);
    } else if (sortBy === 'score-asc') {
      filtered.sort((a, b) => a.evaluation_score - b.evaluation_score);
    }

    return filtered;
  }, [evaluations, companyFilter, dateFilter, sortBy]);

  // Parse job-specific matches from evaluation summary
  const parseJobSpecificMatches = (evaluation: Evaluation) => {
    if (evaluation.parsedSummary?.jobSpecificMatches) {
      return evaluation.parsedSummary.jobSpecificMatches;
    }

    try {
      const summary = JSON.parse(evaluation.evaluation_summary);
      if (summary.matchResult && summary.matchResult.jobSpecificMatches) {
        return summary.matchResult.jobSpecificMatches;
      }
      return [];
    } catch (error) {
      console.error('Error parsing job matches:', error);
      return [];
    }
  };

  // Toggle row expansion
  const toggleRowExpansion = (evaluationId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(evaluationId)) {
      newExpandedRows.delete(evaluationId);
    } else {
      newExpandedRows.add(evaluationId);
    }
    setExpandedRows(newExpandedRows);
  };

  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-gray-800 text-lg">All Evaluations</CardTitle>
        <Button
          className="bg-recruiter-lightblue hover:bg-blue-500"
          onClick={onNewEvaluation}
        >
          <Plus className="mr-2 h-4 w-4" /> New Evaluation
        </Button>
      </CardHeader>

      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">Loading evaluations...</span>
          </div>
        ) : filteredEvaluations.length > 0 ? (
          <div className="space-y-6">
            {/* Filters */}
            <div className="flex flex-wrap gap-4 mb-4">
              <div className="flex items-center">
                <Building className="mr-2 h-4 w-4 text-gray-500" />
                <Select value={companyFilter} onValueChange={setCompanyFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by company" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Companies</SelectItem>
                    {companies.map(company => (
                      <SelectItem key={company} value={company}>{company}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center">
                <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Filter by date" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Time</SelectItem>
                    <SelectItem value="last-week">Last Week</SelectItem>
                    <SelectItem value="last-month">Last Month</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center">
                <Filter className="mr-2 h-4 w-4 text-gray-500" />
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date-desc">Date (Newest First)</SelectItem>
                    <SelectItem value="date-asc">Date (Oldest First)</SelectItem>
                    <SelectItem value="score-desc">Score (Highest First)</SelectItem>
                    <SelectItem value="score-asc">Score (Lowest First)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Evaluations Table */}
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2 px-4 font-medium text-gray-600 w-8"></th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Date</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Company</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Job/Mode</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Score</th>
                    <th className="text-left py-2 px-4 font-medium text-gray-600">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredEvaluations.map((evaluation) => (
                    <React.Fragment key={evaluation.id}>
                      <tr
                        className="border-b hover:bg-gray-50 cursor-pointer"
                        onClick={() => toggleRowExpansion(evaluation.id)}
                      >
                        <td className="py-2 px-4">
                          {expandedRows.has(evaluation.id) ? (
                            <ChevronDown className="h-4 w-4 text-gray-500" />
                          ) : (
                            <ChevronRight className="h-4 w-4 text-gray-500" />
                          )}
                        </td>
                        <td className="py-2 px-4 text-sm">{evaluation.evaluation_date}</td>
                        <td className="py-2 px-4 text-sm">
                          {evaluation.company_name || 'All Companies'}
                        </td>
                        <td className="py-2 px-4 text-sm">
                          {evaluation.evaluation_mode === 'specific-job' ? (
                            <span>{evaluation.job_title}</span>
                          ) : evaluation.evaluation_mode === 'all-company-jobs' ? (
                            <Badge className="bg-blue-500/20 text-blue-500 hover:bg-blue-500/30">
                              All Company Jobs
                            </Badge>
                          ) : (
                            <Badge className="bg-purple-500/20 text-purple-500 hover:bg-purple-500/30">
                              All Companies
                            </Badge>
                          )}
                        </td>
                        <td className="py-2 px-4">
                          <div className="flex items-center">
                            <span className={`font-medium ${getMatchScoreColor(evaluation.evaluation_score)}`}>
                              {evaluation.evaluation_score}%
                            </span>
                            <Star
                              className={`ml-1 h-4 w-4 ${getMatchScoreColor(evaluation.evaluation_score)}`}
                              fill="currentColor"
                            />
                          </div>
                        </td>
                        <td className="py-2 px-4">
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-gray-500 hover:text-gray-700"
                            onClick={(e) => {
                              e.stopPropagation();
                              onViewFullEvaluation(evaluation.id);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Full
                          </Button>
                        </td>
                      </tr>

                      {/* Expanded row content */}
                      {expandedRows.has(evaluation.id) && (
                        <tr>
                          <td colSpan={6} className="py-0">
                            <div className="p-4 bg-gray-50 border-b">
                              <div className="grid grid-cols-2 gap-4 mb-4">
                                <div>
                                  <p className="text-sm text-gray-500">Evaluation Mode</p>
                                  <p className="font-medium">
                                    {evaluation.evaluation_mode === 'specific-job' ? 'Specific Job' :
                                     evaluation.evaluation_mode === 'all-company-jobs' ? 'All Jobs in Company' :
                                     'All Companies'}
                                  </p>
                                </div>
                                <div>
                                  <p className="text-sm text-gray-500">Overall Score</p>
                                  <div className="flex items-center">
                                    <span className={`font-medium ${getMatchScoreColor(evaluation.evaluation_score)}`}>
                                      {evaluation.evaluation_score}%
                                    </span>
                                  </div>
                                </div>
                              </div>

                              {/* Job-specific matches for company evaluations */}
                              {(evaluation.evaluation_mode === 'all-company-jobs' || evaluation.evaluation_mode === 'all-companies') && (
                                <div className="mt-4">
                                  <h4 className="text-md font-medium text-gray-800 mb-3">Job-Specific Matches</h4>
                                  <div className="overflow-x-auto">
                                    <table className="w-full">
                                      <thead>
                                        <tr className="border-b">
                                          <th className="text-left py-2 px-3 font-medium text-gray-600 text-xs">Job Title</th>
                                          <th className="text-center py-2 px-3 font-medium text-gray-600 text-xs">Overall</th>
                                          <th className="text-center py-2 px-3 font-medium text-gray-600 text-xs">Skills</th>
                                          <th className="text-center py-2 px-3 font-medium text-gray-600 text-xs">Experience</th>
                                          <th className="text-center py-2 px-3 font-medium text-gray-600 text-xs">Education</th>
                                        </tr>
                                      </thead>
                                      <tbody>
                                        {parseJobSpecificMatches(evaluation).map((match, index) => (
                                          <tr key={index} className="border-b hover:bg-gray-100">
                                            <td className="py-2 px-3 text-xs">{match.jobTitle}</td>
                                            <td className="py-2 px-3 text-center">
                                              <div className="flex items-center justify-center">
                                                <span className={`text-xs font-medium ${getMatchScoreColor(match.overallScore)}`}>
                                                  {match.overallScore}%
                                                </span>
                                              </div>
                                            </td>
                                            <td className="py-2 px-3 text-center">
                                              <div className="flex items-center justify-center">
                                                <span className={`text-xs font-medium ${getMatchScoreColor(match.skillsScore)}`}>
                                                  {match.skillsScore}%
                                                </span>
                                              </div>
                                            </td>
                                            <td className="py-2 px-3 text-center">
                                              <div className="flex items-center justify-center">
                                                <span className={`text-xs font-medium ${getMatchScoreColor(match.experienceScore)}`}>
                                                  {match.experienceScore}%
                                                </span>
                                              </div>
                                            </td>
                                            <td className="py-2 px-3 text-center">
                                              <div className="flex items-center justify-center">
                                                <span className={`text-xs font-medium ${getMatchScoreColor(match.educationScore)}`}>
                                                  {match.educationScore}%
                                                </span>
                                              </div>
                                            </td>
                                          </tr>
                                        ))}
                                      </tbody>
                                    </table>
                                  </div>
                                </div>
                              )}

                              <div className="mt-4">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onViewFullEvaluation(evaluation.id);
                                  }}
                                >
                                  <Eye className="mr-2 h-4 w-4" />
                                  View Full Evaluation
                                </Button>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </React.Fragment>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="text-center py-8">
            <ClipboardList className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800">No evaluations found</h3>
            <p className="text-gray-500 mt-1">
              {companyFilter !== 'all' || dateFilter !== 'all'
                ? 'Try changing your filters to see more results.'
                : 'Evaluate this candidate against jobs or companies to see results here.'}
            </p>
            {companyFilter === 'all' && dateFilter === 'all' && (
              <Button
                className="mt-4 bg-recruiter-lightblue hover:bg-blue-500"
                onClick={onNewEvaluation}
              >
                <Plus className="mr-2 h-4 w-4" /> Evaluate Now
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UnifiedEvaluationTable;
