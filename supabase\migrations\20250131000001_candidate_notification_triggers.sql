-- Create function to handle candidate status change notifications
CREATE OR R<PERSON>LACE FUNCTION handle_candidate_status_change()
RETURNS TRIGGER AS $$
BEGIN
  -- Only proceed if status actually changed
  IF OLD.status IS DISTINCT FROM NEW.status THEN
    -- Insert a notification event that can be picked up by the application
    INSERT INTO public.notification_events (
      event_type,
      candidate_id,
      old_status,
      new_status,
      metadata,
      created_at
    ) VALUES (
      'candidate_status_changed',
      NEW.id,
      COALESCE(OLD.status, 'unknown'),
      NEW.status,
      jsonb_build_object(
        'candidate_name', NEW.name,
        'candidate_email', NEW.email,
        'job_id', NEW.job_id
      ),
      NOW()
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create function to handle application received notifications
CREATE OR REPLACE FUNCTION handle_application_received()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert a notification event for new applications
  INSERT INTO public.notification_events (
    event_type,
    candidate_id,
    application_id,
    job_id,
    metadata,
    created_at
  ) VALUES (
    'application_received',
    NEW.candidate_id,
    NEW.id,
    NEW.job_id,
    jsonb_build_object(
      'application_id', NEW.id,
      'job_id', NEW.job_id
    ),
    NOW()
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create function to handle interview scheduled notifications
CREATE OR REPLACE FUNCTION handle_interview_scheduled()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert a notification event for new interviews
  INSERT INTO public.notification_events (
    event_type,
    candidate_id,
    interview_id,
    metadata,
    created_at
  ) VALUES (
    'interview_scheduled',
    NEW.candidate_id,
    NEW.id,
    jsonb_build_object(
      'interview_id', NEW.id,
      'scheduled_at', NEW.scheduled_at,
      'interview_type', NEW.interview_type,
      'timezone', NEW.timezone
    ),
    NOW()
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create notification_events table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.notification_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  event_type TEXT NOT NULL,
  candidate_id UUID REFERENCES public.candidates(id) ON DELETE CASCADE,
  application_id UUID,
  interview_id UUID,
  job_id UUID,
  old_status TEXT,
  new_status TEXT,
  metadata JSONB DEFAULT '{}',
  processed BOOLEAN DEFAULT FALSE,
  processed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_notification_events_candidate_id ON public.notification_events(candidate_id);
CREATE INDEX IF NOT EXISTS idx_notification_events_event_type ON public.notification_events(event_type);
CREATE INDEX IF NOT EXISTS idx_notification_events_processed ON public.notification_events(processed);
CREATE INDEX IF NOT EXISTS idx_notification_events_created_at ON public.notification_events(created_at);

-- Create triggers
DROP TRIGGER IF EXISTS trigger_candidate_status_change ON public.candidates;
CREATE TRIGGER trigger_candidate_status_change
  AFTER UPDATE ON public.candidates
  FOR EACH ROW
  EXECUTE FUNCTION handle_candidate_status_change();

DROP TRIGGER IF EXISTS trigger_application_received ON public.applications;
CREATE TRIGGER trigger_application_received
  AFTER INSERT ON public.applications
  FOR EACH ROW
  EXECUTE FUNCTION handle_application_received();

DROP TRIGGER IF EXISTS trigger_interview_scheduled ON public.interviews;
CREATE TRIGGER trigger_interview_scheduled
  AFTER INSERT ON public.interviews
  FOR EACH ROW
  EXECUTE FUNCTION handle_interview_scheduled();

-- Enable RLS on notification_events table
ALTER TABLE public.notification_events ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own notification events" ON public.notification_events
  FOR SELECT USING (
    candidate_id IN (
      SELECT c.id FROM public.candidates c
      JOIN public.candidate_assignments ca ON c.id = ca.candidate_id
      JOIN public.companies comp ON ca.company_id = comp.id
      WHERE comp.user_id = auth.uid()
    )
  );

CREATE POLICY "System can insert notification events" ON public.notification_events
  FOR INSERT WITH CHECK (true);

CREATE POLICY "System can update notification events" ON public.notification_events
  FOR UPDATE USING (true);

-- Add comment for documentation
COMMENT ON TABLE public.notification_events IS 'Stores notification events for automated candidate email notifications';
COMMENT ON COLUMN public.notification_events.event_type IS 'Type of notification event (application_received, candidate_status_changed, interview_scheduled)';
COMMENT ON COLUMN public.notification_events.processed IS 'Whether the notification has been processed and email sent';
COMMENT ON COLUMN public.notification_events.metadata IS 'Additional data needed for the notification';
