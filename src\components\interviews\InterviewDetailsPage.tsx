import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  MessageSquare,
  Star,
  Edit,
  Trash2,
  Mail,
  Phone,
  Video
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog';
import { getInterviewById } from '@/services/supabase/interviews';
import { getInterviewParticipants } from '@/services/supabase/interview-participants';
import { getInterviewFeedback, getInterviewFeedbackSummary } from '@/services/supabase/interview-feedback';
import InterviewFeedbackForm from './InterviewFeedbackForm';
import { useCreateInterviewFeedback } from '@/hooks/use-interviews';
import { toast } from '@/hooks/use-toast';

const InterviewDetailsPage: React.FC = () => {
  const { interviewId } = useParams<{ interviewId: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [showFeedbackForm, setShowFeedbackForm] = useState(false);
  
  const { mutateAsync: createFeedback, isPending: isSubmittingFeedback } = useCreateInterviewFeedback();

  // Fetch interview details
  const { data: interview, isLoading: isLoadingInterview } = useQuery({
    queryKey: ['interview', interviewId],
    queryFn: () => getInterviewById(interviewId!),
    enabled: !!interviewId
  });

  // Fetch participants
  const { data: participants = [] } = useQuery({
    queryKey: ['interview-participants', interviewId],
    queryFn: () => getInterviewParticipants(interviewId!),
    enabled: !!interviewId
  });

  // Fetch feedback
  const { data: feedbacks = [] } = useQuery({
    queryKey: ['interview-feedback', interviewId],
    queryFn: () => getInterviewFeedback(interviewId!),
    enabled: !!interviewId
  });

  // Fetch feedback summary
  const { data: feedbackSummary } = useQuery({
    queryKey: ['interview-feedback-summary', interviewId],
    queryFn: () => getInterviewFeedbackSummary(interviewId!),
    enabled: !!interviewId
  });

  const handleSubmitFeedback = async (feedbackData: any) => {
    if (!user || !interview) return;

    try {
      await createFeedback({
        interview_id: interview.id,
        user_id: user.id,
        ...feedbackData
      });

      toast({
        title: 'Feedback Submitted',
        description: 'Your interview feedback has been recorded.',
      });

      setShowFeedbackForm(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to submit feedback. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const getInterviewTypeIcon = (type: string) => {
    switch (type) {
      case 'phone': return <Phone className="h-4 w-4" />;
      case 'video': return <Video className="h-4 w-4" />;
      default: return <MapPin className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      case 'rescheduled': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case 'strong_yes': return 'bg-green-100 text-green-800';
      case 'yes': return 'bg-green-50 text-green-700';
      case 'maybe': return 'bg-yellow-100 text-yellow-800';
      case 'no': return 'bg-red-50 text-red-700';
      case 'strong_no': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoadingInterview) {
    return <div className="p-6">Loading interview details...</div>;
  }

  if (!interview) {
    return <div className="p-6">Interview not found.</div>;
  }

  const userHasSubmittedFeedback = feedbacks.some(f => f.user_id === user?.id);
  const canSubmitFeedback = user && (
    participants.some(p => p.user_id === user.id) ||
    interview.created_by === user.id ||
    interview.user_id === user.id
  ) && !userHasSubmittedFeedback;

  return (
    <DashboardLayout>
      <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">Interview Details</h1>
          <p className="text-gray-600 mt-1">
            {interview.candidate_name} - {interview.job_title}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={() => navigate('/dashboard/interviews')}>
            Back to Interviews
          </Button>
          {canSubmitFeedback && (
            <Dialog open={showFeedbackForm} onOpenChange={setShowFeedbackForm}>
              <DialogTrigger asChild>
                <Button>
                  <MessageSquare className="h-4 w-4 mr-2" />
                  Submit Feedback
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
                <InterviewFeedbackForm
                  interviewId={interview.id}
                  candidateName={interview.candidate_name || 'Unknown'}
                  jobTitle={interview.job_title || 'Unknown Position'}
                  onSubmit={handleSubmitFeedback}
                  onCancel={() => setShowFeedbackForm(false)}
                  isSubmitting={isSubmittingFeedback}
                />
              </DialogContent>
            </Dialog>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Interview Information */}
        <div className="lg:col-span-2 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Interview Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Date & Time</label>
                  <p className="text-lg">
                    {format(new Date(interview.scheduled_at), 'EEEE, MMMM d, yyyy')}
                  </p>
                  <p className="text-gray-600">
                    {format(new Date(interview.scheduled_at), 'h:mm a')}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Duration</label>
                  <p className="text-lg flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {interview.duration_minutes} minutes
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Type</label>
                  <p className="text-lg flex items-center gap-2">
                    {getInterviewTypeIcon(interview.interview_type)}
                    {interview.interview_type.charAt(0).toUpperCase() + interview.interview_type.slice(1)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500">Status</label>
                  <Badge className={getStatusColor(interview.status)}>
                    {interview.status.charAt(0).toUpperCase() + interview.status.slice(1)}
                  </Badge>
                </div>
              </div>

              {interview.location && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Location</label>
                  <p className="text-lg flex items-center gap-2">
                    <MapPin className="h-4 w-4" />
                    {interview.location}
                  </p>
                </div>
              )}

              {interview.notes && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Notes</label>
                  <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                    {interview.notes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Participants */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Participants
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {participants.map((participant) => (
                  <div key={participant.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <p className="font-medium">{participant.name || 'Unknown'}</p>
                      <p className="text-sm text-gray-600">{participant.email}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline">
                        {participant.role.charAt(0).toUpperCase() + participant.role.slice(1)}
                      </Badge>
                      <Badge className={
                        participant.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        participant.status === 'declined' ? 'bg-red-100 text-red-800' :
                        'bg-yellow-100 text-yellow-800'
                      }>
                        {participant.status.replace('_', ' ').charAt(0).toUpperCase() + participant.status.slice(1)}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Feedback Section */}
        <div className="space-y-6">
          {/* Feedback Summary */}
          {feedbackSummary && feedbackSummary.totalFeedbacks > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Star className="h-5 w-5" />
                  Feedback Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-blue-600">
                    {feedbackSummary.averageOverallRating.toFixed(1)}
                  </div>
                  <div className="text-sm text-gray-600">Overall Rating</div>
                  <div className="flex justify-center mt-1">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`h-4 w-4 ${
                          star <= Math.round(feedbackSummary.averageOverallRating)
                            ? 'fill-yellow-400 text-yellow-400'
                            : 'text-gray-300'
                        }`}
                      />
                    ))}
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Technical:</span>
                    <span className="font-medium">{feedbackSummary.averageTechnicalRating.toFixed(1)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm">Cultural Fit:</span>
                    <span className="font-medium">{feedbackSummary.averageCulturalRating.toFixed(1)}</span>
                  </div>
                </div>

                <Separator />

                <div>
                  <div className="text-sm font-medium mb-2">Recommendations:</div>
                  <div className="space-y-1">
                    {Object.entries(feedbackSummary.recommendationCounts).map(([rec, count]) => (
                      <div key={rec} className="flex justify-between text-sm">
                        <Badge className={getRecommendationColor(rec)} variant="outline">
                          {rec.replace('_', ' ').charAt(0).toUpperCase() + rec.slice(1)}
                        </Badge>
                        <span>{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Individual Feedback */}
          <Card>
            <CardHeader>
              <CardTitle>Individual Feedback</CardTitle>
            </CardHeader>
            <CardContent>
              {feedbacks.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No feedback submitted yet.</p>
              ) : (
                <div className="space-y-4">
                  {feedbacks.map((feedback) => (
                    <div key={feedback.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <p className="font-medium">{feedback.user_name}</p>
                          <p className="text-sm text-gray-600">
                            {format(new Date(feedback.created_at), 'MMM d, yyyy')}
                          </p>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                          <span className="font-medium">{feedback.overall_rating}</span>
                        </div>
                      </div>
                      
                      <Badge className={getRecommendationColor(feedback.recommendation)} variant="outline">
                        {feedback.recommendation.replace('_', ' ').charAt(0).toUpperCase() + feedback.recommendation.slice(1)}
                      </Badge>

                      {feedback.strengths && (
                        <div className="mt-3">
                          <p className="text-sm font-medium text-green-700">Strengths:</p>
                          <p className="text-sm text-gray-700">{feedback.strengths}</p>
                        </div>
                      )}

                      {feedback.weaknesses && (
                        <div className="mt-2">
                          <p className="text-sm font-medium text-red-700">Areas for Improvement:</p>
                          <p className="text-sm text-gray-700">{feedback.weaknesses}</p>
                        </div>
                      )}

                      {feedback.notes && (
                        <div className="mt-2">
                          <p className="text-sm font-medium">Additional Notes:</p>
                          <p className="text-sm text-gray-700">{feedback.notes}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      </div>
    </DashboardLayout>
  );
};

export default InterviewDetailsPage;