/**
 * React Query hooks for candidate data
 */

import { useAuth } from '@/contexts/AuthContext';
import { useEnhancedQuery, useEnhancedMutation, queryKeys } from '@/lib/queryUtils';
import * as candidateService from '@/services/supabase/candidates';
import { Candidate, CandidateInsert, CandidateUpdate, CandidateRealtimeEvent } from '@/services/supabase/candidates';
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { parseExistingCandidateCV } from '@/services/cv-processing/cvParser';

/**
 * Hook to fetch all candidates
 */
export function useCandidates(jobId?: string) {
  return useEnhancedQuery<Candidate[]>(
    jobId ? queryKeys.candidates.byJob(jobId) : queryKeys.candidates.all,
    () => candidateService.getCandidates(jobId),
    {
      fallbackData: [],
      errorMessage: jobId
        ? `Failed to load candidates for job ${jobId}`
        : 'Failed to load candidates',
    }
  );
}

/**
 * Hook to fetch a specific candidate
 */
export function useCandidate(id: string) {
  return useEnhancedQuery<Candidate | null>(
    queryKeys.candidates.byId(id),
    () => candidateService.getCandidate(id),
    {
      enabled: !!id,
      fallbackData: null,
      errorMessage: `Failed to load candidate with ID ${id}`,
    }
  );
}

/**
 * Hook to create a new candidate
 */
export function useCreateCandidate() {
  const { user } = useAuth();

  return useEnhancedMutation<Candidate, CandidateInsert>(
    (candidate) => candidateService.createCandidate({
      ...candidate,
      user_id: candidate.user_id || user?.id,
    }),
    {
      errorMessage: 'Failed to create candidate',
      successMessage: 'Candidate created successfully',
      invalidateQueries: [queryKeys.candidates.all],
    }
  );
}

/**
 * Hook to update a candidate
 */
export function useUpdateCandidate() {
  return useEnhancedMutation<Candidate, { id: string; candidate: CandidateUpdate }>(
    ({ id, candidate }) => candidateService.updateCandidate(id, candidate),
    {
      errorMessage: 'Failed to update candidate',
      successMessage: 'Candidate updated successfully',
      invalidateQueries: [queryKeys.candidates.all],
      onSuccess: (data, variables) => {
        // Also invalidate the specific candidate query
        return [queryKeys.candidates.byId(variables.id)];
      },
    }
  );
}

/**
 * Hook to delete a candidate
 */
export function useDeleteCandidate() {
  return useEnhancedMutation<boolean, string>(
    (id) => candidateService.deleteCandidate(id),
    {
      errorMessage: 'Failed to delete candidate',
      successMessage: 'Candidate deleted successfully',
      invalidateQueries: [queryKeys.candidates.all],
    }
  );
}

/**
 * Hook to upload a CV file
 */
export function useUploadCV() {
  return useEnhancedMutation<string, File>(
    (file) => candidateService.uploadCV(file),
    {
      errorMessage: 'Failed to upload CV',
      successMessage: 'CV uploaded successfully',
    }
  );
}

/**
 * Hook to fetch candidate statistics
 */
export function useCandidateStatistics(jobId?: string) {
  return useEnhancedQuery(
    ['candidateStatistics', jobId],
    () => candidateService.getCandidateStatistics(jobId),
    {
      fallbackData: {
        total: 0,
        new: 0,
        reviewing: 0,
        interviewed: 0,
        hired: 0,
        rejected: 0
      },
      errorMessage: 'Failed to load candidate statistics',
    }
  );
}

/**
 * Hook to subscribe to real-time candidate updates
 *
 * This hook automatically subscribes to candidate updates and updates the React Query cache
 * when candidates are created, updated, or deleted.
 */
export function useCandidateSubscription(jobId?: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!user?.id) return;

    // Subscribe to candidate updates
    const subscription = candidateService.subscribeToCandidates(
      user.id,
      jobId,
      (event: CandidateRealtimeEvent, candidate: Candidate) => {
        // Handle different event types
        switch (event) {
          case 'INSERT':
            // Add the new candidate to the cache
            queryClient.setQueryData<Candidate[]>(
              jobId ? queryKeys.candidates.byJob(jobId) : queryKeys.candidates.all,
              (oldCandidates = []) => [candidate, ...oldCandidates]
            );

            // Invalidate candidate statistics
            queryClient.invalidateQueries({ queryKey: ['candidateStatistics', jobId] });
            break;

          case 'UPDATE':
            // Update the candidate in the cache
            queryClient.setQueryData<Candidate[]>(
              jobId ? queryKeys.candidates.byJob(jobId) : queryKeys.candidates.all,
              (oldCandidates = []) => oldCandidates.map(oldCandidate =>
                oldCandidate.id === candidate.id ? candidate : oldCandidate
              )
            );

            // Update the specific candidate in the cache
            queryClient.setQueryData(
              queryKeys.candidates.byId(candidate.id),
              candidate
            );

            // Invalidate candidate statistics if status changed
            queryClient.invalidateQueries({ queryKey: ['candidateStatistics', jobId] });
            break;

          case 'DELETE':
            // Remove the candidate from the cache
            queryClient.setQueryData<Candidate[]>(
              jobId ? queryKeys.candidates.byJob(jobId) : queryKeys.candidates.all,
              (oldCandidates = []) => oldCandidates.filter(oldCandidate => oldCandidate.id !== candidate.id)
            );

            // Remove the specific candidate from the cache
            queryClient.removeQueries({ queryKey: queryKeys.candidates.byId(candidate.id) });

            // Invalidate candidate statistics
            queryClient.invalidateQueries({ queryKey: ['candidateStatistics', jobId] });
            break;
        }
      }
    );

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, [user?.id, jobId, queryClient]);
}

/**
 * Hook to parse an existing candidate's CV
 */
export function useParseCandidateCV() {
  const queryClient = useQueryClient();

  return useEnhancedMutation(
    async ({ candidateId, cvUrl }: { candidateId: string; cvUrl: string }) => {
      await parseExistingCandidateCV(candidateId, cvUrl);
    },
    {
      onSuccess: () => {
        // Invalidate candidates queries to refresh the data
        queryClient.invalidateQueries({ queryKey: queryKeys.candidates.all });
        queryClient.invalidateQueries({ queryKey: ['candidateStatistics'] });
      },
      successMessage: 'CV parsed successfully',
      errorMessage: 'Failed to parse CV',
    }
  );
}
