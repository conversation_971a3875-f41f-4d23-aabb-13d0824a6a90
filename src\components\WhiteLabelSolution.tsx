import React from 'react';
import { Link } from 'react-router-dom';
import {
  <PERSON>alog,
  DialogPortal,
  DialogOverlay,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import { X } from 'lucide-react';

interface WhiteLabelSolutionProps {
  trigger?: React.ReactNode;
}

export const WhiteLabelSolution: React.FC<WhiteLabelSolutionProps> = ({
  trigger = <span className="text-white hover:text-recruiter-blue transition cursor-pointer">White Label Solution</span>
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <div className="fixed left-[50%] top-[50%] z-50 w-full max-w-lg translate-x-[-50%] translate-y-[-50%] duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          <div className="inline-block align-bottom bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div className="bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div className="sm:flex sm:items-start">
                <div className="mt-3 text-center sm:mt-0 sm:text-left w-full">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-2xl leading-6 font-semibold text-white">
                      White Label Solution
                    </h3>
                    <DialogClose className="p-2 text-gray-400 rounded-md hover:bg-gray-700">
                      <X className="w-5 h-5" />
                    </DialogClose>
                  </div>
                  <div className="mt-4">
                    <h4 className="text-lg font-medium text-sky-400 mb-2">What is White Labeling?</h4>
                    <p className="text-gray-300 mb-4">
                      Our White Label solution allows recruitment agencies to offer our AI-powered CV evaluation platform under their own brand. This means you get all our premium features, customized with your agency's branding.
                    </p>

                    <h4 className="text-lg font-medium text-sky-400 mb-2">Benefits for Agencies</h4>
                    <ul className="text-gray-300 list-disc list-inside mb-4">
                      <li>Custom branded platform</li>
                      <li>Full AI CV evaluation capabilities</li>
                      <li>Dedicated support team</li>
                      <li>Custom domain integration</li>
                      <li>Unlimited CV processing</li>
                      <li>Advanced analytics dashboard</li>
                      <li>API access for custom integration</li>
                    </ul>

                    <h4 className="text-lg font-medium text-sky-400 mb-2">Investment Range</h4>
                    <p className="text-gray-300 mb-4">
                      $5,000 - $10,000 depending on customization requirements and scale.
                    </p>

                    <p className="text-gray-300">Contact us to discuss your requirements:</p>
                    <a href="mailto:<EMAIL>" className="text-sky-400 hover:text-sky-300 mt-2 block">
                      <EMAIL>
                    </a>
                    <Link to="/contact" className="text-sky-400 hover:text-sky-300 mt-2 block">
                      Visit our contact page for more information
                    </Link>
                  </div>
                </div>
              </div>
            </div>
            <div className="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <DialogClose className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-gray-300 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-sky-500 sm:mt-0 sm:w-auto sm:text-sm">
                Close
              </DialogClose>
            </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
};
