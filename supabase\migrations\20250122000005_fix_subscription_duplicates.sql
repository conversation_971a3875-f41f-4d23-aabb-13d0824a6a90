-- Fix subscription duplicates and prevent future duplicates
-- This migration cleans up duplicate subscription records and adds a unique constraint

-- Step 1: Remove duplicate subscription records (keep the most recent one for each subscription_id)
DELETE FROM public.subscriptions 
WHERE id NOT IN (
  SELECT DISTINCT ON (subscription_id) id 
  FROM public.subscriptions 
  ORDER BY subscription_id, created_at DESC
);

-- Step 2: Add unique constraint to prevent future duplicates
ALTER TABLE public.subscriptions 
ADD CONSTRAINT unique_subscription_id UNIQUE (subscription_id);

-- Add comment for documentation
COMMENT ON CONSTRAINT unique_subscription_id ON public.subscriptions IS 'Ensures each Stripe subscription_id appears only once in the database';
