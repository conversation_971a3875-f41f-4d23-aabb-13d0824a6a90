import { supabase } from '@/lib/supabase';
import { FEATURES } from '@/config/paypal';

export interface PlanFeature {
  id: string;
  plan_name: string;
  feature_key: string;
  enabled: boolean;
  created_at: string;
  updated_at: string;
  updated_by?: string;
}

export interface PlanFeaturesConfig {
  [planName: string]: {
    [featureKey: string]: boolean;
  };
}

/**
 * Get all plan features from the database
 */
export const getPlanFeatures = async (): Promise<PlanFeature[]> => {
  const { data, error } = await supabase
    .from('plan_features')
    .select('*')
    .order('plan_name', { ascending: true })
    .order('feature_key', { ascending: true });

  if (error) {
    console.error('Error fetching plan features:', error);
    throw new Error(`Failed to fetch plan features: ${error.message}`);
  }

  return data || [];
};

/**
 * Get plan features in the format expected by the application
 */
export const getPlanFeaturesConfig = async (): Promise<PlanFeaturesConfig> => {
  const planFeatures = await getPlanFeatures();
  const config: PlanFeaturesConfig = {};

  planFeatures.forEach(feature => {
    if (!config[feature.plan_name]) {
      config[feature.plan_name] = {};
    }
    config[feature.plan_name][feature.feature_key] = feature.enabled;
  });

  return config;
};

/**
 * Update plan features configuration in the database
 */
export const updatePlanFeatures = async (
  config: PlanFeaturesConfig,
  updatedBy: string
): Promise<void> => {
  const updates: Array<{
    plan_name: string;
    feature_key: string;
    enabled: boolean;
    updated_by: string;
  }> = [];

  // Convert config to update array
  Object.entries(config).forEach(([planName, features]) => {
    Object.entries(features).forEach(([featureKey, enabled]) => {
      updates.push({
        plan_name: planName,
        feature_key: featureKey,
        enabled,
        updated_by: updatedBy
      });
    });
  });

  // Use upsert to insert or update
  const { error } = await supabase
    .from('plan_features')
    .upsert(updates, {
      onConflict: 'plan_name,feature_key'
    });

  if (error) {
    console.error('Error updating plan features:', error);
    throw new Error(`Failed to update plan features: ${error.message}`);
  }
};

/**
 * Get features for a specific plan
 */
export const getPlanFeaturesByPlan = async (planName: string): Promise<Record<string, boolean>> => {
  const { data, error } = await supabase
    .from('plan_features')
    .select('feature_key, enabled')
    .eq('plan_name', planName.toUpperCase());

  if (error) {
    console.error('Error fetching plan features for plan:', planName, error);
    throw new Error(`Failed to fetch features for plan ${planName}: ${error.message}`);
  }

  const features: Record<string, boolean> = {};
  data?.forEach(item => {
    features[item.feature_key] = item.enabled;
  });

  return features;
};

/**
 * Check if a specific feature is enabled for a plan
 */
export const isFeatureEnabledForPlan = async (
  planName: string,
  featureKey: string
): Promise<boolean> => {
  const { data, error } = await supabase
    .from('plan_features')
    .select('enabled')
    .eq('plan_name', planName.toUpperCase())
    .eq('feature_key', featureKey)
    .single();

  if (error) {
    console.error('Error checking feature for plan:', planName, featureKey, error);
    // Return false if feature not found or error
    return false;
  }

  return data?.enabled || false;
};

/**
 * Initialize plan features with default configuration if they don't exist
 */
export const initializePlanFeatures = async (): Promise<void> => {
  // Check if plan features already exist
  const { data: existingFeatures, error: checkError } = await supabase
    .from('plan_features')
    .select('id')
    .limit(1);

  if (checkError) {
    console.error('Error checking existing plan features:', checkError);
    return;
  }

  // If features already exist, don't initialize
  if (existingFeatures && existingFeatures.length > 0) {
    return;
  }

  // Initialize with default configuration from config file
  const defaultConfig = {
    STARTER: {
      [FEATURES.CV_UPLOAD]: true,
      [FEATURES.JOB_POSTING]: true,
      [FEATURES.COMPANY_MANAGEMENT]: true,
      [FEATURES.CV_EVALUATION]: true,
      [FEATURES.REPORTS]: true,
      [FEATURES.INTERVIEW_SCHEDULING]: false,
      [FEATURES.TEAM_MANAGEMENT]: false,
      [FEATURES.CUSTOM_SCORING]: false,
      [FEATURES.ADVANCED_ANALYTICS]: false,
      [FEATURES.API_ACCESS]: false,
      [FEATURES.WHITE_LABEL]: false,
      [FEATURES.PRIORITY_SUPPORT]: false
    },
    GROWTH: {
      [FEATURES.CV_UPLOAD]: true,
      [FEATURES.JOB_POSTING]: true,
      [FEATURES.COMPANY_MANAGEMENT]: true,
      [FEATURES.CV_EVALUATION]: true,
      [FEATURES.REPORTS]: true,
      [FEATURES.INTERVIEW_SCHEDULING]: true,
      [FEATURES.TEAM_MANAGEMENT]: true,
      [FEATURES.CUSTOM_SCORING]: false,
      [FEATURES.ADVANCED_ANALYTICS]: true,
      [FEATURES.API_ACCESS]: false,
      [FEATURES.WHITE_LABEL]: false,
      [FEATURES.PRIORITY_SUPPORT]: false
    },
    PRO: {
      [FEATURES.CV_UPLOAD]: true,
      [FEATURES.JOB_POSTING]: true,
      [FEATURES.COMPANY_MANAGEMENT]: true,
      [FEATURES.CV_EVALUATION]: true,
      [FEATURES.REPORTS]: true,
      [FEATURES.INTERVIEW_SCHEDULING]: true,
      [FEATURES.TEAM_MANAGEMENT]: true,
      [FEATURES.CUSTOM_SCORING]: true,
      [FEATURES.ADVANCED_ANALYTICS]: true,
      [FEATURES.API_ACCESS]: true,
      [FEATURES.WHITE_LABEL]: true,
      [FEATURES.PRIORITY_SUPPORT]: true
    }
  };

  try {
    await updatePlanFeatures(defaultConfig, 'system');
    console.log('Plan features initialized successfully');
  } catch (error) {
    console.error('Error initializing plan features:', error);
  }
};
