import React from 'react';
import { format } from 'date-fns';
import { getTimezoneByValue } from '@/lib/timezones';
import { 
  CalendarClock, 
  Clock, 
  MapPin, 
  Phone, 
  Video, 
  Users, 
  User, 
  Briefcase,
  Building,
  Check,
  X,
  Edit,
  Trash2
} from 'lucide-react';
import { useInterview, useInterviewParticipants, useDeleteInterview } from '@/hooks/use-interviews';
import { useAuth } from '@/contexts/AuthContext';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/components/ui/use-toast';

interface InterviewDetailsProps {
  interviewId: string;
  onEdit?: () => void;
  onDelete?: () => void;
}

const InterviewDetails: React.FC<InterviewDetailsProps> = ({
  interviewId,
  onEdit,
  onDelete
}) => {
  const { user } = useAuth();
  const { data: interview, isLoading } = useInterview(interviewId);
  const { data: participants, isLoading: isLoadingParticipants } = useInterviewParticipants(interviewId);
  const { mutate: deleteInterview, isPending: isDeleting } = useDeleteInterview();
  const { toast } = useToast();
  const [confirmDelete, setConfirmDelete] = React.useState(false);

  // Check if user is the owner of the interview
  const isOwner = user?.id === interview?.user_id || user?.id === interview?.created_by;

  // Handle delete
  const handleDelete = () => {
    deleteInterview(interviewId, {
      onSuccess: () => {
        setConfirmDelete(false);
        if (onDelete) onDelete();
        toast({
          title: 'Interview deleted',
          description: 'The interview has been deleted successfully.',
        });
      }
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-8 w-3/4" />
        </CardHeader>
        <CardContent className="space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
        </CardContent>
      </Card>
    );
  }

  if (!interview) {
    return (
      <Card>
        <CardContent className="py-8 text-center">
          <p className="text-gray-500">Interview not found</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-start justify-between">
        <div>
          <CardTitle className="text-xl">{interview.candidate_name}</CardTitle>
          <div className="flex items-center mt-1 text-sm text-gray-500">
            <Briefcase className="h-4 w-4 mr-1" />
            <span>{interview.job_title}</span>
          </div>
          <div className="flex items-center mt-1 text-sm text-gray-500">
            <Building className="h-4 w-4 mr-1" />
            <span>{interview.company_name}</span>
          </div>
        </div>
        <Badge
          variant={
            interview.status === 'scheduled' ? 'default' :
            interview.status === 'completed' ? 'secondary' :
            interview.status === 'cancelled' ? 'destructive' : 'outline'
          }
          className="capitalize"
        >
          {interview.status}
        </Badge>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-start">
            <CalendarClock className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium">Date & Time</p>
              <p className="text-gray-600">
                {format(new Date(interview.scheduled_at), 'EEEE, MMMM d, yyyy')}
              </p>
              <p className="text-gray-600">
                {format(new Date(interview.scheduled_at), 'h:mm a')}
                {interview.timezone && (
                  <span className="text-gray-500 ml-2">
                    ({getTimezoneByValue(interview.timezone)?.label || interview.timezone})
                  </span>
                )}
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <Clock className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium">Duration</p>
              <p className="text-gray-600">
                {interview.duration_minutes} minutes
              </p>
            </div>
          </div>

          <div className="flex items-start">
            {interview.interview_type === 'phone' ? (
              <Phone className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
            ) : interview.interview_type === 'video' ? (
              <Video className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
            ) : (
              <MapPin className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
            )}
            <div>
              <p className="font-medium">Interview Type</p>
              <p className="text-gray-600 capitalize">
                {interview.interview_type}
              </p>
            </div>
          </div>

          <div className="flex items-start">
            <MapPin className="h-5 w-5 mr-2 text-gray-500 mt-0.5" />
            <div>
              <p className="font-medium">Location</p>
              <p className="text-gray-600">
                {interview.location || 'Not specified'}
              </p>
            </div>
          </div>
        </div>

        <Separator />

        <div>
          <h3 className="font-medium flex items-center mb-3">
            <Users className="h-5 w-5 mr-2 text-gray-500" />
            Participants
          </h3>
          
          {isLoadingParticipants ? (
            <div className="space-y-2">
              <Skeleton className="h-6 w-full" />
              <Skeleton className="h-6 w-full" />
            </div>
          ) : participants && participants.length > 0 ? (
            <div className="space-y-2">
              {participants.map(participant => (
                <div key={participant.id} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <User className="h-4 w-4 mr-2 text-gray-500" />
                    <span>{participant.user_name}</span>
                  </div>
                  <Badge variant="outline" className="capitalize">
                    {participant.role}
                  </Badge>
                  <Badge
                    variant={
                      participant.status === 'confirmed' ? 'default' :
                      participant.status === 'declined' ? 'destructive' : 'outline'
                    }
                    className="capitalize"
                  >
                    {participant.status}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500 text-sm">No participants added yet</p>
          )}
        </div>

        {interview.notes && (
          <>
            <Separator />
            <div>
              <h3 className="font-medium mb-2">Notes</h3>
              <p className="text-gray-600 whitespace-pre-line">{interview.notes}</p>
            </div>
          </>
        )}
      </CardContent>
      
      {isOwner && (
        <CardFooter className="flex justify-between">
          <Button variant="outline" onClick={onEdit}>
            <Edit className="h-4 w-4 mr-2" />
            Edit
          </Button>
          
          <Dialog open={confirmDelete} onOpenChange={setConfirmDelete}>
            <DialogTrigger asChild>
              <Button variant="destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete Interview</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this interview? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setConfirmDelete(false)}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button variant="destructive" onClick={handleDelete} disabled={isDeleting}>
                  <Check className="h-4 w-4 mr-2" />
                  {isDeleting ? 'Deleting...' : 'Confirm Delete'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardFooter>
      )}
    </Card>
  );
};

export default InterviewDetails;
