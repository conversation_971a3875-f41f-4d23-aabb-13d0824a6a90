import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Users, Building, FileText, BarChart2, ArrowR<PERSON>, ChevronRight, ChevronDown, Check } from 'lucide-react';

// Story Card Component
interface StoryCardProps {
  title: string;
  subtitle: string;
  icon: React.ReactNode;
  color: string;
  isExpanded: boolean;
  onClick: () => void;
  children?: React.ReactNode;
}

const StoryCard: React.FC<StoryCardProps> = ({
  title,
  subtitle,
  icon,
  color,
  isExpanded,
  onClick,
  children
}) => {
  const colorClasses = {
    blue: {
      bg: 'bg-blue-500/10',
      border: 'border-blue-500/30',
      text: 'text-blue-400',
      iconBg: 'bg-blue-500/20'
    },
    purple: {
      bg: 'bg-purple-500/10',
      border: 'border-purple-500/30',
      text: 'text-purple-400',
      iconBg: 'bg-purple-500/20'
    },
    green: {
      bg: 'bg-green-500/10',
      border: 'border-green-500/30',
      text: 'text-green-400',
      iconBg: 'bg-green-500/20'
    }
  };
  
  const classes = colorClasses[color as keyof typeof colorClasses];
  
  return (
    <motion.div 
      className={`rounded-xl border ${isExpanded ? classes.border : 'border-gray-800'} overflow-hidden transition-colors duration-300 cursor-pointer`}
      layout
    >
      <div 
        className={`p-6 ${isExpanded ? classes.bg : 'bg-gray-800'}`}
        onClick={onClick}
      >
        <div className="flex items-start">
          <div className={`w-12 h-12 rounded-lg ${classes.iconBg} flex items-center justify-center mr-4 shrink-0`}>
            {icon}
          </div>
          <div className="flex-1">
            <h3 className="text-xl font-bold text-white mb-1">{title}</h3>
            <p className="text-gray-400">{subtitle}</p>
          </div>
          <div className="ml-4">
            <motion.div
              animate={{ rotate: isExpanded ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              <ChevronDown className={`h-6 w-6 ${isExpanded ? classes.text : 'text-gray-400'}`} />
            </motion.div>
          </div>
        </div>
      </div>
      
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="overflow-hidden"
          >
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

// Story Timeline Item Component
interface TimelineItemProps {
  step: number;
  title: string;
  description: string;
  isActive: boolean;
  color: string;
}

const TimelineItem: React.FC<TimelineItemProps> = ({
  step,
  title,
  description,
  isActive,
  color
}) => {
  const colorClasses = {
    blue: {
      bg: 'bg-blue-500',
      text: 'text-blue-400',
      border: 'border-blue-500'
    },
    purple: {
      bg: 'bg-purple-500',
      text: 'text-purple-400',
      border: 'border-purple-500'
    },
    green: {
      bg: 'bg-green-500',
      text: 'text-green-400',
      border: 'border-green-500'
    }
  };
  
  const classes = colorClasses[color as keyof typeof colorClasses];
  
  return (
    <div className="flex mb-6 last:mb-0">
      <div className="mr-4 relative">
        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${isActive ? classes.bg : 'bg-gray-700'} relative z-10`}>
          {isActive ? <Check size={16} className="text-white" /> : <span className="text-white text-sm">{step}</span>}
        </div>
        {step > 1 && (
          <div className="absolute top-0 bottom-0 left-1/2 transform -translate-x-1/2 w-0.5 bg-gray-700 -translate-y-full h-full"></div>
        )}
      </div>
      <div className={`flex-1 p-4 rounded-lg ${isActive ? `border ${classes.border} bg-gray-800` : 'bg-gray-800'}`}>
        <h4 className={`font-medium mb-1 ${isActive ? classes.text : 'text-white'}`}>{title}</h4>
        <p className="text-gray-400 text-sm">{description}</p>
      </div>
    </div>
  );
};

// Agency Story Component
const AgencyStory: React.FC<{ color: string; activeStep: number; onNextStep: () => void }> = ({ 
  color, 
  activeStep,
  onNextStep
}) => {
  const timelineItems = [
    {
      title: 'Challenge Identification',
      description: 'Agency struggled with manual CV screening, taking 2-3 hours per candidate.'
    },
    {
      title: 'Platform Implementation',
      description: 'Integrated Sourcio.ai into their workflow, trained team in 1 day.'
    },
    {
      title: 'Process Optimization',
      description: 'Automated initial CV screening and skills matching with companies.'
    },
    {
      title: 'Results & ROI',
      description: 'Reduced screening time by 85%, improved match quality by 40%.'
    }
  ];
  
  const colorClasses = {
    blue: {
      text: 'text-blue-400',
      bg: 'bg-blue-500',
      border: 'border-blue-500/30',
      buttonBg: 'bg-blue-600 hover:bg-blue-700'
    },
    purple: {
      text: 'text-purple-400',
      bg: 'bg-purple-500',
      border: 'border-purple-500/30',
      buttonBg: 'bg-purple-600 hover:bg-purple-700'
    },
    green: {
      text: 'text-green-400',
      bg: 'bg-green-500',
      border: 'border-green-500/30',
      buttonBg: 'bg-green-600 hover:bg-green-700'
    }
  };
  
  const classes = colorClasses[color as keyof typeof colorClasses];
  
  return (
    <div className="p-6 bg-gray-900 rounded-b-xl">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-white font-medium">Agency Journey</h3>
          <div className={`px-2 py-1 rounded ${classes.text} text-xs border ${classes.border}`}>
            Step {activeStep} of {timelineItems.length}
          </div>
        </div>
        
        <div className="space-y-1 mb-6">
          {timelineItems.map((item, index) => (
            <TimelineItem
              key={index}
              step={index + 1}
              title={item.title}
              description={item.description}
              isActive={index + 1 <= activeStep}
              color={color}
            />
          ))}
        </div>
        
        {activeStep === 4 && (
          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <h4 className="text-white font-medium mb-2">Results Summary</h4>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-900 p-3 rounded-lg">
                <div className={`text-2xl font-bold ${classes.text} mb-1`}>85%</div>
                <div className="text-gray-400 text-sm">Reduction in screening time</div>
              </div>
              <div className="bg-gray-900 p-3 rounded-lg">
                <div className={`text-2xl font-bold ${classes.text} mb-1`}>40%</div>
                <div className="text-gray-400 text-sm">Improvement in match quality</div>
              </div>
              <div className="bg-gray-900 p-3 rounded-lg">
                <div className={`text-2xl font-bold ${classes.text} mb-1`}>3x</div>
                <div className="text-gray-400 text-sm">Increase in candidates processed</div>
              </div>
              <div className="bg-gray-900 p-3 rounded-lg">
                <div className={`text-2xl font-bold ${classes.text} mb-1`}>$24k</div>
                <div className="text-gray-400 text-sm">Annual cost savings</div>
              </div>
            </div>
          </div>
        )}
        
        {activeStep < timelineItems.length ? (
          <button 
            className={`w-full py-2 ${classes.buttonBg} text-white rounded-lg transition-colors flex items-center justify-center`}
            onClick={onNextStep}
          >
            Continue to Next Step <ArrowRight size={16} className="ml-2" />
          </button>
        ) : (
          <button 
            className="w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors"
            onClick={() => onNextStep()}
          >
            Restart Journey
          </button>
        )}
      </div>
    </div>
  );
};

// Main Component
export const InteractiveStoryCards: React.FC = () => {
  const [expandedCard, setExpandedCard] = useState<string | null>(null);
  const [agencySteps, setAgencySteps] = useState({
    'large-agency': 1,
    'small-agency': 1,
    'startup-agency': 1
  });
  
  const handleCardClick = (cardId: string) => {
    setExpandedCard(expandedCard === cardId ? null : cardId);
  };
  
  const handleNextStep = (cardId: string) => {
    setAgencySteps(prev => ({
      ...prev,
      [cardId]: prev[cardId as keyof typeof prev] < 4 ? prev[cardId as keyof typeof prev] + 1 : 1
    }));
  };
  
  return (
    <section className="py-16 bg-recruiter-darknavy">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Recruitment Success Stories</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            See how different recruitment agencies solved real problems using our platform
          </p>
        </div>
        
        <div className="max-w-3xl mx-auto space-y-6">
          <StoryCard
            title="Large Agency (500+ CVs/month)"
            subtitle="How a large recruitment firm automated their CV screening process"
            icon={<Building size={24} className="text-blue-400" />}
            color="blue"
            isExpanded={expandedCard === 'large-agency'}
            onClick={() => handleCardClick('large-agency')}
          >
            <AgencyStory 
              color="blue" 
              activeStep={agencySteps['large-agency']} 
              onNextStep={() => handleNextStep('large-agency')} 
            />
          </StoryCard>
          
          <StoryCard
            title="Boutique Agency (50-100 CVs/month)"
            subtitle="How a specialized recruitment agency improved match quality"
            icon={<Users size={24} className="text-purple-400" />}
            color="purple"
            isExpanded={expandedCard === 'small-agency'}
            onClick={() => handleCardClick('small-agency')}
          >
            <AgencyStory 
              color="purple" 
              activeStep={agencySteps['small-agency']} 
              onNextStep={() => handleNextStep('small-agency')} 
            />
          </StoryCard>
          
          <StoryCard
            title="Startup Agency (Growing Fast)"
            subtitle="How a new agency scaled their operations without adding headcount"
            icon={<BarChart2 size={24} className="text-green-400" />}
            color="green"
            isExpanded={expandedCard === 'startup-agency'}
            onClick={() => handleCardClick('startup-agency')}
          >
            <AgencyStory 
              color="green" 
              activeStep={agencySteps['startup-agency']} 
              onNextStep={() => handleNextStep('startup-agency')} 
            />
          </StoryCard>
        </div>
      </div>
    </section>
  );
};
