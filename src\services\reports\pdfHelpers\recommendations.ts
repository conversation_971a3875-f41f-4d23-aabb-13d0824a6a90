import { jsPDF } from 'jspdf';
import { ReportData, ReportType } from '../types';

/**
 * Helper function to generate recommendations based on report type and data
 * @param doc The PDF document
 * @param reportData The report data
 * @param reportType The type of report
 * @param startY The Y position to start drawing
 * @returns The new Y position after drawing
 */
export const addRecommendations = (doc: jsPDF, reportData: ReportData, reportType: ReportType, startY: number): number => {
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;

  // Check if we need to add a page break
  if (startY > pageHeight - 100) {
    doc.addPage();
    startY = 20;
  }

  // Add section title
  doc.setFontSize(16);
  doc.setTextColor(41, 128, 185);
  doc.text('Recommendations', 14, startY);
  startY += 10;

  // If recommendations are provided, use them
  if (reportData.recommendations && reportData.recommendations.length > 0) {
    doc.setFontSize(10);
    doc.setTextColor(70);

    reportData.recommendations.forEach((recommendation, index) => {
      const bulletPoint = `• ${recommendation}`;
      const lines = doc.splitTextToSize(bulletPoint, pageWidth - 40);
      doc.text(lines, 14, startY);
      startY += lines.length * 6 + 4;
    });

    return startY;
  }

  // Otherwise, generate recommendations based on the report type and data
  doc.setFontSize(10);
  doc.setTextColor(70);

  let recommendations: string[] = [];

  switch (reportType) {
    case 'recruitment_funnel':
      if (reportData.data && reportData.data.length > 0) {
        // Find the stage with the lowest conversion rate
        let lowestConversionStage = { from: '', to: '', rate: 100 };

        for (let i = 0; i < reportData.data.length - 1; i++) {
          const fromStage = reportData.data[i];
          const toStage = reportData.data[i + 1];
          const fromCount = fromStage.count || 0;
          const toCount = toStage.count || 0;

          if (fromCount > 0) {
            const rate = (toCount / fromCount) * 100;
            if (rate < lowestConversionStage.rate) {
              lowestConversionStage = {
                from: fromStage.stage,
                to: toStage.stage,
                rate
              };
            }
          }
        }

        // Add recommendations based on the analysis
        if (lowestConversionStage.from) {
          recommendations.push(
            `Focus on improving the conversion from ${lowestConversionStage.from} to ${lowestConversionStage.to}, which has the lowest rate at ${lowestConversionStage.rate.toFixed(1)}%.`
          );
        }

        // Add general recommendations
        recommendations.push(
          'Regularly review your recruitment process for bottlenecks and inefficiencies.',
          'Consider implementing structured interviews to improve candidate evaluation consistency.',
          'Track and analyze reasons for candidate dropoffs at each stage.',
          'Set up automated follow-ups to reduce candidate ghosting.',
          'Optimize job descriptions to attract more qualified candidates.'
        );
      }
      break;

    case 'time_to_hire':
      if (reportData.data && reportData.data.length > 0) {
        // Calculate average time to hire
        const totalDays = reportData.data.reduce((sum, item) => sum + (item.days || 0), 0);
        const avgDays = Math.round(totalDays / reportData.data.length);

        // Find slowest position
        let slowestPosition = { position: '', days: 0 };
        reportData.data.forEach(item => {
          const days = item.days || 0;
          if (days > slowestPosition.days) {
            slowestPosition = { position: item.position, days };
          }
        });

        // Add recommendations based on the analysis
        if (avgDays > 30) {
          recommendations.push(
            'Your average time to hire is above the industry benchmark of 30 days. Consider streamlining your recruitment process.'
          );
        }

        if (slowestPosition.position) {
          recommendations.push(
            `Review the hiring process for ${slowestPosition.position}, which takes the longest to fill at ${slowestPosition.days} days.`
          );
        }

        // Add general recommendations
        recommendations.push(
          'Implement a more efficient screening process to reduce time-to-hire.',
          'Consider using pre-employment assessments to quickly identify qualified candidates.',
          'Streamline the interview scheduling process to reduce delays.',
          'Set clear timelines for each stage of the recruitment process.',
          'Analyze which stages take the longest and look for ways to optimize them.'
        );
      }
      break;

    case 'source_effectiveness':
      if (reportData.data && reportData.data.length > 0) {
        // Find best and worst sources
        let bestSource = { source: '', conversion: 0, quality: 0 };
        let worstSource = { source: '', conversion: 100, quality: 100 };

        reportData.data.forEach(item => {
          const applicants = item.applicants || 0;
          const hires = item.hires || 0;
          const conversion = applicants > 0 ? (hires / applicants) * 100 : 0;
          const quality = item.quality || 0;

          // Check for best source (based on conversion and quality)
          if (conversion > bestSource.conversion && quality >= bestSource.quality) {
            bestSource = { source: item.source, conversion, quality };
          }

          // Check for worst source
          if (applicants > 0 && conversion < worstSource.conversion && quality <= worstSource.quality) {
            worstSource = { source: item.source, conversion, quality };
          }
        });

        // Add recommendations based on the analysis
        if (bestSource.source) {
          recommendations.push(
            `Allocate more resources to ${bestSource.source}, which has the best combination of conversion rate (${bestSource.conversion.toFixed(1)}%) and candidate quality (${bestSource.quality.toFixed(1)}%).`
          );
        }

        if (worstSource.source) {
          recommendations.push(
            `Reconsider your investment in ${worstSource.source}, which has a low conversion rate (${worstSource.conversion.toFixed(1)}%) and candidate quality (${worstSource.quality.toFixed(1)}%).`
          );
        }

        // Add general recommendations
        recommendations.push(
          'Diversify your recruitment sources to reach a wider pool of candidates.',
          'Track cost per hire for each source to optimize your recruitment budget.',
          'Regularly review the quality of hires from each source.',
          'Consider implementing a referral program if you don\'t already have one.',
          'Test new recruitment channels on a small scale before investing heavily.'
        );
      }
      break;

    default:
      // Generic recommendations
      recommendations.push(
        'Regularly review your recruitment metrics to identify trends and opportunities for improvement.',
        'Set clear goals and benchmarks for your recruitment process.',
        'Invest in training for your recruitment team.',
        'Collect feedback from candidates and new hires to improve the recruitment experience.',
        'Stay up-to-date with industry trends and best practices.'
      );
      break;
  }

  // Add the recommendations
  recommendations.forEach((recommendation, index) => {
    const bulletPoint = `• ${recommendation}`;
    const lines = doc.splitTextToSize(bulletPoint, pageWidth - 40);
    doc.text(lines, 14, startY);
    startY += lines.length * 6 + 4;
  });

  return startY;
};
