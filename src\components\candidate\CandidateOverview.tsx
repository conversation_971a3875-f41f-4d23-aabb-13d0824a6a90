import React, { useState, useMemo } from 'react';
import { Card, CardHeader, CardContent, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Star,
  Mail,
  MessageSquare,
  ClipboardList,
  Briefcase,
  MapPin,
  FileText,
  Download,
  Check,
  AlertCircle,
  Calendar,
  CalendarClock,
  Phone,
  Maximize2,
  X
} from 'lucide-react';
import { formatDate } from '@/utils/candidateUtils';
import CVPreview from '@/components/cv/CVPreview.jsx';
import JobMatchRadarChart from '@/components/cv/JobMatchRadarChart';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import ProPlanGuard from '@/components/guards/ProPlanGuard';

interface CandidateOverviewProps {
  candidate: any;
  evaluations: any[];
  evaluationData: any;
  status: string;
  setActiveTab: (tab: string) => void;
  setIsEvaluateModalOpen: (isOpen: boolean) => void;
  setIsScheduleInterviewModalOpen: (isOpen: boolean) => void;
  getStatusColor: (status: string) => string;
  getProgressColor: (score: number) => string;
  getMatchScoreColor: (score: number) => string;
}

const CandidateOverview: React.FC<CandidateOverviewProps> = ({
  candidate,
  evaluations,
  evaluationData,
  status,
  setActiveTab,
  setIsEvaluateModalOpen,
  setIsScheduleInterviewModalOpen,
  getStatusColor,
  getProgressColor,
  getMatchScoreColor
}) => {
  // State for modals
  const [isCVModalOpen, setIsCVModalOpen] = useState(false);
  const [selectedJob, setSelectedJob] = useState<any>(null);

  // Create a combined topMatchingJobs array from all evaluations
  const allJobMatches = useMemo(() => {
    // Only process evaluations from the evaluations table
    let jobMatches: any[] = [];

    if (evaluations && evaluations.length > 0) {
      const evaluationJobMatches = evaluations.flatMap(evaluation => {
        try {
          const summary = JSON.parse(evaluation.evaluation_summary);

          // Case 1: jobSpecificMatches exists in summary.matchResult
          if (summary.matchResult && summary.matchResult.jobSpecificMatches &&
              summary.matchResult.jobSpecificMatches.length > 0) {
            return summary.matchResult.jobSpecificMatches;
          }

          // Case 2: topMatchingJobs exists in summary.matchResult
          if (summary.matchResult && summary.matchResult.topMatchingJobs &&
              summary.matchResult.topMatchingJobs.length > 0) {
            return summary.matchResult.topMatchingJobs;
          }

          // Case 3: jobSpecificMatches exists directly in summary
          if (summary.jobSpecificMatches && summary.jobSpecificMatches.length > 0) {
            return summary.jobSpecificMatches;
          }

          // Case 4: topMatchingJobs exists directly in summary
          if (summary.topMatchingJobs && summary.topMatchingJobs.length > 0) {
            return summary.topMatchingJobs;
          }

          // Case 5: For specific job evaluations, create a synthetic job match
          if (evaluation.evaluation_mode === 'specific-job' ||
              (summary.position && (summary.overallMatch || summary.overallScore))) {

            // Extract skill scores if available
            let skillsScore = 70;
            if (summary.skillsMatch && summary.skillsMatch.score) {
              skillsScore = summary.skillsMatch.score;
            } else if (summary.skills && summary.skills.length > 0) {
              skillsScore = Math.round(summary.skills.reduce((sum, skill) => sum + (skill.match || 0), 0) / summary.skills.length);
            }

            // Extract experience score if available
            let experienceScore = 70;
            if (summary.experienceMatch && summary.experienceMatch.score) {
              experienceScore = summary.experienceMatch.score;
            } else if (summary.experience && summary.experience.length > 0) {
              experienceScore = Math.round(summary.experience.reduce((sum, exp) => sum + (exp.relevance || 0), 0) / summary.experience.length);
            }

            // Extract education score if available
            let educationScore = 70;
            if (summary.educationMatch && summary.educationMatch.score) {
              educationScore = summary.educationMatch.score;
            } else if (summary.education && summary.education.length > 0) {
              educationScore = Math.round(summary.education.reduce((sum, edu) => sum + (edu.relevance || 0), 0) / summary.education.length);
            }

            // Create synthetic job object
            const syntheticJob = {
              jobId: evaluation.job_id || 'specific-job',
              jobTitle: evaluation.job_title || summary.position || 'Specific Job',
              overallScore: evaluation.evaluation_score || summary.overallMatch || summary.overallScore || 0,
              skillsScore: skillsScore,
              experienceScore: experienceScore,
              educationScore: educationScore,
              locationScore: 70,
              strengths: summary.matchResult?.strengths || summary.strengths || [],
              gaps: summary.matchResult?.gaps || summary.gaps || summary.weaknesses || [],
              companyName: evaluation.company_name || 'Unknown Company',
              recommendation: summary.matchResult?.recommendation || summary.recommendation || '',
              // Add analysis text as description if available
              description: summary.matchResult?.skillsMatch?.analysis ||
                          summary.skillsMatch?.analysis ||
                          summary.matchResult?.experienceMatch?.analysis ||
                          summary.experienceMatch?.analysis ||
                          ''
            };

            return [syntheticJob];
          }

          return [];
        } catch (error) {
          console.error('Error parsing job matches:', error);
          return [];
        }
      });

      jobMatches = [...jobMatches, ...evaluationJobMatches];
    }

    // Remove duplicates based on jobId and jobTitle (in case jobId is not unique)
    const uniqueJobMatches = Array.from(
      new Map(jobMatches.map(job => [`${job.jobId}-${job.jobTitle}`, job])).values()
    );

    // Sort by score (highest first)
    return uniqueJobMatches.sort((a, b) => b.overallScore - a.overallScore);
  }, [evaluations]);
  return (
    <div>
      {/* CV Document Modal */}
      <Dialog open={isCVModalOpen} onOpenChange={setIsCVModalOpen}>
        <DialogContent className="max-w-4xl h-[90vh] flex flex-col">
          <DialogHeader>
            <DialogTitle>CV Document: {candidate.name}</DialogTitle>
          </DialogHeader>
          <div className="flex-grow overflow-auto">
            <CVPreview
              url={candidate.cv_url}
              fileName={candidate.name ? `${candidate.name.replace(/\s+/g, '_')}.pdf` : 'resume.pdf'}
            />
          </div>
        </DialogContent>
      </Dialog>

      {/* Job Details Modal */}
      <Dialog open={!!selectedJob} onOpenChange={(open) => !open && setSelectedJob(null)}>
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <div>
              <DialogTitle className="text-xl">{selectedJob?.jobTitle}</DialogTitle>
              <DialogDescription className="text-gray-500">{selectedJob?.companyName}</DialogDescription>
            </div>
          </DialogHeader>

          {selectedJob && (
            <div className="space-y-6 py-4">
              {/* Overall match score */}
              <div className="flex items-center justify-center">
                <div className="relative">
                  <div
                    className="w-32 h-32 rounded-full border-8 flex items-center justify-center"
                    style={{
                      borderColor: getProgressColor(selectedJob.overallScore),
                      background: `conic-gradient(${getProgressColor(selectedJob.overallScore)} ${selectedJob.overallScore}%, transparent 0)`
                    }}
                  >
                    <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center">
                      <div className="text-center">
                        <div className={`text-3xl font-bold ${getMatchScoreColor(selectedJob.overallScore)}`}>
                          {selectedJob.overallScore}%
                        </div>
                        <div className="text-xs text-gray-400">Match</div>
                      </div>
                    </div>
                  </div>
                  <div className="absolute top-0 right-0">
                    <Star className={`h-8 w-8 ${getMatchScoreColor(selectedJob.overallScore)}`} fill="currentColor" />
                  </div>
                </div>
              </div>

              {/* Score breakdown */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="text-sm font-medium mb-3">Score Breakdown</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div>
                    <p className="text-xs text-gray-500">Skills</p>
                    <div className="flex items-center mt-1">
                      <span className={`font-medium ${getMatchScoreColor(selectedJob.skillsScore)}`}>
                        {selectedJob.skillsScore}%
                      </span>
                      <Progress
                        value={selectedJob.skillsScore}
                        className="h-1.5 w-16 ml-2"
                        indicatorClassName={
                          selectedJob.skillsScore >= 90 ? 'bg-green-500' :
                          selectedJob.skillsScore >= 75 ? 'bg-blue-500' :
                          selectedJob.skillsScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                        }
                      />
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Experience</p>
                    <div className="flex items-center mt-1">
                      <span className={`font-medium ${getMatchScoreColor(selectedJob.experienceScore)}`}>
                        {selectedJob.experienceScore}%
                      </span>
                      <Progress
                        value={selectedJob.experienceScore}
                        className="h-1.5 w-16 ml-2"
                        indicatorClassName={
                          selectedJob.experienceScore >= 90 ? 'bg-green-500' :
                          selectedJob.experienceScore >= 75 ? 'bg-blue-500' :
                          selectedJob.experienceScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                        }
                      />
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Education</p>
                    <div className="flex items-center mt-1">
                      <span className={`font-medium ${getMatchScoreColor(selectedJob.educationScore)}`}>
                        {selectedJob.educationScore}%
                      </span>
                      <Progress
                        value={selectedJob.educationScore}
                        className="h-1.5 w-16 ml-2"
                        indicatorClassName={
                          selectedJob.educationScore >= 90 ? 'bg-green-500' :
                          selectedJob.educationScore >= 75 ? 'bg-blue-500' :
                          selectedJob.educationScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                        }
                      />
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Location</p>
                    <div className="flex items-center mt-1">
                      <span className={`font-medium ${getMatchScoreColor(selectedJob.locationScore)}`}>
                        {selectedJob.locationScore}%
                      </span>
                      <Progress
                        value={selectedJob.locationScore}
                        className="h-1.5 w-16 ml-2"
                        indicatorClassName={
                          selectedJob.locationScore >= 90 ? 'bg-green-500' :
                          selectedJob.locationScore >= 75 ? 'bg-blue-500' :
                          selectedJob.locationScore >= 60 ? 'bg-amber-500' : 'bg-red-500'
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Strengths and gaps */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <Check className="h-4 w-4 text-green-500 mr-1" /> Strengths
                  </h3>
                  <ul className="space-y-2">
                    {selectedJob.strengths && selectedJob.strengths.length > 0 ? (
                      selectedJob.strengths.map((strength, i) => (
                        <li key={i} className="text-sm text-gray-700 flex items-start">
                          <Check className="h-3 w-3 text-green-500 mr-1 mt-0.5" />
                          <span>{strength}</span>
                        </li>
                      ))
                    ) : (
                      <li className="text-sm text-gray-500">No specific strengths identified</li>
                    )}
                  </ul>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium mb-3 flex items-center">
                    <AlertCircle className="h-4 w-4 text-amber-500 mr-1" /> Areas for Improvement
                  </h3>
                  <ul className="space-y-2">
                    {selectedJob.gaps && selectedJob.gaps.length > 0 ? (
                      selectedJob.gaps.map((gap, i) => (
                        <li key={i} className="text-sm text-gray-700 flex items-start">
                          <AlertCircle className="h-3 w-3 text-amber-500 mr-1 mt-0.5" />
                          <span>{gap}</span>
                        </li>
                      ))
                    ) : (
                      <li className="text-sm text-gray-500">No specific gaps identified</li>
                    )}
                  </ul>
                </div>
              </div>

              {/* Recommendation */}
              {selectedJob.recommendation && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium mb-2">Recommendation</h3>
                  <p className="text-sm text-gray-700">{selectedJob.recommendation}</p>
                </div>
              )}
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setSelectedJob(null)}
              className="mr-2"
            >
              Close
            </Button>
            <Button
              className="bg-recruiter-lightblue hover:bg-blue-500"
              onClick={() => {
                setSelectedJob(null);
                setActiveTab('evaluation');
              }}
            >
              View All Evaluations
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-6">
        {/* Left Column (Main Content) - 8/12 width on large screens */}
        <div className="lg:col-span-8 space-y-6">
          {/* Top section: Candidate Summary and Evaluation Summary */}
        <div className="grid grid-cols-1 gap-6">
          {/* Candidate Summary and Evaluation Summary */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Candidate Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">Full Name</p>
                    <p className="font-medium">{candidate.name}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Email</p>
                    <p className="font-medium">{candidate.email}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Phone</p>
                    <p className="font-medium">{candidate.phone}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Location</p>
                    <p className="font-medium">{candidate.location}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Status</p>
                    <Badge className={getStatusColor(status)}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </Badge>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Uploaded</p>
                    <p className="font-medium">{formatDate(candidate.created_at)}</p>
                  </div>
                  {candidate.source && (
                    <div>
                      <p className="text-sm text-gray-500">Source</p>
                      <p className="font-medium">{candidate.source}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Evaluation Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {candidate.matchScore ? (
                  <>
                    <div className="flex justify-center mb-4">
                      <div className="relative">
                        <div
                          className="w-32 h-32 rounded-full border-8 flex items-center justify-center"
                          style={{
                            borderColor: getProgressColor(candidate.matchScore),
                            background: `conic-gradient(${getProgressColor(candidate.matchScore)} ${candidate.matchScore}%, transparent 0)`
                          }}
                        >
                          <div className="w-24 h-24 bg-white rounded-full flex items-center justify-center">
                            <div className="text-center">
                              <div className={`text-3xl font-bold ${getMatchScoreColor(candidate.matchScore)}`}>
                                {candidate.matchScore}%
                              </div>
                              <div className="text-xs text-gray-400">Match</div>
                            </div>
                          </div>
                        </div>
                        <div className="absolute top-0 right-0">
                          <Star className={`h-8 w-8 ${getMatchScoreColor(candidate.matchScore)}`} fill="currentColor" />
                        </div>
                      </div>
                    </div>

                    <div className="text-center mb-4">
                      {(() => {
                        // Count the total number of job-specific matches across all evaluations
                        let totalJobMatches = 0;

                        evaluations.forEach(evaluation => {
                          try {
                            const summary = JSON.parse(evaluation.evaluation_summary);
                            if (summary.matchResult && summary.matchResult.jobSpecificMatches) {
                              totalJobMatches += summary.matchResult.jobSpecificMatches.length;
                            } else if (evaluation.evaluation_mode === 'specific-job') {
                              // If there are no job-specific matches but it's a specific-job evaluation,
                              // count it as one job match
                              totalJobMatches += 1;
                            }
                          } catch (error) {
                            console.error('Error parsing evaluation summary:', error);
                            // If we can't parse the summary, assume it's at least one job match
                            totalJobMatches += 1;
                          }
                        });

                        // If we still have no job matches but have evaluations, use the number of evaluations as a fallback
                        if (totalJobMatches === 0 && evaluations.length > 0) {
                          totalJobMatches = evaluations.length;
                        }

                        return (
                          <p className="text-gray-500">
                            {totalJobMatches} evaluation{totalJobMatches !== 1 ? 's' : ''} performed
                          </p>
                        );
                      })()}
                      <p className="text-sm text-gray-400">
                        Last evaluation: {evaluations.length > 0 ? evaluations[0].evaluation_date : 'Unknown'}
                      </p>
                    </div>

                    <Button
                      className="w-full bg-recruiter-lightblue hover:bg-blue-500"
                      onClick={() => setActiveTab('evaluation')}
                    >
                      <ClipboardList className="mr-2 h-4 w-4" /> View Evaluations
                    </Button>
                  </>
                ) : (
                  <div className="text-center py-8">
                    <Star className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-800">Not Evaluated Yet</h3>
                    <p className="text-gray-500 mt-1 mb-4">
                      Evaluate this candidate to see their match score and details.
                    </p>
                    <div className="flex gap-2 justify-center">
                      <Button
                        className="bg-recruiter-lightblue hover:bg-blue-500"
                        onClick={() => setIsEvaluateModalOpen(true)}
                      >
                        <Star className="mr-2 h-4 w-4" /> Evaluate Now
                      </Button>
                      <ProPlanGuard featureName="Interview Scheduling">
                        <Button
                          variant="outline"
                          onClick={() => setIsScheduleInterviewModalOpen(true)}
                        >
                          <CalendarClock className="mr-2 h-4 w-4" /> Schedule Interview
                        </Button>
                      </ProPlanGuard>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Job Recommendations Section */}

        {allJobMatches && allJobMatches.length > 0 && (
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-gray-800 text-lg">Recommended Jobs</CardTitle>
                {allJobMatches &&
                  allJobMatches.length > 0 &&
                  !allJobMatches.some(job => job.overallScore >= 60) && (
                  <Badge variant="outline" className="text-amber-500 border-amber-200 bg-amber-50">
                    No strong matches found
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">

              {allJobMatches &&
               !allJobMatches.some(job => job.overallScore >= 60) && (
                <div className="p-3 rounded-lg border border-amber-200 bg-amber-50 mb-4">
                  <p className="text-sm text-amber-700">
                    This candidate doesn't seem to be a strong fit for any of the available jobs. Consider evaluating against different positions or reviewing their qualifications.
                  </p>
                </div>
              )}
              <div className="space-y-3">
                {allJobMatches && allJobMatches.length > 0 ? (
                  allJobMatches.slice(0, 3).map((job, index) => {
                  // Determine if this is a good match (score >= 60)
                  const isGoodMatch = job.overallScore >= 60;
                  // Only show "Best Match" if it's the highest score AND it's a good match
                  const isBestMatch = index === 0 && isGoodMatch;

                  return (
                    <div
                      key={job.jobId}
                      className={`p-3 rounded-lg border ${isBestMatch ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'} hover:shadow-md transition-all duration-200`}
                    >
                      <div className="flex justify-between items-center">
                        <div>
                          <h3 className="font-medium text-gray-800">
                            {isBestMatch && <Badge className="mr-2 bg-green-500 text-white">Best Match</Badge>}
                            {job.jobTitle}
                          </h3>
                          <p className="text-xs text-gray-500 mt-1">
                            {job.companyName || 'Unknown Company'}
                          </p>
                        </div>
                        <div className="flex items-center">
                          <span className={`text-lg font-bold ${getMatchScoreColor(job.overallScore)}`}>
                            {job.overallScore}%
                          </span>
                          <Star className={`ml-1 h-4 w-4 ${getMatchScoreColor(job.overallScore)}`} fill="currentColor" />
                        </div>
                      </div>

                      {/* Job description - brief preview */}
                      {job.description && (
                        <div className="mt-2">
                          <p className="text-sm text-gray-600 line-clamp-2">{job.description}</p>
                        </div>
                      )}

                      {/* View details button */}
                      <div className="mt-3 flex justify-end">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          onClick={() => setSelectedJob(job)}
                        >
                          <Maximize2 className="mr-1 h-3 w-3" /> View Details
                        </Button>
                      </div>
                  </div>
                );
                }))
                : (
                  <div className="text-center py-4">
                    <p className="text-gray-500">No job matches available</p>
                  </div>
                )}
                <Button
                  variant="outline"
                  className="w-full text-sm"
                  onClick={() => setActiveTab('evaluation')}
                >
                  View All Job Matches
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Skills and Strengths/Weaknesses Section */}
        {evaluationData && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Skills Summary */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Key Skills</CardTitle>
              </CardHeader>
              <CardContent>
                {evaluationData.skills && evaluationData.skills.length > 0 ? (
                  <div className="space-y-3">
                    {evaluationData.skills.slice(0, 5).map((skill) => (
                      <div key={skill.name} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="font-medium">{skill.name}</span>
                          <span className={getMatchScoreColor(skill.match)}>{skill.match}%</span>
                        </div>
                        <Progress
                          value={skill.match}
                          className="h-2"
                          indicatorClassName={
                            skill.match >= 90 ? 'bg-green-500' :
                            skill.match >= 75 ? 'bg-blue-500' :
                            skill.match >= 60 ? 'bg-amber-500' : 'bg-red-500'
                          }
                        />
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-2">No skills data available</p>
                )}
              </CardContent>
            </Card>

            {/* Strengths & Areas for Improvement */}
            <div className="space-y-6">
              <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                <CardHeader>
                  <CardTitle className="text-gray-800 text-lg">Strengths</CardTitle>
                </CardHeader>
                <CardContent>
                  {evaluationData.strengths && evaluationData.strengths.length > 0 ? (
                    <ul className="space-y-2">
                      {evaluationData.strengths.slice(0, 3).map((strength, index) => (
                        <li key={index} className="flex items-start">
                          <Check className="h-4 w-4 text-green-500 mr-2 mt-1 flex-shrink-0" />
                          <span className="text-gray-700 text-sm">{strength}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 text-center py-2">No strengths data available</p>
                  )}
                </CardContent>
              </Card>

              <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                <CardHeader>
                  <CardTitle className="text-gray-800 text-lg">Areas for Improvement</CardTitle>
                </CardHeader>
                <CardContent>
                  {evaluationData.weaknesses && evaluationData.weaknesses.length > 0 ? (
                    <ul className="space-y-2">
                      {evaluationData.weaknesses.slice(0, 3).map((weakness, index) => (
                        <li key={index} className="flex items-start">
                          <AlertCircle className="h-4 w-4 text-amber-500 mr-2 mt-1 flex-shrink-0" />
                          <span className="text-gray-700 text-sm">{weakness}</span>
                        </li>
                      ))}
                    </ul>
                  ) : (
                    <p className="text-gray-500 text-center py-2">No improvement areas identified</p>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </div>

      {/* Right Column (Supporting Content) - 4/12 width on large screens */}
      <div className="lg:col-span-4 space-y-6">
        {/* Job Match Comparison Radar Chart */}
        {allJobMatches && allJobMatches.length > 0 && (
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 overflow-hidden">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg">Job Match Comparison</CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="w-full h-[420px] flex items-center justify-center">
                <div className="w-[90%] h-[90%]">
                  <JobMatchRadarChart
                    chartData={{
                      labels: ['Skills', 'Experience', 'Education', 'Location'],
                      datasets: allJobMatches.slice(0, 3).map((job, index) => ({
                        label: job.jobTitle,
                        data: [job.skillsScore, job.experienceScore, job.educationScore, job.locationScore],
                        backgroundColor: [
                          'rgba(54, 162, 235, 0.2)',
                          'rgba(255, 99, 132, 0.2)',
                          'rgba(75, 192, 192, 0.2)'
                        ][index],
                        borderColor: [
                          'rgb(54, 162, 235)',
                          'rgb(255, 99, 132)',
                          'rgb(75, 192, 192)'
                        ][index],
                        borderWidth: 1
                      }))
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* CV Document Button */}
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg">CV Document</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50">
              <FileText className="h-12 w-12 text-gray-400 mb-3" />
              <p className="text-gray-600 text-sm mb-4 text-center">
                View the candidate's CV document to see their full resume and qualifications
              </p>
              <div className="flex gap-2">
                <Button
                  className="bg-recruiter-lightblue hover:bg-blue-500"
                  onClick={() => setIsCVModalOpen(true)}
                >
                  <Maximize2 className="mr-2 h-4 w-4" /> View CV
                </Button>
                <Button
                  variant="outline"
                  className="text-gray-700"
                  onClick={() => window.open(candidate.cv_url, '_blank')}
                >
                  <Download className="mr-2 h-4 w-4" /> Download
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Interview Actions */}
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg">Interview Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col items-center justify-center p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50">
              <CalendarClock className="h-12 w-12 text-gray-400 mb-3" />
              <p className="text-gray-600 text-sm mb-4 text-center">
                Schedule an interview with this candidate for a job position
              </p>
              <ProPlanGuard featureName="Interview Scheduling">
                <Button
                  className="bg-recruiter-lightblue hover:bg-blue-500 w-full"
                  onClick={() => setIsScheduleInterviewModalOpen(true)}
                >
                  <CalendarClock className="mr-2 h-4 w-4" /> Schedule Interview
                </Button>
              </ProPlanGuard>
            </div>
          </CardContent>
        </Card>

        {/* Recent Activity Timeline */}
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg">Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {candidate.notes && candidate.notes.length > 0 ? (
                <div className="space-y-3 relative before:absolute before:inset-0 before:ml-5 before:-translate-x-px before:h-full before:w-0.5 before:bg-gray-200">
                  {candidate.notes.slice(0, 3).map((note, index) => (
                    <div key={index} className="relative pl-6">
                      <div className="absolute left-0 top-2 h-2 w-2 rounded-full bg-blue-500"></div>
                      <div className="text-sm">
                        <span className="font-medium text-gray-800">{note.author}</span>
                        <span className="text-gray-500 ml-2">{formatDate(note.date)}</span>
                      </div>
                      <p className="text-gray-600 text-sm mt-1">{note.content.length > 100 ? `${note.content.substring(0, 100)}...` : note.content}</p>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 text-center py-2">No recent activity</p>
              )}
              <Button
                variant="outline"
                className="w-full text-sm"
                onClick={() => setActiveTab('notes')}
              >
                View All Activity
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
    </div>
  );
};

export default CandidateOverview;

