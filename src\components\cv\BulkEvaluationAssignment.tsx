import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { 
  Building2, 
  Briefcase, 
  Star, 
  Users, 
  CheckCircle2, 
  ArrowRight,
  Filter,
  Target,
  Zap
} from 'lucide-react';
import { JobSpecificMatch } from '@/services/cv-processing/jobMatcher';

interface BulkEvaluationAssignmentProps {
  candidateId: string;
  candidateName: string;
  jobSpecificMatches: JobSpecificMatch[];
  onAssignment: (assignments: AssignmentData[]) => void;
  onClose: () => void;
}

interface AssignmentData {
  jobId: string;
  companyId: string;
  jobTitle: string;
  companyName: string;
  matchScore: number;
}

const BulkEvaluationAssignment: React.FC<BulkEvaluationAssignmentProps> = ({
  candidateId,
  candidateName,
  jobSpecificMatches,
  onAssignment,
  onClose
}) => {
  const { toast } = useToast();
  const [selectedJobs, setSelectedJobs] = useState<Set<string>>(new Set());
  const [minScoreThreshold, setMinScoreThreshold] = useState(75);
  const [isAssigning, setIsAssigning] = useState(false);

  // Sort matches by score (highest first)
  const sortedMatches = [...jobSpecificMatches].sort((a, b) => b.overallScore - a.overallScore);

  // Get match color based on score
  const getMatchColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-50 border-green-200';
    if (score >= 80) return 'text-blue-600 bg-blue-50 border-blue-200';
    if (score >= 70) return 'text-amber-600 bg-amber-50 border-amber-200';
    return 'text-red-600 bg-red-50 border-red-200';
  };

  // Get recommendation badge color
  const getRecommendationColor = (score: number) => {
    if (score >= 85) return 'bg-green-500';
    if (score >= 75) return 'bg-blue-500';
    if (score >= 65) return 'bg-amber-500';
    return 'bg-red-500';
  };

  // Handle individual job selection
  const handleJobToggle = (jobId: string) => {
    const newSelected = new Set(selectedJobs);
    if (newSelected.has(jobId)) {
      newSelected.delete(jobId);
    } else {
      newSelected.add(jobId);
    }
    setSelectedJobs(newSelected);
  };

  // Smart assignment functions
  const assignTopMatches = (count: number) => {
    const topJobs = sortedMatches.slice(0, count).map(match => match.jobId);
    setSelectedJobs(new Set(topJobs));
  };

  const assignByThreshold = (threshold: number) => {
    const qualifyingJobs = sortedMatches
      .filter(match => match.overallScore >= threshold)
      .map(match => match.jobId);
    setSelectedJobs(new Set(qualifyingJobs));
  };

  // Handle assignment submission
  const handleAssignment = async () => {
    if (selectedJobs.size === 0) {
      toast({
        title: 'No jobs selected',
        description: 'Please select at least one job to assign the candidate to.',
        variant: 'destructive',
      });
      return;
    }

    setIsAssigning(true);

    try {
      const assignments: AssignmentData[] = sortedMatches
        .filter(match => selectedJobs.has(match.jobId))
        .map(match => ({
          jobId: match.jobId,
          companyId: match.companyId || '',
          jobTitle: match.jobTitle,
          companyName: match.companyName || '',
          matchScore: match.overallScore
        }));

      await onAssignment(assignments);

      toast({
        title: 'Assignment successful',
        description: `${candidateName} has been assigned to ${assignments.length} job(s).`,
      });

      onClose();
    } catch (error) {
      console.error('Assignment error:', error);
      toast({
        title: 'Assignment failed',
        description: 'Failed to assign candidate. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsAssigning(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900">Assignment Results</h2>
        <p className="text-gray-600 mt-2">
          Evaluation complete for <span className="font-semibold">{candidateName}</span>
        </p>
        <p className="text-sm text-gray-500">
          Found {sortedMatches.length} potential matches across all companies
        </p>
      </div>

      {/* Quick Assignment Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-blue-500" />
            Smart Assignment Options
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button
              variant="outline"
              onClick={() => assignTopMatches(3)}
              className="flex items-center gap-2"
            >
              <Target className="h-4 w-4" />
              Top 3 Matches
            </Button>
            <Button
              variant="outline"
              onClick={() => assignTopMatches(5)}
              className="flex items-center gap-2"
            >
              <Star className="h-4 w-4" />
              Top 5 Matches
            </Button>
            <div className="flex items-center gap-2">
              <Input
                type="number"
                value={minScoreThreshold}
                onChange={(e) => setMinScoreThreshold(Number(e.target.value))}
                min="0"
                max="100"
                className="w-20"
              />
              <Button
                variant="outline"
                onClick={() => assignByThreshold(minScoreThreshold)}
                className="flex items-center gap-2"
              >
                <Filter className="h-4 w-4" />
                {minScoreThreshold}%+
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Match Results */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Building2 className="h-5 w-5 text-gray-600" />
              Job Matches ({sortedMatches.length})
            </span>
            <Badge variant="outline">
              {selectedJobs.size} selected
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {sortedMatches.map((match) => (
              <div
                key={match.jobId}
                className={`p-4 border rounded-lg transition-all cursor-pointer hover:shadow-md ${
                  selectedJobs.has(match.jobId) 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleJobToggle(match.jobId)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    <Checkbox
                      checked={selectedJobs.has(match.jobId)}
                      onChange={() => handleJobToggle(match.jobId)}
                      className="mt-1"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-gray-900">{match.jobTitle}</h3>
                        <Badge 
                          className={getRecommendationColor(match.overallScore)}
                          variant="secondary"
                        >
                          {match.overallScore >= 85 ? 'Excellent' : 
                           match.overallScore >= 75 ? 'Good' : 
                           match.overallScore >= 65 ? 'Fair' : 'Poor'} Match
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{match.companyName}</p>
                      
                      {/* Score breakdown */}
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-xs">
                        <div className="text-center">
                          <div className="font-medium text-gray-700">Skills</div>
                          <div className={`font-bold ${getMatchColor(match.skillsScore).split(' ')[0]}`}>
                            {match.skillsScore}%
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-gray-700">Experience</div>
                          <div className={`font-bold ${getMatchColor(match.experienceScore).split(' ')[0]}`}>
                            {match.experienceScore}%
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-gray-700">Education</div>
                          <div className={`font-bold ${getMatchColor(match.educationScore).split(' ')[0]}`}>
                            {match.educationScore}%
                          </div>
                        </div>
                        <div className="text-center">
                          <div className="font-medium text-gray-700">Location</div>
                          <div className={`font-bold ${getMatchColor(match.locationScore).split(' ')[0]}`}>
                            {match.locationScore}%
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Overall score */}
                  <div className="text-center ml-4">
                    <div className={`text-2xl font-bold ${getMatchColor(match.overallScore).split(' ')[0]}`}>
                      {match.overallScore}%
                    </div>
                    <div className="text-xs text-gray-500">Overall</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-between items-center pt-4">
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <div className="flex gap-2">
          <Button
            onClick={() => setSelectedJobs(new Set())}
            variant="outline"
            disabled={selectedJobs.size === 0}
          >
            Clear Selection
          </Button>
          <Button
            onClick={handleAssignment}
            disabled={selectedJobs.size === 0 || isAssigning}
            className="flex items-center gap-2"
          >
            {isAssigning ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Assigning...
              </>
            ) : (
              <>
                <CheckCircle2 className="h-4 w-4" />
                Assign to {selectedJobs.size} Job{selectedJobs.size !== 1 ? 's' : ''}
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BulkEvaluationAssignment;
