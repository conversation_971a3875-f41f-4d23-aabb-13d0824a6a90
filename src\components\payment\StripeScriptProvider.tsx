import React, { createContext, useContext, useEffect, useState } from 'react';
import { STRIPE_PUBLISHABLE_KEY } from '@/config/stripe';

interface StripeScriptContextType {
  isLoaded: boolean;
  isLoading: boolean;
  failedToLoad: boolean;
  reload: () => void;
  stripe: any;
}

const StripeScriptContext = createContext<StripeScriptContextType>({
  isLoaded: false,
  isLoading: false,
  failedToLoad: false,
  reload: () => {},
  stripe: null,
});

export const useStripeScriptContext = () => useContext(StripeScriptContext);

interface StripeScriptProviderProps {
  children: React.ReactNode;
}

export const StripeScriptProvider: React.FC<StripeScriptProviderProps> = ({ 
  children
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [failedToLoad, setFailedToLoad] = useState(false);
  const [stripe, setStripe] = useState<any>(null);
  const [scriptId] = useState(`stripe-script-${Math.random().toString(36).substring(7)}`);

  const loadScript = () => {
    // Skip if already loading or loaded
    if (isLoading || isLoaded) return;

    // Check if Stripe is already available globally
    if (window.Stripe) {
      const stripeInstance = window.Stripe(STRIPE_PUBLISHABLE_KEY);
      setStripe(stripeInstance);
      setIsLoaded(true);
      return;
    }

    // Check if script already exists
    const existingScript = document.getElementById(scriptId);
    if (existingScript) {
      setIsLoaded(true);
      return;
    }

    setIsLoading(true);
    setFailedToLoad(false);

    // Create script element
    const script = document.createElement('script');
    script.id = scriptId;
    script.src = 'https://js.stripe.com/v3/';
    script.async = true;

    // Handle script load events
    script.onload = () => {
      console.log('Stripe.js loaded successfully');
      try {
        const stripeInstance = window.Stripe(STRIPE_PUBLISHABLE_KEY);
        setStripe(stripeInstance);
        setIsLoaded(true);
        setIsLoading(false);
      } catch (error) {
        console.error('Error initializing Stripe:', error);
        setFailedToLoad(true);
        setIsLoading(false);
      }
    };

    script.onerror = () => {
      console.error('Failed to load Stripe.js');
      setFailedToLoad(true);
      setIsLoading(false);
    };

    // Append script to document head
    document.head.appendChild(script);
  };

  const reload = () => {
    // Remove existing script
    const existingScript = document.getElementById(scriptId);
    if (existingScript) {
      existingScript.remove();
    }

    // Reset state
    setIsLoaded(false);
    setIsLoading(false);
    setFailedToLoad(false);
    setStripe(null);

    // Reload script
    loadScript();
  };

  // Load script on mount
  useEffect(() => {
    loadScript();
  }, []);

  const contextValue = {
    isLoaded,
    isLoading,
    failedToLoad,
    reload,
    stripe,
  };

  return (
    <StripeScriptContext.Provider value={contextValue}>
      {children}
    </StripeScriptContext.Provider>
  );
};

// Extend Window interface to include Stripe
declare global {
  interface Window {
    Stripe?: any;
  }
}

export default StripeScriptProvider;
