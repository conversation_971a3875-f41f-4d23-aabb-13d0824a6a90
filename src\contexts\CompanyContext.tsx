import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getCompany } from '@/services/supabase/companies';

interface CompanyContextType {
  activeCompanyId: string | null;
  activeCompany: any | null;
  setActiveCompanyId: (id: string | null) => void;
  showAllCompanies: boolean;
  setShowAllCompanies: (show: boolean) => void;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export const CompanyProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [activeCompanyId, setActiveCompanyId] = useState<string | null>(null);
  const [activeCompany, setActiveCompany] = useState<any | null>(null);
  const [showAllCompanies, setShowAllCompanies] = useState<boolean>(false);

  // Get the active company ID from localStorage on initial load
  useEffect(() => {
    const storedCompanyId = localStorage.getItem('activeCompanyId');
    if (storedCompanyId) {
      setActiveCompanyId(storedCompanyId);
    }
  }, []);

  // Fetch active company details when ID changes
  useEffect(() => {
    const fetchActiveCompany = async () => {
      if (activeCompanyId) {
        try {
          const company = await getCompany(activeCompanyId);
          setActiveCompany(company);
        } catch (error) {
          console.error('Error fetching active company:', error);
        }
      } else {
        setActiveCompany(null);
      }
    };

    fetchActiveCompany();
  }, [activeCompanyId]);

  // Update localStorage when active company changes
  const handleSetActiveCompanyId = (id: string | null) => {
    if (id) {
      localStorage.setItem('activeCompanyId', id);
    } else {
      localStorage.removeItem('activeCompanyId');
    }
    setActiveCompanyId(id);
  };

  return (
    <CompanyContext.Provider
      value={{
        activeCompanyId,
        activeCompany,
        setActiveCompanyId: handleSetActiveCompanyId,
        showAllCompanies,
        setShowAllCompanies,
      }}
    >
      {children}
    </CompanyContext.Provider>
  );
};

export const useCompanyContext = (): CompanyContextType => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error('useCompanyContext must be used within a CompanyProvider');
  }
  return context;
};
