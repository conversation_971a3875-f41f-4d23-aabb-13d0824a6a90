-- Implement candidate pool system with job applications
-- This migration creates a proper many-to-many relationship between candidates and jobs

-- Drop existing functions and policies first to avoid conflicts
DROP FUNCTION IF EXISTS public.get_candidate_statistics();
DROP POLICY IF EXISTS "Users can view their own candidates" ON public.candidates;
DROP POLICY IF EXISTS "Users can insert their own candidates" ON public.candidates;
DROP POLICY IF EXISTS "Users can update their own candidates" ON public.candidates;
DROP POLICY IF EXISTS "Users can delete their own candidates" ON public.candidates;

-- Add company_id to candidates table for direct company association
ALTER TABLE public.candidates 
ADD COLUMN IF NOT EXISTS company_id uuid REFERENCES public.companies(id) ON DELETE CASCADE;

-- Create applications table for many-to-many relationship between candidates and jobs
CREATE TABLE IF NOT EXISTS public.applications (
  id uuid NOT NULL DEFAULT uuid_generate_v4(),
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  candidate_id uuid NOT NULL REFERENCES public.candidates(id) ON DELETE CASCADE,
  job_id uuid NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
  status text NOT NULL DEFAULT 'applied' CHECK (status IN ('applied', 'screening', 'interviewed', 'offer', 'hired', 'rejected')),
  applied_at timestamp with time zone NOT NULL DEFAULT now(),
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  notes text,
  PRIMARY KEY (id),
  UNIQUE(candidate_id, job_id)
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_applications_candidate_id ON public.applications(candidate_id);
CREATE INDEX IF NOT EXISTS idx_applications_job_id ON public.applications(job_id);
CREATE INDEX IF NOT EXISTS idx_applications_user_id ON public.applications(user_id);
CREATE INDEX IF NOT EXISTS idx_applications_status ON public.applications(status);
CREATE INDEX IF NOT EXISTS idx_candidates_company_id ON public.candidates(company_id);

-- Add user_type to auth.users metadata for distinguishing recruiters vs candidates
-- This will be stored in user_metadata, no schema change needed

-- Enable RLS on applications table
ALTER TABLE public.applications ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for applications table
CREATE POLICY "Users can view their own applications" ON public.applications
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own applications" ON public.applications
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own applications" ON public.applications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own applications" ON public.applications
  FOR DELETE USING (auth.uid() = user_id);

-- Recreate RLS policies for candidates table to include company_id
CREATE POLICY "Users can view their own candidates" ON public.candidates
  FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own candidates" ON public.candidates
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own candidates" ON public.candidates
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own candidates" ON public.candidates
  FOR DELETE USING (auth.uid() = user_id);

-- Create function to get candidate statistics
CREATE OR REPLACE FUNCTION public.get_candidate_statistics()
RETURNS TABLE (
  total bigint,
  new bigint,
  reviewing bigint,
  screening bigint,
  interviewed bigint,
  offer bigint,
  hired bigint,
  rejected bigint
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    COUNT(*) as total,
    COUNT(*) FILTER (WHERE status = 'new') as new,
    COUNT(*) FILTER (WHERE status = 'reviewing') as reviewing,
    COUNT(*) FILTER (WHERE status = 'screening') as screening,
    COUNT(*) FILTER (WHERE status = 'interviewed') as interviewed,
    COUNT(*) FILTER (WHERE status = 'offer') as offer,
    COUNT(*) FILTER (WHERE status = 'hired') as hired,
    COUNT(*) FILTER (WHERE status = 'rejected') as rejected
  FROM public.candidates
  WHERE user_id = auth.uid();
END;
$$;

-- Create function to get job applicant count
CREATE OR REPLACE FUNCTION public.get_job_applicant_count(job_uuid uuid)
RETURNS bigint
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM public.applications
    WHERE job_id = job_uuid AND user_id = auth.uid()
  );
END;
$$;

-- Create function to assign candidate to job
CREATE OR REPLACE FUNCTION public.assign_candidate_to_job(
  candidate_uuid uuid,
  job_uuid uuid,
  application_notes text DEFAULT NULL
)
RETURNS uuid
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  application_id uuid;
BEGIN
  -- Insert application record
  INSERT INTO public.applications (candidate_id, job_id, user_id, notes)
  VALUES (candidate_uuid, job_uuid, auth.uid(), application_notes)
  ON CONFLICT (candidate_id, job_id) DO UPDATE SET
    notes = EXCLUDED.notes,
    applied_at = now()
  RETURNING id INTO application_id;
  
  RETURN application_id;
END;
$$;

-- Add comments to document the new structure
COMMENT ON TABLE public.applications IS 'Many-to-many relationship between candidates and jobs';
COMMENT ON COLUMN public.candidates.company_id IS 'Direct association with company for candidate pool filtering';
COMMENT ON COLUMN public.candidates.job_id IS 'Legacy field - now optional. Use applications table for job assignments';

-- Grant necessary permissions
GRANT ALL ON public.applications TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_candidate_statistics() TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_job_applicant_count(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.assign_candidate_to_job(uuid, uuid, text) TO authenticated;
