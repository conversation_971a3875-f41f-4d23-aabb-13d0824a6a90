import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

/**
 * Upload a file to Supabase storage
 * @param file The file to upload
 * @param bucket The storage bucket name (e.g., 'cvs', 'profiles', 'companies')
 * @returns The public URL of the uploaded file
 */
export const uploadFile = async (file: File, bucket: string): Promise<string> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Generate a unique filename
      const fileExt = file.name.split('.').pop();
      const fileName = `${userId}_${Date.now()}_${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `${fileName}`;

      // Upload the file
      const { error } = await supabase.storage
        .from(bucket)
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Error uploading file:', error);
        throw error;
      }

      // Get the public URL
      const { data } = supabase.storage.from(bucket).getPublicUrl(filePath);

      if (!data.publicUrl) {
        throw new Error('Failed to generate public URL for uploaded file');
      }

      return data.publicUrl;
    },
    `Failed to upload file: ${file.name}`
  );
};

/**
 * Delete a file from Supabase storage
 * @param filePath The path of the file to delete
 * @param bucket The storage bucket name
 * @returns True if successful
 */
export const deleteFile = async (filePath: string, bucket: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([filePath]);

      if (error) {
        throw error;
      }

      return true;
    },
    `Failed to delete file: ${filePath}`,
    false
  );
};

/**
 * Get the public URL for a file
 * @param filePath The path of the file
 * @param bucket The storage bucket name
 * @returns The public URL
 */
export const getFileUrl = (filePath: string, bucket: string): string => {
  const { data } = supabase.storage.from(bucket).getPublicUrl(filePath);
  return data.publicUrl;
};
