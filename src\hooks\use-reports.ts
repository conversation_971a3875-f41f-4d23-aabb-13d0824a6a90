import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useToast } from '@/hooks/use-toast';
import { useEnhancedQuery } from '@/lib/queryUtils';
import * as profileService from '@/services/supabase/profiles';
import {
  ReportType,
  ReportFormat,
  ReportData,
  exportReport,
  canExportReportFormat,
  getAvailableReportFormats,
  getAvailableReportTypes
} from '@/services/reports/reportExporter';

/**
 * Hook to handle report exports
 */
export function useReportExport() {
  const { user } = useAuth();
  const { hasRole } = usePermissions();
  const { toast } = useToast();
  const [isExporting, setIsExporting] = useState(false);

  // Get user profile to determine subscription tier
  const { data: profile } = useEnhancedQuery(
    ['profile', user?.id],
    () => profileService.getProfile(user?.id || ''),
    {
      enabled: !!user?.id,
      fallbackData: null,
      errorMessage: 'Failed to load profile',
    }
  );

  const subscriptionTier = profile?.subscription_tier || 'starter';

  /**
   * Export a report
   */
  const exportReportData = async (
    reportData: ReportData,
    reportType: ReportType,
    format: ReportFormat
  ) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'You must be logged in to export reports.',
        variant: 'destructive',
      });
      return;
    }

    // Admin users can export in any format regardless of subscription tier
    // Check if user can export in this format based on subscription tier
    if (!hasRole('platform_admin') && !canExportReportFormat(format, subscriptionTier as 'starter' | 'growth' | 'pro')) {
      toast({
        title: 'Subscription required',
        description: `Your current plan doesn't support ${format.toUpperCase()} exports. Please upgrade to export reports.`,
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsExporting(true);

      // Export the report
      exportReport(reportData, reportType, format);

      toast({
        title: 'Report exported',
        description: `Your ${reportData.title} has been exported successfully.`,
      });
    } catch (error: any) {
      console.error('Error exporting report:', error);
      toast({
        title: 'Export failed',
        description: error.message || 'An error occurred while exporting the report.',
        variant: 'destructive',
      });
    } finally {
      setIsExporting(false);
    }
  };

  /**
   * Get available report formats based on subscription tier
   * Admin users have access to all formats regardless of subscription tier
   */
  const availableFormats = user?.isPlatformAdmin
    ? ['pdf', 'excel'] as ReportFormat[]
    : getAvailableReportFormats(subscriptionTier as 'starter' | 'growth' | 'pro');

  /**
   * Get available report types based on subscription tier
   * Admin users have access to all report types regardless of subscription tier
   */
  const availableReportTypes = user?.isPlatformAdmin
    ? ['recruitment_funnel', 'time_to_hire', 'source_effectiveness', 'candidate_pipeline', 'skill_analysis', 'comprehensive'] as ReportType[]
    : getAvailableReportTypes(subscriptionTier as 'starter' | 'growth' | 'pro');

  /**
   * Check if a specific report type is available for the user's subscription tier
   */
  const isReportTypeAvailable = (reportType: ReportType) => {
    return availableReportTypes.includes(reportType);
  };

  /**
   * Check if a specific export format is available for the user's subscription tier
   */
  const isFormatAvailable = (format: ReportFormat) => {
    return availableFormats.includes(format);
  };

  return {
    exportReportData,
    isExporting,
    availableFormats,
    availableReportTypes,
    isReportTypeAvailable,
    isFormatAvailable,
    subscriptionTier,
  };
}
