import React from 'react';
import UnifiedEvaluationTable from '@/components/cv/UnifiedEvaluationTable';
import { Evaluation } from '@/services/supabase/evaluations';

interface EvaluationHistoryTabProps {
  evaluations: Evaluation[];
  selectedEvaluation: Evaluation | null;
  isLoadingEvaluations: boolean;
  handleEvaluationSelect: (evaluationId: string) => void;
  setActiveTab: (tab: string) => void;
  setIsEvaluateModalOpen: (isOpen: boolean) => void;
}

const EvaluationHistoryTab: React.FC<EvaluationHistoryTabProps> = ({
  evaluations,
  selectedEvaluation,
  isLoadingEvaluations,
  handleEvaluationSelect,
  setActiveTab,
  setIsEvaluateModalOpen
}) => {
  // Handle viewing the full evaluation
  const handleViewFullEvaluation = (evaluationId: string) => {
    handleEvaluationSelect(evaluationId);
    setActiveTab('cv-details');
  };

  return (
    <UnifiedEvaluationTable
      evaluations={evaluations}
      onSelectEvaluation={handleEvaluationSelect}
      onViewFullEvaluation={handleViewFullEvaluation}
      isLoading={isLoadingEvaluations}
      onNewEvaluation={() => setIsEvaluateModalOpen(true)}
    />
  );
};

export default EvaluationHistoryTab;
