-- Add free trial tracking to profiles table
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS free_trial_used BOOLEAN DEFAULT FALSE;

-- Add comment for documentation
COMMENT ON COLUMN public.profiles.free_trial_used IS 'Indicates whether the user has used their free CV evaluation';

-- <PERSON><PERSON> function to check and update free trial status
CREATE OR REPLACE FUNCTION public.check_free_trial_status()
RETURNS TRIGGER AS $$
BEGIN
    -- If this is a new CV upload and the user hasn't used their free trial yet
    IF NEW.cv_uploads_count = 1 AND OLD.cv_uploads_count = 0 THEN
        -- Mark the free trial as used
        UPDATE public.profiles
        SET free_trial_used = TRUE
        WHERE user_id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON>reate trigger to update free trial status when a CV is uploaded
DROP TRIGGER IF EXISTS update_free_trial_status ON public.usage_tracking;
CREATE TRIGGER update_free_trial_status
AFTER UPDATE OF cv_uploads_count ON public.usage_tracking
FOR EACH ROW
WHEN (NEW.cv_uploads_count = 1 AND OLD.cv_uploads_count = 0)
EXECUTE FUNCTION public.check_free_trial_status();

-- Add comment for documentation
COMMENT ON FUNCTION public.check_free_trial_status() IS 'Updates free trial status when a user uploads their first CV';
