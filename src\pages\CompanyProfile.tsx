import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  Building,
  MapPin,
  Globe,
  Phone,
  Mail,
  Users,
  Edit,
  Save,
  Upload,
  X,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';
import { useCompany } from '@/hooks/use-companies';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { Al<PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert';

// Default empty company structure
const emptyCompany = {
  id: '',
  name: '',
  logo_url: '',
  description: '',
  industry: '',
  size: '',
  founded: '',
  website: '',
  phone: '',
  email: '',
  address: {
    street: '',
    city: '',
    state: '',
    zip: '',
    country: ''
  },
  socialMedia: {
    linkedin: '',
    twitter: '',
    facebook: ''
  }
};

const CompanyProfile = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { data: companyData, isLoading, error } = useCompany(id || '');

  const [isEditing, setIsEditing] = useState(false);
  const [company, setCompany] = useState(emptyCompany);
  const [formData, setFormData] = useState(emptyCompany);

  // Update local state when company data is loaded
  useEffect(() => {
    if (companyData) {
      // Transform API data to match our component structure
      const transformedCompany = {
        ...emptyCompany,
        ...companyData,
        // Handle potential missing nested objects
        address: companyData.address || emptyCompany.address,
        socialMedia: companyData.socialMedia || emptyCompany.socialMedia
      };

      setCompany(transformedCompany);
      setFormData(transformedCompany);
    }
  }, [companyData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // Handle nested objects (address, socialMedia)
    if (name.includes('.')) {
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent as keyof typeof formData] as Record<string, any>,
          [child]: value
        }
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleSave = () => {
    setCompany(formData);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setFormData(company);
    setIsEditing(false);
  };

  // If no ID is provided, redirect to companies page
  useEffect(() => {
    if (!id) {
      toast({
        title: "No company selected",
        description: "Please select a company to view its profile",
        variant: "destructive"
      });
      navigate('/dashboard/companies');
    }
  }, [id, navigate, toast]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <Skeleton className="h-8 w-64" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
          <Skeleton className="h-96 w-full" />
          <Skeleton className="h-96 w-full lg:col-span-2" />
        </div>
      </div>
    );
  }

  // Handle error state
  if (error || !companyData) {
    return (
      <div className="space-y-6">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error ? `Failed to load company: ${error.message}` : 'Company not found'}
          </AlertDescription>
        </Alert>
        <Button
          onClick={() => navigate('/dashboard/companies')}
          className="bg-recruiter-lightblue hover:bg-blue-500"
        >
          Return to Companies
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <h1 className="text-2xl font-bold text-gray-800">{company.name} Profile</h1>
        {!isEditing ? (
          <Button
            className="bg-recruiter-lightblue hover:bg-blue-500"
            onClick={() => setIsEditing(true)}
          >
            <Edit className="mr-2 h-4 w-4" /> Edit Profile
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              variant="outline"
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
              onClick={handleCancel}
            >
              <X className="mr-2 h-4 w-4" /> Cancel
            </Button>
            <Button
              className="bg-green-600 hover:bg-green-700"
              onClick={handleSave}
            >
              <Save className="mr-2 h-4 w-4" /> Save Changes
            </Button>
          </div>
        )}
      </div>

      <Tabs defaultValue="profile">
        <TabsList className="bg-white border-b border-gray-200 w-full justify-start">
          <TabsTrigger value="profile" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Profile
          </TabsTrigger>
          <TabsTrigger value="branding" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Branding
          </TabsTrigger>
          <TabsTrigger value="team" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Team
          </TabsTrigger>
        </TabsList>

        {/* Profile Tab */}
        <TabsContent value="profile">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mt-6">
            {/* Company Logo and Basic Info */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 lg:col-span-1">
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center">
                  <div className="relative mb-4">
                    <Avatar className="h-32 w-32">
                      <AvatarImage src={company.logo_url} alt={company.name} />
                      <AvatarFallback className="text-3xl bg-recruiter-blue">
                        {company.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    {isEditing && (
                      <Button
                        size="sm"
                        className="absolute bottom-0 right-0 rounded-full bg-recruiter-lightblue hover:bg-blue-500 h-8 w-8 p-0"
                      >
                        <Upload className="h-4 w-4" />
                      </Button>
                    )}
                  </div>

                  {!isEditing ? (
                    <>
                      <h2 className="text-xl font-bold text-gray-800 mb-1">{company.name}</h2>
                      <p className="text-gray-500 mb-4">{company.industry}</p>

                      <div className="w-full space-y-3 text-left text-gray-600">
                        <div className="flex items-center">
                          <Building className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{company.size}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2 text-gray-500" />
                          <span>
                            {company.address.city}, {company.address.state}, {company.address.country}
                          </span>
                        </div>
                        <div className="flex items-center">
                          <Globe className="h-4 w-4 mr-2 text-gray-500" />
                          <a href={company.website} target="_blank" rel="noopener noreferrer" className="text-primary hover:underline">
                            {company.website.replace(/^https?:\/\//, '')}
                          </a>
                        </div>
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 mr-2 text-gray-500" />
                          <span>{company.phone}</span>
                        </div>
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 mr-2 text-gray-500" />
                          <a href={`mailto:${company.email}`} className="text-primary hover:underline">
                            {company.email}
                          </a>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="w-full space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="name" className="text-gray-700">Company Name</Label>
                        <Input
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="industry" className="text-gray-700">Industry</Label>
                        <Input
                          id="industry"
                          name="industry"
                          value={formData.industry}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="size" className="text-gray-700">Company Size</Label>
                        <Input
                          id="size"
                          name="size"
                          value={formData.size}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="website" className="text-gray-700">Website</Label>
                        <Input
                          id="website"
                          name="website"
                          value={formData.website}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="phone" className="text-gray-700">Phone</Label>
                        <Input
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email" className="text-gray-700">Email</Label>
                        <Input
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="bg-white border-gray-200 text-gray-800"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Company Details */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 lg:col-span-2">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Company Details</CardTitle>
              </CardHeader>
              <CardContent>
                {!isEditing ? (
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-gray-800 font-medium mb-2">About</h3>
                      <p className="text-gray-600">{company.description}</p>
                    </div>

                    <div>
                      <h3 className="text-gray-800 font-medium mb-2">Address</h3>
                      <p className="text-gray-600">
                        {company.address.street}<br />
                        {company.address.city}, {company.address.state} {company.address.zip}<br />
                        {company.address.country}
                      </p>
                    </div>

                    <div>
                      <h3 className="text-gray-800 font-medium mb-2">Social Media</h3>
                      <div className="flex gap-4">
                        <a
                          href={company.socialMedia.linkedin}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-500 hover:text-primary"
                        >
                          <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M19 0h-14c-2.761 0-5 2.239-5 5v14c0 2.761 2.239 5 5 5h14c2.762 0 5-2.239 5-5v-14c0-2.761-2.238-5-5-5zm-11 19h-3v-11h3v11zm-1.5-12.268c-.966 0-1.75-.79-1.75-1.764s.784-1.764 1.75-1.764 1.75.79 1.75 1.764-.783 1.764-1.75 1.764zm13.5 12.268h-3v-5.604c0-3.368-4-3.113-4 0v5.604h-3v-11h3v1.765c1.396-2.586 7-2.777 7 2.476v6.759z"/>
                          </svg>
                        </a>
                        <a
                          href={company.socialMedia.twitter}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-500 hover:text-primary"
                        >
                          <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                          </svg>
                        </a>
                        <a
                          href={company.socialMedia.facebook}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gray-500 hover:text-primary"
                        >
                          <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"/>
                          </svg>
                        </a>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="description" className="text-gray-700">About</Label>
                      <Textarea
                        id="description"
                        name="description"
                        value={formData.description}
                        onChange={handleInputChange}
                        className="bg-white border-gray-200 text-gray-800 min-h-[120px]"
                      />
                    </div>

                    <div>
                      <h3 className="text-gray-800 font-medium mb-3">Address</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="address.street" className="text-gray-700">Street</Label>
                          <Input
                            id="address.street"
                            name="address.street"
                            value={formData.address.street}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="address.city" className="text-gray-700">City</Label>
                          <Input
                            id="address.city"
                            name="address.city"
                            value={formData.address.city}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="address.state" className="text-gray-700">State/Province</Label>
                          <Input
                            id="address.state"
                            name="address.state"
                            value={formData.address.state}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="address.zip" className="text-gray-700">ZIP/Postal Code</Label>
                          <Input
                            id="address.zip"
                            name="address.zip"
                            value={formData.address.zip}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                        <div className="space-y-2 md:col-span-2">
                          <Label htmlFor="address.country" className="text-gray-700">Country</Label>
                          <Input
                            id="address.country"
                            name="address.country"
                            value={formData.address.country}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                      </div>
                    </div>

                    <div>
                      <h3 className="text-gray-800 font-medium mb-3">Social Media</h3>
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="socialMedia.linkedin" className="text-gray-700">LinkedIn</Label>
                          <Input
                            id="socialMedia.linkedin"
                            name="socialMedia.linkedin"
                            value={formData.socialMedia.linkedin}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="socialMedia.twitter" className="text-gray-700">Twitter</Label>
                          <Input
                            id="socialMedia.twitter"
                            name="socialMedia.twitter"
                            value={formData.socialMedia.twitter}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="socialMedia.facebook" className="text-gray-700">Facebook</Label>
                          <Input
                            id="socialMedia.facebook"
                            name="socialMedia.facebook"
                            value={formData.socialMedia.facebook}
                            onChange={handleInputChange}
                            className="bg-white border-gray-200 text-gray-800"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Branding Tab */}
        <TabsContent value="branding">
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 mt-6">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg">Company Branding</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-gray-800 font-medium mb-3">Logo</h3>
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 flex flex-col items-center justify-center">
                      <Avatar className="h-32 w-32 mb-4">
                        <AvatarImage src={company.logo_url} alt={company.name} />
                        <AvatarFallback className="text-3xl bg-recruiter-blue">
                          {company.name.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <Button className="bg-recruiter-lightblue hover:bg-blue-500">
                        <Upload className="mr-2 h-4 w-4" /> Upload Logo
                      </Button>
                      <p className="text-xs text-gray-500 mt-2">Recommended size: 400x400px (Max: 2MB)</p>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-gray-800 font-medium mb-3">Cover Image</h3>
                    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 flex flex-col items-center justify-center h-full">
                      <div className="w-full h-32 bg-gray-200 rounded-lg mb-4 flex items-center justify-center">
                        <Upload className="h-8 w-8 text-gray-600" />
                      </div>
                      <Button className="bg-recruiter-lightblue hover:bg-blue-500">
                        <Upload className="mr-2 h-4 w-4" /> Upload Cover
                      </Button>
                      <p className="text-xs text-gray-500 mt-2">Recommended size: 1200x400px (Max: 5MB)</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-gray-800 font-medium mb-3">Company Colors</h3>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {['#3B82F6', '#10B981', '#6366F1', '#F59E0B'].map((color, index) => (
                      <div key={index} className="flex flex-col items-center">
                        <div
                          className="w-16 h-16 rounded-lg mb-2"
                          style={{ backgroundColor: color }}
                        ></div>
                        <span className="text-gray-600">{color}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Team Tab */}
        <TabsContent value="team">
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 mt-6">
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle className="text-gray-800 text-lg">Team Members</CardTitle>
              <Button className="bg-recruiter-lightblue hover:bg-blue-500">
                <Users className="mr-2 h-4 w-4" /> Add Team Member
              </Button>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  { name: 'John Smith', role: 'CEO', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=1' },
                  { name: 'Sarah Johnson', role: 'HR Director', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=2' },
                  { name: 'Michael Brown', role: 'CTO', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=3' },
                  { name: 'Emily Davis', role: 'Recruitment Manager', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=4' },
                  { name: 'David Wilson', role: 'Technical Recruiter', email: '<EMAIL>', avatar: 'https://i.pravatar.cc/150?u=5' },
                ].map((member, index) => (
                  <div key={index} className="bg-white border border-gray-200 rounded-lg p-4 flex items-start gap-4 shadow-sm hover:shadow-md transition-all duration-200">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={member.avatar} alt={member.name} />
                      <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="text-gray-800 font-medium">{member.name}</h3>
                      <p className="text-gray-500 text-sm">{member.role}</p>
                      <a href={`mailto:${member.email}`} className="text-recruiter-blue text-sm hover:underline">
                        {member.email}
                      </a>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Wrap the CompanyProfile component with the DashboardLayout
const CompanyProfileWithLayout = () => (
  <DashboardLayout>
    <CompanyProfile />
  </DashboardLayout>
);

export default CompanyProfileWithLayout;
