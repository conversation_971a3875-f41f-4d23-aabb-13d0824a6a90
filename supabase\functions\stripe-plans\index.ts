import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import { corsHeaders } from '../_shared/cors.ts'

// Helper function to get Stripe client
function getStripeClient() {
  const stripeSecretKey = Deno.env.get('STRIPE_SECRET_KEY')
  
  if (!stripeSecretKey) {
    throw new Error('Stripe secret key is not configured')
  }
  
  return {
    secretKey: stripeSecretKey,
    baseUrl: 'https://api.stripe.com/v1',
    headers: {
      'Authorization': `Bearer ${stripeSecretKey}`,
      'Content-Type': 'application/json',
      'Stripe-Version': '2023-10-16'
    }
  }
}

// Helper function to make Stripe API requests
async function makeStripeRequest(endpoint: string, options: RequestInit = {}) {
  const stripe = getStripeClient()
  const url = `${stripe.baseUrl}${endpoint}`
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...stripe.headers,
      ...options.headers
    }
  })
  
  const data = await response.json()
  
  if (!response.ok) {
    throw new Error(data.error?.message || 'Stripe API request failed')
  }
  
  return data
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }
  
  // Create a Supabase client with the service role key
  const supabaseAdmin = createClient(
    Deno.env.get('SUPABASE_URL') ?? '',
    Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
    { global: { headers: { Authorization: req.headers.get('Authorization')! } } }
  )
  
  // Get the URL path
  const url = new URL(req.url)
  const path = url.pathname.split('/').filter(Boolean)
  const endpoint = path[path.length - 1]
  
  try {
    // Handle different endpoints
    if (req.method === 'GET') {
      // Get all products
      if (endpoint === 'products') {
        const data = await makeStripeRequest('/products?limit=100')
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      
      // Get all prices
      if (endpoint === 'prices') {
        const data = await makeStripeRequest('/prices?limit=100&expand[]=data.product')
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      
      // Get a specific product
      if (endpoint.startsWith('product-')) {
        const productId = endpoint.replace('product-', '')
        const data = await makeStripeRequest(`/products/${productId}`)
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      
      // Get a specific price
      if (endpoint.startsWith('price-')) {
        const priceId = endpoint.replace('price-', '')
        const data = await makeStripeRequest(`/prices/${priceId}?expand[]=product`)
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      
      // Get prices for a specific product
      if (endpoint.startsWith('product-prices-')) {
        const productId = endpoint.replace('product-prices-', '')
        const data = await makeStripeRequest(`/prices?product=${productId}&limit=100`)
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
    }
    
    if (req.method === 'POST') {
      const body = await req.json()
      
      // Create a new product
      if (endpoint === 'products') {
        const formData = new URLSearchParams()
        formData.append('name', body.name)
        if (body.description) formData.append('description', body.description)
        if (body.type) formData.append('type', body.type)
        
        const data = await makeStripeRequest('/products', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: formData
        })
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 201,
        })
      }
      
      // Create a new price
      if (endpoint === 'prices') {
        const formData = new URLSearchParams()
        formData.append('product', body.product)
        formData.append('unit_amount', body.unit_amount.toString())
        formData.append('currency', body.currency)
        
        if (body.recurring) {
          formData.append('recurring[interval]', body.recurring.interval)
          if (body.recurring.interval_count) {
            formData.append('recurring[interval_count]', body.recurring.interval_count.toString())
          }
        }
        
        if (body.nickname) formData.append('nickname', body.nickname)
        
        const data = await makeStripeRequest('/prices', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: formData
        })
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 201,
        })
      }
    }
    
    if (req.method === 'PATCH') {
      const body = await req.json()
      
      // Update a product
      if (endpoint.startsWith('product-')) {
        const productId = endpoint.replace('product-', '')
        
        const formData = new URLSearchParams()
        if (body.name) formData.append('name', body.name)
        if (body.description) formData.append('description', body.description)
        if (body.active !== undefined) formData.append('active', body.active.toString())
        
        const data = await makeStripeRequest(`/products/${productId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: formData
        })
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
      
      // Update a price (limited operations)
      if (endpoint.startsWith('price-')) {
        const priceId = endpoint.replace('price-', '')
        
        const formData = new URLSearchParams()
        if (body.nickname) formData.append('nickname', body.nickname)
        if (body.active !== undefined) formData.append('active', body.active.toString())
        
        const data = await makeStripeRequest(`/prices/${priceId}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          body: formData
        })
        
        return new Response(JSON.stringify({ status: 'success', data }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        })
      }
    }
    
    // If no endpoint matched
    return new Response(JSON.stringify({ error: 'Endpoint not found' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 404,
    })
  } catch (error) {
    console.error('Error processing Stripe request:', error)
    
    return new Response(JSON.stringify({ 
      status: 'error',
      error: error.message 
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})
