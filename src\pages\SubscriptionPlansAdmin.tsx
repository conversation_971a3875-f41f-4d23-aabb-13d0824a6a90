import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useToast } from '@/hooks/use-toast';
import SubscriptionPlanManager from '@/components/admin/SubscriptionPlanManager';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';

const SubscriptionPlansAdmin: React.FC = () => {
  const { user, isLoading } = useAuth();
  const { hasRole, isLoading: permissionsLoading } = usePermissions();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Check if user is admin
  useEffect(() => {
    if (!isLoading && !permissionsLoading && user && !hasRole('platform_admin')) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access this page.',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }
  }, [user, isLoading, permissionsLoading, hasRole, navigate, toast]);

  // Show loading state or unauthorized message
  if (isLoading || permissionsLoading) {
    return (
      <DashboardLayout>
        <div className="flex justify-center items-center h-full">
          <div className="animate-pulse">Loading...</div>
        </div>
      </DashboardLayout>
    );
  }

  if (!user || !hasRole('platform_admin')) {
    return (
      <DashboardLayout>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Access Denied</AlertTitle>
          <AlertDescription>
            You do not have permission to access this page.
          </AlertDescription>
        </Alert>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <SubscriptionPlanManager />
    </DashboardLayout>
  );
};

export default SubscriptionPlansAdmin;
