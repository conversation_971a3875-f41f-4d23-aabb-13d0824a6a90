/**
 * React Query hooks for profile data
 */

import { useAuth } from '@/contexts/AuthContext';
import { useEnhancedQuery, useEnhancedMutation, queryKeys } from '@/lib/queryUtils';
import * as profileService from '@/services/supabase/profiles';
import { Profile, ProfileUpdate } from '@/services/supabase/profiles';

/**
 * Hook to fetch the current user's profile
 */
export function useCurrentProfile() {
  const { user } = useAuth();
  
  return useEnhancedQuery<Profile | null>(
    queryKeys.profiles.current,
    () => profileService.getProfile(user?.id || ''),
    {
      enabled: !!user?.id,
      fallbackData: null,
      errorMessage: 'Failed to load your profile',
    }
  );
}

/**
 * Hook to fetch a specific user's profile
 */
export function useProfile(userId: string) {
  return useEnhancedQuery<Profile | null>(
    queryKeys.profiles.byId(userId),
    () => profileService.getProfile(userId),
    {
      enabled: !!userId,
      fallbackData: null,
      errorMessage: `Failed to load profile for user ${userId}`,
    }
  );
}

/**
 * Hook to update a user's profile
 */
export function useUpdateProfile() {
  return useEnhancedMutation<Profile | null, ProfileUpdate>(
    (profile) => profileService.updateProfile(profile),
    {
      errorMessage: 'Failed to update profile',
      successMessage: 'Profile updated successfully',
      invalidateQueries: [queryKeys.profiles.current],
    }
  );
}

/**
 * Hook to upload a profile avatar
 */
export function useUploadAvatar() {
  const { user } = useAuth();
  
  return useEnhancedMutation<string, File>(
    (file) => profileService.uploadAvatar(user?.id || '', file),
    {
      errorMessage: 'Failed to upload avatar',
      successMessage: 'Avatar uploaded successfully',
      invalidateQueries: [queryKeys.profiles.current],
      onSuccess: (publicUrl, _, context) => {
        // Update the profile with the new avatar URL
        if (user?.id && publicUrl) {
          profileService.updateProfile({
            user_id: user.id,
            avatar_url: publicUrl,
          });
        }
      },
    }
  );
}

/**
 * Hook to fetch subscription details
 */
export function useSubscription() {
  const { user } = useAuth();
  
  return useEnhancedQuery(
    ['subscription', user?.id],
    () => profileService.getSubscription(user?.id || ''),
    {
      enabled: !!user?.id,
      fallbackData: { tier: 'starter', status: 'inactive', endDate: null },
      errorMessage: 'Failed to load subscription details',
    }
  );
}
