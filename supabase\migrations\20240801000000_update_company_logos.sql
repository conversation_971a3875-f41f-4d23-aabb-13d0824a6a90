-- Update company logos with professional images
-- This script updates the existing companies with appropriate logo images

DO $$
BEGIN
    -- Update Tech Innovations Inc. with a tech company logo
    UPDATE public.companies
    SET logo_url = 'https://cdn-icons-png.flaticon.com/512/2920/2920349.png'
    WHERE name = 'Tech Innovations Inc.';

    -- Update Global Finance Group with a finance company logo
    UPDATE public.companies
    SET logo_url = 'https://cdn-icons-png.flaticon.com/512/2830/2830284.png'
    WHERE name = 'Global Finance Group';

    -- Update HealthTech Solutions with a healthcare tech logo
    UPDATE public.companies
    SET logo_url = 'https://cdn-icons-png.flaticon.com/512/2966/2966327.png'
    WHERE name = 'HealthTech Solutions';

    -- Update Creative Media Agency with a creative/design logo
    UPDATE public.companies
    SET logo_url = 'https://cdn-icons-png.flaticon.com/512/5968/5968705.png'
    WHERE name = 'Creative Media Agency';

    -- Update Sustainable Energy Innovations with a green energy logo
    UPDATE public.companies
    SET logo_url = 'https://cdn-icons-png.flaticon.com/512/4051/4051072.png'
    WHERE name = 'Sustainable Energy Innovations';

    -- Update TechCorp Solutions (from seed data) with a tech logo
    UPDATE public.companies
    SET logo_url = 'https://cdn-icons-png.flaticon.com/512/2920/2920277.png'
    WHERE name = 'TechCorp Solutions';

    -- Fallback update for any companies that might have placeholder images
    UPDATE public.companies
    SET logo_url = 'https://cdn-icons-png.flaticon.com/512/3061/3061341.png'
    WHERE logo_url LIKE '%placeholder%';

    RAISE NOTICE 'Company logos updated successfully';
END $$;
