import React, { useEffect, useRef } from 'react';
import { UsageLimitType } from '@/contexts/PermissionsContext';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/lib/toast';
import { useUsageLimit } from '@/contexts/UsageContext';

interface SubscriptionGuardProps {
  limitType: UsageLimitType;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  /** Use a toast notification instead of inline alert (default: false) */
  useToast?: boolean;
  /** Use a compact inline notification instead of full alert (default: false) */
  useCompact?: boolean;
}

/**
 * SubscriptionGuard component
 *
 * Checks if the user has reached their subscription limit for a specific feature.
 * If the limit is reached, it shows a fallback UI or a default message.
 *
 * @example
 * <SubscriptionGuard limitType="cv_uploads">
 *   <CVUploadForm />
 * </SubscriptionGuard>
 */
const SubscriptionGuard: React.FC<SubscriptionGuardProps> = ({
  limitType,
  children,
  fallback,
  useToast = false,
  useCompact = false
}) => {
  const navigate = useNavigate();
  const { data: limitInfo, isLoading } = useUsageLimit(limitType as any);

  // Reference to track if we've shown the toast already
  const toastShownRef = useRef(false);

  // Show toast notification if enabled and limit is reached, but only once
  useEffect(() => {
    if (useToast && limitInfo?.hasReachedLimit && !toastShownRef.current) {
      toast({
        title: 'Subscription Limit Reached',
        description: `You've reached your limit for this feature (${limitInfo.currentUsage}/${limitInfo.limit}). Please upgrade to continue.`,
        variant: 'warning'
      });
      // Mark that we've shown the toast
      toastShownRef.current = true;
    }

    // Reset the toast shown flag when the component unmounts or limitType changes
    return () => {
      toastShownRef.current = false;
    };
  }, [limitType, limitInfo?.hasReachedLimit, useToast, limitInfo?.currentUsage, limitInfo?.limit]);

  if (isLoading) {
    return <div className="p-2 text-center text-sm text-gray-500">Checking limits...</div>;
  }

  if (limitInfo?.hasReachedLimit) {
    // If a custom fallback is provided, use it
    if (fallback) {
      return <>{fallback}</>;
    }

    // If toast notification is enabled, just show a minimal inline message
    if (useToast) {
      return (
        <div className="text-center py-2">
          <p className="text-sm text-amber-600 flex items-center justify-center gap-1">
            <AlertCircle className="h-3 w-3" />
            <span>Limit reached.</span>
            <Button
              variant="link"
              className="h-auto p-0 text-sm text-blue-600"
              onClick={() => navigate('/dashboard/pricing')}
            >
              Upgrade
            </Button>
          </p>
        </div>
      );
    }

    // Compact version - single line with button
    if (useCompact) {
      return (
        <div className="py-2 px-3 bg-amber-50 border border-amber-200 rounded-md text-amber-800 text-sm flex items-center justify-between">
          <div className="flex items-center gap-2">
            <AlertCircle className="h-4 w-4 flex-shrink-0" />
            <span>Limit reached ({limitInfo.currentUsage}/{limitInfo.limit})</span>
          </div>
          <Button
            size="sm"
            variant="outline"
            onClick={() => navigate('/dashboard/pricing')}
            className="h-7 border-amber-300 text-amber-800 hover:bg-amber-100"
          >
            Upgrade
          </Button>
        </div>
      );
    }

    // Default fallback UI - more subtle than before
    return (
      <div className="p-2">
        <Alert className="mb-2 py-2 px-3 bg-amber-50 border-amber-200 text-amber-800">
          <AlertCircle className="h-4 w-4 mr-2 text-amber-800" />
          <AlertDescription className="flex items-center justify-between w-full">
            <span>Limit reached ({limitInfo.currentUsage}/{limitInfo.limit})</span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => navigate('/dashboard/pricing')}
              className="ml-2 h-7 border-amber-300 text-amber-800 hover:bg-amber-100"
            >
              Upgrade Plan
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  // If limit not reached, render children
  return <>{children}</>;
};

export default SubscriptionGuard;
