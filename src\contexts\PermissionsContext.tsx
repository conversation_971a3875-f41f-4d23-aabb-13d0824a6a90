import React, { createContext, useContext, useState, useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import * as profileService from '@/services/supabase/profiles';
import * as usageService from '@/services/supabase/usage';

// Define permission types
export type ResourceType = 'dashboard' | 'jobs' | 'candidates' | 'evaluations' | 'team' | 'settings' | 'billing';
export type ActionType = 'view' | 'create' | 'edit' | 'delete' | 'invite' | 'remove';
export type RoleType = 'admin' | 'recruiter' | 'viewer' | 'candidate' | 'platform_admin';

// Define permissions structure
export interface Permissions {
  [resource: string]: {
    [action: string]: boolean;
  };
}

// Define role permissions
export interface RolePermissions {
  [role: string]: Permissions;
}

// Default permissions for each role
const DEFAULT_PERMISSIONS: RolePermissions = {
  platform_admin: {
    dashboard: { view: true, edit: true },
    jobs: { view: true, create: true, edit: true, delete: true },
    candidates: { view: true, create: true, edit: true, delete: true },
    evaluations: { view: true, create: true, edit: true, delete: true },
    team: { view: true, invite: true, edit: true, remove: true },
    settings: { view: true, edit: true },
    billing: { view: true, edit: true },
    admin: { view: true, edit: true }
  },
  admin: {
    dashboard: { view: true, edit: true },
    jobs: { view: true, create: true, edit: true, delete: true },
    candidates: { view: true, create: true, edit: true, delete: true },
    evaluations: { view: true, create: true, edit: true, delete: true },
    team: { view: true, invite: true, edit: true, remove: true },
    settings: { view: true, edit: true },
    billing: { view: true, edit: true }
  },
  recruiter: {
    dashboard: { view: true, edit: false },
    jobs: { view: true, create: true, edit: true, delete: false },
    candidates: { view: true, create: true, edit: true, delete: false },
    evaluations: { view: true, create: true, edit: true, delete: false },
    team: { view: true, invite: false, edit: false, remove: false },
    settings: { view: true, edit: false },
    billing: { view: false, edit: false }
  },
  viewer: {
    dashboard: { view: true, edit: false },
    jobs: { view: true, create: false, edit: false, delete: false },
    candidates: { view: true, create: false, edit: false, delete: false },
    evaluations: { view: true, create: false, edit: false, delete: false },
    team: { view: true, invite: false, edit: false, remove: false },
    settings: { view: false, edit: false },
    billing: { view: false, edit: false }
  },
  candidate: {
    dashboard: { view: false, edit: false },
    jobs: { view: false, create: false, edit: false, delete: false },
    candidates: { view: false, create: false, edit: false, delete: false },
    evaluations: { view: false, create: false, edit: false, delete: false },
    team: { view: false, invite: false, edit: false, remove: false },
    settings: { view: false, edit: false },
    billing: { view: false, edit: false }
  }
};

// Role descriptions
export const ROLE_DESCRIPTIONS = {
  platform_admin: 'System administrator with full access to all platform features and user management.',
  admin: 'Full access to all features and settings. Can manage team members and billing.',
  recruiter: 'Can manage jobs, candidates, and evaluations. Limited access to team and settings.',
  viewer: 'Read-only access to jobs, candidates, and evaluations. Cannot make changes.',
  candidate: 'Job applicant with access only to candidate portal features. No dashboard access.'
};

// Define usage limit types
export type UsageLimitType = 'cv_uploads' | 'active_jobs' | 'company_profiles' | 'team_members';

// Define context type
interface PermissionsContextType {
  userRole: RoleType;
  permissions: Permissions;
  rolePermissions: RolePermissions;
  can: (resource: ResourceType, action: ActionType) => boolean;
  hasRole: (role: RoleType | RoleType[]) => boolean;
  updateRolePermissions: (role: string, newPermissions: Permissions) => Promise<void>;
  checkUsageLimit: (limitType: UsageLimitType) => Promise<{ hasReachedLimit: boolean; currentUsage: number; limit: number }>;
  isLoading: boolean;
}

// Create context
const PermissionsContext = createContext<PermissionsContextType | undefined>(undefined);

// Provider component
export const PermissionsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, isAuthenticated, isLoading: authLoading } = useAuth();
  const [userRole, setUserRole] = useState<RoleType>('viewer');
  const [permissions, setPermissions] = useState<Permissions>({});
  const [rolePermissions, setRolePermissions] = useState<RolePermissions>(DEFAULT_PERMISSIONS);
  const [isLoading, setIsLoading] = useState(true);
  const [lastLoadedUserId, setLastLoadedUserId] = useState<string | null>(null);

  // Track if we're currently in a tab visibility change
  const isHandlingVisibilityChange = useRef(false);

  // Load user role and permissions
  useEffect(() => {
    let retryCount = 0;
    let retryTimeout: NodeJS.Timeout;
    const MAX_RETRIES = 3;
    const RETRY_DELAY = 2000; // 2 seconds

    // Skip loading if auth is still loading
    if (authLoading) {
      return;
    }

    // If user is not authenticated, reset permissions and stop loading
    if (!isAuthenticated) {
      setUserRole('viewer');
      setPermissions(DEFAULT_PERMISSIONS.viewer);
      setIsLoading(false);
      setLastLoadedUserId(null);
      return;
    }

    // Skip if we've already loaded permissions for this user
    // OR if we're handling a tab visibility change (to prevent duplicate loads)
    if ((user && lastLoadedUserId === user.id && !isLoading) || isHandlingVisibilityChange.current) {
      return;
    }

    const loadPermissions = async () => {
      if (!user) {
        // If no user, set default permissions and stop loading
        setUserRole('viewer');
        setPermissions(DEFAULT_PERMISSIONS.viewer);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        console.log('Loading permissions for user:', user.id);

        // Get user profile to determine role
        const profile = await profileService.getProfile(user.id);

        if (!profile) {
          console.warn('No profile found, retrying...', retryCount);

          // If we've reached max retries, fall back to defaults
          if (retryCount >= MAX_RETRIES) {
            console.warn(`Max retries (${MAX_RETRIES}) reached, using default permissions`);
            setUserRole('viewer');
            setPermissions(DEFAULT_PERMISSIONS.viewer);
            setIsLoading(false);
            return;
          }

          // Otherwise, retry after a delay
          retryCount++;
          retryTimeout = setTimeout(loadPermissions, RETRY_DELAY);
          return;
        }

        console.log('Profile loaded successfully:', profile.full_name);

        // Determine role
        let role: RoleType = 'viewer';

        // Check if user is a candidate first
        if (user.userType === 'candidate') {
          role = 'candidate';
        } else if (profile.platform_admin) {
          role = 'platform_admin';
        } else if (profile.subscription_tier === 'pro') {
          role = 'admin';
        } else if (profile.subscription_tier === 'growth' || profile.subscription_tier === 'starter') {
          // Give starter tier users recruiter role so they can create jobs
          // This allows them to test the platform with 1 free CV evaluation
          // The subscription limits will still restrict them to 1 job
          role = 'recruiter';
        }

        console.log('User role determined:', role);
        setUserRole(role);

        // Load custom permissions if available
        let customPermissions = {};
        if (profile.metadata) {
          try {
            const metadata = typeof profile.metadata === 'string'
              ? JSON.parse(profile.metadata)
              : profile.metadata;

            if (metadata.customPermissions) {
              customPermissions = metadata.customPermissions;
              setRolePermissions(prev => ({
                ...prev,
                ...customPermissions
              }));
            }
          } catch (metadataError) {
            console.error('Error parsing profile metadata:', metadataError);
          }
        }

        // Set user permissions based on role
        // Use the current role permissions or fall back to defaults
        const currentRolePermissions = customPermissions[role] || DEFAULT_PERMISSIONS[role];
        setPermissions(currentRolePermissions);
        console.log('Permissions loaded successfully');
        // Mark this user as loaded
        setLastLoadedUserId(user.id);
      } catch (error) {
        console.error('Error loading permissions:', error);

        // If we've reached max retries, fall back to defaults
        if (retryCount >= MAX_RETRIES) {
          console.warn(`Max retries (${MAX_RETRIES}) reached after error, using default permissions`);
          setUserRole('viewer');
          setPermissions(DEFAULT_PERMISSIONS.viewer);
        } else {
          // Otherwise, retry after a delay
          retryCount++;
          console.log(`Retrying (${retryCount}/${MAX_RETRIES})...`);
          retryTimeout = setTimeout(loadPermissions, RETRY_DELAY);
          return; // Don't set isLoading to false yet
        }
      } finally {
        if (retryCount >= MAX_RETRIES || !retryTimeout) {
          setIsLoading(false);
        }
      }
    };

    // Start loading permissions
    setIsLoading(true);
    loadPermissions();

    // Clean up any pending retries on unmount
    return () => {
      if (retryTimeout) {
        clearTimeout(retryTimeout);
      }
    };
  }, [user, authLoading, isAuthenticated, lastLoadedUserId, isLoading]); // Include all dependencies

  // Update permissions when rolePermissions change
  useEffect(() => {
    if (userRole) {
      setPermissions(rolePermissions[userRole]);
    }
  }, [userRole, rolePermissions]);

  // Handle visibility change events to prevent unnecessary permission reloads
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'hidden') {
        console.log('PermissionsContext: Page hidden');
      } else {
        console.log('PermissionsContext: Page visible - preventing unnecessary permission reloads');
        // Mark that we're handling a visibility change to prevent duplicate permission loads
        isHandlingVisibilityChange.current = true;

        // Reset the flag after a short delay to allow for normal operation
        setTimeout(() => {
          isHandlingVisibilityChange.current = false;
        }, 2000);
      }
    };

    // Add event listener
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // Clean up
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  // Check if user has permission for a specific action on a resource
  const can = (resource: ResourceType, action: ActionType): boolean => {
    if (!permissions || !permissions[resource]) return false;
    return !!permissions[resource][action];
  };

  // Check if user has a specific role
  const hasRole = (role: RoleType | RoleType[]): boolean => {
    if (Array.isArray(role)) {
      return role.includes(userRole);
    }
    return userRole === role;
  };

  // Check if user has reached a usage limit
  const checkUsageLimit = async (
    limitType: UsageLimitType
  ): Promise<{ hasReachedLimit: boolean; currentUsage: number; limit: number }> => {
    if (!user) {
      return { hasReachedLimit: true, currentUsage: 0, limit: 0 };
    }

    try {
      // Get user's subscription tier
      const profile = await profileService.getProfile(user.id);

      if (!profile) {
        throw new Error('Profile not found');
      }

      const tier = profile.subscription_tier || 'starter';

      // Check usage limit
      return await usageService.checkUsageLimit(
        user.id,
        limitType,
        tier as 'starter' | 'growth' | 'pro'
      );
    } catch (error) {
      console.error(`Error checking ${limitType} usage limit:`, error);
      // Conservative default - assume limit is reached on error
      return { hasReachedLimit: true, currentUsage: 0, limit: 0 };
    }
  };

  // Update role permissions
  const updateRolePermissions = async (role: string, newPermissions: Permissions): Promise<void> => {
    if (!user) throw new Error('User not authenticated');

    try {
      // Only platform admins and admins can update permissions
      if (!hasRole(['platform_admin', 'admin'])) {
        throw new Error('Insufficient permissions to update role permissions');
      }

      // Update local state
      setRolePermissions(prev => ({
        ...prev,
        [role]: newPermissions
      }));

      // Get user profile
      const profile = await profileService.getProfile(user.id);

      if (!profile) {
        throw new Error('Profile not found');
      }

      // Parse existing metadata or create new object
      const metadata = profile.metadata
        ? (typeof profile.metadata === 'string' ? JSON.parse(profile.metadata) : profile.metadata)
        : {};

      // Update custom permissions in metadata
      metadata.customPermissions = {
        ...metadata.customPermissions,
        [role]: newPermissions
      };

      // Update profile with new metadata
      await profileService.updateProfile({
        user_id: user.id,
        metadata: JSON.stringify(metadata)
      });
    } catch (error) {
      console.error('Error updating role permissions:', error);
      throw error;
    }
  };

  const value = {
    userRole,
    permissions,
    rolePermissions,
    can,
    hasRole,
    updateRolePermissions,
    checkUsageLimit,
    isLoading
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
};

// Custom hook to use the permissions context
export const usePermissions = () => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};
