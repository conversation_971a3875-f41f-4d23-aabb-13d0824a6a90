import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Star, Send } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';

const feedbackSchema = z.object({
  overall_rating: z.coerce.number().min(1).max(5),
  technical_rating: z.coerce.number().min(1).max(5).optional(),
  cultural_rating: z.coerce.number().min(1).max(5).optional(),
  strengths: z.string().optional(),
  weaknesses: z.string().optional(),
  notes: z.string().optional(),
  recommendation: z.enum(['strong_yes', 'yes', 'maybe', 'no', 'strong_no'])
});

type FeedbackFormValues = z.infer<typeof feedbackSchema>;

interface InterviewFeedbackFormProps {
  interviewId: string;
  candidateName: string;
  jobTitle: string;
  onSubmit: (feedback: FeedbackFormValues) => Promise<void>;
  onCancel: () => void;
  isSubmitting?: boolean;
}

const InterviewFeedbackForm: React.FC<InterviewFeedbackFormProps> = ({
  interviewId,
  candidateName,
  jobTitle,
  onSubmit,
  onCancel,
  isSubmitting = false
}) => {
  const { user } = useAuth();

  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackSchema),
    defaultValues: {
      overall_rating: 3,
      technical_rating: 3,
      cultural_rating: 3,
      strengths: '',
      weaknesses: '',
      notes: '',
      recommendation: 'maybe'
    }
  });

  const handleSubmit = async (values: FeedbackFormValues) => {
    await onSubmit(values);
  };

  const StarRating = ({ value, onChange, label }: { 
    value: number; 
    onChange: (value: number) => void; 
    label: string;
  }) => (
    <div className="space-y-2">
      <Label className="text-sm font-medium">{label}</Label>
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            onClick={() => onChange(star)}
            className="p-1 hover:scale-110 transition-transform"
          >
            <Star
              className={`h-6 w-6 ${
                star <= value 
                  ? 'fill-yellow-400 text-yellow-400' 
                  : 'text-gray-300'
              }`}
            />
          </button>
        ))}
      </div>
    </div>
  );

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Interview Feedback</CardTitle>
        <div className="text-sm text-gray-600">
          <p><strong>Candidate:</strong> {candidateName}</p>
          <p><strong>Position:</strong> {jobTitle}</p>
        </div>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            {/* Overall Rating */}
            <FormField
              control={form.control}
              name="overall_rating"
              render={({ field }) => (
                <FormItem>
                  <StarRating
                    value={field.value}
                    onChange={field.onChange}
                    label="Overall Rating"
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Technical Rating */}
            <FormField
              control={form.control}
              name="technical_rating"
              render={({ field }) => (
                <FormItem>
                  <StarRating
                    value={field.value || 3}
                    onChange={field.onChange}
                    label="Technical Skills"
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Cultural Rating */}
            <FormField
              control={form.control}
              name="cultural_rating"
              render={({ field }) => (
                <FormItem>
                  <StarRating
                    value={field.value || 3}
                    onChange={field.onChange}
                    label="Cultural Fit"
                  />
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            {/* Recommendation */}
            <FormField
              control={form.control}
              name="recommendation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Recommendation</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="grid grid-cols-2 gap-4"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="strong_yes" id="strong_yes" />
                        <Label htmlFor="strong_yes">Strong Yes</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="yes" id="yes" />
                        <Label htmlFor="yes">Yes</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="maybe" id="maybe" />
                        <Label htmlFor="maybe">Maybe</Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="no" id="no" />
                        <Label htmlFor="no">No</Label>
                      </div>
                      <div className="flex items-center space-x-2 col-span-2">
                        <RadioGroupItem value="strong_no" id="strong_no" />
                        <Label htmlFor="strong_no">Strong No</Label>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Strengths */}
            <FormField
              control={form.control}
              name="strengths"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Strengths</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="What were the candidate's key strengths during the interview?"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Weaknesses */}
            <FormField
              control={form.control}
              name="weaknesses"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Areas for Improvement</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="What areas could the candidate improve on?"
                      className="resize-none"
                      rows={3}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Additional Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional observations or comments about the interview"
                      className="resize-none"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex gap-3 pt-4">
              <Button type="submit" disabled={isSubmitting} className="flex-1">
                {isSubmitting ? 'Submitting...' : 'Submit Feedback'}
                <Send className="h-4 w-4 ml-2" />
              </Button>
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default InterviewFeedbackForm;