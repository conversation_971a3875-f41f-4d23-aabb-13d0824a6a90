-- Add metadata column to team_members table
ALTER TABLE public.team_members ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT NULL;

-- Add comment for documentation
COMMENT ON COLUMN public.team_members.metadata IS 'Additional team member data stored as JSON, including name and email for pending invitations';

-- Create index for faster queries on metadata
CREATE INDEX IF NOT EXISTS idx_team_members_metadata ON public.team_members USING GIN (metadata);
