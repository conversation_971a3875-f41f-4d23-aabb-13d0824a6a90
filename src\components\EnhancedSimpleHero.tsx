import React, { useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import { ArrowR<PERSON>, Star } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useGSAP } from '@gsap/react';
import gsap from 'gsap';

// Floating CV Card Mockup
const FloatingCV: React.FC<{ delay?: number; className?: string }> = ({
  delay = 0,
  className = ""
}) => {
  const cvRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (cvRef.current) {
      gsap.fromTo(
        cvRef.current,
        {
          y: '+=20',
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );

      // Floating animation
      gsap.to(cvRef.current, {
        y: '+=10',
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
    }
  }, [delay]);

  return (
    <div
      ref={cvRef}
      className={`w-48 h-64 bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-lg shadow-xl p-4 flex flex-col ${className}`}
      style={{ opacity: 0 }}
    >
      <div className="w-full h-4 bg-blue-500 rounded mb-3"></div>
      <div className="w-3/4 h-3 bg-gray-600 rounded mb-2"></div>
      <div className="w-1/2 h-3 bg-gray-600 rounded mb-4"></div>
      <div className="w-full h-2 bg-gray-700 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-700 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-700 rounded mb-3"></div>
      <div className="w-1/2 h-6 bg-indigo-500 rounded mb-3"></div>
      <div className="w-full h-2 bg-gray-700 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-700 rounded mb-1"></div>
      <div className="w-2/3 h-2 bg-gray-700 rounded"></div>
      <div className="mt-auto flex justify-between">
        <div className="w-8 h-8 rounded-full bg-green-500/80"></div>
        <div className="w-12 h-4 bg-blue-500/80 rounded"></div>
      </div>
    </div>
  );
};

// Floating Company Profile Mockup
const FloatingCompany: React.FC<{ delay?: number; className?: string }> = ({
  delay = 0,
  className = ""
}) => {
  const companyRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (companyRef.current) {
      gsap.fromTo(
        companyRef.current,
        {
          y: '+=20',
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );

      // Floating animation
      gsap.to(companyRef.current, {
        y: '+=10',
        duration: 2.5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: 0.5,
      });
    }
  }, [delay]);

  return (
    <div
      ref={companyRef}
      className={`w-56 h-72 bg-gray-900/80 backdrop-blur-sm border border-gray-700/50 rounded-lg shadow-xl p-4 flex flex-col ${className}`}
      style={{ opacity: 0 }}
    >
      <div className="flex items-center mb-4">
        <div className="w-12 h-12 rounded-full bg-purple-500/80 mr-3"></div>
        <div>
          <div className="w-32 h-4 bg-gray-500 rounded mb-1"></div>
          <div className="w-24 h-3 bg-gray-600 rounded"></div>
        </div>
      </div>
      <div className="w-full h-3 bg-gray-600 rounded mb-2"></div>
      <div className="w-full h-3 bg-gray-600 rounded mb-2"></div>
      <div className="w-3/4 h-3 bg-gray-600 rounded mb-4"></div>

      <div className="grid grid-cols-2 gap-2 mb-4">
        <div className="h-8 bg-blue-900/50 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-blue-500/80 rounded"></div>
        </div>
        <div className="h-8 bg-green-900/50 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-green-500/80 rounded"></div>
        </div>
        <div className="h-8 bg-purple-900/50 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-purple-500/80 rounded"></div>
        </div>
        <div className="h-8 bg-yellow-900/50 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-yellow-500/80 rounded"></div>
        </div>
      </div>

      <div className="w-full h-2 bg-gray-700 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-700 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-700 rounded"></div>

      <div className="mt-auto flex justify-end">
        <div className="w-20 h-6 bg-indigo-500/80 rounded"></div>
      </div>
    </div>
  );
};

// Note: FloatingMatch component removed as it's no longer used

// Animated background element
const AnimatedBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Create particles
    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'absolute rounded-full bg-white opacity-0';

      // Random size between 2px and 6px
      const size = Math.random() * 4 + 2;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;

      // Random position within the container
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      particle.style.left = `${posX}%`;
      particle.style.top = `${posY}%`;

      // Add to DOM
      containerRef.current?.appendChild(particle);

      // Animate with CSS
      particle.animate(
        [
          { opacity: 0, transform: 'translateY(0)' },
          { opacity: 0.8, transform: 'translateY(-20px)' },
          { opacity: 0, transform: 'translateY(-40px)' },
        ],
        {
          duration: 3000 + Math.random() * 2000,
          easing: 'ease-out',
        }
      );

      // Remove after animation
      setTimeout(() => {
        particle.remove();
      }, 5000);
    };

    // Create particles at intervals
    const interval = setInterval(() => {
      for (let i = 0; i < 3; i++) {
        createParticle();
      }
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <div ref={containerRef} className="absolute inset-0 overflow-hidden pointer-events-none z-10">
      {/* Animated floating blob */}
      <motion.div
        className="absolute top-1/4 right-1/4 w-[300px] h-[300px] rounded-full bg-indigo-500/5 blur-xl"
        animate={{
          x: [0, 30, 0],
          y: [0, 15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      ></motion.div>

      {/* Additional animated blob */}
      <motion.div
        className="absolute bottom-1/4 left-1/3 w-[250px] h-[250px] rounded-full bg-cyan-500/5 blur-xl"
        animate={{
          x: [0, -20, 0],
          y: [0, 10, 0],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      ></motion.div>
    </div>
  );
};

// Animated typing effect
const TypedHeadline: React.FC = () => {
  const headlineRef = useRef<HTMLHeadingElement>(null);
  const cursorRef = useRef<HTMLSpanElement>(null);

  useGSAP(() => {
    if (!headlineRef.current || !cursorRef.current) return;

    const headlines = [
      "AI-Powered CV Evaluation Platform for Agencies",
      "Match Candidates to Companies With Precision",
      "Streamline Your Agency's Recruitment Process",
      "Evaluate CVs Against Multiple Job Requirements"
    ];

    let currentHeadline = 0;
    let currentChar = 0;
    let isDeleting = false;
    let typingSpeed = 50; // Faster typing speed

    const type = () => {
      if (!headlineRef.current) return;

      const headline = headlines[currentHeadline];

      if (isDeleting) {
        headlineRef.current.textContent = headline.substring(0, currentChar - 1);
        currentChar--;
        typingSpeed = 30; // Faster deletion
      } else {
        headlineRef.current.textContent = headline.substring(0, currentChar + 1);
        currentChar++;
        typingSpeed = 50; // Faster typing
      }

      if (!isDeleting && currentChar === headline.length) {
        // Pause at the end of typing
        isDeleting = true;
        typingSpeed = 1000; // Pause before deleting
      } else if (isDeleting && currentChar === 0) {
        isDeleting = false;
        currentHeadline = (currentHeadline + 1) % headlines.length; // Cycle through headlines
        typingSpeed = 300; // Pause before typing next headline
      }

      setTimeout(type, typingSpeed);
    };

    // Start typing
    setTimeout(type, 300);

    // Animate cursor
    if (cursorRef.current) {
      gsap.to(cursorRef.current, {
        opacity: 0,
        duration: 0.5,
        repeat: -1,
        yoyo: true,
        ease: 'power2.inOut',
      });
    }
  }, []);

  return (
    <h2 className="text-4xl text-white font-extrabold mx-auto md:text-5xl flex items-center justify-center h-[80px]">
      <span ref={headlineRef}></span>
      <span ref={cursorRef} className="ml-1">|</span>
    </h2>
  );
};

// Main EnhancedSimpleHero component
export const EnhancedSimpleHero: React.FC = () => {

  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* The blurred gradient background */}
      <div
        className="absolute inset-0 m-auto max-w-xs h-[357px] blur-[118px] sm:max-w-md md:max-w-lg"
        style={{
          background: 'linear-gradient(106.89deg, rgba(192, 132, 252, 0.11) 15.73%, rgba(14, 165, 233, 0.41) 15.74%, rgba(232, 121, 249, 0.26) 56.49%, rgba(79, 70, 229, 0.4) 115.91%)'
        }}
      ></div>

      {/* Animated background */}
      <AnimatedBackground />

      {/* Floating UI Elements - Repositioned to avoid overlap with content */}
      <div className="absolute inset-0 z-10 pointer-events-none hidden md:block">
        <div className="relative w-full h-full">
          <FloatingCV className="absolute top-[15%] left-[5%] max-w-[200px] transform -rotate-6" delay={0.5} />
          <FloatingCompany className="absolute top-[20%] right-[5%] max-w-[220px] transform rotate-3" delay={0.8} />
          {/* Removed the third floating element to reduce clutter */}
        </div>
      </div>

      {/* Content - Adjusted padding-top to account for the absolute positioned navbar */}
      <div className="relative z-20 max-w-screen-xl mx-auto px-4 pt-36 pb-28 md:px-8">
        <div className="space-y-5 max-w-4xl mx-auto text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            <TypedHeadline />
          </motion.div>

          <motion.p
            className="max-w-2xl mx-auto text-gray-400"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Upload CVs, match against company requirements, and deliver better candidates to your clients.
            Our intelligent system helps agencies evaluate talent more effectively.
          </motion.p>

          <motion.div
            className="flex flex-col sm:flex-row items-center justify-center gap-4"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full sm:w-auto"
            >
              <Link
                to="/signup"
                className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white py-4 px-8 rounded-lg flex items-center justify-center transition-all duration-300 w-full shadow-lg shadow-blue-500/20"
              >
                Start Evaluating Candidates <ArrowRight size={16} className="ml-2" />
              </Link>
            </motion.div>

            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="w-full sm:w-auto"
            >
              <Link
                to="/login"
                className="bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white py-4 px-8 rounded-lg flex items-center justify-center transition-colors w-full"
              >
                Sign In
              </Link>
            </motion.div>
          </motion.div>



          <motion.div
            className="flex justify-center items-center gap-x-4 text-gray-400 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" />
              ))}
            </div>
            <p><span className="text-gray-100">5.0</span> by over 65 companies</p>
          </motion.div>
        </div>
      </div>
    </section>
  );
};
