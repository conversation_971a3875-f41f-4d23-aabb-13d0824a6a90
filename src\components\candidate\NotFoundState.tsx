import React from 'react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const NotFoundState: React.FC = () => {
  const navigate = useNavigate();
  
  return (
    <div className="flex items-center justify-center h-[60vh]">
      <div className="flex flex-col items-center gap-2">
        <p className="text-gray-500">Candidate not found. Please try again or go back to the candidates list.</p>
        <Button
          className="mt-4"
          onClick={() => navigate('/dashboard/cvs')}
        >
          Back to Candidates
        </Button>
      </div>
    </div>
  );
};

export default NotFoundState;
