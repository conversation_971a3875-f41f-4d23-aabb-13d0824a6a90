import React, { useState, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Upload, FileText, X, Eye, EyeOff } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface CoverLetterData {
  content?: string;
  file?: File;
  url?: string;
}

interface CoverLetterInputProps {
  onCoverLetterChange: (data: CoverLetterData | null) => void;
  disabled?: boolean;
  className?: string;
}

const CoverLetterInput: React.FC<CoverLetterInputProps> = ({
  onCoverLetterChange,
  disabled = false,
  className = ''
}) => {
  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const [activeTab, setActiveTab] = useState<'text' | 'upload'>('text');
  const [textContent, setTextContent] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [isPreviewExpanded, setIsPreviewExpanded] = useState(false);

  // Handle text input change
  const handleTextChange = (value: string) => {
    setTextContent(value);
    
    if (value.trim()) {
      onCoverLetterChange({ content: value });
    } else {
      onCoverLetterChange(null);
    }
  };

  // Handle file upload
  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    
    if (!file) return;

    // Validate file type
    const allowedTypes = [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ];

    if (!allowedTypes.includes(file.type)) {
      toast({
        title: 'Invalid File Type',
        description: 'Please upload a PDF, Word document, or text file.',
        variant: 'destructive'
      });
      return;
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      toast({
        title: 'File Too Large',
        description: 'Please upload a file smaller than 5MB.',
        variant: 'destructive'
      });
      return;
    }

    setUploadedFile(file);
    onCoverLetterChange({ file });
    
    toast({
      title: 'Cover Letter Uploaded',
      description: `${file.name} has been uploaded successfully.`,
    });
  };

  // Remove uploaded file
  const removeFile = () => {
    setUploadedFile(null);
    onCoverLetterChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Clear text content
  const clearText = () => {
    setTextContent('');
    onCoverLetterChange(null);
  };

  // Switch tabs and clear other input
  const handleTabChange = (value: string) => {
    setActiveTab(value as 'text' | 'upload');
    
    // Clear the other input when switching tabs
    if (value === 'text') {
      removeFile();
    } else {
      clearText();
    }
  };

  return (
    <Card className={`${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <FileText className="h-5 w-5" />
          Cover Letter (Optional)
        </CardTitle>
        <p className="text-sm text-gray-600">
          Add a cover letter to provide additional context for your application.
        </p>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="text">Paste Text</TabsTrigger>
            <TabsTrigger value="upload">Upload File</TabsTrigger>
          </TabsList>
          
          <TabsContent value="text" className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="cover-letter-text">Cover Letter Content</Label>
                {textContent && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={clearText}
                    disabled={disabled}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                )}
              </div>
              <Textarea
                id="cover-letter-text"
                placeholder="Paste your cover letter content here..."
                value={textContent}
                onChange={(e) => handleTextChange(e.target.value)}
                disabled={disabled}
                rows={8}
                className="resize-none"
              />
              <p className="text-xs text-gray-500">
                {textContent.length}/2000 characters
              </p>
            </div>
          </TabsContent>
          
          <TabsContent value="upload" className="space-y-4">
            <div className="space-y-4">
              {!uploadedFile ? (
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Upload className="mx-auto h-12 w-12 text-gray-400" />
                  <div className="mt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => fileInputRef.current?.click()}
                      disabled={disabled}
                    >
                      Choose File
                    </Button>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept=".pdf,.doc,.docx,.txt"
                      onChange={handleFileUpload}
                      className="hidden"
                      disabled={disabled}
                    />
                  </div>
                  <p className="mt-2 text-sm text-gray-500">
                    Upload PDF, Word, or text files (max 5MB)
                  </p>
                </div>
              ) : (
                <div className="border border-gray-200 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <FileText className="h-8 w-8 text-blue-500" />
                      <div>
                        <p className="font-medium">{uploadedFile.name}</p>
                        <p className="text-sm text-gray-500">
                          {(uploadedFile.size / 1024 / 1024).toFixed(2)} MB
                        </p>
                      </div>
                    </div>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={removeFile}
                      disabled={disabled}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default CoverLetterInput;
