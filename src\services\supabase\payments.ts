import { supabase } from '@/lib/supabase';

/**
 * Payment data interface
 */
export interface PaymentData {
  id: string;
  user_id: string;
  subscription_id: string | null;
  payment_id: string;
  amount: number;
  currency: string;
  status: string;
  payment_method: string;
  payment_type: 'subscription' | 'one-time';
  created_at: string;
  payment_details: any;
}

/**
 * Record a payment
 */
export const recordPayment = async (
  userId: string,
  paymentData: {
    payment_id: string;
    amount: number;
    currency: string;
    status: string;
    payment_method: string;
    payment_type: 'subscription' | 'one-time';
    subscription_id?: string;
    payment_details?: any;
  }
): Promise<PaymentData | null> => {
  try {
    const { data, error } = await supabase
      .from('payments')
      .insert({
        user_id: userId,
        payment_id: paymentData.payment_id,
        amount: paymentData.amount,
        currency: paymentData.currency,
        status: paymentData.status,
        payment_method: paymentData.payment_method,
        payment_type: paymentData.payment_type,
        subscription_id: paymentData.subscription_id || null,
        payment_details: paymentData.payment_details || {},
      })
      .select()
      .single();

    if (error) {
      console.error('Error recording payment:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in recordPayment:', error);
    throw error;
  }
};

/**
 * Get payment history for a user
 */
export const getPaymentHistory = async (userId: string): Promise<PaymentData[]> => {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching payment history:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getPaymentHistory:', error);
    return [];
  }
};

/**
 * Get a specific payment by ID
 */
export const getPaymentById = async (
  paymentId: string,
  userId: string
): Promise<PaymentData | null> => {
  try {
    const { data, error } = await supabase
      .from('payments')
      .select('*')
      .eq('payment_id', paymentId)
      .eq('user_id', userId)
      .single();

    if (error) {
      console.error('Error fetching payment:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getPaymentById:', error);
    return null;
  }
};

/**
 * Update payment status
 */
export const updatePaymentStatus = async (
  paymentId: string,
  status: string,
  userId: string
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('payments')
      .update({ status })
      .eq('payment_id', paymentId)
      .eq('user_id', userId);

    if (error) {
      console.error('Error updating payment status:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in updatePaymentStatus:', error);
    throw error;
  }
};

/**
 * Get subscription payments
 */
export const getSubscriptionPayments = async (
  subscriptionId: string,
  userId: string
): Promise<PaymentData[]> => {
  try {
    const { data, error } = await supabase
      .from('payment_history')
      .select('*')
      .eq('subscription_id', subscriptionId)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching subscription payments:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getSubscriptionPayments:', error);
    return [];
  }
};
