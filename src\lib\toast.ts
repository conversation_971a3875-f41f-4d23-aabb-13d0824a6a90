/**
 * Simple toast utility for showing notifications
 * This is a wrapper around the useToast hook that can be used outside of React components
 */

// Define the toast function type
type ToastFunction = (props: {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
}) => void;

// Initialize with a no-op function
let toastFn: ToastFunction = () => {};

// Function to set the toast function
export const setToast = (fn: ToastFunction) => {
  toastFn = fn;
};

// Function to show a toast
export const toast = (props: {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive' | 'success' | 'warning';
}) => {
  toastFn(props);
};

// Convenience methods
export const successToast = (title: string, description?: string) => {
  toast({ title, description, variant: 'success' });
};

export const errorToast = (title: string, description?: string) => {
  toast({ title, description, variant: 'destructive' });
};

export const warningToast = (title: string, description?: string) => {
  toast({ title, description, variant: 'warning' });
};

export const infoToast = (title: string, description?: string) => {
  toast({ title, description, variant: 'default' });
};
