import React, { useEffect, useRef } from 'react';
import { ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';

export const EnhancedHeroSection: React.FC = () => {
  const heroRef = useRef<HTMLDivElement>(null);

  // Animation variants for staggered animations
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.3,
      },
    },
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 10,
      },
    },
  };

  // Particle animation effect
  useEffect(() => {
    if (!heroRef.current) return;

    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'absolute rounded-full bg-white opacity-0';
      
      // Random size between 2px and 6px
      const size = Math.random() * 4 + 2;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;
      
      // Random position within the hero section
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      particle.style.left = `${posX}%`;
      particle.style.top = `${posY}%`;
      
      // Add to DOM
      heroRef.current?.appendChild(particle);
      
      // Animate with CSS
      particle.animate(
        [
          { opacity: 0, transform: 'translateY(0)' },
          { opacity: 0.8, transform: 'translateY(-20px)' },
          { opacity: 0, transform: 'translateY(-40px)' },
        ],
        {
          duration: 3000 + Math.random() * 2000,
          easing: 'ease-out',
        }
      );
      
      // Remove after animation
      setTimeout(() => {
        particle.remove();
      }, 5000);
    };
    
    // Create particles at intervals
    const interval = setInterval(() => {
      for (let i = 0; i < 3; i++) {
        createParticle();
      }
    }, 500);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <section className="relative py-20 overflow-hidden" ref={heroRef}>
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-recruiter-navy to-recruiter-darknavy"></div>
      
      {/* Animated background circles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-[300px] -left-[300px] w-[600px] h-[600px] rounded-full bg-blue-500/10 blur-3xl"></div>
        <div className="absolute -bottom-[200px] -right-[200px] w-[500px] h-[500px] rounded-full bg-purple-500/10 blur-3xl"></div>
        <motion.div 
          className="absolute top-1/4 right-1/4 w-[300px] h-[300px] rounded-full bg-indigo-500/5 blur-xl"
          animate={{
            x: [0, 30, 0],
            y: [0, 15, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        ></motion.div>
      </div>
      
      <div className="container mx-auto px-6 relative z-10">
        <motion.div 
          className="text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h1 
            className="text-5xl font-bold text-white mb-6"
            variants={itemVariants}
          >
            AI-Powered CV Evaluation<br />Platform for Agencies
          </motion.h1>
          
          <motion.p 
            className="text-gray-300 mb-8 max-w-3xl mx-auto"
            variants={itemVariants}
          >
            Upload CVs, match against company requirements, and deliver better candidates to your clients. 
            Our intelligent system helps agencies evaluate talent more effectively.
          </motion.p>
          
          <motion.div 
            className="flex flex-col sm:flex-row items-center justify-center gap-4"
            variants={itemVariants}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to="/signup"
                className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white py-4 px-8 rounded-lg flex items-center justify-center transition-all duration-300 w-full sm:w-auto shadow-lg shadow-blue-500/20"
              >
                Start Evaluating Candidates <ArrowRight size={16} className="ml-2" />
              </Link>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Link
                to="/login"
                className="bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white py-4 px-8 rounded-lg flex items-center justify-center transition-colors w-full sm:w-auto"
              >
                Sign In
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
