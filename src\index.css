
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222 47% 11%;

    --card: 0 0% 100%;
    --card-foreground: 222 47% 11%;

    --popover: 0 0% 100%;
    --popover-foreground: 222 47% 11%;

    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 199 89% 48%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 199 89% 48%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222 47% 11%;
    --sidebar-primary: 210 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 210 40% 96.1%;
    --sidebar-accent-foreground: 222 47% 11%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 199 89% 48%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Fix for document viewer iframe heights */
  #msdoc-iframe {
    height: 45em !important;
    min-height: 45em !important;
  }

  .react-pdf__Document,
  .react-pdf__Page {
    height: 45em !important;
    min-height: 45em !important;
  }

  embed[type="application/pdf"] {
    height: 45em !important;
    min-height: 45em !important;
  }

  /* Dashboard zoom level */
  .dashboard-zoom {
    zoom: 75%;
  }

  /* Apply zoom to modals/dialogs when dashboard is active */
  .dashboard-zoom [role="dialog"],
  .dashboard-zoom [data-radix-dialog-content],
  .dashboard-zoom .fixed[role="dialog"] {
    zoom: 75%;
  }

  /* Also apply to any fixed positioned modals that might be outside the dashboard container */
  body:has(.dashboard-zoom) [role="dialog"],
  body:has(.dashboard-zoom) [data-radix-dialog-content] {
    zoom: 75%;
  }
}

@layer components {
  .bg-hero-gradient {
    background: linear-gradient(to bottom, #0A0C1B, #111838);
    background-size: cover;
  }

  .bg-card-gradient {
    background: linear-gradient(to bottom, #101b36, #0d1429);
  }

  .bg-sidebar-gradient {
    background: linear-gradient(135deg, #ffffff, #f5f7fa);
  }

  .bg-header-gradient {
    background: linear-gradient(to right, #ffffff, #f5f7fa);
  }

  .bg-primary-gradient {
    background: linear-gradient(135deg, #1E7BEA, #00B2FF);
  }

  .bg-secondary-gradient {
    background: linear-gradient(135deg, #FF6B6B, #FFE66D);
  }

  .bg-success-gradient {
    background: linear-gradient(135deg, #4CAF50, #8BC34A);
  }

  .bg-info-gradient {
    background: linear-gradient(135deg, #00BCD4, #03A9F4);
  }

  .bg-warning-gradient {
    background: linear-gradient(135deg, #FF9800, #FFEB3B);
  }

  .bg-danger-gradient {
    background: linear-gradient(135deg, #F44336, #FF5722);
  }

  .bg-purple-gradient {
    background: linear-gradient(135deg, #9C27B0, #673AB7);
  }

  .bg-card-hover {
    transition: all 0.3s ease;
  }

  .bg-card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-in-left {
  from {
    transform: translateX(-20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-right {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slide-in-top {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slide-in-bottom {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scale-in {
  from {
    transform: scale(0.9);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out forwards;
}

.animate-slide-in-left {
  animation: slide-in-left 0.5s ease-out forwards;
}

.animate-slide-in-right {
  animation: slide-in-right 0.5s ease-out forwards;
}

.animate-slide-in-top {
  animation: slide-in-top 0.5s ease-out forwards;
}

.animate-slide-in-bottom {
  animation: slide-in-bottom 0.5s ease-out forwards;
}

.animate-scale-in {
  animation: scale-in 0.5s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-delay-100 {
  animation-delay: 100ms;
}

.animate-delay-200 {
  animation-delay: 200ms;
}

.animate-delay-300 {
  animation-delay: 300ms;
}

.animate-delay-400 {
  animation-delay: 400ms;
}

.animate-delay-500 {
  animation-delay: 500ms;
}

