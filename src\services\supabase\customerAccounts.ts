import { supabase } from '@/lib/supabase';

export interface CustomerAccount {
  id: string;
  user_id: string;
  provider: 'stripe' | 'paypal' | 'square' | 'razorpay';
  customer_id: string;
  customer_email?: string;
  customer_name?: string;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

/**
 * Get customer account for a specific provider
 */
export const getCustomerAccount = async (
  userId: string,
  provider: CustomerAccount['provider']
): Promise<CustomerAccount | null> => {
  try {
    const { data, error } = await supabase
      .from('customer_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('provider', provider)
      .eq('is_active', true)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows returned
        return null;
      }
      console.error('Error fetching customer account:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getCustomerAccount:', error);
    return null;
  }
};

/**
 * Create or update customer account
 */
export const upsertCustomerAccount = async (
  customerAccount: Omit<CustomerAccount, 'id' | 'created_at' | 'updated_at'>
): Promise<CustomerAccount | null> => {
  try {
    const { data, error } = await supabase
      .from('customer_accounts')
      .upsert({
        ...customerAccount,
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('Error upserting customer account:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in upsertCustomerAccount:', error);
    throw error;
  }
};

/**
 * Get all customer accounts for a user
 */
export const getUserCustomerAccounts = async (
  userId: string
): Promise<CustomerAccount[]> => {
  try {
    const { data, error } = await supabase
      .from('customer_accounts')
      .select('*')
      .eq('user_id', userId)
      .eq('is_active', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching user customer accounts:', error);
      throw error;
    }

    return data || [];
  } catch (error) {
    console.error('Error in getUserCustomerAccounts:', error);
    return [];
  }
};

/**
 * Deactivate customer account (soft delete)
 */
export const deactivateCustomerAccount = async (
  userId: string,
  provider: CustomerAccount['provider']
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('customer_accounts')
      .update({ 
        is_active: false,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .eq('provider', provider);

    if (error) {
      console.error('Error deactivating customer account:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deactivateCustomerAccount:', error);
    return false;
  }
};

/**
 * Update customer account metadata
 */
export const updateCustomerAccountMetadata = async (
  userId: string,
  provider: CustomerAccount['provider'],
  metadata: Record<string, any>
): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('customer_accounts')
      .update({ 
        metadata,
        updated_at: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .eq('provider', provider)
      .eq('is_active', true);

    if (error) {
      console.error('Error updating customer account metadata:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in updateCustomerAccountMetadata:', error);
    return false;
  }
};
