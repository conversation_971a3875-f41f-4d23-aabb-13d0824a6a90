import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Award, Briefcase, GraduationCap } from 'lucide-react';
import CVPreview from '@/components/cv/CVPreview.jsx';
import CVEvaluationResults from '@/components/cv/CVEvaluationResults';

interface CVEvaluationTabProps {
  candidate: any;
  evaluationData: any;
  setIsEvaluateModalOpen: (isOpen: boolean) => void;
}

const CVEvaluationTab: React.FC<CVEvaluationTabProps> = ({
  candidate,
  evaluationData,
  setIsEvaluateModalOpen
}) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <div>
        <CVPreview
          url={candidate.cv_url}
          fileName={candidate.name ? `${candidate.name.replace(/\s+/g, '_')}.pdf` : 'resume.pdf'}
        />
      </div>
      <div>
        {evaluationData ? (
          <CVEvaluationResults {...evaluationData} />
        ) : (
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg">Evaluation Results</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex flex-col items-center">
                <div className="mb-2 p-4 bg-gray-50 rounded-lg border border-gray-200 text-center">
                  <div className="text-gray-500 mb-2">Not Evaluated</div>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs border-gray-200 text-gray-700 hover:bg-gray-100"
                    onClick={() => setIsEvaluateModalOpen(true)}
                  >
                    Evaluate Candidate
                  </Button>
                </div>
                <h2 className="text-xl font-bold text-gray-800">{candidate.name}</h2>
                <p className="text-gray-500">CV Information</p>
              </div>

              <div>
                <h3 className="text-gray-800 font-medium mb-3 flex items-center">
                  <Award className="mr-2 h-4 w-4 text-gray-500" /> Skills
                </h3>
                <div className="text-gray-500 text-sm">
                  Evaluate this candidate to see skills analysis
                </div>
              </div>

              <div>
                <h3 className="text-gray-800 font-medium mb-3 flex items-center">
                  <Briefcase className="mr-2 h-4 w-4 text-gray-500" /> Experience
                </h3>
                <div className="text-gray-500 text-sm">
                  Evaluate this candidate to see experience analysis
                </div>
              </div>

              <div>
                <h3 className="text-gray-800 font-medium mb-3 flex items-center">
                  <GraduationCap className="mr-2 h-4 w-4 text-gray-500" /> Education
                </h3>
                <div className="text-gray-500 text-sm">
                  Evaluate this candidate to see education analysis
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

export default CVEvaluationTab;
