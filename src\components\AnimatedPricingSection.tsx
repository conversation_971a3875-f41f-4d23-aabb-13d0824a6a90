import React, { useState, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Spark<PERSON>, X, ArrowRight, Database, Zap } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useDynamicPricing } from '@/hooks/use-dynamic-pricing';

interface PricingFeature {
  text: string;
  highlighted?: boolean;
  enabled?: boolean;
  featureKey?: string;
}

interface PricingTier {
  title: string;
  emoji: string;
  price: string;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
  color: string;
  planKey?: 'STARTER' | 'GROWTH' | 'PRO';
}

// Base pricing tiers with static features (usage limits, etc.)
const basePricingTiers: PricingTier[] = [
  {
    title: "Starter Plan",
    emoji: "🌱",
    price: "$49",
    description: "Great for small recruitment agencies with few clients.",
    color: "blue",
    planKey: "STARTER",
    features: [
      { text: "Upload up to 50 CVs per month", highlighted: true, enabled: true },
      { text: "Manage 1 company profile", enabled: true },
      { text: "Automatic CV evaluation", enabled: true },
      { text: "Basic match quality reports", enabled: true },
      { text: "Candidate skill extraction", enabled: true },
    ]
  },
  {
    title: "Growth Plan",
    emoji: "🚀",
    price: "$99",
    description: "For growing agencies with multiple client companies.",
    color: "purple",
    isPopular: true,
    planKey: "GROWTH",
    features: [
      { text: "Upload up to 500 CVs per month", highlighted: true, enabled: true },
      { text: "Manage up to 5 company profiles", highlighted: true, enabled: true },
      { text: "Advanced CV matching algorithms", enabled: true },
      { text: "Download reports (PDF or Excel)", enabled: true },
      { text: "Insights to improve your matching", enabled: true },
      { text: "Bulk CV processing", enabled: true },
    ]
  },
  {
    title: "Pro Plan",
    emoji: "🏢",
    price: "$199",
    description: "Best for established recruitment agencies with many clients.",
    color: "indigo",
    planKey: "PRO",
    features: [
      { text: "Unlimited CV uploads", highlighted: true, enabled: true },
      { text: "Unlimited company profiles", highlighted: true, enabled: true },
      { text: "Custom rules for candidate scoring", enabled: true },
      { text: "Add team members and collaborate", enabled: true },
      { text: "Detailed evaluation reports", enabled: true },
    ]
  }
];

// Feature mapping from database to display text
const featureDisplayMap: Record<string, { text: string; highlighted?: boolean }> = {
  'interview_scheduling': { text: "Interview Scheduling", highlighted: true },
  'team_management': { text: "Team Member Management" },
  'custom_scoring': { text: "Custom Scoring Rules", highlighted: true },
  'advanced_analytics': { text: "Advanced Analytics & Insights" },
  'api_access': { text: "API Access for Integrations" },
  'white_label': { text: "White-label Solution" },
  'priority_support': { text: "Priority Customer Support" }
};

// Function to merge base features with dynamic features from database
const mergeFeaturesWithDatabase = (baseTiers: PricingTier[], dbPlanFeatures: any) => {
  if (!dbPlanFeatures) return baseTiers;

  // First, identify features that are disabled on ALL plans
  const allFeatureKeys = Object.keys(featureDisplayMap);
  const featuresDisabledOnAllPlans = allFeatureKeys.filter(featureKey => {
    return Object.values(dbPlanFeatures).every((planFeatures: any) =>
      planFeatures[featureKey] === false
    );
  });

  return baseTiers.map(tier => {
    if (!tier.planKey) return tier;

    const planFeatures = dbPlanFeatures[tier.planKey] || {};
    const dynamicFeatures: PricingFeature[] = [];

    // Add dynamic features from database, but SKIP features disabled on all plans
    Object.entries(planFeatures).forEach(([featureKey, enabled]) => {
      const featureDisplay = featureDisplayMap[featureKey];

      // Skip features that are disabled on ALL plans (hide them completely)
      if (featureDisplay && !featuresDisabledOnAllPlans.includes(featureKey)) {
        dynamicFeatures.push({
          text: featureDisplay.text,
          highlighted: featureDisplay.highlighted,
          enabled: enabled as boolean,
          featureKey
        });
      }
    });

    return {
      ...tier,
      features: [...tier.features, ...dynamicFeatures]
    };
  });
};

// Pricing Card Component
const PricingCard: React.FC<{
  tier: PricingTier;
  isExpanded: boolean;
  onToggle: () => void;
  delay: number;
}> = ({ tier, isExpanded, onToggle, delay }) => {
  const cardRef = useRef<HTMLDivElement>(null);

  return (
    <motion.div
      ref={cardRef}
      initial={{ opacity: 0, y: 50 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay: delay * 0.2 }}
      className={`relative bg-card-gradient rounded-xl p-6 flex flex-col overflow-hidden ${
        tier.isPopular ? `border-2 border-${tier.color}-500` : 'border border-gray-800'
      }`}
    >
      {/* Popular badge */}
      {tier.isPopular && (
        <div className="absolute -right-12 bg-gradient-to-r from-indigo-500 to-purple-600 text-white text-xs font-bold py-1 px-12 transform rotate-45 shadow-lg">
          MOST POPULAR
        </div>
      )}

      <div className="mb-6">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <div className="text-3xl mr-3">{tier.emoji}</div>
            <h3 className="text-white text-xl font-bold">{tier.title}</h3>
          </div>
        </div>

        <div className="mb-3">
          <div className="flex items-baseline">
            <span className="text-white text-4xl font-bold">{tier.price}</span>
            <span className="text-gray-400 ml-2">/month</span>
          </div>
          <p className="text-gray-300 mt-2">{tier.description}</p>
          {tier.title === "Starter Plan" && (
            <p className="text-blue-300 mt-2 text-sm font-medium">
              Try with 1 free CV evaluation before subscribing!
            </p>
          )}
        </div>
      </div>

      <div className="mb-6 flex-grow">
        <ul className="space-y-3">
          {tier.features.slice(0, isExpanded ? tier.features.length : 4).map((feature, index) => (
            <motion.li
              key={index}
              className="flex items-start"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
            >
              <div className={`mt-1 mr-3 flex-shrink-0 w-5 h-5 rounded-full ${
                feature.enabled === false
                  ? 'bg-gray-500/20'
                  : `bg-${tier.color}-500/20`
              } flex items-center justify-center`}>
                {feature.enabled === false ? (
                  <X size={12} className="text-gray-500" />
                ) : (
                  <Check size={12} className={`text-${tier.color}-400`} />
                )}
              </div>
              <span className={`${
                feature.enabled === false
                  ? 'text-gray-500 line-through'
                  : feature.highlighted
                    ? 'text-white font-semibold'
                    : 'text-gray-300'
              }`}>
                {feature.text}
                {feature.highlighted && (
                  <Sparkles size={12} className="inline-block ml-1 text-yellow-400" />
                )}
              </span>
            </motion.li>
          ))}
        </ul>

        {tier.features.length > 4 && (
          <button
            onClick={onToggle}
            className="mt-3 text-sm text-gray-400 hover:text-white flex items-center transition-colors"
          >
            {isExpanded ? (
              <>
                <X size={12} className="mr-1" />
                Show less
              </>
            ) : (
              <>
                <ArrowRight size={12} className="mr-1" />
                Show all features
              </>
            )}
          </button>
        )}
      </div>

      <motion.div
        whileHover={{ scale: 1.03 }}
        whileTap={{ scale: 0.98 }}
      >
        <Link
          to="/signup"
          className={`w-full py-3 rounded-lg text-center font-medium transition-all flex items-center justify-center
            ${tier.isPopular
              ? `bg-gradient-to-r from-${tier.color}-500 to-${tier.color}-600 hover:from-${tier.color}-600 hover:to-${tier.color}-700 text-white shadow-lg shadow-${tier.color}-500/20`
              : 'bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white'}`}
        >
          Get Started
        </Link>
      </motion.div>
    </motion.div>
  );
};

// Main Pricing Section Component
export const AnimatedPricingSection: React.FC = () => {
  const [expandedCards, setExpandedCards] = useState<{[key: string]: boolean}>({});

  // Get dynamic pricing data from database
  const { pricingTiers: dbPricingTiers, isLoading, hasDbFeatures } = useDynamicPricing();

  // Merge base features with database features
  const enhancedPricingTiers = mergeFeaturesWithDatabase(
    basePricingTiers,
    dbPricingTiers.reduce((acc, tier) => {
      acc[tier.planKey] = tier.enabledFeatures;
      return acc;
    }, {} as any)
  );

  const toggleCard = (title: string) => {
    setExpandedCards(prev => ({
      ...prev,
      [title]: !prev[title]
    }));
  };

  return (
    <section id="pricing" className="py-24 bg-recruiter-navy overflow-hidden">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-4xl font-bold text-white mb-4"
          >
            Choose Your Agency Plan
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Select the plan that fits your recruitment agency's needs.
          </motion.p>

          {/* Dynamic Features Indicator */}
   
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {enhancedPricingTiers.map((tier, index) => (
            <PricingCard
              key={tier.title}
              tier={tier}
              isExpanded={!!expandedCards[tier.title]}
              onToggle={() => toggleCard(tier.title)}
              delay={index}
            />
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.6 }}
          className="mt-16 text-center"
        >
          <h3 className="text-2xl font-bold text-white mb-4">Need a Custom Solution?</h3>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            We offer white-label solutions and custom enterprise plans for larger recruitment agencies with specific needs.
          </p>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="inline-block"
          >
            <Link
              to="/contact"
              className="bg-gradient-to-r from-gray-700 to-gray-800 hover:from-gray-600 hover:to-gray-700 text-white py-3 px-8 rounded-lg flex items-center justify-center transition-all duration-300 mx-auto"
            >
              Contact Us for Custom Pricing
            </Link>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};
