-- Create scalable customer_accounts table for multiple payment providers
-- This table stores customer IDs for different payment providers (Stripe, PayPal, etc.)

CREATE TABLE IF NOT EXISTS public.customer_accounts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    provider TEXT NOT NULL CHECK (provider IN ('stripe', 'paypal', 'square', 'razorpay')),
    customer_id TEXT NOT NULL,
    customer_email TEXT,
    customer_name TEXT,
    is_active BOOLEAN DEFAULT true NOT NULL,
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    
    -- Ensure one customer account per user per provider
    UNIQUE(user_id, provider)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_customer_accounts_user_id 
ON public.customer_accounts(user_id);

CREATE INDEX IF NOT EXISTS idx_customer_accounts_provider 
ON public.customer_accounts(provider);

CREATE INDEX IF NOT EXISTS idx_customer_accounts_customer_id 
ON public.customer_accounts(customer_id);

-- Enable RLS
ALTER TABLE public.customer_accounts ENABLE ROW LEVEL SECURITY;

-- Users can view their own customer accounts
CREATE POLICY "Users can view their own customer accounts"
ON public.customer_accounts
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Users can insert their own customer accounts
CREATE POLICY "Users can insert their own customer accounts"
ON public.customer_accounts
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Users can update their own customer accounts
CREATE POLICY "Users can update their own customer accounts"
ON public.customer_accounts
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

-- Service role can manage all customer accounts (for webhooks and admin functions)
CREATE POLICY "Service role can manage customer accounts"
ON public.customer_accounts
FOR ALL
TO service_role
USING (true);

-- Add comments for documentation
COMMENT ON TABLE public.customer_accounts IS 'Stores customer account IDs for different payment providers. Scalable design supports multiple payment providers.';
COMMENT ON COLUMN public.customer_accounts.provider IS 'Payment provider name (stripe, paypal, square, etc.)';
COMMENT ON COLUMN public.customer_accounts.customer_id IS 'Customer ID from the payment provider';
COMMENT ON COLUMN public.customer_accounts.metadata IS 'Additional provider-specific data';
