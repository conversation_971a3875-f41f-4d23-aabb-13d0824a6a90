import { extractSkillFromStrength, extractSkillNamesFromText } from './candidateUtils';

// Generate evaluation data based on candidate information
export const generateEvaluationData = (candidate: any, parsedCV: any, extractedSkills: any, position: string, matchScore: number) => {
  // Check if we have real evaluation data from the matchResult
  let matchResult = null;
  try {
    if (candidate.evaluation_summary) {
      const summary = JSON.parse(candidate.evaluation_summary);

      // First, try to get matchResult from the standard format
      if (summary.matchResult) {
        matchResult = summary.matchResult;
        console.log("Using real match result data:", matchResult);
      }
      // If not found, try to parse it from the raw GROQ API response
      else if (summary.raw) {
        try {
          // Try to extract J<PERSON><PERSON> from the raw response
          const jsonMatch = summary.raw.match(/```json\n([\s\S]*?)\n```/) ||
                          summary.raw.match(/```\n([\s\S]*?)\n```/) ||
                          summary.raw.match(/{[\s\S]*}/);

          if (jsonMatch) {
            const jsonStr = jsonMatch[1] || jsonMatch[0];
            matchResult = JSON.parse(jsonStr);
            console.log("Extracted match result from raw response:", matchResult);
          }
        } catch (rawParseError) {
          console.error("Error parsing raw match result:", rawParseError);
        }
      }
      // If still not found, check if the summary itself is the match result
      else if (summary.overallScore !== undefined && summary.strengths !== undefined) {
        matchResult = summary;
        console.log("Using summary as match result:", matchResult);
      }
    }
  } catch (e) {
    console.error("Error parsing match result:", e);
  }

  // Process skills with real match scores from the evaluation
  const processSkills = () => {
    // If we have real match result data with skills analysis, use it
    if (matchResult) {
      // Try to extract skills from the match result
      if (matchResult.skillsMatch) {
        const skillsData = matchResult.skillsMatch;

        // Some match results might have detailed skill breakdowns
        if (skillsData.skills && Array.isArray(skillsData.skills)) {
          return skillsData.skills.map((skill: any) => ({
            name: skill.name,
            match: skill.score,
            required: skill.required || false
          }));
        }

        // If no detailed skills but we have a skills analysis, create skills from it
        if (skillsData.analysis) {
          // Extract skill names from the analysis text
          const skillNames = extractSkillNamesFromText(skillsData.analysis);
          if (skillNames.length > 0) {
            return skillNames.map(name => ({
              name,
              match: skillsData.score || 70,
              required: false
            }));
          }
        }
      }

      // If we have strengths, use them as skills
      if (matchResult.strengths && Array.isArray(matchResult.strengths) && matchResult.strengths.length > 0) {
        return matchResult.strengths.map((strength: string) => {
          // Extract skill name from strength text
          const skillName = extractSkillFromStrength(strength);
          return {
            name: skillName,
            match: 80, // Strengths are generally high match
            required: false
          };
        });
      }
    }

    // Fallback to extracted skills if no match data
    if (extractedSkills) {
      // Get job skills if available
      let jobSkills: string[] = [];
      if (candidate.job_id && candidate.job_requirements) {
        try {
          const requirements = typeof candidate.job_requirements === 'string'
            ? JSON.parse(candidate.job_requirements)
            : candidate.job_requirements;

          if (requirements && requirements.skills) {
            jobSkills = requirements.skills;
          }
        } catch (e) {
          console.error('Error parsing job requirements:', e);
        }
      }

      // Combine all skills from extracted skills
      const allSkills = [];

      // Process technical skills
      if (extractedSkills.technical) {
        const technicalSkills = extractedSkills.technical.map((skill: any) => {
          const skillName = typeof skill === 'string' ? skill : skill.name;
          // Check if skill is in job requirements
          const isRequired = jobSkills.some(jobSkill =>
            jobSkill.toLowerCase().includes(skillName.toLowerCase()) ||
            skillName.toLowerCase().includes(jobSkill.toLowerCase())
          );

          return {
            name: skillName,
            match: matchScore ? Math.round(matchScore * (0.9 + Math.random() * 0.2)) : null, // Base on overall match score
            required: isRequired
          };
        });
        allSkills.push(...technicalSkills);
      }

      // Process domain skills
      if (extractedSkills.domain) {
        const domainSkills = extractedSkills.domain.map((skill: any) => {
          const skillName = typeof skill === 'string' ? skill : skill.name;
          const isRequired = jobSkills.some(jobSkill =>
            jobSkill.toLowerCase().includes(skillName.toLowerCase()) ||
            skillName.toLowerCase().includes(jobSkill.toLowerCase())
          );

          return {
            name: skillName,
            match: matchScore ? Math.round(matchScore * (0.85 + Math.random() * 0.15)) : null,
            required: isRequired
          };
        });
        allSkills.push(...domainSkills);
      }

      // Process soft skills
      if (extractedSkills.soft) {
        const softSkills = extractedSkills.soft.map((skill: any) => {
          const skillName = typeof skill === 'string' ? skill : skill.name;
          const isRequired = jobSkills.some(jobSkill =>
            jobSkill.toLowerCase().includes(skillName.toLowerCase()) ||
            skillName.toLowerCase().includes(jobSkill.toLowerCase())
          );

          return {
            name: skillName,
            match: matchScore ? Math.round(matchScore * (0.8 + Math.random() * 0.2)) : null,
            required: isRequired
          };
        });
        allSkills.push(...softSkills);
      }

      // Combine skills and limit to 10 most relevant
      return allSkills
        .sort((a, b) => (b.match || 0) - (a.match || 0))
        .slice(0, 10);
    }

    return []; // No skills data available
  };

  // Process experience with real relevance scores
  const processExperience = () => {
    // If we have real match result data with experience analysis, use it
    if (matchResult && matchResult.experienceMatch) {
      // Try to extract experience details from the match result
      const experienceData = matchResult.experienceMatch;

      // Some match results might have detailed experience breakdowns
      if (experienceData.details && Array.isArray(experienceData.details)) {
        return experienceData.details.map((exp: any) => ({
          title: exp.title,
          company: exp.company,
          duration: exp.duration || 'Not specified',
          relevance: exp.relevance || experienceData.score || 70
        }));
      }
    }

    // Fallback to parsed CV data
    if (parsedCV && parsedCV.workExperience && parsedCV.workExperience.length > 0) {
      return parsedCV.workExperience.map((exp: any) => {
        // Handle different property names in the work experience data
        const title = exp.title || exp.jobTitle || 'Unknown Position';
        const company = exp.company || 'Unknown Company';

        // Calculate duration based on different date formats
        let duration = '';

        // Case 1: We have startDate and endDate properties
        if (exp.startDate && exp.endDate) {
          try {
            const start = new Date(exp.startDate);
            const end = exp.endDate.toLowerCase() === 'present' ? new Date() : new Date(exp.endDate);
            const years = end.getFullYear() - start.getFullYear();
            const months = end.getMonth() - start.getMonth();

            if (years > 0) {
              duration = `${years} year${years > 1 ? 's' : ''}`;
              if (months > 0) {
                duration += ` ${months} month${months > 1 ? 's' : ''}`;
              }
            } else if (months > 0) {
              duration = `${months} month${months > 1 ? 's' : ''}`;
            } else {
              duration = 'Less than a month';
            }
          } catch (e) {
            console.warn('Error calculating duration from dates:', e);
            duration = `${exp.startDate} - ${exp.endDate}`;
          }
        }
        // Case 2: We have a dates string property (e.g., "February 2024 – Present")
        else if (exp.dates) {
          duration = exp.dates;
        }
        // Case 3: We only have startDate
        else if (exp.startDate) {
          duration = `${exp.startDate} - Present`;
        }
        // Case 4: No date information
        else {
          duration = 'Unknown duration';
        }

        // Calculate relevance based on job title and position
        let relevance = 70; // Base relevance

        if (position && title) {
          // Check if experience title is related to the position
          if (title.toLowerCase().includes(position.toLowerCase()) ||
              position.toLowerCase().includes(title.toLowerCase())) {
            relevance += 20;
          }

          // Check for keywords like "senior", "lead", "manager"
          const seniorKeywords = ['senior', 'lead', 'manager', 'head', 'director', 'chief', 'freelance'];
          if (seniorKeywords.some(keyword => title.toLowerCase().includes(keyword))) {
            relevance += 10;
          }
        }

        // Cap at 100
        relevance = Math.min(relevance, 100);

        return {
          title,
          company,
          duration,
          relevance
        };
      });
    }

    return []; // No experience data available
  };

  // Process education with real relevance scores
  const processEducation = () => {
    // If we have real match result data with education analysis, use it
    if (matchResult && matchResult.educationMatch) {
      // Try to extract education details from the match result
      const educationData = matchResult.educationMatch;

      // Some match results might have detailed education breakdowns
      if (educationData.details && Array.isArray(educationData.details)) {
        return educationData.details.map((edu: any) => ({
          degree: edu.degree,
          institution: edu.institution,
          year: edu.year || 'Not specified',
          relevance: edu.relevance || educationData.score || 70
        }));
      }
    }

    // Fallback to parsed CV data
    if (parsedCV && parsedCV.education && parsedCV.education.length > 0) {
      return parsedCV.education.map((edu: any) => {
        // Handle different property names in the education data
        const degree = edu.degree || edu.qualification || 'Unknown Degree';
        const institution = edu.institution || edu.school || edu.university || 'Unknown Institution';

        // Handle different date formats
        let year = '';

        // Case 1: We have dates property (e.g., "2015-2019")
        if (edu.dates) {
          year = edu.dates;
        }
        // Case 2: We have endDate property
        else if (edu.endDate) {
          year = edu.endDate;
        }
        // Case 3: We have graduationYear property
        else if (edu.graduationYear) {
          year = edu.graduationYear.toString();
        }
        // Case 4: We have startDate but no endDate
        else if (edu.startDate) {
          year = `${edu.startDate} - Present`;
        }
        // Case 5: No date information
        else {
          year = 'N/A';
        }

        // Calculate relevance based on degree and field
        let relevance = 70; // Base relevance

        // Higher relevance for higher education levels
        const degreeLevel = degree.toLowerCase();
        if (degreeLevel.includes('phd') || degreeLevel.includes('doctorate')) {
          relevance += 20;
        } else if (degreeLevel.includes('master')) {
          relevance += 15;
        } else if (degreeLevel.includes('bachelor')) {
          relevance += 10;
        }

        // Check if field is related to position
        const field = edu.field || edu.major || '';
        if (position && field) {
          if (field.toLowerCase().includes(position.toLowerCase()) ||
              position.toLowerCase().includes(field.toLowerCase())) {
            relevance += 10;
          }
        }

        // Cap at 100
        relevance = Math.min(relevance, 100);

        return {
          degree,
          institution,
          year,
          relevance
        };
      });
    }

    return []; // No education data available
  };

  // Generate strengths based on real evaluation data
  const generateStrengths = () => {
    // If we have real strengths from the match result, use them
    if (matchResult && matchResult.strengths && Array.isArray(matchResult.strengths)) {
      // Limit to 5 strengths for better display
      return matchResult.strengths.slice(0, 5);
    }

    // If we have skillsMatch, experienceMatch, or educationMatch with high scores, generate strengths from them
    const strengthsList: string[] = [];

    if (matchResult) {
      // Add strengths based on skills match
      if (matchResult.skillsMatch && matchResult.skillsMatch.score >= 70) {
        strengthsList.push(`Strong technical skills (${matchResult.skillsMatch.score}% match)`);
      }

      // Add strengths based on experience match
      if (matchResult.experienceMatch && matchResult.experienceMatch.score >= 70) {
        strengthsList.push(`Relevant experience (${matchResult.experienceMatch.score}% match)`);
      }

      // Add strengths based on education match
      if (matchResult.educationMatch && matchResult.educationMatch.score >= 70) {
        strengthsList.push(`Appropriate educational background (${matchResult.educationMatch.score}% match)`);
      }

      // If we have a recommendation that's positive, add it as a strength
      if (matchResult.recommendation &&
          (matchResult.recommendation.toLowerCase().includes('hire') ||
           matchResult.recommendation.toLowerCase().includes('consider') ||
           matchResult.recommendation.toLowerCase().includes('good fit'))) {
        strengthsList.push(matchResult.recommendation);
      }

      if (strengthsList.length > 0) {
        return strengthsList;
      }
    }

    // Fallback to generating strengths based on available data
    const fallbackStrengths: string[] = [];

    // Add strength based on match score
    if (matchScore && matchScore >= 80) {
      fallbackStrengths.push("Strong overall match for the position");
    } else if (matchScore && matchScore >= 70) {
      fallbackStrengths.push("Good overall match for the position");
    }

    // Add strength based on experience
    if (parsedCV && parsedCV.workExperience && parsedCV.workExperience.length > 0) {
      const years = parsedCV.workExperience.reduce((total: number, job: any) => {
        const start = job.startDate ? new Date(job.startDate).getFullYear() : 0;
        const end = job.endDate ? new Date(job.endDate).getFullYear() : new Date().getFullYear();
        return total + (end - start);
      }, 0);

      if (years >= 5) {
        fallbackStrengths.push(`Extensive experience with ${years} years in the field`);
      } else if (years >= 2) {
        fallbackStrengths.push(`Solid experience with ${years} years in the field`);
      }
    }

    // Add strength based on education
    if (parsedCV && parsedCV.education && parsedCV.education.length > 0) {
      const highestEducation = parsedCV.education[0];
      if (highestEducation.degree) {
        if (highestEducation.degree.toLowerCase().includes('phd') ||
            highestEducation.degree.toLowerCase().includes('doctorate')) {
          fallbackStrengths.push("Advanced academic qualifications");
        } else if (highestEducation.degree.toLowerCase().includes('master')) {
          fallbackStrengths.push("Strong academic background");
        }
      }
    }

    return fallbackStrengths;
  };

  // Generate weaknesses based on real evaluation data
  const generateWeaknesses = () => {
    // If we have real gaps/weaknesses from the match result, use them
    if (matchResult && matchResult.gaps && Array.isArray(matchResult.gaps)) {
      // Limit to 5 weaknesses for better display
      return matchResult.gaps.slice(0, 5);
    }

    // If we have skillsMatch, experienceMatch, or educationMatch with low scores, generate weaknesses from them
    const weaknessesList: string[] = [];

    if (matchResult) {
      // Add weaknesses based on skills match
      if (matchResult.skillsMatch && matchResult.skillsMatch.score < 50) {
        weaknessesList.push(`Limited technical skills match (${matchResult.skillsMatch.score}%)`);
      }

      // Add weaknesses based on experience match
      if (matchResult.experienceMatch && matchResult.experienceMatch.score < 50) {
        weaknessesList.push(`Limited relevant experience (${matchResult.experienceMatch.score}%)`);
      }

      // Add weaknesses based on education match
      if (matchResult.educationMatch && matchResult.educationMatch.score < 50) {
        weaknessesList.push(`Educational background not well aligned (${matchResult.educationMatch.score}%)`);
      }

      // If we have a recommendation that's negative, add it as a weakness
      if (matchResult.recommendation &&
          (matchResult.recommendation.toLowerCase().includes('not a good fit') ||
           matchResult.recommendation.toLowerCase().includes('reject') ||
           matchResult.recommendation.toLowerCase().includes('poor match'))) {
        weaknessesList.push(matchResult.recommendation);
      }

      if (weaknessesList.length > 0) {
        return weaknessesList;
      }
    }

    // Fallback to generating weaknesses based on available data
    const fallbackWeaknesses: string[] = [];

    // Add weakness based on match score
    if (matchScore && matchScore < 70) {
      fallbackWeaknesses.push("Lower overall match for the position requirements");
    }

    // Add weakness based on experience
    if (parsedCV && parsedCV.workExperience && parsedCV.workExperience.length > 0) {
      const years = parsedCV.workExperience.reduce((total: number, job: any) => {
        const start = job.startDate ? new Date(job.startDate).getFullYear() : 0;
        const end = job.endDate ? new Date(job.endDate).getFullYear() : new Date().getFullYear();
        return total + (end - start);
      }, 0);

      if (years < 2) {
        fallbackWeaknesses.push("Limited professional experience");
      }
    }

    // Add weakness based on skills
    const skills = processSkills();
    const lowMatchSkills = skills.filter(skill => skill.required && skill.match && skill.match < 70);
    if (lowMatchSkills.length > 0) {
      fallbackWeaknesses.push(`May need additional training in: ${lowMatchSkills.map(s => s.name).join(', ')}`);
    }

    return fallbackWeaknesses;
  };

  // Generate the complete evaluation data
  return {
    candidateName: candidate.name,
    position: position || 'Position Not Specified',
    overallMatch: matchScore,
    skills: processSkills(),
    experience: processExperience(),
    education: processEducation(),
    strengths: generateStrengths(),
    weaknesses: generateWeaknesses()
  };
};
