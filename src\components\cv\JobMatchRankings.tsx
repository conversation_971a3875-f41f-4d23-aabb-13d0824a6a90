import React from 'react';
import { Star, Award, Briefcase, GraduationCap, MapPin } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { JobSpecificMatch } from '@/services/cv-processing/jobMatcher';

interface JobMatchRankingsProps {
  jobMatches: JobSpecificMatch[];
  onSelectJob?: (jobId: string) => void;
}

const JobMatchRankings: React.FC<JobMatchRankingsProps> = ({ jobMatches, onSelectJob }) => {
  // Sort jobs by overall score (descending)
  const sortedJobs = [...jobMatches].sort((a, b) => b.overallScore - a.overallScore);

  // Get color based on match score
  const getMatchColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 75) return 'text-blue-500';
    if (score >= 60) return 'text-amber-500';
    return 'text-red-500';
  };

  // Get progress bar color based on match score
  const getProgressColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 75) return 'bg-blue-500';
    if (score >= 60) return 'bg-amber-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-4">
      {sortedJobs.length > 0 ? (
        <>
          <p className="text-sm text-gray-500 mb-4">
            The candidate has been evaluated against {sortedJobs.length} job positions.
            Below are the match scores for each position.
          </p>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2 px-4 font-medium text-gray-600">Company</th>
                  <th className="text-left py-2 px-4 font-medium text-gray-600">Job Title</th>
                  <th className="text-left py-2 px-4 font-medium text-gray-600">Score</th>
                  <th className="text-left py-2 px-4 font-medium text-gray-600">Actions</th>
                </tr>
              </thead>
              <tbody>
                {sortedJobs.map((job, index) => (
                  <React.Fragment key={job.jobId}>
                    <tr className="border-b hover:bg-gray-100">
                      <td className="py-2 px-4">
                        {/* Company name would ideally come from job data */}
                        {index === 0 && <Badge className="mr-2 bg-green-500">Top Match</Badge>}
                      </td>
                      <td className="py-2 px-4">{job.jobTitle}</td>
                      <td className="py-2 px-4">
                        <div className="flex items-center">
                          <span className={`font-medium ${getMatchColor(job.overallScore)}`}>
                            {job.overallScore}%
                          </span>
                          <Star
                            className={`ml-1 h-4 w-4 ${getMatchColor(job.overallScore)}`}
                            fill="currentColor"
                          />
                        </div>
                      </td>
                      <td className="py-2 px-4">
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-xs"
                          onClick={() => {
                            const detailRow = document.getElementById(`job-details-ranking-${index}`);
                            if (detailRow) {
                              detailRow.classList.toggle('hidden');
                            }
                          }}
                        >
                          View Details
                        </Button>
                        {onSelectJob && (
                          <Button
                            variant="outline"
                            size="sm"
                            className="text-xs ml-2 border-gray-200 text-gray-700 hover:bg-gray-100"
                            onClick={() => onSelectJob(job.jobId)}
                          >
                            Full Match
                          </Button>
                        )}
                      </td>
                    </tr>
                    <tr id={`job-details-ranking-${index}`} className="hidden bg-gray-50">
                      <td colSpan={4} className="py-4 px-6">
                        <div className="grid grid-cols-4 gap-4">
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Skills</h4>
                            <div className="flex items-center">
                              <span className={`font-medium ${getMatchColor(job.skillsScore)}`}>
                                {job.skillsScore}%
                              </span>
                              <Progress
                                value={job.skillsScore}
                                className="h-1.5 w-16 ml-2"
                                indicatorClassName={getProgressColor(job.skillsScore)}
                              />
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Experience</h4>
                            <div className="flex items-center">
                              <span className={`font-medium ${getMatchColor(job.experienceScore)}`}>
                                {job.experienceScore}%
                              </span>
                              <Progress
                                value={job.experienceScore}
                                className="h-1.5 w-16 ml-2"
                                indicatorClassName={getProgressColor(job.experienceScore)}
                              />
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Education</h4>
                            <div className="flex items-center">
                              <span className={`font-medium ${getMatchColor(job.educationScore)}`}>
                                {job.educationScore}%
                              </span>
                              <Progress
                                value={job.educationScore}
                                className="h-1.5 w-16 ml-2"
                                indicatorClassName={getProgressColor(job.educationScore)}
                              />
                            </div>
                          </div>
                          <div>
                            <h4 className="text-sm font-medium text-gray-700 mb-2">Location</h4>
                            <div className="flex items-center">
                              <span className={`font-medium ${getMatchColor(job.locationScore)}`}>
                                {job.locationScore}%
                              </span>
                              <Progress
                                value={job.locationScore}
                                className="h-1.5 w-16 ml-2"
                                indicatorClassName={getProgressColor(job.locationScore)}
                              />
                            </div>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </React.Fragment>
                ))}
              </tbody>
            </table>
          </div>
        </>
      ) : (
        <div className="text-center py-6">
          <p className="text-gray-500">No job matches available</p>
        </div>
      )}
    </div>
  );
};

export default JobMatchRankings;
