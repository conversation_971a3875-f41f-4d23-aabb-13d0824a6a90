import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { MessageSquare } from 'lucide-react';

interface NotesTabProps {
  candidate: any;
  newNote: string;
  setNewNote: (note: string) => void;
  handleAddNote: () => void;
  isSubmittingNote: boolean;
}

// Format date
const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

const NotesTab: React.FC<NotesTabProps> = ({
  candidate,
  newNote,
  setNewNote,
  handleAddNote,
  isSubmittingNote
}) => {
  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader>
        <CardTitle className="text-gray-800 text-lg">Candidate Notes</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Add note form */}
        <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
          <h4 className="text-gray-800 font-medium mb-2">Add Note</h4>
          <Textarea
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            placeholder="Enter your note here..."
            className="bg-white border-gray-200 text-gray-800 mb-3"
            rows={3}
          />
          <Button
            className="w-full bg-recruiter-lightblue hover:bg-blue-500"
            onClick={handleAddNote}
            disabled={isSubmittingNote}
          >
            {isSubmittingNote ? 'Adding...' : 'Add Note'}
          </Button>
        </div>

        {/* Notes list */}
        {candidate.notes && candidate.notes.length > 0 ? (
          candidate.notes.map((note: any) => (
            <div key={note.id} className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex items-start gap-3 mb-2">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={note.authorAvatar} alt={note.author} />
                  <AvatarFallback>{note.author.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center">
                    <h4 className="text-gray-800 font-medium">{note.author}</h4>
                  </div>
                  <p className="text-gray-500 text-xs">{formatDate(note.date)}</p>
                </div>
              </div>
              <p className="text-gray-600 text-sm">{note.content}</p>
            </div>
          ))
        ) : (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800">No notes yet</h3>
            <p className="text-gray-500 mt-1">
              Add notes about this candidate to keep track of important information.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default NotesTab;
