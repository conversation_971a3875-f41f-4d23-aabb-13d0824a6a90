// Format date
export const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = { year: 'numeric', month: 'long', day: 'numeric' };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

// Generate notes based on candidate data
export const generateCandidateNotes = (candidate: any, parsedCV: any) => {
  const notes = [];
  const createdDate = new Date(candidate.created_at);

  // Get user's name from localStorage or use default
  let currentUser = 'Recruiter';
  try {
    const userDataStr = localStorage.getItem('userData');
    if (userDataStr) {
      const userData = JSON.parse(userDataStr);
      currentUser = userData.full_name || userData.email || 'Recruiter';
    }
  } catch (e) {
    console.error('Error getting user data from localStorage:', e);
  }

  // Initial CV Review Note
  const initialReviewDate = new Date(createdDate);
  initialReviewDate.setDate(initialReviewDate.getDate() + 1);

  let initialNoteContent = `CV review complete. `;

  if (candidate.evaluation_score) {
    if (candidate.evaluation_score >= 80) {
      initialNoteContent += `Excellent match with a score of ${candidate.evaluation_score}%. `;
    } else if (candidate.evaluation_score >= 60) {
      initialNoteContent += `Good match with a score of ${candidate.evaluation_score}%. `;
    } else {
      initialNoteContent += `Potential match with a score of ${candidate.evaluation_score}%. `;
    }
  }

  // Add CV submission date and time
  const cvAddedDate = new Date(candidate.created_at);
  const formattedDateTime = cvAddedDate.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
  initialNoteContent += `CV added on ${formattedDateTime}. `;

  if (parsedCV) {

    // Extract skills from different possible structures
    let skills: string[] = [];

    // Check if skills is an array of strings
    if (Array.isArray(parsedCV.skills)) {
      skills = parsedCV.skills.slice(0, 3);
    }
    // Check if skills has a technical property that's an array
    else if (parsedCV.skills && Array.isArray(parsedCV.skills.technical)) {
      skills = parsedCV.skills.technical.slice(0, 3).map((skill: any) =>
        typeof skill === 'string' ? skill : skill.name
      );
    }

    if (skills.length > 0) {
      initialNoteContent += `Key skills include ${skills.join(', ')}.`;
    }
  }

  notes.push({
    id: '1',
    date: initialReviewDate.toISOString(),
    author: currentUser,
    authorAvatar: 'https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png?ssl=1',
    content: initialNoteContent,
    status: 'new'
  });

  // Screening Call Note (if status is 'screening' or beyond)
  if (['screening', 'interview', 'offer', 'hired'].includes(candidate.status)) {
    const screeningDate = new Date(createdDate);
    screeningDate.setDate(screeningDate.getDate() + 3);

    notes.push({
      id: '2',
      date: screeningDate.toISOString(),
      author: currentUser,
      authorAvatar: `https://i.pravatar.cc/150?u=${candidate.id}_2`,
      content: `Initial screening call completed. ${candidate.name} demonstrated good communication skills and technical knowledge. Candidate is interested in the position and available to start within 2 weeks.`,
      status: 'screening'
    });
  }

  // Interview Note (if status is 'interview' or beyond)
  if (['interview', 'offer', 'hired'].includes(candidate.status)) {
    const interviewDate = new Date(createdDate);
    interviewDate.setDate(interviewDate.getDate() + 7);

    notes.push({
      id: '3',
      date: interviewDate.toISOString(),
      author: 'Technical Manager',
      authorAvatar: `https://i.pravatar.cc/150?u=${candidate.id}_3`,
      content: `Technical interview completed. Candidate performed well on coding exercises and system design questions. Demonstrated strong problem-solving skills and good understanding of software architecture principles.`,
      status: 'interview'
    });
  }

  // Offer Note (if status is 'offer' or 'hired')
  if (['offer', 'hired'].includes(candidate.status)) {
    const offerDate = new Date(createdDate);
    offerDate.setDate(offerDate.getDate() + 12);

    notes.push({
      id: '4',
      date: offerDate.toISOString(),
      author: 'HR Manager',
      authorAvatar: `https://i.pravatar.cc/150?u=${candidate.id}_4`,
      content: `Offer extended to ${candidate.name}. Compensation package includes competitive salary, benefits, and flexible work arrangements. Awaiting candidate's response.`,
      status: 'offer'
    });
  }

  // Hired Note (if status is 'hired')
  if (candidate.status === 'hired') {
    const hiredDate = new Date(createdDate);
    hiredDate.setDate(hiredDate.getDate() + 17);

    notes.push({
      id: '5',
      date: hiredDate.toISOString(),
      author: 'HR Manager',
      authorAvatar: `https://i.pravatar.cc/150?u=${candidate.id}_5`,
      content: `${candidate.name} has accepted our offer! Start date confirmed for ${formatDate(new Date(hiredDate.getTime() + 14 * 24 * 60 * 60 * 1000).toISOString())}. Onboarding process initiated.`,
      status: 'hired'
    });
  }

  // Rejected Note (if status is 'rejected')
  if (candidate.status === 'rejected') {
    // Use a date in the past based on the candidate's creation date
    const rejectedDate = new Date(createdDate);
    rejectedDate.setDate(rejectedDate.getDate() + 8);

    // Make sure the date is not in the future
    const currentDate = new Date();
    if (rejectedDate > currentDate) {
      rejectedDate.setTime(currentDate.getTime() - (24 * 60 * 60 * 1000)); // Set to yesterday
    }

    notes.push({
      id: '6',
      date: rejectedDate.toISOString(),
      author: currentUser,
      authorAvatar: `https://i.pravatar.cc/150?u=${candidate.id}_6`,
      content: `After careful consideration, we've decided not to move forward with ${candidate.name}'s application. The candidate lacks some of the key skills required for this position. Rejection email has been sent.`,
      status: 'rejected'
    });
  }

  // Sort notes by date (newest first)
  return notes.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
};

// Helper function to extract skill names from analysis text
export const extractSkillNamesFromText = (text: string): string[] => {
  if (!text) return [];

  // Common skill keywords that might appear in analysis text
  const skillKeywords = [
    'experience', 'knowledge', 'proficiency', 'expertise', 'skills',
    'familiar with', 'background in', 'understanding of'
  ];

  // Try to extract skills from sentences containing skill keywords
  const skills: string[] = [];

  // Split by sentence
  const sentences = text.split(/[.!?]/).filter(s => s.trim().length > 0);

  // First, try to extract skills using specific patterns
  const specificSkillPatterns = [
    /such as ([^,\.]+(?:, [^,\.]+)*(?:, and [^,\.]+)?)/i,
    /skills (?:in|with) ([^,\.]+(?:, [^,\.]+)*(?:, and [^,\.]+)?)/i,
    /experience (?:in|with) ([^,\.]+(?:, [^,\.]+)*(?:, and [^,\.]+)?)/i
  ];

  for (const pattern of specificSkillPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      // Split the matched skills by commas and 'and'
      const extractedSkills = match[1].split(/,\s*|\s+and\s+/).map(s => s.trim());
      skills.push(...extractedSkills);
    }
  }

  // If no skills found with specific patterns, try the general approach
  if (skills.length === 0) {
    for (const sentence of sentences) {
      // Check if sentence contains skill keywords
      if (skillKeywords.some(keyword => sentence.toLowerCase().includes(keyword))) {
        // Extract potential skill names (capitalized words or technical terms)
        const words = sentence.match(/\b([A-Z][a-z]+|[A-Z]+|[a-z]+\+\+|[a-z]#|[a-z]+\.js)\b/g) || [];

        // Add words that are likely skills (exclude common words)
        const commonWords = ['the', 'and', 'but', 'or', 'in', 'on', 'at', 'to', 'for', 'with', 'by'];
        words.forEach(word => {
          if (!commonWords.includes(word.toLowerCase()) && word.length > 2) {
            skills.push(word);
          }
        });
      }
    }
  }

  // If no skills found with the above method, try a simpler approach
  if (skills.length === 0) {
    // Look for technical terms that are likely skills
    const techTerms = text.match(/\b(JavaScript|Python|Java|C\+\+|React|Angular|Vue|Node\.js|SQL|AWS|Azure|DevOps|UI\/UX|HTML|CSS|PHP|Laravel|Figma|Git|HTML5|CSS3|Digital Marketing)\b/g);
    if (techTerms) {
      skills.push(...techTerms);
    }
  }

  // Remove duplicates and return
  return [...new Set(skills)];
};

// Helper function to extract skill name from strength text
export const extractSkillFromStrength = (strength: string): string => {
  if (!strength) return 'Unspecified Skill';

  // If strength is short, use it directly
  if (strength.length < 30) return strength;

  // Try to extract the main skill from the strength text
  const skillPhrases = [
    'Strong in', 'Expertise in', 'Proficient in', 'Skilled in',
    'Experience with', 'Knowledge of', 'Background in'
  ];

  for (const phrase of skillPhrases) {
    if (strength.includes(phrase)) {
      const afterPhrase = strength.split(phrase)[1];
      if (afterPhrase) {
        // Take the first few words after the phrase
        const words = afterPhrase.trim().split(' ').slice(0, 3).join(' ');
        return words;
      }
    }
  }

  // If no specific phrase found, take the first part of the strength
  return strength.split(',')[0].trim();
};

// Status badge color mapping
export const getStatusColor = (status: string): string => {
  switch (status?.toLowerCase()) {
    case 'new':
      return 'bg-blue-500/20 text-blue-500 hover:bg-blue-500/30';
    case 'screening':
      return 'bg-amber-500/20 text-amber-500 hover:bg-amber-500/30';
    case 'interview':
      return 'bg-purple-500/20 text-purple-500 hover:bg-purple-500/30';
    case 'offer':
      return 'bg-green-500/20 text-green-500 hover:bg-green-500/30';
    case 'hired':
      return 'bg-green-700/20 text-green-700 hover:bg-green-700/30';
    case 'rejected':
      return 'bg-red-500/20 text-red-500 hover:bg-red-500/30';
    case 'scheduled':
      return 'bg-blue-500/20 text-blue-500 hover:bg-blue-500/30';
    case 'completed':
      return 'bg-green-500/20 text-green-500 hover:bg-green-500/30';
    case 'cancelled':
      return 'bg-red-500/20 text-red-500 hover:bg-red-500/30';
    default:
      return 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30';
  }
};

// Progress color mapping
export const getProgressColor = (score: number): string => {
  if (score >= 90) return '#10b981'; // green-500
  if (score >= 75) return '#3b82f6'; // blue-500
  if (score >= 60) return '#f59e0b'; // amber-500
  return '#ef4444'; // red-500
};

// Get match score color
export const getMatchScoreColor = (score: number): string => {
  if (score >= 90) return 'text-green-500';
  if (score >= 75) return 'text-blue-500';
  if (score >= 60) return 'text-amber-500';
  return 'text-red-500';
};
