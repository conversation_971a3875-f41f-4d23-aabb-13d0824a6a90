import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FileText, Code, Zap, Database, Sliders, ChevronRight, X, Check, AlertCircle, Search } from 'lucide-react';

// Feature Card Component
interface FeatureCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  color: string;
  isExpanded: boolean;
  onClick: () => void;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  title,
  description,
  icon,
  color,
  isExpanded,
  onClick
}) => {
  const colorClasses = {
    blue: 'bg-blue-500/20 text-blue-400 border-blue-500/30',
    purple: 'bg-purple-500/20 text-purple-400 border-purple-500/30',
    green: 'bg-green-500/20 text-green-400 border-green-500/30',
    amber: 'bg-amber-500/20 text-amber-400 border-amber-500/30'
  };

  return (
    <motion.div
      className={`rounded-lg border border-gray-700 hover:border-gray-600 cursor-pointer transition-colors ${isExpanded ? 'col-span-2 row-span-2' : ''}`}
      layout
      onClick={onClick}
    >
      <div className="p-5">
        <div className={`w-10 h-10 rounded-lg ${colorClasses[color as keyof typeof colorClasses]} flex items-center justify-center mb-3`}>
          {icon}
        </div>
        <h3 className="text-lg font-medium text-white mb-2">{title}</h3>
        <p className="text-gray-400 text-sm">{description}</p>
      </div>
    </motion.div>
  );
};

// CV Parsing Demo Component
const CVParsingDemo: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const [confidenceThreshold, setConfidenceThreshold] = useState(70);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);

  const cvSections = [
    { id: 'name', label: 'Name', content: 'John Smith', confidence: 98 },
    { id: 'position', label: 'Position', content: 'Senior Software Developer', confidence: 95 },
    { id: 'skills', label: 'Skills', content: 'React, TypeScript, Node.js, AWS, GraphQL', confidence: 88 },
    { id: 'experience', label: 'Experience', content: '8 years', confidence: 75 },
    { id: 'education', label: 'Education', content: 'MSc Computer Science', confidence: 92 },
    { id: 'languages', label: 'Languages', content: 'English (Native), Spanish (Intermediate)', confidence: 65 }
  ];

  const filteredSections = cvSections.filter(section => section.confidence >= confidenceThreshold);

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-6"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-gray-900 rounded-xl overflow-hidden max-w-4xl w-full max-h-[80vh] shadow-2xl"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-6 border-b border-gray-800">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-lg bg-blue-500/20 flex items-center justify-center mr-3">
              <FileText size={20} className="text-blue-400" />
            </div>
            <h2 className="text-xl font-bold text-white">CV Parsing</h2>
          </div>
          <button
            className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center hover:bg-gray-700"
            onClick={onClose}
          >
            <X size={18} className="text-gray-400" />
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-0 h-[calc(80vh-76px)]">
          <div className="bg-gray-900 p-6 overflow-y-auto border-r border-gray-800">
            <h3 className="text-white font-medium mb-4">Original CV</h3>
            <div className="bg-gray-800 rounded-lg p-4 text-gray-300 font-mono text-sm">
              <p className="mb-4 text-lg font-bold">John Smith</p>
              <p className="mb-4 text-gray-400">Senior Software Developer</p>

              <p className="mb-2 text-blue-400 font-medium">PROFESSIONAL SUMMARY</p>
              <p className="mb-4">Experienced software developer with 8 years of experience in React and TypeScript development. Proficient in building scalable web applications using Node.js and GraphQL. Experience with cloud deployment on AWS.</p>

              <p className="mb-2 text-blue-400 font-medium">EXPERIENCE</p>
              <p className="font-medium">Senior Developer - TechCorp Inc.</p>
              <p className="text-gray-400 mb-1">2018 - Present</p>
              <ul className="list-disc list-inside mb-4 space-y-1">
                <li>Led development of customer-facing web applications using React and TypeScript</li>
                <li>Implemented GraphQL APIs for improved data fetching</li>
                <li>Deployed and maintained applications on AWS</li>
              </ul>

              <p className="font-medium">Developer - WebSolutions Ltd.</p>
              <p className="text-gray-400 mb-1">2015 - 2018</p>
              <ul className="list-disc list-inside mb-4 space-y-1">
                <li>Developed backend services using Node.js</li>
                <li>Created frontend components with React</li>
              </ul>

              <p className="mb-2 text-blue-400 font-medium">EDUCATION</p>
              <p className="font-medium">MSc Computer Science</p>
              <p className="text-gray-400 mb-4">University of Technology, 2015</p>

              <p className="mb-2 text-blue-400 font-medium">LANGUAGES</p>
              <p className="mb-4">English (Native), Spanish (Intermediate)</p>
            </div>
          </div>

          <div className="bg-gray-900 p-6 overflow-y-auto">
            <h3 className="text-white font-medium mb-4">AI Extraction</h3>

            <div className="mb-6">
              <label className="text-gray-300 text-sm mb-2 block">Confidence Threshold: {confidenceThreshold}%</label>
              <input
                type="range"
                min="0"
                max="100"
                value={confidenceThreshold}
                onChange={(e) => setConfidenceThreshold(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>0%</span>
                <span>50%</span>
                <span>100%</span>
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-4 mb-6">
              <div className="flex justify-between items-center mb-4">
                <h4 className="text-white font-medium">Extracted Data</h4>
                <div className="text-xs text-gray-400">
                  {filteredSections.length} of {cvSections.length} fields extracted
                </div>
              </div>

              <div className="space-y-3">
                {filteredSections.map(section => (
                  <div
                    key={section.id}
                    className={`p-3 rounded-lg cursor-pointer ${
                      selectedSection === section.id
                        ? 'bg-blue-900/30 border border-blue-500/30'
                        : 'bg-gray-700 hover:bg-gray-600'
                    }`}
                    onClick={() => setSelectedSection(section.id)}
                  >
                    <div className="flex justify-between items-center mb-1">
                      <div className="text-gray-300 font-medium">{section.label}</div>
                      <div className={`text-xs px-2 py-1 rounded ${
                        section.confidence >= 90
                          ? 'bg-green-500/20 text-green-400'
                          : section.confidence >= 70
                            ? 'bg-blue-500/20 text-blue-400'
                            : 'bg-amber-500/20 text-amber-400'
                      }`}>
                        {section.confidence}% confidence
                      </div>
                    </div>
                    <div className="text-white">{section.content}</div>
                  </div>
                ))}

                {cvSections.filter(section => section.confidence < confidenceThreshold).length > 0 && (
                  <div className="p-3 rounded-lg bg-gray-700 border border-amber-500/30">
                    <div className="flex items-center text-amber-400 mb-1">
                      <AlertCircle size={16} className="mr-2" />
                      <span className="font-medium">Low Confidence Fields</span>
                    </div>
                    <div className="text-gray-400 text-sm">
                      {cvSections.filter(section => section.confidence < confidenceThreshold).length} fields below threshold
                    </div>
                  </div>
                )}
              </div>
            </div>

            <div className="bg-gray-800 rounded-lg p-4">
              <h4 className="text-white font-medium mb-3">AI Processing</h4>
              <div className="bg-gray-900 rounded p-3 font-mono text-xs text-gray-400 h-32 overflow-y-auto">
                <p><span className="text-green-400">INFO</span> [Parser] Starting CV analysis...</p>
                <p><span className="text-blue-400">DATA</span> [Parser] Identified document structure</p>
                <p><span className="text-blue-400">DATA</span> [Parser] Extracting personal information</p>
                <p><span className="text-green-400">INFO</span> [Parser] Name extracted with 98% confidence</p>
                <p><span className="text-green-400">INFO</span> [Parser] Position extracted with 95% confidence</p>
                <p><span className="text-blue-400">DATA</span> [Parser] Analyzing skills section</p>
                <p><span className="text-green-400">INFO</span> [Parser] 5 skills identified with 88% confidence</p>
                <p><span className="text-blue-400">DATA</span> [Parser] Processing experience section</p>
                <p><span className="text-amber-400">WARN</span> [Parser] Experience duration extracted with 75% confidence</p>
                <p><span className="text-blue-400">DATA</span> [Parser] Analyzing education history</p>
                <p><span className="text-green-400">INFO</span> [Parser] Education extracted with 92% confidence</p>
                <p><span className="text-blue-400">DATA</span> [Parser] Processing languages section</p>
                <p><span className="text-amber-400">WARN</span> [Parser] Languages extracted with 65% confidence</p>
                <p><span className="text-green-400">INFO</span> [Parser] CV analysis complete</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

// Skills Matching Demo Component
const SkillsMatchingDemo: React.FC<{ onClose: () => void }> = ({ onClose }) => {
  const [matchThreshold, setMatchThreshold] = useState(60);

  const skills = [
    { name: 'React', level: 'Expert', candidate: 95, job: 90, match: 95 },
    { name: 'TypeScript', level: 'Advanced', candidate: 90, job: 85, match: 94 },
    { name: 'Node.js', level: 'Intermediate', candidate: 75, job: 80, match: 85 },
    { name: 'AWS', level: 'Beginner', candidate: 60, job: 85, match: 70 },
    { name: 'GraphQL', level: 'Intermediate', candidate: 80, job: 70, match: 87 },
    { name: 'Docker', level: 'Beginner', candidate: 50, job: 80, match: 62 },
    { name: 'Kubernetes', level: 'None', candidate: 20, job: 70, match: 28 },
    { name: 'CI/CD', level: 'Intermediate', candidate: 70, job: 75, match: 80 },
  ];

  const filteredSkills = skills.filter(skill => skill.match >= matchThreshold);
  const overallMatch = Math.round(
    filteredSkills.reduce((sum, skill) => sum + skill.match, 0) /
    (filteredSkills.length || 1)
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-6"
      onClick={onClose}
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        className="bg-gray-900 rounded-xl overflow-hidden max-w-4xl w-full max-h-[80vh] shadow-2xl"
        onClick={e => e.stopPropagation()}
      >
        <div className="flex justify-between items-center p-6 border-b border-gray-800">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-lg bg-purple-500/20 flex items-center justify-center mr-3">
              <Zap size={20} className="text-purple-400" />
            </div>
            <h2 className="text-xl font-bold text-white">Skills Matching</h2>
          </div>
          <button
            className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center hover:bg-gray-700"
            onClick={onClose}
          >
            <X size={18} className="text-gray-400" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto h-[calc(80vh-76px)]">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
                  <span className="text-blue-400 font-medium">JS</span>
                </div>
                <div>
                  <h3 className="text-white font-medium">John Smith</h3>
                  <p className="text-gray-400 text-sm">Senior Developer</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Experience</span>
                  <span className="text-white">8 years</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Education</span>
                  <span className="text-white">MSc Computer Science</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 rounded-full bg-purple-500/20 flex items-center justify-center mr-3">
                  <span className="text-purple-400 font-medium">TC</span>
                </div>
                <div>
                  <h3 className="text-white font-medium">TechCorp Inc.</h3>
                  <p className="text-gray-400 text-sm">Frontend Lead</p>
                </div>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Required Experience</span>
                  <span className="text-white">5+ years</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-400">Required Education</span>
                  <span className="text-white">Bachelor's or higher</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 p-4 rounded-lg">
              <div className="text-center mb-3">
                <div className="text-3xl font-bold text-white mb-1">{overallMatch}%</div>
                <p className="text-gray-400 text-sm">Overall Match Score</p>
              </div>
              <div className="w-full h-3 bg-gray-700 rounded-full overflow-hidden mb-3">
                <div
                  className={`h-full rounded-full ${
                    overallMatch >= 85
                      ? 'bg-green-500'
                      : overallMatch >= 70
                        ? 'bg-blue-500'
                        : 'bg-amber-500'
                  }`}
                  style={{ width: `${overallMatch}%` }}
                ></div>
              </div>
              <div className="flex justify-between text-xs">
                <span className={`px-2 py-1 rounded ${overallMatch >= 85 ? 'bg-green-500/20 text-green-400' : 'bg-gray-700 text-gray-400'}`}>
                  Strong Match
                </span>
                <span className={`px-2 py-1 rounded ${overallMatch >= 70 && overallMatch < 85 ? 'bg-blue-500/20 text-blue-400' : 'bg-gray-700 text-gray-400'}`}>
                  Good Match
                </span>
                <span className={`px-2 py-1 rounded ${overallMatch < 70 ? 'bg-amber-500/20 text-amber-400' : 'bg-gray-700 text-gray-400'}`}>
                  Partial Match
                </span>
              </div>
            </div>
          </div>

          <div className="mb-6">
            <label className="text-gray-300 text-sm mb-2 block">Match Threshold: {matchThreshold}%</label>
            <input
              type="range"
              min="0"
              max="100"
              value={matchThreshold}
              onChange={(e) => setMatchThreshold(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>

          <div className="bg-gray-800 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-white font-medium">Skills Analysis</h3>
              <div className="text-xs text-gray-400">
                {filteredSkills.length} of {skills.length} skills matched
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left border-b border-gray-700">
                    <th className="pb-2 text-gray-400 font-medium">Skill</th>
                    <th className="pb-2 text-gray-400 font-medium">Candidate Level</th>
                    <th className="pb-2 text-gray-400 font-medium">Job Requirement</th>
                    <th className="pb-2 text-gray-400 font-medium">Match Score</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredSkills.map(skill => (
                    <tr key={skill.name} className="border-b border-gray-700">
                      <td className="py-3 text-white">{skill.name}</td>
                      <td className="py-3">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-700 h-2 rounded-full mr-2">
                            <div className="bg-blue-500 h-2 rounded-full" style={{ width: `${skill.candidate}%` }}></div>
                          </div>
                          <span className="text-blue-400">{skill.candidate}%</span>
                        </div>
                      </td>
                      <td className="py-3">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-700 h-2 rounded-full mr-2">
                            <div className="bg-purple-500 h-2 rounded-full" style={{ width: `${skill.job}%` }}></div>
                          </div>
                          <span className="text-purple-400">{skill.job}%</span>
                        </div>
                      </td>
                      <td className="py-3">
                        <div className="flex items-center">
                          <div className="w-24 bg-gray-700 h-2 rounded-full mr-2">
                            <div
                              className={`h-2 rounded-full ${
                                skill.match >= 85
                                  ? 'bg-green-500'
                                  : skill.match >= 70
                                    ? 'bg-blue-500'
                                    : 'bg-amber-500'
                              }`}
                              style={{ width: `${skill.match}%` }}
                            ></div>
                          </div>
                          <span className={`
                            ${skill.match >= 85 ? 'text-green-400' : skill.match >= 70 ? 'text-blue-400' : 'text-amber-400'}
                          `}>{skill.match}%</span>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {skills.filter(skill => skill.match < matchThreshold).length > 0 && (
              <div className="mt-4 p-3 rounded-lg bg-gray-700 border border-amber-500/30">
                <div className="flex items-center text-amber-400 mb-1">
                  <AlertCircle size={16} className="mr-2" />
                  <span className="font-medium">Skills Below Threshold</span>
                </div>
                <div className="text-gray-400 text-sm">
                  {skills.filter(skill => skill.match < matchThreshold).map(skill => skill.name).join(', ')}
                </div>
              </div>
            )}
          </div>

          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-white font-medium mb-3">AI Insights</h3>
            <p className="text-gray-300 text-sm mb-3">
              This candidate is a strong match for the Frontend Lead position at TechCorp Inc. Their React and TypeScript skills are excellent matches for the job requirements.
            </p>
            <div className="space-y-2">
              <div className="flex items-start">
                <Check size={16} className="text-green-400 mr-2 mt-0.5" />
                <p className="text-gray-300 text-sm">Strong frontend development skills with React ecosystem</p>
              </div>
              <div className="flex items-start">
                <Check size={16} className="text-green-400 mr-2 mt-0.5" />
                <p className="text-gray-300 text-sm">Experience exceeds the minimum requirements by 3 years</p>
              </div>
              <div className="flex items-start">
                <AlertCircle size={16} className="text-amber-400 mr-2 mt-0.5" />
                <p className="text-gray-300 text-sm">Cloud deployment skills (AWS) could be stronger for this role</p>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

// Main Component
export const InteractiveFeaturePlayground: React.FC = () => {
  const [expandedFeature, setExpandedFeature] = useState<string | null>(null);

  const handleFeatureClick = (featureId: string) => {
    setExpandedFeature(featureId);
  };

  const handleCloseFeature = () => {
    setExpandedFeature(null);
  };

  return (
    <section className="py-16 bg-recruiter-darknavy">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Explore the AI Capabilities</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Try our powerful AI features and see how they can transform your recruitment process
          </p>
        </div>

        <div className="max-w-5xl mx-auto">
          <motion.div layout className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FeatureCard
              title="CV Parsing"
              description="See how our AI extracts structured data from CVs with high accuracy"
              icon={<FileText size={20} />}
              color="blue"
              isExpanded={false}
              onClick={() => handleFeatureClick('cv-parsing')}
            />

            <FeatureCard
              title="Skills Matching"
              description="Experience our intelligent skills matching algorithm in action"
              icon={<Zap size={20} />}
              color="purple"
              isExpanded={false}
              onClick={() => handleFeatureClick('skills-matching')}
            />

            <FeatureCard
              title="Semantic Search"
              description="Try our advanced semantic search to find the perfect candidates"
              icon={<Search size={20} />}
              color="green"
              isExpanded={false}
              onClick={() => handleFeatureClick('semantic-search')}
            />

            <FeatureCard
              title="Candidate Scoring"
              description="See how we calculate comprehensive candidate scores"
              icon={<Sliders size={20} />}
              color="amber"
              isExpanded={false}
              onClick={() => handleFeatureClick('candidate-scoring')}
            />
          </motion.div>
        </div>
      </div>

      <AnimatePresence>
        {expandedFeature === 'cv-parsing' && (
          <CVParsingDemo onClose={handleCloseFeature} />
        )}
        {expandedFeature === 'skills-matching' && (
          <SkillsMatchingDemo onClose={handleCloseFeature} />
        )}
      </AnimatePresence>
    </section>
  );
};
