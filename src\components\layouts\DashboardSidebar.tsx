import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { usePlanFeatures } from '@/hooks/use-plan-features';
import { Link, useLocation } from 'react-router-dom';
import { cn } from '@/lib/utils';
import {
  LayoutDashboard,
  Briefcase,
  FileText,
  Users,
  BarChart3,
  Settings,
  HelpCircle,
  Building,
  Shield,
  UserCog,
  CreditCard,
  Bell,
  CalendarClock,
  ClipboardCheck,
  BrainCircuit,
  CheckSquare
} from 'lucide-react';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Separator } from '@/components/ui/separator';
import { ColorfulIcon } from '@/components/ui/colorful-icon';
import { Badge } from '@/components/ui/badge';

interface SidebarItemProps {
  icon: React.ElementType;
  label: string;
  href: string;
  active?: boolean;
  collapsed?: boolean;
  color?: 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'danger' | 'purple';
  badge?: string;
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline';
}

const SidebarItem: React.FC<SidebarItemProps> = ({
  icon: Icon,
  label,
  href,
  active = false,
  collapsed = false,
  color = 'primary',
  badge,
  badgeVariant = 'default'
}) => {
  return (
    <TooltipProvider delayDuration={0}>
      <Tooltip>
        <TooltipTrigger asChild>
          <Link
            to={href}
            className={cn(
              "flex items-center gap-3 px-3 py-2 rounded-md",
              "font-medium transition-all duration-200",
              active
                ? "bg-primary-gradient text-white shadow-md"
                : "text-gray-700 hover:text-primary hover:bg-gray-100 hover:translate-x-1"
            )}
          >
            {active ? (
              <ColorfulIcon icon={Icon} color={color} size={18} />
            ) : (
              <Icon size={20} className="text-gray-500" />
            )}
            {!collapsed && (
              <div className="flex items-center gap-2">
                <span>{label}</span>
                {badge && (
                  <Badge
                    variant={badgeVariant}
                    className={badgeVariant === 'default' ? 'bg-purple-600 hover:bg-purple-700' : ''}
                  >
                    {badge}
                  </Badge>
                )}
              </div>
            )}
          </Link>
        </TooltipTrigger>
        {collapsed && (
          <TooltipContent side="right">
            <div className="flex items-center gap-2">
              <span>{label}</span>
              {badge && (
                <Badge
                  variant={badgeVariant}
                  className={badgeVariant === 'default' ? 'bg-purple-600 hover:bg-purple-700' : ''}
                >
                  {badge}
                </Badge>
              )}
            </div>
          </TooltipContent>
        )}
      </Tooltip>
    </TooltipProvider>
  );
};

interface SidebarSectionProps {
  title?: string;
  children: React.ReactNode;
  collapsed?: boolean;
}

const SidebarSection: React.FC<SidebarSectionProps> = ({ title, children, collapsed = false }) => {
  return (
    <div className="mb-6">
      {title && !collapsed && (
        <h3 className="px-3 mb-2 text-xs font-semibold text-gray-600 uppercase tracking-wider">
          {title}
        </h3>
      )}
      <div className="space-y-1">
        {children}
      </div>
    </div>
  );
};

interface DashboardSidebarProps {
  open: boolean;
}

export const DashboardSidebar: React.FC<DashboardSidebarProps> = ({ open }) => {
  const location = useLocation();
  const { user } = useAuth();
  const { hasRole } = usePermissions();
  const { hasFeature, currentPlan } = usePlanFeatures();
  const isActive = (path: string) => location.pathname === path;

  // Check if user has access to specific features
  const hasInterviewScheduling = hasFeature('INTERVIEW_SCHEDULING');
  const hasTeamManagement = hasFeature('TEAM_MANAGEMENT');
  const hasAdvancedAnalytics = hasFeature('ADVANCED_ANALYTICS');

  return (
    <aside
      className={cn(
        "fixed left-0 top-[73px] bottom-0 z-10 bg-sidebar-gradient border-r border-gray-200 shadow-md transition-all duration-300 ease-in-out overflow-y-auto overflow-x-hidden animate-slide-in-left",
        open ? "w-64" : "w-20",
        "hidden md:block"
      )}
    >
      <div className="p-4">
        <SidebarSection title="Main" collapsed={!open}>
          <SidebarItem
            icon={LayoutDashboard}
            label="Dashboard"
            href="/dashboard"
            active={isActive('/dashboard')}
            collapsed={!open}
            color="primary"
          />
          <SidebarItem
            icon={Briefcase}
            label="Jobs"
            href="/dashboard/jobs"
            active={isActive('/dashboard/jobs')}
            collapsed={!open}
            color="success"
          />
          <SidebarItem
            icon={FileText}
            label="Candidates"
            href="/dashboard/cvs"
            active={isActive('/dashboard/cvs')}
            collapsed={!open}
            color="info"
          />
        </SidebarSection>

        <Separator className="my-4 bg-gray-200" />

        <SidebarSection title="Organization" collapsed={!open}>
          <SidebarItem
            icon={Building}
            label="Companies"
            href="/dashboard/companies"
            active={isActive('/dashboard/companies') || location.pathname.startsWith('/dashboard/company/')}
            collapsed={!open}
            color="purple"
          />
          {/* Team Management - conditional based on plan */}
          {hasTeamManagement && (
            <SidebarItem
              icon={Users}
              label="Team"
              href="/dashboard/team"
              active={isActive('/dashboard/team')}
              collapsed={!open}
              color="info"
              badge={currentPlan === 'starter' ? 'Growth+' : undefined}
            />
          )}
          {/* Only show Roles link to admin and platform_admin users */}
          {hasRole(['admin', 'platform_admin']) && (
            <SidebarItem
              icon={Shield}
              label="Roles"
              href="/dashboard/roles"
              active={isActive('/dashboard/roles')}
              collapsed={!open}
              color="danger"
            />
          )}
        </SidebarSection>

        <Separator className="my-4 bg-gray-200" />

        <SidebarSection title="Account" collapsed={!open}>
          <SidebarItem
            icon={UserCog}
            label="Profile"
            href="/dashboard/profile"
            active={isActive('/dashboard/profile')}
            collapsed={!open}
            color="primary"
          />
          <SidebarItem
            icon={CreditCard}
            label="Billing & Subscription"
            href="/dashboard/billing"
            active={isActive('/dashboard/billing') || isActive('/dashboard/subscription')}
            collapsed={!open}
            color="success"
          />
          <SidebarItem
            icon={Bell}
            label="Notifications"
            href="/dashboard/notifications"
            active={isActive('/dashboard/notifications')}
            collapsed={!open}
            color="warning"
          />
     
        </SidebarSection>


        {/* Advanced Features - Show based on plan features */}
        {(hasInterviewScheduling || hasAdvancedAnalytics) && (
          <>
            <Separator className="my-4 bg-gray-200" />

            <SidebarSection title="Advanced Features" collapsed={!open}>
              {/* Interview Scheduling - conditional based on plan */}
              {hasInterviewScheduling && (
                <SidebarItem
                  icon={CalendarClock}
                  label="Interview Scheduling"
                  href="/dashboard/interviews"
                  active={isActive('/dashboard/interviews')}
                  collapsed={!open}
                  color="success"
                  badge={currentPlan === 'starter' ? 'Growth+' : undefined}
                />
              )}
              {/* Temporarily hidden - Assessment Tests */}
              {/* <SidebarItem
                icon={ClipboardCheck}
                label="Assessment Tests"
                href="/dashboard/assessments"
                active={isActive('/dashboard/assessments')}
                collapsed={!open}
                color="info"
                badge="Pro"
              /> */}
              {/* Temporarily hidden - AI Interview Prep */}
              {/* <SidebarItem
                icon={BrainCircuit}
                label="AI Interview Prep"
                href="/dashboard/interview-prep"
                active={isActive('/dashboard/interview-prep')}
                collapsed={!open}
                color="purple"
                badge="Pro"
              /> */}
              {/* Temporarily hidden - Reference Checks */}
              {/* <SidebarItem
                icon={CheckSquare}
                label="Reference Checks"
                href="/dashboard/references"
                active={isActive('/dashboard/references')}
                collapsed={!open}
                color="danger"
                badge="Pro"
              /> */}
            </SidebarSection>
          </>
        )}

        <Separator className="my-4 bg-gray-200" />

        <SidebarSection title="Insights" collapsed={!open}>
          <SidebarItem
            icon={BarChart3}
            label="Analytics"
            href="/dashboard/analytics"
            active={isActive('/dashboard/analytics')}
            collapsed={!open}
            color="warning"
          />
          <SidebarItem
            icon={HelpCircle}
            label="Help"
            href="/dashboard/help"
            active={isActive('/dashboard/help')}
            collapsed={!open}
            color="info"
          />
        </SidebarSection>
      </div>
    </aside>
  );
};
