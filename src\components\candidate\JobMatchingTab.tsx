import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Briefcase, MapPin, Star, Plus } from 'lucide-react';

interface JobMatchingTabProps {
  candidate: any;
  setIsEvaluateModalOpen: (isOpen: boolean) => void;
}

// Helper function to get color based on match score
const getMatchScoreColor = (score: number) => {
  if (score >= 90) return 'text-green-500';
  if (score >= 75) return 'text-blue-500';
  if (score >= 60) return 'text-amber-500';
  return 'text-red-500';
};

const JobMatchingTab: React.FC<JobMatchingTabProps> = ({
  candidate,
  setIsEvaluateModalOpen
}) => {
  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-gray-800 text-lg">Job Matching</CardTitle>
        <Button
          className="bg-recruiter-lightblue hover:bg-blue-500"
          onClick={() => setIsEvaluateModalOpen(true)}
        >
          <Plus className="mr-2 h-4 w-4" /> Match to Job
        </Button>
      </CardHeader>
      <CardContent>
        {!candidate.job_id ? (
          <div className="text-center py-8">
            <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800">No job matches yet</h3>
            <p className="text-gray-500 mt-1">
              Match this candidate to a job to evaluate their fit.
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                <div>
                  <div className="flex items-center">
                    <h3 className="text-gray-800 font-medium">{candidate.job_title || 'Job #' + candidate.job_id}</h3>
                    <Badge className="ml-2 bg-blue-500/20 text-blue-500 hover:bg-blue-500/30">
                      Current Match
                    </Badge>
                  </div>
                  <div className="flex items-center mt-2 text-gray-600 text-sm">
                    <Briefcase className="mr-1 h-4 w-4 text-gray-500" />
                    <span>Company: {candidate.company_name || 'Not specified'}</span>
                  </div>
                  <div className="flex items-center mt-1 text-gray-600 text-sm">
                    <MapPin className="mr-1 h-4 w-4 text-gray-500" />
                    <span>Location: {candidate.job_location || 'Not specified'}</span>
                  </div>
                </div>
                <div className="flex items-center">
                  {candidate.matchScore ? (
                    <div className="flex flex-col items-center">
                      <div className="flex items-center">
                        <span className={`text-xl font-bold ${getMatchScoreColor(candidate.matchScore)}`}>
                          {candidate.matchScore}%
                        </span>
                        <Star className={`h-4 w-4 ml-1 ${getMatchScoreColor(candidate.matchScore)}`} fill="currentColor" />
                      </div>
                      <span className="text-xs text-gray-500">Match Score</span>
                    </div>
                  ) : (
                    <span className="text-sm text-gray-500">Not Evaluated</span>
                  )}
                </div>
              </div>
            </div>

            <div className="mt-6">
              <h3 className="text-gray-800 font-medium mb-3">Match to Other Jobs</h3>
              <p className="text-gray-500 text-sm mb-4">
                Click the "Match to Job" button above to evaluate this candidate against other jobs.
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default JobMatchingTab;
