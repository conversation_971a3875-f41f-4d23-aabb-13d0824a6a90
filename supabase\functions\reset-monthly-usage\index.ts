// Follow this setup guide to integrate the Deno language server with your editor:
// https://deno.land/manual/getting_started/setup_your_environment
// This enables autocomplete, go to definition, etc.

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Create a Supabase client with the Auth context of the function
    const supabaseClient = createClient(
      // Supabase API URL - env var exported by default.
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exported by default.
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? '',
      // Create client with Auth context of the function
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    )

    // Check if this is a scheduled function call
    const isScheduled = req.headers.get('Authorization') === `Bearer ${Deno.env.get('FUNCTION_SECRET')}`
    
    if (!isScheduled) {
      // If not scheduled, check if user is an admin
      const { data: { user }, error: userError } = await supabaseClient.auth.getUser()
      
      if (userError) {
        return new Response(JSON.stringify({ error: 'Unauthorized' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 401,
        })
      }
      
      // Check if user is a platform admin
      const { data: profile, error: profileError } = await supabaseClient
        .from('profiles')
        .select('platform_admin')
        .eq('user_id', user.id)
        .single()
        
      if (profileError || !profile.platform_admin) {
        return new Response(JSON.stringify({ error: 'Unauthorized - Admin access required' }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 403,
        })
      }
    }
    
    // Call the reset_monthly_usage function
    const { data, error } = await supabaseClient.rpc('reset_monthly_usage')
    
    if (error) {
      throw error
    }
    
    return new Response(JSON.stringify({ success: true, message: 'Monthly usage reset successfully' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 400,
    })
  }
})

/* To invoke:
  1. Via scheduled cron job (set up in Supabase dashboard)
  2. Via curl:
     curl -i --location --request POST 'https://<project-ref>.functions.supabase.co/reset-monthly-usage' \
     --header 'Authorization: Bearer <service-role-key>' \
     --header 'Content-Type: application/json'
*/
