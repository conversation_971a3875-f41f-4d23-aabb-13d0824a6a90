import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useCompanyContext } from '@/contexts/CompanyContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import {
  Plus,
  Search,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Users,
  Clock,
  Calendar,
  Building,
  AlertCircle,
  Share2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { JobSocialShare } from "@/components/JobSocialShare";
import { createPortal } from 'react-dom';

import { getJobs } from '@/services/supabase/jobs';
import { getJobApplicantCounts } from '@/services/supabase/applications';
import { getCompanies, getCompany } from '@/services/supabase/companies';
import { useToast } from '@/hooks/use-toast';

// Define the job interface to match our data structure
interface Job {
  id: string;
  title: string;
  description: string;
  requirements: string;
  location: string;
  job_type: string;
  experience_level: string;
  status: string;
  created_at: string;
  company_id: string;
  user_id: string;
  department?: string; // Added for compatibility with mock data
  applicants?: number; // Will be calculated from candidates
  closingDate?: string; // Added for compatibility with mock data
}

// Fallback to mock data if API fails
const mockJobs = [
  {
    id: '1',
    title: 'Frontend Developer',
    department: 'Engineering',
    location: 'Remote',
    job_type: 'Full-time',
    applicants: 12,
    status: 'active',
    created_at: '2023-09-15T00:00:00Z',
    closingDate: '2023-10-15',
    description: '',
    requirements: '',
    experience_level: '',
    company_id: '',
    user_id: ''
  },
  {
    id: '2',
    title: 'UX Designer',
    department: 'Design',
    location: 'New York, NY',
    job_type: 'Full-time',
    applicants: 8,
    status: 'active',
    created_at: '2023-09-20T00:00:00Z',
    closingDate: '2023-10-20',
    description: '',
    requirements: '',
    experience_level: '',
    company_id: '',
    user_id: ''
  },
  {
    id: '3',
    title: 'Product Manager',
    department: 'Product',
    location: 'San Francisco, CA',
    job_type: 'Full-time',
    applicants: 15,
    status: 'closed',
    created_at: '2023-08-01T00:00:00Z',
    closingDate: '2023-09-01',
    description: '',
    requirements: '',
    experience_level: '',
    company_id: '',
    user_id: ''
  },
  {
    id: '4',
    title: 'DevOps Engineer',
    department: 'Engineering',
    location: 'Remote',
    job_type: 'Contract',
    applicants: 5,
    status: 'active',
    created_at: '2023-09-25T00:00:00Z',
    closingDate: '2023-10-25',
    description: '',
    requirements: '',
    experience_level: '',
    company_id: '',
    user_id: ''
  },
  {
    id: '5',
    title: 'Marketing Specialist',
    department: 'Marketing',
    location: 'London, UK',
    job_type: 'Part-time',
    applicants: 7,
    status: 'draft',
    created_at: '2023-09-28T00:00:00Z',
    closingDate: '2023-10-28',
    description: '',
    requirements: '',
    experience_level: '',
    company_id: '',
    user_id: ''
  },
];

const JobListings = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const { hasRole } = usePermissions();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [departmentFilter, setDepartmentFilter] = useState('all');
  const [jobs, setJobs] = useState<Job[]>([]);
  const [loading, setLoading] = useState(true);
  const [jobApplicants, setJobApplicants] = useState<Record<string, number>>({});
  const { activeCompany, activeCompanyId, showAllCompanies: showAllJobs, setShowAllCompanies: setShowAllJobs } = useCompanyContext();

  // Only admins should be able to see all jobs across companies
  const canViewAllJobs = hasRole(['admin', 'platform_admin']);

  // Add state for managing share modal
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [selectedJobForShare, setSelectedJobForShare] = useState<any>(null);

  // Add this useEffect to reset showAllJobs if user doesn't have permission
  useEffect(() => {
    if (!canViewAllJobs && showAllJobs) {
      setShowAllJobs(false);
    }
  }, [canViewAllJobs, showAllJobs, setShowAllJobs]);

  // Fetch jobs from Supabase
  useEffect(() => {
    const fetchJobsAndCandidates = async () => {
      try {
        setLoading(true);
        
        // Force to only show company jobs if user doesn't have permission
        const shouldShowAllJobs = showAllJobs && canViewAllJobs;
        
        // Debug logging
        console.log('Permission check:', { 
          showAllJobs, 
          canViewAllJobs, 
          shouldShowAllJobs, 
          activeCompanyId 
        });
        
        const jobsData = await getJobs(shouldShowAllJobs ? undefined : activeCompanyId || undefined);

        // Add department field based on job title for compatibility
        const enhancedJobs = jobsData.map(job => ({
          ...job,
          department: getDepartmentFromTitle(job.title)
        }));

        setJobs(enhancedJobs);

        // Fetch job applicant counts from applications table
        try {
          const jobIds = jobsData.map(job => job.id);
          const applicantCounts = await getJobApplicantCounts(jobIds);
          setJobApplicants(applicantCounts);
        } catch (error) {
          console.error('Error fetching job applicant counts:', error);
          const applicantCounts: Record<string, number> = {};
          jobsData.forEach(job => {
            applicantCounts[job.id] = 0;
          });
          setJobApplicants(applicantCounts);
        }
      } catch (error) {
        console.error('Error fetching jobs:', error);
        toast({
          title: 'Error',
          description: 'Failed to load jobs. Using mock data instead.',
          variant: 'destructive'
        });
        setJobs(mockJobs);
      } finally {
        setLoading(false);
      }
    };

    fetchJobsAndCandidates();
  }, [activeCompanyId, showAllJobs, canViewAllJobs, toast]);

  // Update page title based on active company
  useEffect(() => {
    document.title = activeCompany
      ? `Jobs - ${activeCompany.name} - Sourcio.ai`
      : 'Jobs - Sourcio.ai';
  }, [activeCompany]);

  // Helper function to extract department from job title
  const getDepartmentFromTitle = (title: string): string => {
    if (title.toLowerCase().includes('developer') || title.toLowerCase().includes('engineer')) {
      return 'Engineering';
    } else if (title.toLowerCase().includes('design')) {
      return 'Design';
    } else if (title.toLowerCase().includes('product')) {
      return 'Product';
    } else if (title.toLowerCase().includes('market')) {
      return 'Marketing';
    } else {
      return 'Other';
    }
  };

  // Filter jobs based on search query and filters
  const filteredJobs = jobs.filter(job => {
    const matchesSearch = job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (job.department?.toLowerCase() || '').includes(searchQuery.toLowerCase()) ||
                         job.location.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesStatus = statusFilter === 'all' || job.status.toLowerCase() === statusFilter.toLowerCase();
    const matchesDepartment = departmentFilter === 'all' ||
                             (job.department?.toLowerCase() || '').includes(departmentFilter.toLowerCase());

    return matchesSearch && matchesStatus && matchesDepartment;
  });

  // Get unique departments for filter
  const departments = ['all', ...new Set(jobs.map(job => (job.department || 'Other').toLowerCase()))];

  // Add this right before your return statement to debug
  console.log('Render - shareModalOpen:', shareModalOpen, 'selectedJobForShare:', selectedJobForShare);

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Job Listings</h1>
          {activeCompany && !showAllJobs && canViewAllJobs && !hasRole(['admin', 'platform_admin']) && (
            <div className="flex items-center mt-1 text-sm text-gray-600">
              <Building className="mr-1 h-4 w-4" />
              <span>Showing jobs for: <span className="font-medium">{activeCompany.name}</span></span>
              <Button
                variant="link"
                className="text-primary p-0 h-auto ml-2"
                onClick={() => setShowAllJobs(true)}
              >
                Show all jobs
              </Button>
            </div>
          )}
          {showAllJobs && canViewAllJobs && (
            <div className="flex items-center mt-1 text-sm text-gray-600">
              <Building className="mr-1 h-4 w-4" />
              <span>Showing jobs for all companies</span>
              {activeCompany && (
                <Button
                  variant="link"
                  className="text-primary p-0 h-auto ml-2"
                  onClick={() => setShowAllJobs(false)}
                >
                  Show only {activeCompany.name} jobs
                </Button>
              )}
            </div>
          )}
        </div>
        <Button
          className="bg-primary-gradient text-white hover:shadow-md transition-all"
          onClick={() => navigate('/dashboard/jobs/new')}
        >
          <Plus className="mr-2 h-4 w-4" /> Create New Job
        </Button>
      </div>

      {!activeCompanyId && (
        <Alert className="bg-amber-50 border-amber-200 text-amber-800">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No active company selected</AlertTitle>
          <AlertDescription>
            You haven't selected an active company. Jobs will be shown for all companies.
            <Button
              variant="link"
              className="text-amber-800 p-0 h-auto ml-2 underline"
              onClick={() => navigate('/dashboard/companies')}
            >
              Go to Company Management
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Filters and search */}
      <Card className="bg-white border-gray-200 shadow-sm">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
              <Input
                type="text"
                placeholder="Search jobs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-white border-gray-200 text-gray-800 w-full"
              />
            </div>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="w-full sm:w-40">
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200 text-gray-800">
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="closed">Closed</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="w-full sm:w-40">
                <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
                  <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                    <SelectValue placeholder="Department" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200 text-gray-800">
                    <SelectItem value="all">All Departments</SelectItem>
                    {departments.filter(d => d !== 'all').map((department) => (
                      <SelectItem key={department} value={department}>
                        {department.charAt(0).toUpperCase() + department.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <Button variant="outline" className="border-gray-200 text-gray-700 hover:bg-gray-100">
                <Filter className="mr-2 h-4 w-4" /> More Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Job listings */}
      <div className="space-y-4">
        {loading ? (
          <Card className="bg-white border-gray-200 p-8 text-center">
            <p className="text-gray-500">Loading jobs...</p>
          </Card>
        ) : filteredJobs.length === 0 ? (
          <Card className="bg-white border-gray-200 p-8 text-center">
            <p className="text-gray-500">No jobs found matching your criteria.</p>
          </Card>
        ) : (
          filteredJobs.map((job) => (
            <Card key={job.id} className="bg-white border-gray-200 hover:shadow-md transition-all duration-200 bg-card-hover">
              <CardContent className="p-6">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="flex items-center gap-3">
                          <h3 className="text-lg font-medium text-gray-800">{job.title}</h3>
                          <Badge
                            className={
                              job.status.toLowerCase() === 'active' ? 'bg-green-500/20 text-green-500 hover:bg-green-500/30' :
                              job.status.toLowerCase() === 'closed' ? 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30' :
                              'bg-amber-500/20 text-amber-500 hover:bg-amber-500/30'
                            }
                          >
                            {job.status}
                          </Badge>
                        </div>
                        <div className="flex flex-wrap items-center gap-2 mt-1 text-sm text-gray-600">
                          <span>{job.department || 'Other'}</span>
                          <span>•</span>
                          <span>{job.location}</span>
                          <span>•</span>
                          <span>{job.job_type}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-wrap gap-4 mt-4">
                      <div className="flex items-center text-sm text-gray-600">
                        <Users className="mr-1 h-4 w-4" />
                        <span>{jobApplicants[job.id] || 0} Applicants</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Calendar className="mr-1 h-4 w-4" />
                        <span>Posted: {new Date(job.created_at).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center text-sm text-gray-600">
                        <Clock className="mr-1 h-4 w-4" />
                        <span>Closing: {job.closingDate ? new Date(job.closingDate).toLocaleDateString() : 'Not set'}</span>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 self-end md:self-center">
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => navigate(`/dashboard/jobs/${job.id}`)}
                    >
                      <Eye className="mr-1 h-4 w-4" /> View
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => navigate(`/dashboard/jobs/edit/${job.id}`)}
                    >
                      <Edit className="mr-1 h-4 w-4" /> Edit
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" size="icon" className="border-gray-200 text-gray-700 hover:bg-gray-100">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end" className="bg-white border-gray-200 text-gray-800 shadow-lg rounded-lg animate-scale-in">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator className="bg-gray-200" />
                        <DropdownMenuItem
                          className="hover:bg-gray-100 cursor-pointer"
                          onClick={() => navigate(`/dashboard/jobs/${job.id}?tab=applicants`)}
                        >
                          <Users className="mr-2 h-4 w-4" /> View Applicants
                        </DropdownMenuItem>
                        <DropdownMenuItem className="hover:bg-gray-100 cursor-pointer">
                          {job.status.toLowerCase() === 'active' ? (
                            <>
                              <Eye className="mr-2 h-4 w-4" /> Mark as Closed
                            </>
                          ) : job.status.toLowerCase() === 'draft' ? (
                            <>
                              <Eye className="mr-2 h-4 w-4" /> Publish Job
                            </>
                          ) : (
                            <>
                              <Eye className="mr-2 h-4 w-4" /> Reopen Job
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-gray-200" />
                        <DropdownMenuItem 
                          className="hover:bg-gray-100 cursor-pointer"
                          onClick={(e) => {
                            e.preventDefault();
                            console.log('Share job clicked for:', job); // Debug log
                            setSelectedJobForShare(job);
                            setShareModalOpen(true);
                            console.log('Modal should be open now'); // Debug log
                          }}
                        >
                          <Share2 className="mr-2 h-4 w-4" /> Share Job
                        </DropdownMenuItem>
                        <DropdownMenuSeparator className="bg-gray-200" />
                        <DropdownMenuItem className="text-red-500 hover:bg-red-50 cursor-pointer">
                          <Trash2 className="mr-2 h-4 w-4" /> Delete Job
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Debug the modal condition */}
      {console.log('Modal condition check:', shareModalOpen && selectedJobForShare)}

      {/* Share Modal */}
      {shareModalOpen && selectedJobForShare && createPortal(
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
          onClick={() => setShareModalOpen(false)}
        >
          <div 
            className="bg-white rounded-lg p-6 max-w-md w-full mx-4"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Share Job</h3>
              <button
                className="text-gray-500 hover:text-gray-700 text-xl"
                onClick={() => setShareModalOpen(false)}
              >
                ×
              </button>
            </div>
            <div className="mb-4">
              <p className="text-sm text-gray-600 mb-4">
                Share "{selectedJobForShare.title}"
              </p>
              <JobSocialShare 
                jobId={selectedJobForShare.id}
                jobTitle={selectedJobForShare.title}
                companyName={selectedJobForShare.company_name || 'Company'}
                location={selectedJobForShare.location}
                jobType={selectedJobForShare.job_type}
                experienceLevel={selectedJobForShare.experience_level}
                salaryMin={selectedJobForShare.salary_min}
                salaryMax={selectedJobForShare.salary_max}
                salaryCurrency={selectedJobForShare.salary_currency}
                department={selectedJobForShare.department}
                description={selectedJobForShare.description}
              />
            </div>
          </div>
        </div>,
        document.body
      )}
    </div>
  );
};

// Wrap the JobListings component with the DashboardLayout
const JobListingsWithLayout = () => (
  <DashboardLayout>
    <JobListings />
  </DashboardLayout>
);

export default JobListingsWithLayout;
