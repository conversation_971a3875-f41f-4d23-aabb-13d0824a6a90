import React, { useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  FileUp,
  Briefcase,
  Building,
  FileText,
  BarChart2,
  ChevronDown,
  Plus,
  Upload,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuGroup,
  DropdownMenuLabel,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useCreateCompany, useUploadCompanyLogo } from '@/hooks/use-companies';

interface CompanyFormData {
  name: string;
  description: string;
  industry: string;
  size: string;
  location: string;
  website: string;
  logo_url?: string;
}

interface QuickActionsProps {
  className?: string;
}

const QuickActions: React.FC<QuickActionsProps> = ({ className }) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const createCompanyMutation = useCreateCompany();
  const uploadLogoMutation = useUploadCompanyLogo();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isCompanyDialogOpen, setIsCompanyDialogOpen] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    description: '',
    industry: '',
    size: '',
    location: '',
    website: ''
  });

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle logo file change
  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setLogoFile(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle logo removal
  const handleRemoveLogo = () => {
    setLogoFile(null);
    setLogoPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  // Handle form submission
  const handleCreateCompany = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      toast({
        title: 'Error',
        description: 'Company name is required',
        variant: 'destructive'
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // First create the company
      const newCompany = await createCompanyMutation.mutateAsync({
        name: formData.name,
        description: formData.description,
        industry: formData.industry,
        size: formData.size,
        location: formData.location,
        website: formData.website
      });

      // If we have a logo file, upload it and update the company
      if (logoFile && newCompany?.id) {
        await uploadLogoMutation.mutateAsync({
          companyId: newCompany.id,
          file: logoFile
        });
      }

      // Reset form and close dialog
      setFormData({
        name: '',
        description: '',
        industry: '',
        size: '',
        location: '',
        website: ''
      });
      setLogoFile(null);
      setLogoPreview(null);
      setIsCompanyDialogOpen(false);

      // Navigate to companies page
      navigate('/dashboard/companies');
    } catch (error) {
      console.error('Error creating company:', error);
      toast({
        title: 'Error',
        description: 'Failed to create company. Please try again.',
        variant: 'destructive'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Define quick actions
  const primaryActions = [
    {
      icon: <FileUp className="mr-2 h-4 w-4" />,
      label: 'Upload CV',
      action: () => navigate('/dashboard/cvs/evaluation'),
      color: 'text-blue-600'
    },
    {
      icon: <Briefcase className="mr-2 h-4 w-4" />,
      label: 'Create Job',
      action: () => navigate('/dashboard/jobs/new'),
      color: 'text-green-600'
    },
    {
      icon: <Building className="mr-2 h-4 w-4" />,
      label: 'Add Company',
      action: () => setIsCompanyDialogOpen(true),
      color: 'text-purple-600'
    }
  ];

  const secondaryActions = [
    {
      icon: <FileText className="mr-2 h-4 w-4" />,
      label: 'View Candidates',
      action: () => navigate('/dashboard/cvs')
    },
    {
      icon: <BarChart2 className="mr-2 h-4 w-4" />,
      label: 'Analytics',
      action: () => navigate('/dashboard/analytics')
    }
  ];

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      {/* Company Creation Dialog */}
      <Dialog
        open={isCompanyDialogOpen}
        onOpenChange={(open) => {
          setIsCompanyDialogOpen(open);
          if (!open) {
            // Reset form when dialog is closed
            setLogoFile(null);
            setLogoPreview(null);
            setFormData({
              name: '',
              description: '',
              industry: '',
              size: '',
              location: '',
              website: ''
            });
          }
        }}
      >
        <DialogContent className="sm:max-w-[550px]">
          <DialogHeader>
            <DialogTitle>Create New Company</DialogTitle>
            <DialogDescription>
              Fill in the details below to create a new company profile.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleCreateCompany}>
            <div className="grid gap-4 py-4">
              {/* Company Logo Upload */}
              <div className="space-y-2">
                <Label className="text-gray-700">Company Logo</Label>
                <div className="flex flex-col items-center p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50">
                  {logoPreview ? (
                    <div className="relative mb-4">
                      <img
                        src={logoPreview}
                        alt="Company logo preview"
                        className="w-24 h-24 object-contain rounded-md"
                      />
                      <button
                        type="button"
                        onClick={handleRemoveLogo}
                        className="absolute -top-2 -right-2 bg-red-100 text-red-600 rounded-full p-1 hover:bg-red-200"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>
                  ) : (
                    <Building className="h-16 w-16 text-gray-300 mb-4" />
                  )}
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleLogoChange}
                    accept="image/*"
                    className="hidden"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    className="border-gray-300 text-gray-700 hover:bg-gray-100"
                    onClick={() => fileInputRef.current?.click()}
                  >
                    <Upload className="mr-2 h-4 w-4" /> Upload Logo
                  </Button>
                  <p className="text-xs text-gray-500 mt-2">Recommended size: 400x400px (Max: 2MB)</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="name" className="text-gray-700">Company Name <span className="text-red-500">*</span></Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g. Acme Corporation"
                  className="bg-white border-gray-200 text-gray-800"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description" className="text-gray-700">Description</Label>
                <Textarea
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  placeholder="Brief description of your company"
                  className="bg-white border-gray-200 text-gray-800 min-h-[100px]"
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="industry" className="text-gray-700">Industry</Label>
                  <Input
                    id="industry"
                    name="industry"
                    value={formData.industry}
                    onChange={handleInputChange}
                    placeholder="e.g. Technology"
                    className="bg-white border-gray-200 text-gray-800"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="size" className="text-gray-700">Company Size</Label>
                  <Input
                    id="size"
                    name="size"
                    value={formData.size}
                    onChange={handleInputChange}
                    placeholder="e.g. 1-50 employees"
                    className="bg-white border-gray-200 text-gray-800"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="location" className="text-gray-700">Location</Label>
                  <Input
                    id="location"
                    name="location"
                    value={formData.location}
                    onChange={handleInputChange}
                    placeholder="e.g. New York, NY"
                    className="bg-white border-gray-200 text-gray-800"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="website" className="text-gray-700">Website</Label>
                  <Input
                    id="website"
                    name="website"
                    value={formData.website}
                    onChange={handleInputChange}
                    placeholder="e.g. https://example.com"
                    className="bg-white border-gray-200 text-gray-800"
                  />
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                    Creating...
                  </div>
                ) : (
                  'Create Company'
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Primary action buttons (visible on larger screens) */}
      <div className="hidden lg:flex items-center gap-2">
        {primaryActions.map((action, index) => (
          <TooltipProvider key={index} delayDuration={300}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={action.action}
                  className="bg-white border-gray-200 hover:bg-gray-50 shadow-sm"
                >
                  <span className={action.color}>{action.icon}</span>
                  <span className="hidden xl:inline">{action.label}</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{action.label}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ))}
      </div>

      {/* Quick actions dropdown (always visible) */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="bg-white border-gray-200 hover:bg-gray-50 shadow-sm"
          >
            <Plus className="mr-2 h-4 w-4 text-primary" />
            <span className="hidden md:inline">Quick Actions</span>
            <span className="md:hidden sr-only">Actions</span>
            <ChevronDown className="ml-2 h-4 w-4 md:inline hidden" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56 bg-white border-gray-200 shadow-lg rounded-lg">
          <DropdownMenuLabel>Quick Actions</DropdownMenuLabel>
          <DropdownMenuSeparator />
          <DropdownMenuGroup>
            {/* Show primary actions in dropdown on mobile */}
            <div className="lg:hidden">
              {primaryActions.map((action, index) => (
                <DropdownMenuItem
                  key={`primary-${index}`}
                  onClick={action.action}
                  className="cursor-pointer"
                >
                  <span className={action.color}>{action.icon}</span>
                  {action.label}
                </DropdownMenuItem>
              ))}
              <DropdownMenuSeparator />
            </div>

            {/* Secondary actions */}
            {secondaryActions.map((action, index) => (
              <DropdownMenuItem
                key={`secondary-${index}`}
                onClick={action.action}
                className="cursor-pointer"
              >
                {action.icon}
                {action.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuGroup>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};

export default QuickActions;
