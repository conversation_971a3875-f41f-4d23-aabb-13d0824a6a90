import { getAllActiveAISettings } from '@/services/supabase/aiSettings';
import { env } from '@/lib/env';
import { AIProvider } from '@/types/aiSettings';

/**
 * AI Configuration manager that handles loading settings from database with environment variable fallback
 */

interface AIConfig {
  provider: AIProvider;
  model: string;
  apiKey: string;
}

// Cache for AI configuration to avoid repeated database calls
let cachedConfig: AIConfig | null = null;
let lastConfigLoad = 0;
const CONFIG_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

/**
 * Get AI configuration from database with fallback to environment variables
 */
export async function getAIConfig(): Promise<AIConfig> {
  const now = Date.now();
  
  // Return cached config if it's still valid
  if (cachedConfig && (now - lastConfigLoad) < CONFIG_CACHE_DURATION) {
    return cachedConfig;
  }

  try {
    // Try to load from database - get all active settings (properly decrypted)
    const { data: allActiveSettings, error } = await getAllActiveAISettings();

    if (allActiveSettings && !error && allActiveSettings.length > 0) {
      // Get the first active setting (there should only be one)
      const activeSetting = allActiveSettings[0];

      if (activeSetting && activeSetting.api_key !== 'PLACEHOLDER_API_KEY') {
        cachedConfig = {
          provider: activeSetting.provider,
          model: activeSetting.model,
          apiKey: activeSetting.api_key // This is already decrypted by getAllActiveAISettings
        };
        lastConfigLoad = now;
        return cachedConfig;
      }
    }
  } catch (error) {
    console.warn('Failed to load AI settings from database, falling back to environment variables:', error);
  }

  // Fallback to environment variables
  cachedConfig = {
    provider: 'groq',
    model: env.GROQ_MODEL || 'llama-3.3-70b-versatile',
    apiKey: env.GROQ_API_KEY
  };
  lastConfigLoad = now;
  
  return cachedConfig;
}

/**
 * Clear the configuration cache (useful when settings are updated)
 */
export function clearAIConfigCache(): void {
  cachedConfig = null;
  lastConfigLoad = 0;
}

/**
 * Get the base URL for the AI provider
 */
export function getProviderBaseURL(provider: AIProvider): string {
  switch (provider) {
    case 'groq':
      return 'https://api.groq.com/openai/v1';
    case 'anthropic':
      return 'https://api.anthropic.com';
    case 'openai':
      return 'https://api.openai.com/v1';
    case 'gemini':
      return 'https://generativelanguage.googleapis.com/v1beta';
    default:
      throw new Error(`Unsupported AI provider: ${provider}`);
  }
}

/**
 * Validate if the current AI configuration is complete
 */
export async function validateAIConfig(): Promise<{ isValid: boolean; error?: string }> {
  try {
    const config = await getAIConfig();
    
    if (!config.apiKey || config.apiKey.trim() === '') {
      return { isValid: false, error: 'API key is missing' };
    }
    
    if (!config.model || config.model.trim() === '') {
      return { isValid: false, error: 'Model is not specified' };
    }
    
    return { isValid: true };
  } catch (error) {
    return { isValid: false, error: 'Failed to load AI configuration' };
  }
}
