import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import {
  applyToJob,
  getMyCandidateApplications,
  hasAppliedToJob,
  getCandidateProfile,
  updateCandidateProfile,
  CandidateApplication,
  ApplyToJobPayload
} from '@/services/supabase/candidates';

/**
 * Hook to apply to a job
 */
export function useApplyToJob() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: applyToJob,
    onSuccess: () => {
      // Invalidate and refetch applications
      queryClient.invalidateQueries({ queryKey: ['candidate-applications'] });
      queryClient.invalidateQueries({ queryKey: ['has-applied'] });
      
      toast({
        title: 'Application submitted!',
        description: 'Your application has been successfully submitted. You can track its status in your dashboard.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Application failed',
        description: error.message || 'Failed to submit your application. Please try again.',
        variant: 'destructive',
      });
    },
  });
}

/**
 * Hook to get candidate's applications
 */
export function useCandidateApplications() {
  return useQuery<CandidateApplication[]>({
    queryKey: ['candidate-applications'],
    queryFn: getMyCandidateApplications,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to check if user has applied to a specific job
 */
export function useHasAppliedToJob(jobId: string) {
  return useQuery<boolean>({
    queryKey: ['has-applied', jobId],
    queryFn: () => hasAppliedToJob(jobId),
    enabled: !!jobId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

/**
 * Hook to get candidate profile
 */
export function useCandidateProfile() {
  return useQuery({
    queryKey: ['candidate-profile'],
    queryFn: getCandidateProfile,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to update candidate profile
 */
export function useUpdateCandidateProfile() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: updateCandidateProfile,
    onSuccess: () => {
      // Invalidate and refetch profile
      queryClient.invalidateQueries({ queryKey: ['candidate-profile'] });
      
      toast({
        title: 'Profile updated',
        description: 'Your profile has been successfully updated.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Update failed',
        description: error.message || 'Failed to update your profile. Please try again.',
        variant: 'destructive',
      });
    },
  });
}
