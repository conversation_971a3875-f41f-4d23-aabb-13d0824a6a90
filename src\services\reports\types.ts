import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';

// Extend jsPDF with autotable
declare module 'jspdf' {
  interface jsPDF {
    autoTable: typeof autoTable;
    lastAutoTable: {
      finalY: number;
    };
  }
}

/**
 * Report types
 */
export type ReportType = 'recruitment_funnel' | 'time_to_hire' | 'source_effectiveness' | 'candidate_pipeline' | 'skill_analysis' | 'comprehensive';

/**
 * Report format
 */
export type ReportFormat = 'pdf' | 'excel';

/**
 * Report data interface
 */
export interface ReportData {
  title: string;
  description?: string;
  date: Date;
  companyName?: string;
  companyLogo?: string; // Base64 encoded image or URL
  data: any;
  columns: Array<{
    header: string;
    dataKey: string;
    width?: number;
  }>;
  summary?: {
    [key: string]: string | number;
  };
  filters?: {
    [key: string]: string | number | boolean | Date;
  };
  // Additional fields for enhanced reports
  executiveSummary?: string;
  recommendations?: string[];
  historicalData?: {
    period: string;
    data: any[];
  }[];
  benchmarkData?: {
    name: string;
    value: number;
    description?: string;
  }[];
  // For comprehensive reports with multiple sections
  sections?: Array<{
    title: string;
    description?: string;
    data: any;
    columns: Array<{
      header: string;
      dataKey: string;
      width?: number;
    }>;
    type?: ReportType;
  }>;
  // Color scheme for charts and visualizations
  colors?: {
    primary?: string;
    secondary?: string;
    accent?: string;
    background?: string;
    text?: string;
  };
}
