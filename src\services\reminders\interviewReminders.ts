import { supabase } from '@/lib/supabase';
import { getInterviews } from '@/services/supabase/interviews';
import { getInterviewParticipants } from '@/services/supabase/interview-participants';
import { getCandidates } from '@/services/supabase/candidates';
import { sendInterviewReminder } from '@/services/email/interviewNotifications';
import { addHours, subHours, isAfter, isBefore } from 'date-fns';

/**
 * Send 24-hour reminders for upcoming interviews
 */
export const send24HourReminders = async (): Promise<void> => {
  try {
    const now = new Date();
    const in24Hours = addHours(now, 24);
    const in25Hours = addHours(now, 25);

    // Get interviews scheduled between 24-25 hours from now
    const interviews = await getInterviews({
      status: 'scheduled',
      startDate: in24Hours.toISOString(),
      endDate: in25Hours.toISOString()
    });

    for (const interview of interviews) {
      try {
        // Get participants
        const participants = await getInterviewParticipants(interview.id);
        
        // Get candidate details
        const candidates = await getCandidates();
        const candidate = candidates.find(c => c.id === interview.candidate_id);

        if (!candidate) continue;

        // Send reminder to candidate
        await sendInterviewReminder(
          interview,
          { email: candidate.email, name: candidate.name, role: 'candidate' },
          '24h'
        );

        // Send reminders to interviewers
        for (const participant of participants) {
          if (participant.email && participant.role === 'interviewer') {
            await sendInterviewReminder(
              interview,
              { email: participant.email, name: participant.name || 'Interviewer', role: 'interviewer' },
              '24h'
            );
          }
        }

        console.log(`24h reminders sent for interview ${interview.id}`);
      } catch (error) {
        console.error(`Failed to send 24h reminder for interview ${interview.id}:`, error);
      }
    }
  } catch (error) {
    console.error('Failed to send 24-hour reminders:', error);
  }
};

/**
 * Send 1-hour reminders for upcoming interviews
 */
export const send1HourReminders = async (): Promise<void> => {
  try {
    const now = new Date();
    const in1Hour = addHours(now, 1);
    const in2Hours = addHours(now, 2);

    // Get interviews scheduled between 1-2 hours from now
    const interviews = await getInterviews({
      status: 'scheduled',
      startDate: in1Hour.toISOString(),
      endDate: in2Hours.toISOString()
    });

    for (const interview of interviews) {
      try {
        // Get participants
        const participants = await getInterviewParticipants(interview.id);
        
        // Get candidate details
        const candidates = await getCandidates();
        const candidate = candidates.find(c => c.id === interview.candidate_id);

        if (!candidate) continue;

        // Send reminder to candidate
        await sendInterviewReminder(
          interview,
          { email: candidate.email, name: candidate.name, role: 'candidate' },
          '1h'
        );

        // Send reminders to interviewers
        for (const participant of participants) {
          if (participant.email && participant.role === 'interviewer') {
            await sendInterviewReminder(
              interview,
              { email: participant.email, name: participant.name || 'Interviewer', role: 'interviewer' },
              '1h'
            );
          }
        }

        console.log(`1h reminders sent for interview ${interview.id}`);
      } catch (error) {
        console.error(`Failed to send 1h reminder for interview ${interview.id}:`, error);
      }
    }
  } catch (error) {
    console.error('Failed to send 1-hour reminders:', error);
  }
};

/**
 * Check and send all due reminders
 */
export const processInterviewReminders = async (): Promise<void> => {
  console.log('Processing interview reminders...');
  
  await Promise.all([
    send24HourReminders(),
    send1HourReminders()
  ]);
  
  console.log('Interview reminders processing completed');
};