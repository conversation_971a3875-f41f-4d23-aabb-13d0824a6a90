# Centralized AI System

## Overview

The Sourcio.ai application now uses a centralized AI system that ensures consistent input/output formats regardless of which AI model is selected in the admin settings. This system eliminates duplication and provides a single source of truth for all AI operations.

## Architecture

### 1. Centralized Schemas (`src/lib/ai/schemas.ts`)

All JSON schemas for AI operations are defined in one place:

- **CV_PARSING_SCHEMA**: Defines the structure for parsed CV data
- **JOB_MATCHING_SCHEMA**: Defines the structure for job-candidate matching results
- **SKILL_EXTRACTION_SCHEMA**: Defines the structure for extracted skills
- **CANDIDATE_RANKING_SCHEMA**: Defines the structure for candidate ranking results

### 2. Centralized Prompts (`src/lib/ai/prompts.ts`)

All system prompts are defined in one place and reference the centralized schemas:

- **CV_PARSING_PROMPT**: For parsing CVs and extracting structured information
- **JOB_MATCHING_PROMPT**: For matching candidates to job descriptions
- **SK<PERSON>L_EXTRACTION_PROMPT**: For extracting skills from text
- **CANDIDATE_RANKING_PROMPT**: For scoring and ranking candidates

### 3. Unified AI Operations (`src/lib/ai/operations.ts`)

A service layer that provides standardized functions for all AI operations:

- `parseCV(cvText: string)`: Parse CV text and extract structured information
- `matchCandidateToJob(candidateProfile, jobDescription, coverLetter?)`: Match candidate to job
- `extractSkills(text: string)`: Extract skills from text
- `rankCandidatesForJob(candidates, jobDescription)`: Score and rank candidates

### 4. Server-Side Consistency (`supabase/functions/_shared/ai-operations.ts`)

A shared module for Supabase edge functions that mirrors the client-side system to ensure consistency across client and server operations.

## Benefits

### 1. **Consistent Output Format**
Regardless of which AI model is selected (GROQ, OpenAI, Anthropic), the input and output formats remain exactly the same.

### 2. **Single Source of Truth**
All prompts and schemas are defined in one place, making them easy to maintain and update.

### 3. **No Duplication**
Eliminates the previous issue where prompts were duplicated across different files and could become inconsistent.

### 4. **Easy Maintenance**
To update a prompt or schema, you only need to change it in one place, and it automatically applies everywhere.

### 5. **Type Safety**
TypeScript types are provided for all operations, ensuring compile-time safety.

## Usage

### Client-Side Usage

```typescript
import { parseCV, matchCandidateToJob, extractSkills, rankCandidatesForJob } from '@/lib/ai/operations';

// Parse a CV
const parsedCV = await parseCV(cvText);

// Match candidate to job
const matchResult = await matchCandidateToJob(candidateProfile, jobDescription, coverLetter);

// Extract skills
const skills = await extractSkills(text);

// Rank candidates
const rankings = await rankCandidatesForJob(candidates, jobDescription);
```

### Server-Side Usage (Edge Functions)

```typescript
import { rankCandidatesForJob } from '../_shared/ai-operations.ts';

// Rank candidates using the same centralized system
const rankings = await rankCandidatesForJob(apiKey, model, candidates, jobDescription);
```

## AI Model Configuration

The system automatically uses the AI model configured in the admin settings (`/dashboard/admin/ai-model-settings`). You can switch between:

- **GROQ API**: High-performance inference for Llama models
- **Anthropic Claude**: Advanced reasoning and analysis capabilities
- **OpenAI GPT**: Versatile language models for various tasks
- **Google Gemini**: Advanced multimodal AI with strong reasoning and native structured output support

The input and output formats remain consistent regardless of which provider/model is selected.

## Migration from Old System

The old system in `src/lib/groq.ts` has been updated to use the new centralized operations while maintaining backward compatibility:

- All old function signatures remain the same
- Old prompt exports are still available for backward compatibility
- Functions now delegate to the centralized operations

## File Structure

```
src/lib/ai/
├── schemas.ts          # All JSON schemas
├── prompts.ts          # All system prompts
└── operations.ts       # Unified AI operations

supabase/functions/_shared/
└── ai-operations.ts    # Server-side AI operations

src/lib/
└── groq.ts            # Legacy compatibility layer
```

## Future Enhancements

1. **Schema Validation**: Add runtime schema validation for AI responses
2. **Prompt Versioning**: Track prompt versions for A/B testing
3. **Performance Monitoring**: Add metrics for different AI providers
4. **Fallback Mechanisms**: Implement automatic fallback between providers
5. **Caching**: Add response caching for improved performance

## Best Practices

1. **Always use the centralized operations** instead of calling AI APIs directly
2. **Update schemas and prompts in the centralized files** only
3. **Test with multiple AI providers** to ensure consistency
4. **Use TypeScript types** provided by the operations for type safety
5. **Handle errors gracefully** as the operations include proper error handling

This centralized system ensures that your AI operations are consistent, maintainable, and future-proof regardless of which AI model you choose to use.
