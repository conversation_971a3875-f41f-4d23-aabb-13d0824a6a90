import { serve } from 'https://deno.land/std@0.177.0/http/server.ts'

// Deno type declaration for IDE
declare const Deno: any
import { corsHeaders } from '../_shared/cors.ts'

interface EmailOptions {
  to: string | string[];
  subject: string;
  html: string;
  companyName?: string;
  attachments?: Array<{
    filename: string;
    content: string;
    contentType: string;
  }>;
}

serve(async (req) => {
  // Handle CORS preflight request
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  if (req.method !== 'POST') {
    return new Response(JSON.stringify({ error: 'Method not allowed' }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 405,
    })
  }

  try {
    const { to, subject, html, companyName, attachments = [] }: EmailOptions = await req.json()

    if (!to || !subject || !html) {
      return new Response(JSON.stringify({ error: 'Missing required fields' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      })
    }

    // SMTP configuration from environment
    const smtpConfig = {
      host: Deno.env.get('SMTP_HOST'),
      port: parseInt(Deno.env.get('SMTP_PORT') || '587'),
      secure: Deno.env.get('SMTP_SECURE') === 'true',
      username: Deno.env.get('SMTP_USER'),
      password: Deno.env.get('SMTP_PASSWORD'),
      from: Deno.env.get('SMTP_FROM'),
    }

    if (!smtpConfig.host || !smtpConfig.username || !smtpConfig.password) {
      throw new Error('SMTP configuration incomplete')
    }

    // Create email payload - use verified domain for Resend
    const fromEmail = smtpConfig.from.includes('@expert-recruiters.com')
      ? smtpConfig.from.replace('@expert-recruiters.com', '@mail.expert-recruiters.com')
      : smtpConfig.from

    const senderName = companyName || 'Sourcio.ai';
    const emailPayload = {
      from: `"${senderName}" <${fromEmail}>`,
      to: Array.isArray(to) ? to.join(', ') : to,
      subject,
      html,
      attachments: attachments.map(attachment => ({
        filename: attachment.filename,
        content: attachment.content,
        encoding: 'base64',
        contentType: attachment.contentType,
      })),
    }

    // Use a third-party email service API (like SendGrid, Mailgun, etc.)
    // For now, we'll use a simple SMTP approach with fetch
    const response = await sendEmailViaSMTP(emailPayload, smtpConfig)

    if (!response.success) {
      throw new Error(response.error || 'Failed to send email')
    }

    console.log('Email sent successfully to:', to)
    
    return new Response(JSON.stringify({ success: true }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200,
    })
  } catch (error) {
    console.error('Failed to send email:', error)

    // Return proper error response with CORS headers
    return new Response(JSON.stringify({
      error: error.message,
      timestamp: new Date().toISOString(),
      details: 'Check edge function logs for more information'
    }), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500,
    })
  }
})

// Email implementation using Resend API
async function sendEmailViaSMTP(emailPayload: any, _smtpConfig: any) {
  try {
    console.log('Sending email via Resend API...')
    console.log('Email details:', {
      to: emailPayload.to,
      subject: emailPayload.subject,
      hasAttachments: emailPayload.attachments?.length > 0,
      from: emailPayload.from
    })

    // Get Resend API key
    const resendApiKey = Deno.env.get('RESEND_API_KEY')

    if (!resendApiKey) {
      throw new Error('RESEND_API_KEY environment variable is not set')
    }

    console.log('Using Resend API...')

    // Prepare email data for Resend
    const emailData: any = {
      from: emailPayload.from,
      to: Array.isArray(emailPayload.to) ? emailPayload.to : [emailPayload.to],
      subject: emailPayload.subject,
      html: emailPayload.html,
    }

    // Add attachments if present
    if (emailPayload.attachments && emailPayload.attachments.length > 0) {
      emailData.attachments = emailPayload.attachments.map((att: any) => ({
        filename: att.filename,
        content: att.content, // Resend expects base64 content
        contentType: att.contentType,
      }))
      console.log(`Adding ${emailData.attachments.length} attachments`)
    }

    // Send email via Resend API
    const response = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData)
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('Resend API error response:', errorData)
      throw new Error(`Resend API error (${response.status}): ${errorData}`)
    }

    const result = await response.json()
    console.log('Email sent successfully via Resend API:', result.id)

    return { success: true, messageId: result.id }

  } catch (error) {
    console.error('Email send error:', error)
    return { success: false, error: error.message }
  }
}