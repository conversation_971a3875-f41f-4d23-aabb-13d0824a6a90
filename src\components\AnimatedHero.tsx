import React, { useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { useGSAP } from '@gsap/react';
import gsap from 'gsap';

// Animated background element
const AnimatedBackground: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Create particles
    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'absolute rounded-full bg-white opacity-0';

      // Random size between 2px and 6px
      const size = Math.random() * 4 + 2;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;

      // Random position within the container
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      particle.style.left = `${posX}%`;
      particle.style.top = `${posY}%`;

      // Add to DOM
      containerRef.current?.appendChild(particle);

      // Animate with CSS
      particle.animate(
        [
          { opacity: 0, transform: 'translateY(0)' },
          { opacity: 0.8, transform: 'translateY(-20px)' },
          { opacity: 0, transform: 'translateY(-40px)' },
        ],
        {
          duration: 3000 + Math.random() * 2000,
          easing: 'ease-out',
        }
      );

      // Remove after animation
      setTimeout(() => {
        particle.remove();
      }, 5000);
    };

    // Create particles at intervals
    const interval = setInterval(() => {
      for (let i = 0; i < 3; i++) {
        createParticle();
      }
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <div ref={containerRef} className="absolute inset-0 overflow-hidden pointer-events-none z-10">
      {/* Animated gradient blobs */}
      <div className="absolute -top-[300px] -left-[300px] w-[600px] h-[600px] rounded-full bg-blue-500/10 blur-3xl"></div>
      <div className="absolute -bottom-[200px] -right-[200px] w-[500px] h-[500px] rounded-full bg-purple-500/10 blur-3xl"></div>
      <div className="absolute top-[40%] left-[20%] w-[400px] h-[400px] rounded-full bg-pink-500/5 blur-3xl"></div>

      {/* Animated floating blob */}
      <motion.div
        className="absolute top-1/4 right-1/4 w-[300px] h-[300px] rounded-full bg-indigo-500/5 blur-xl"
        animate={{
          x: [0, 30, 0],
          y: [0, 15, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      ></motion.div>

      {/* Additional animated blob */}
      <motion.div
        className="absolute bottom-1/4 left-1/3 w-[250px] h-[250px] rounded-full bg-cyan-500/5 blur-xl"
        animate={{
          x: [0, -20, 0],
          y: [0, 10, 0],
        }}
        transition={{
          duration: 10,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      ></motion.div>
    </div>
  );
};

// Floating UI mockup
const FloatingUI: React.FC<{
  className?: string;
  delay?: number;
  children: React.ReactNode;
}> = ({ className = "", delay = 0, children }) => {
  const elementRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (elementRef.current) {
      gsap.fromTo(
        elementRef.current,
        {
          y: '+=20',
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );

      // Floating animation
      gsap.to(elementRef.current, {
        y: '+=10',
        duration: 2 + Math.random(),
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: Math.random() * 0.5,
      });
    }
  }, [delay]);

  return (
    <div
      ref={elementRef}
      className={`bg-white rounded-lg shadow-xl ${className}`}
      style={{ opacity: 0 }}
    >
      {children}
    </div>
  );
};

// CV Card Mockup
const CVCard: React.FC = () => {
  return (
    <div className="w-48 h-64 p-4 flex flex-col">
      <div className="w-full h-4 bg-blue-500 rounded mb-3"></div>
      <div className="w-3/4 h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-1/2 h-3 bg-gray-200 rounded mb-4"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded mb-3"></div>
      <div className="w-1/2 h-6 bg-indigo-500 rounded mb-3"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-2/3 h-2 bg-gray-200 rounded"></div>
      <div className="mt-auto flex justify-between">
        <div className="w-8 h-8 rounded-full bg-green-500"></div>
        <div className="w-12 h-4 bg-blue-500 rounded"></div>
      </div>
    </div>
  );
};

// Company Profile Mockup
const CompanyCard: React.FC = () => {
  return (
    <div className="w-56 h-72 p-4 flex flex-col">
      <div className="flex items-center mb-4">
        <div className="w-12 h-12 rounded-full bg-purple-500 mr-3"></div>
        <div>
          <div className="w-32 h-4 bg-gray-800 rounded mb-1"></div>
          <div className="w-24 h-3 bg-gray-400 rounded"></div>
        </div>
      </div>
      <div className="w-full h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-full h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-3/4 h-3 bg-gray-200 rounded mb-4"></div>

      <div className="grid grid-cols-2 gap-2 mb-4">
        <div className="h-8 bg-blue-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-blue-500 rounded"></div>
        </div>
        <div className="h-8 bg-green-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-green-500 rounded"></div>
        </div>
        <div className="h-8 bg-purple-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-purple-500 rounded"></div>
        </div>
        <div className="h-8 bg-yellow-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-yellow-500 rounded"></div>
        </div>
      </div>

      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded"></div>

      <div className="mt-auto flex justify-end">
        <div className="w-20 h-6 bg-indigo-500 rounded"></div>
      </div>
    </div>
  );
};

// Match Result Mockup
const MatchCard: React.FC = () => {
  const progressRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (progressRef.current) {
      gsap.fromTo(
        progressRef.current,
        { width: '0%' },
        {
          width: '85%',
          duration: 1.5,
          delay: 0.5,
          ease: 'power2.out',
        }
      );
    }
  }, []);

  return (
    <div className="w-64 h-48 p-4 flex flex-col">
      <div className="flex justify-between items-center mb-4">
        <div className="w-32 h-5 bg-gray-800 rounded"></div>
        <div className="w-12 h-12 rounded-full bg-indigo-500 flex items-center justify-center text-white font-bold">
          85%
        </div>
      </div>

      <div className="w-full h-4 bg-gray-200 rounded-full mb-4 overflow-hidden">
        <div ref={progressRef} className="progress-fill h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full" style={{ width: '0%' }}></div>
      </div>

      <div className="grid grid-cols-2 gap-2 mb-3">
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
      </div>

      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded"></div>

      <div className="mt-auto flex justify-end">
        <div className="w-24 h-6 bg-green-500 rounded"></div>
      </div>
    </div>
  );
};

// Animated typing effect
const TypedHeadline: React.FC = () => {
  const headlineRef = useRef<HTMLHeadingElement>(null);
  const cursorRef = useRef<HTMLSpanElement>(null);

  useGSAP(() => {
    if (!headlineRef.current || !cursorRef.current) return;

    const headlines = [
      "AI-Powered CV Evaluation Platform for Agencies",
      "Match Candidates to Companies With Precision",
      "Streamline Your Agency's Recruitment Process"
    ];

    let currentHeadline = 0;
    let currentChar = 0;
    let isDeleting = false;
    let typingSpeed = 100;

    const type = () => {
      const headline = headlines[currentHeadline];

      if (isDeleting) {
        headlineRef.current!.textContent = headline.substring(0, currentChar - 1);
        currentChar--;
        typingSpeed = 50;
      } else {
        headlineRef.current!.textContent = headline.substring(0, currentChar + 1);
        currentChar++;
        typingSpeed = 100;
      }

      if (!isDeleting && currentChar === headline.length) {
        // Pause at the end of typing
        isDeleting = true;
        typingSpeed = 1500;
      } else if (isDeleting && currentChar === 0) {
        isDeleting = false;
        currentHeadline = (currentHeadline + 1) % headlines.length;
        typingSpeed = 500;
      }

      setTimeout(type, typingSpeed);
    };

    // Start typing
    setTimeout(type, 1000);

    // Animate cursor
    gsap.to(cursorRef.current, {
      opacity: 0,
      duration: 0.5,
      repeat: -1,
      yoyo: true,
      ease: 'power2.inOut',
    });
  }, []);

  return (
    <h1 className="text-5xl md:text-6xl font-bold text-white mb-6 h-[120px] md:h-[144px] flex items-center justify-center">
      <span ref={headlineRef}></span>
      <span ref={cursorRef} className="ml-1">|</span>
    </h1>
  );
};

// Main AnimatedHero component
export const AnimatedHero: React.FC = () => {
  return (
    <section className="relative h-screen overflow-hidden">
      {/* Custom gradient background */}
      <div
        className="absolute inset-0 z-0"
        style={{
          background: 'linear-gradient(106.89deg, rgba(192, 132, 252, 0.11) 15.73%, rgba(14, 165, 233, 0.41) 15.74%, rgba(232, 121, 249, 0.26) 56.49%, rgba(79, 70, 229, 0.4) 115.91%)',
        }}
      ></div>

      {/* Mesh gradient overlay */}
      <div
        className="absolute inset-0 z-0 opacity-40"
        style={{
          backgroundImage: 'url("data:image/svg+xml,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' width=\'100%25\' height=\'100%25\'%3E%3Cdefs%3E%3ClinearGradient id=\'a\' x1=\'0\' y1=\'0\' x2=\'100%25\' y2=\'100%25\' gradientUnits=\'userSpaceOnUse\'%3E%3Cstop offset=\'0\' stop-color=\'%23080E24\'/%3E%3Cstop offset=\'1\' stop-color=\'%23131B41\'/%3E%3C/linearGradient%3E%3Cpattern id=\'b\' width=\'300\' height=\'300\' patternUnits=\'userSpaceOnUse\'%3E%3Ccircle fill=\'%23FFFFFF\' cx=\'150\' cy=\'150\' r=\'120\'/%3E%3C/pattern%3E%3CradialGradient id=\'c\' cx=\'50%25\' cy=\'50%25\' r=\'50%25\' fx=\'50%25\' fy=\'50%25\'%3E%3Cstop offset=\'0%25\' stop-color=\'%23FFFFFF\' stop-opacity=\'0.1\'/%3E%3Cstop offset=\'100%25\' stop-color=\'%23FFFFFF\' stop-opacity=\'0\'/%3E%3C/radialGradient%3E%3C/defs%3E%3Crect fill=\'url(%23a)\' width=\'100%25\' height=\'100%25\'/%3E%3Crect width=\'100%25\' height=\'100%25\' fill=\'url(%23c)\'/%3E%3C/svg%3E")',
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          mixBlendMode: 'overlay',
        }}
      ></div>

      {/* Animated background */}
      <AnimatedBackground />

      {/* Floating UI Elements */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <div className="relative w-full h-full">
          <FloatingUI className="absolute top-[20%] left-[15%]" delay={0.5}>
            <CVCard />
          </FloatingUI>

          <FloatingUI className="absolute top-[30%] right-[15%]" delay={0.8}>
            <CompanyCard />
          </FloatingUI>

          <FloatingUI className="absolute bottom-[25%] left-[25%]" delay={1.1}>
            <MatchCard />
          </FloatingUI>
        </div>
      </div>

      {/* Content */}
      <div className="relative z-20 container mx-auto px-6 h-full flex flex-col justify-center items-center text-center">
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8 }}
        >
          <TypedHeadline />
        </motion.div>

        <motion.p
          className="text-xl text-gray-300 mb-8 max-w-3xl"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Upload CVs, match against company requirements, and deliver better candidates to your clients.
          Our intelligent system helps agencies evaluate talent more effectively.
        </motion.p>

        <motion.div
          className="flex flex-col sm:flex-row items-center justify-center gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="w-full sm:w-auto"
          >
            <Link
              to="/signup"
              className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white py-4 px-8 rounded-lg flex items-center justify-center transition-all duration-300 w-full shadow-lg shadow-blue-500/20"
            >
              Start Evaluating Candidates <ArrowRight size={16} className="ml-2" />
            </Link>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="w-full sm:w-auto"
          >
            <Link
              to="/login"
              className="bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white py-4 px-8 rounded-lg flex items-center justify-center transition-colors w-full"
            >
              Sign In
            </Link>
          </motion.div>
        </motion.div>

        <motion.div
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <div className="w-8 h-12 border-2 border-white/30 rounded-full flex justify-center">
            <motion.div
              className="w-1.5 h-3 bg-white rounded-full mt-2"
              animate={{
                y: [0, 6, 0],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>
      </div>
    </section>
  );
};
