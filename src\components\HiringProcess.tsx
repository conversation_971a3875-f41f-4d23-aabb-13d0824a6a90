
import { useState } from "react";
import { 
  Upload, 
  FileText, 
  Search, 
  BarChart3, 
  CheckCircle2, 
  Clock, 
  TrendingUp, 
  Users, 
  Award,
  Zap
} from "lucide-react";
import { useInView } from "react-intersection-observer";
import { cn } from "@/lib/utils";

const hiringSteps = [
  {
    title: "Upload CVs",
    description: "Companies or agencies upload candidate CVs directly to the platform — no formatting needed.",
    icon: Upload,
    color: "from-blue-500 to-blue-600",
    gradient: "from-blue-500/20 to-blue-600/20"
  },
  {
    title: "Post Jobs",
    description: "Add your job listings. The system understands your requirements and matches them to the right candidates.",
    icon: FileText,
    color: "from-purple-500 to-purple-600", 
    gradient: "from-purple-500/20 to-purple-600/20"
  },
  {
    title: "Automatic Evaluation",
    description: "Every CV is automatically analyzed and scored based on how well it fits the job. You'll instantly see the top matches.",
    icon: Search,
    color: "from-green-500 to-green-600",
    gradient: "from-green-500/20 to-green-600/20"
  },
  {
    title: "View Reports",
    description: "See clear and simple reports for each candidate — no manual sorting or guesswork.",
    icon: BarChart3,
    color: "from-amber-400 to-amber-500",
    gradient: "from-amber-400/20 to-amber-500/20"
  },
  {
    title: "Shortlist & Hire Faster",
    description: "Save hours of work. Just focus on interviewing your top candidates and make faster hiring decisions.",
    icon: CheckCircle2,
    color: "from-recruiter-lightblue to-blue-500",
    gradient: "from-recruiter-lightblue/20 to-blue-500/20"
  }
];

const statsBlocks = [
  {
    value: "70%+",
    description: "time saved on screening and shortlisting candidates",
    icon: Clock,
    color: "from-blue-400 to-blue-500"
  },
  {
    value: "3x",
    description: "faster hiring from job post to offer",
    icon: Zap,
    color: "from-amber-400 to-amber-500"
  },
  {
    value: "Better",
    description: "hires with data-backed decisions",
    icon: Award,
    color: "from-green-400 to-green-500"
  },
  {
    value: "Less",
    description: "admin work, more focus on people",
    icon: Users,
    color: "from-purple-400 to-purple-500"
  }
];

const ProcessCard = ({ step, isEven }: { step: typeof hiringSteps[0], isEven: boolean }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });
  
  return (
    <div 
      ref={ref}
      className={cn(
        "flex flex-col md:flex-row items-center gap-6 py-8 opacity-0",
        inView ? "animate-fade-in" : "",
        isEven ? "md:flex-row-reverse" : ""
      )}
    >
      <div className={cn(
        "bg-gradient-to-br w-20 h-20 rounded-2xl flex items-center justify-center shadow-lg",
        step.gradient
      )}>
        <step.icon size={32} className={cn("text-white")} />
      </div>
      
      <div className="flex-1">
        <h3 className={cn(
          "text-2xl font-bold mb-2 bg-gradient-to-r text-transparent bg-clip-text",
          step.color
        )}>
          {step.title}
        </h3>
        <p className="text-gray-300">{step.description}</p>
      </div>
    </div>
  );
};

const StatCard = ({ stat }: { stat: typeof statsBlocks[0] }) => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
    rootMargin: "50px", // Add some margin to trigger earlier
  });

  return (
    <div
      ref={ref}
      className={cn(
        "bg-card-gradient p-6 rounded-2xl border border-gray-800 shadow-lg hover:shadow-xl transition-all duration-300",
        // Only apply opacity-0 when not in view
        !inView ? "opacity-0" : "opacity-100 animate-fade-in"
      )}
    >
      <div className={cn(
        "w-12 h-12 mb-4 rounded-xl bg-gradient-to-br flex items-center justify-center",
        stat.color
      )}>
        <stat.icon size={24} className="text-white" />
      </div>
      <h4 className="text-3xl font-bold text-white mb-2">{stat.value}</h4>
      <p className="text-gray-300">{stat.description}</p>
    </div>
  );
};

export const HiringProcess = () => {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1,
  });

  return (
    <div id="how-it-works" className="py-16 bg-recruiter-darknavy">
      <div className="container mx-auto px-6">
        <div 
          ref={ref} 
          className={cn(
            "text-center mb-16",
            !inView ? "opacity-0" : "opacity-100 animate-fade-in"
          )}
        >
          <h2 className="text-4xl font-bold text-white mb-4">
            How Our Hiring Process Works
          </h2>
          <p className="text-gray-300 max-w-3xl mx-auto">
            Hiring with our platform is fast, simple, and smart. Here's how it works:
          </p>
        </div>

        <div className="mb-24 space-y-4 md:space-y-0 md:divide-y md:divide-gray-800/30">
          {hiringSteps.map((step, index) => (
            <ProcessCard 
              key={index} 
              step={step} 
              isEven={index % 2 !== 0} 
            />
          ))}
        </div>

        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-white mb-4">
            Boost Your Productivity by Over 70%
          </h2>
          <p className="text-gray-300 max-w-3xl mx-auto mb-12">
            Recruitment agencies and internal HR teams using our platform report:
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
          {statsBlocks.map((stat, index) => (
            <StatCard key={index} stat={stat} />
          ))}
        </div>
        
        <p className="text-gray-300 max-w-3xl mx-auto text-center mt-12">
          Our platform helps you do more placements with less effort, and grow your business without scaling your team.
        </p>
      </div>
    </div>
  );
};


