
import { Check } from "lucide-react";
import { Link } from "react-router-dom";

interface PricingFeature {
  text: string;
}

interface PricingCardProps {
  title: string;
  emoji: string;
  price: string;
  description: string;
  features: PricingFeature[];
  isPopular?: boolean;
}

export const PricingCard = ({ 
  title, 
  emoji, 
  price, 
  description, 
  features, 
  isPopular = false 
}: PricingCardProps) => {
  return (
    <div className={`bg-card-gradient rounded-lg p-6 flex flex-col h-full border-2 ${isPopular ? 'border-recruiter-lightblue' : 'border-transparent'}`}>
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <div>
            <span className="text-3xl mr-2">{emoji}</span>
            <h3 className="inline-block text-white text-xl font-semibold">{title}</h3>
          </div>
          {isPopular && (
            <span className="bg-recruiter-lightblue text-white text-xs font-medium px-2 py-1 rounded">
              Most Popular
            </span>
          )}
        </div>
        <div className="mb-2">
          <span className="text-white text-3xl font-bold">{price}</span>
          <span className="text-gray-400 ml-1">/month</span>
        </div>
        <p className="text-gray-300">{description}</p>
      </div>
      
      <div className="mb-6 flex-grow">
        <ul className="space-y-3">
          {features.map((feature, index) => (
            <li key={index} className="flex items-start">
              <Check size={18} className="text-recruiter-lightblue mt-1 mr-2 flex-shrink-0" />
              <span className="text-gray-300">{feature.text}</span>
            </li>
          ))}
        </ul>
      </div>
      
      <Link 
        to="#signup" 
        className={`w-full py-3 rounded text-center mt-auto transition-colors flex items-center justify-center
          ${isPopular 
            ? 'bg-recruiter-lightblue hover:bg-blue-500 text-white' 
            : 'bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white'}`}
      >
        Get Started
      </Link>
    </div>
  );
};
