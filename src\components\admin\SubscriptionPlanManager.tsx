import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { Loader2, Plus, RefreshCw, Check, X, Edit, Trash2, AlertCircle, CreditCard } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import axios from 'axios';

interface Plan {
  id: string;
  name: string;
  description: string;
  status: string;
  create_time: string;
  billing_cycles?: BillingCycle[];
  links?: Array<{
    href: string;
    rel: string;
    method: string;
  }>;
}

interface Product {
  id: string;
  name: string;
  description: string;
  type?: string;
  create_time?: string;
  active?: boolean;
}

interface StripePrice {
  id: string;
  nickname?: string;
  unit_amount: number;
  currency: string;
  recurring?: {
    interval: string;
    interval_count?: number;
  };
  product: string | Product;
  active: boolean;
}

interface BillingCycle {
  frequency: {
    interval_unit: string;
    interval_count: number;
  };
  tenure_type: string;
  sequence: number;
  total_cycles: number;
  pricing_scheme: {
    fixed_price: {
      value: string;
      currency_code: string;
    };
  };
}

interface CreatePlanFormData {
  productId: string;
  name: string;
  description: string;
  tier: string;
  billingCycles: BillingCycle[];
}

// Project prefix for filtering plans and products
const PROJECT_PREFIX = "ExpertRecruiter";

// Helper function to format plan name with tier
const formatPlanName = (tier: string) => {
  return `${PROJECT_PREFIX} ${tier} Plan`;
};

// Helper function to format plan description with tier
const formatPlanDescription = (tier: string) => {
  return `${PROJECT_PREFIX} ${tier} subscription plan`;
};

const SubscriptionPlanManager: React.FC = () => {
  const [activeProvider, setActiveProvider] = useState<'paypal' | 'stripe'>('paypal');
  const [plans, setPlans] = useState<Plan[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [stripePrices, setStripePrices] = useState<StripePrice[]>([]);
  const [loading, setLoading] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [createProductDialogOpen, setCreateProductDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<Plan | null>(null);
  const [editFormData, setEditFormData] = useState({
    name: '',
    description: '',
    billingCycles: [] as BillingCycle[],
  });
  const [formData, setFormData] = useState<CreatePlanFormData>({
    productId: '',
    tier: 'Starter',
    name: formatPlanName('Starter'),
    description: formatPlanDescription('Starter'),
    billingCycles: [
      {
        frequency: {
          interval_unit: 'MONTH',
          interval_count: 1,
        },
        tenure_type: 'REGULAR',
        sequence: 1,
        total_cycles: 0, // 0 means infinite
        pricing_scheme: {
          fixed_price: {
            value: '49.99',
            currency_code: 'USD',
          },
        },
      },
    ],
  });
  const [productFormData, setProductFormData] = useState({
    name: `${PROJECT_PREFIX} Product`,
    description: `${PROJECT_PREFIX} subscription product`,
    type: 'SERVICE',
  });
  const { toast } = useToast();

  // Fetch plans and products on component mount
  useEffect(() => {
    if (activeProvider === 'paypal') {
      fetchPlans();
      fetchProducts();
    } else {
      fetchStripePrices();
      fetchStripeProducts();
    }
  }, [activeProvider]);

  // Fetch subscription plans
  const fetchPlans = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/plans`, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        }
      });

      // Filter plans to only include those with the project prefix
      const allPlans = response.data.data.plans || [];
      const filteredPlans = allPlans.filter(plan =>
        plan.name.startsWith(PROJECT_PREFIX) ||
        plan.description.includes(PROJECT_PREFIX)
      );

      setPlans(filteredPlans);
    } catch (error) {
      console.error('Error fetching plans:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch subscription plans',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Fetch products
  const fetchProducts = async () => {
    try {
      const response = await axios.get(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/products`, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        }
      });

      // Filter products to only include those with the project prefix
      const allProducts = response.data.data.products || [];
      const filteredProducts = allProducts.filter(product =>
        product.name.startsWith(PROJECT_PREFIX) ||
        product.description.includes(PROJECT_PREFIX)
      );

      setProducts(filteredProducts);
    } catch (error) {
      console.error('Error fetching products:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch products',
        variant: 'destructive',
      });
    }
  };

  // Create a new subscription plan
  const createPlan = async () => {
    try {
      // Validate form data
      if (!formData.productId) {
        toast({
          title: 'Error',
          description: 'Please select a product',
          variant: 'destructive',
        });
        return;
      }

      if (!formData.name || !formData.description) {
        toast({
          title: 'Error',
          description: 'Please fill in all required fields',
          variant: 'destructive',
        });
        return;
      }

      // Create plan data
      const planData = {
        product_id: formData.productId,
        name: formData.name,
        description: formData.description,
        billing_cycles: formData.billingCycles,
        payment_preferences: {
          auto_bill_outstanding: true,
          setup_fee: {
            value: '0',
            currency_code: 'USD',
          },
          setup_fee_failure_action: 'CONTINUE',
          payment_failure_threshold: 3,
        },
      };

      // Send request to create plan
      const response = await axios.post(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/plans`, planData, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Show success message
      toast({
        title: 'Success',
        description: 'Subscription plan created successfully',
      });

      // Close dialog and refresh plans
      setCreateDialogOpen(false);
      fetchPlans();

      // Reset form data
      setFormData({
        productId: '',
        tier: 'Starter',
        name: formatPlanName('Starter'),
        description: formatPlanDescription('Starter'),
        billingCycles: [
          {
            frequency: {
              interval_unit: 'MONTH',
              interval_count: 1,
            },
            tenure_type: 'REGULAR',
            sequence: 1,
            total_cycles: 0,
            pricing_scheme: {
              fixed_price: {
                value: '49.99',
                currency_code: 'USD',
              },
            },
          },
        ],
      });
    } catch (error) {
      console.error('Error creating plan:', error);
      toast({
        title: 'Error',
        description: 'Failed to create subscription plan',
        variant: 'destructive',
      });
    }
  };

  // Create a new product
  const createProduct = async () => {
    try {
      // Validate form data
      if (!productFormData.name || !productFormData.description) {
        toast({
          title: 'Error',
          description: 'Please fill in all required fields',
          variant: 'destructive',
        });
        return;
      }

      // Send request to create product
      const response = await axios.post(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/products`, productFormData, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      // Show success message
      toast({
        title: 'Success',
        description: 'Product created successfully',
      });

      // Close dialog and refresh products
      setCreateProductDialogOpen(false);
      fetchProducts();

      // Reset form data
      setProductFormData({
        name: `${PROJECT_PREFIX} Product`,
        description: `${PROJECT_PREFIX} subscription product`,
        type: 'SERVICE',
      });
    } catch (error) {
      console.error('Error creating product:', error);
      toast({
        title: 'Error',
        description: 'Failed to create product',
        variant: 'destructive',
      });
    }
  };

  // Activate a plan
  const activatePlan = async (planId: string) => {
    try {
      await axios.post(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/activate-${planId}`, {}, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });
      toast({
        title: 'Success',
        description: 'Plan activated successfully',
      });
      fetchPlans();
    } catch (error) {
      console.error('Error activating plan:', error);
      toast({
        title: 'Error',
        description: 'Failed to activate plan',
        variant: 'destructive',
      });
    }
  };

  // Deactivate a plan
  const deactivatePlan = async (planId: string) => {
    try {
      await axios.post(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/deactivate-${planId}`, {}, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });
      toast({
        title: 'Success',
        description: 'Plan deactivated successfully',
      });
      fetchPlans();
    } catch (error) {
      console.error('Error deactivating plan:', error);
      toast({
        title: 'Error',
        description: 'Failed to deactivate plan',
        variant: 'destructive',
      });
    }
  };

  // Update a plan
  const updatePlan = async () => {
    try {
      if (!selectedPlan) return;

      // Validate form data
      if (!editFormData.name || !editFormData.description) {
        toast({
          title: 'Error',
          description: 'Please fill in all required fields',
          variant: 'destructive',
        });
        return;
      }

      // Validate billing cycle data
      if (editFormData.billingCycles.length > 0) {
        const billingCycle = editFormData.billingCycles[0];
        if (!billingCycle.pricing_scheme.fixed_price.value || parseFloat(billingCycle.pricing_scheme.fixed_price.value) <= 0) {
          toast({
            title: 'Error',
            description: 'Please enter a valid price',
            variant: 'destructive',
          });
          return;
        }
      }

      // Step 1: Update basic plan details (name, description)
      const basicUpdateData = [
        {
          op: 'replace',
          path: '/name',
          value: editFormData.name,
        },
        {
          op: 'replace',
          path: '/description',
          value: editFormData.description,
        },
      ];

      console.log('Updating basic plan details:', basicUpdateData);

      const basicResponse = await axios.patch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/plan-${selectedPlan.id}`, basicUpdateData, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      console.log('Basic update response:', basicResponse.data);

      // Step 2: Update pricing if billing cycles exist and have changed
      let pricingUpdated = false;
      if (editFormData.billingCycles.length > 0) {
        const billingCycle = editFormData.billingCycles[0];

        console.log('Checking pricing changes...');
        console.log('Current billing cycle:', billingCycle);
        console.log('Selected plan:', selectedPlan);

        // Check if pricing has changed
        const originalPlan = selectedPlan as Plan & { billing_cycles?: BillingCycle[] };
        let pricingChanged = false;

        if (originalPlan.billing_cycles && originalPlan.billing_cycles.length > 0) {
          const originalBilling = originalPlan.billing_cycles[0];
          console.log('Original billing cycle:', originalBilling);

          pricingChanged = (
            billingCycle.pricing_scheme.fixed_price.value !== originalBilling.pricing_scheme.fixed_price.value ||
            billingCycle.pricing_scheme.fixed_price.currency_code !== originalBilling.pricing_scheme.fixed_price.currency_code
          );

          console.log('Pricing changed?', pricingChanged);
          console.log('Current price:', billingCycle.pricing_scheme.fixed_price.value);
          console.log('Original price:', originalBilling.pricing_scheme.fixed_price.value);
        } else {
          console.log('No original billing cycles found, treating as changed');
          pricingChanged = true; // If no original billing cycles, treat as changed
        }

        if (pricingChanged) {
          try {
            const pricingUpdateData = {
              pricing_schemes: [
                {
                  billing_cycle_sequence: billingCycle.sequence || 1,
                  pricing_scheme: {
                    fixed_price: {
                      value: billingCycle.pricing_scheme.fixed_price.value,
                      currency_code: billingCycle.pricing_scheme.fixed_price.currency_code,
                    },
                  },
                },
              ],
            };

            console.log('Updating pricing:', pricingUpdateData);

            const pricingResponse = await axios.post(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/update-pricing-${selectedPlan.id}`, pricingUpdateData, {
              headers: {
                'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
                'Content-Type': 'application/json'
              }
            });

            console.log('Pricing update response:', pricingResponse.data);
            pricingUpdated = true;
          } catch (pricingError) {
            console.error('Error updating pricing:', pricingError);
            toast({
              title: 'Partial Update',
              description: 'Plan details were updated, but pricing could not be changed. This may be due to active subscriptions.',
              variant: 'default',
            });
          }
        }
      }

      // Show appropriate success message
      if (pricingUpdated) {
        toast({
          title: 'Success',
          description: 'Plan details and pricing updated successfully',
        });
      } else {
        toast({
          title: 'Success',
          description: 'Plan details updated successfully',
        });
      }

      // Close dialog and refresh plans
      setEditDialogOpen(false);
      setSelectedPlan(null);
      fetchPlans();

      // Reset form data
      setEditFormData({
        name: '',
        description: '',
        billingCycles: [],
      });
    } catch (error) {
      console.error('Error updating plan:', error);
      toast({
        title: 'Error',
        description: 'Failed to update plan',
        variant: 'destructive',
      });
    }
  };

  // Open edit dialog with plan data
  const openEditDialog = async (plan: Plan) => {
    try {
      // Fetch detailed plan information to get billing cycles
      const response = await axios.get(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/paypal-plans/plan-${plan.id}`, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        }
      });

      const detailedPlan = response.data.data;
      console.log('Detailed plan data:', detailedPlan);

      // Store the detailed plan with billing cycles
      setSelectedPlan(detailedPlan);

      const billingCycles = detailedPlan.billing_cycles || [];

      setEditFormData({
        name: detailedPlan.name,
        description: detailedPlan.description,
        billingCycles: billingCycles.map((cycle: any) => ({
          frequency: {
            interval_unit: cycle.frequency.interval_unit,
            interval_count: cycle.frequency.interval_count,
          },
          tenure_type: cycle.tenure_type,
          sequence: cycle.sequence,
          total_cycles: cycle.total_cycles,
          pricing_scheme: {
            fixed_price: {
              value: cycle.pricing_scheme.fixed_price.value,
              currency_code: cycle.pricing_scheme.fixed_price.currency_code,
            },
          },
        })),
      });
    } catch (error) {
      console.error('Error fetching plan details:', error);
      // Fallback to basic data if detailed fetch fails
      setEditFormData({
        name: plan.name,
        description: plan.description,
        billingCycles: [],
      });

      toast({
        title: 'Warning',
        description: 'Could not fetch billing details. Only basic plan information will be editable.',
        variant: 'destructive',
      });
    }

    setEditDialogOpen(true);
  };

  // Update billing cycle price
  const updateBillingCyclePrice = (index: number, value: string) => {
    const updatedBillingCycles = [...formData.billingCycles];
    updatedBillingCycles[index] = {
      ...updatedBillingCycles[index],
      pricing_scheme: {
        ...updatedBillingCycles[index].pricing_scheme,
        fixed_price: {
          ...updatedBillingCycles[index].pricing_scheme.fixed_price,
          value,
        },
      },
    };
    setFormData({
      ...formData,
      billingCycles: updatedBillingCycles,
    });
  };

  // Update billing cycle interval
  const updateBillingCycleInterval = (index: number, unit: string) => {
    const updatedBillingCycles = [...formData.billingCycles];
    updatedBillingCycles[index] = {
      ...updatedBillingCycles[index],
      frequency: {
        ...updatedBillingCycles[index].frequency,
        interval_unit: unit,
      },
    };
    setFormData({
      ...formData,
      billingCycles: updatedBillingCycles,
    });
  };

  // Update billing cycle interval count
  const updateBillingCycleIntervalCount = (index: number, count: number) => {
    const updatedBillingCycles = [...formData.billingCycles];
    updatedBillingCycles[index] = {
      ...updatedBillingCycles[index],
      frequency: {
        ...updatedBillingCycles[index].frequency,
        interval_count: count,
      },
    };
    setFormData({
      ...formData,
      billingCycles: updatedBillingCycles,
    });
  };

  // Update edit form billing cycle price
  const updateEditBillingCyclePrice = (index: number, value: string) => {
    const updatedBillingCycles = [...editFormData.billingCycles];
    updatedBillingCycles[index] = {
      ...updatedBillingCycles[index],
      pricing_scheme: {
        ...updatedBillingCycles[index].pricing_scheme,
        fixed_price: {
          ...updatedBillingCycles[index].pricing_scheme.fixed_price,
          value,
        },
      },
    };
    setEditFormData({
      ...editFormData,
      billingCycles: updatedBillingCycles,
    });
  };

  // Update edit form billing cycle interval
  const updateEditBillingCycleInterval = (index: number, unit: string) => {
    const updatedBillingCycles = [...editFormData.billingCycles];
    updatedBillingCycles[index] = {
      ...updatedBillingCycles[index],
      frequency: {
        ...updatedBillingCycles[index].frequency,
        interval_unit: unit,
      },
    };
    setEditFormData({
      ...editFormData,
      billingCycles: updatedBillingCycles,
    });
  };

  // Update edit form billing cycle interval count
  const updateEditBillingCycleIntervalCount = (index: number, count: number) => {
    const updatedBillingCycles = [...editFormData.billingCycles];
    updatedBillingCycles[index] = {
      ...updatedBillingCycles[index],
      frequency: {
        ...updatedBillingCycles[index].frequency,
        interval_count: count,
      },
    };
    setEditFormData({
      ...editFormData,
      billingCycles: updatedBillingCycles,
    });
  };

  // Update edit form billing cycle currency
  const updateEditBillingCycleCurrency = (index: number, currency: string) => {
    const updatedBillingCycles = [...editFormData.billingCycles];
    updatedBillingCycles[index] = {
      ...updatedBillingCycles[index],
      pricing_scheme: {
        ...updatedBillingCycles[index].pricing_scheme,
        fixed_price: {
          ...updatedBillingCycles[index].pricing_scheme.fixed_price,
          currency_code: currency,
        },
      },
    };
    setEditFormData({
      ...editFormData,
      billingCycles: updatedBillingCycles,
    });
  };

  // Stripe functions
  const fetchStripePrices = async () => {
    setLoading(true);
    try {
      const response = await axios.get(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-plans/prices`, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        }
      });

      // Filter prices to only include those with the project prefix
      const allPrices = response.data.data.data || [];
      const filteredPrices = allPrices.filter(price => {
        const productName = typeof price.product === 'string' ? '' : price.product?.name || '';
        return productName.startsWith(PROJECT_PREFIX) || price.nickname?.includes(PROJECT_PREFIX);
      });

      setStripePrices(filteredPrices);
    } catch (error) {
      console.error('Error fetching Stripe prices:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch Stripe prices',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchStripeProducts = async () => {
    try {
      const response = await axios.get(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-plans/products`, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`
        }
      });

      // Filter products to only include those with the project prefix
      const allProducts = response.data.data.data || [];
      const filteredProducts = allProducts.filter(product =>
        product.name.startsWith(PROJECT_PREFIX)
      );

      setProducts(filteredProducts);
    } catch (error) {
      console.error('Error fetching Stripe products:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch Stripe products',
        variant: 'destructive',
      });
    }
  };

  const createStripeProduct = async () => {
    try {
      if (!productFormData.name || !productFormData.description) {
        toast({
          title: 'Error',
          description: 'Please fill in all required fields',
          variant: 'destructive',
        });
        return;
      }

      const response = await axios.post(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-plans/products`, {
        name: productFormData.name,
        description: productFormData.description,
        type: 'service'
      }, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      toast({
        title: 'Success',
        description: 'Stripe product created successfully',
      });

      setCreateProductDialogOpen(false);
      fetchStripeProducts();

      setProductFormData({
        name: `${PROJECT_PREFIX} Product`,
        description: `${PROJECT_PREFIX} subscription product`,
        type: 'SERVICE',
      });
    } catch (error) {
      console.error('Error creating Stripe product:', error);
      toast({
        title: 'Error',
        description: 'Failed to create Stripe product',
        variant: 'destructive',
      });
    }
  };

  const createStripePrice = async () => {
    try {
      if (!formData.productId || !formData.billingCycles[0]) {
        toast({
          title: 'Error',
          description: 'Please select a product and configure pricing',
          variant: 'destructive',
        });
        return;
      }

      const billingCycle = formData.billingCycles[0];
      const unitAmount = Math.round(parseFloat(billingCycle.pricing_scheme.fixed_price.value) * 100); // Convert to cents

      const response = await axios.post(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-plans/prices`, {
        product: formData.productId,
        unit_amount: unitAmount,
        currency: billingCycle.pricing_scheme.fixed_price.currency_code.toLowerCase(),
        recurring: {
          interval: billingCycle.frequency.interval_unit.toLowerCase(),
          interval_count: billingCycle.frequency.interval_count
        },
        nickname: formData.name
      }, {
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        }
      });

      toast({
        title: 'Success',
        description: 'Stripe price created successfully',
      });

      setCreateDialogOpen(false);
      fetchStripePrices();

      // Reset form
      setFormData({
        productId: '',
        tier: 'Starter',
        name: formatPlanName('Starter'),
        description: formatPlanDescription('Starter'),
        billingCycles: [
          {
            frequency: {
              interval_unit: 'MONTH',
              interval_count: 1,
            },
            tenure_type: 'REGULAR',
            sequence: 1,
            total_cycles: 0,
            pricing_scheme: {
              fixed_price: {
                value: '49.99',
                currency_code: 'USD',
              },
            },
          },
        ],
      });
    } catch (error) {
      console.error('Error creating Stripe price:', error);
      toast({
        title: 'Error',
        description: 'Failed to create Stripe price',
        variant: 'destructive',
      });
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Subscription Plans</h2>
          <p className="text-gray-500">Manage your PayPal and Stripe subscription plans</p>
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            className="border-gray-200 text-gray-700 hover:bg-gray-100"
            onClick={activeProvider === 'paypal' ? fetchPlans : fetchStripePrices}
            disabled={loading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>

          <Dialog open={createProductDialogOpen} onOpenChange={setCreateProductDialogOpen}>
            <DialogTrigger asChild>
              <Button variant="outline" className="border-gray-200 text-gray-700 hover:bg-gray-100">
                <Plus className="mr-2 h-4 w-4" />
                New Product
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[550px]">
              <DialogHeader>
                <DialogTitle>Create New Product</DialogTitle>
                <DialogDescription>
                  Create a new product for your subscription plans
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="product-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="product-name"
                    value={productFormData.name}
                    onChange={(e) => setProductFormData({ ...productFormData, name: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="product-description" className="text-right">
                    Description
                  </Label>
                  <Input
                    id="product-description"
                    value={productFormData.description}
                    onChange={(e) => setProductFormData({ ...productFormData, description: e.target.value })}
                    className="col-span-3"
                  />
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setCreateProductDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={activeProvider === 'paypal' ? createProduct : createStripeProduct}>
                  Create Product
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                New Plan
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Create New Subscription Plan</DialogTitle>
                <DialogDescription>
                  Create a new subscription plan for your customers
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="product-id" className="text-right">
                    Product
                  </Label>
                  <Select
                    value={formData.productId}
                    onValueChange={(value) => setFormData({ ...formData, productId: value })}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select a product" />
                    </SelectTrigger>
                    <SelectContent>
                      {products.map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="plan-tier" className="text-right">
                    Tier
                  </Label>
                  <Select
                    value={formData.tier}
                    onValueChange={(value) => {
                      setFormData({
                        ...formData,
                        tier: value,
                        name: formatPlanName(value),
                        description: formatPlanDescription(value),
                        billingCycles: [
                          {
                            ...formData.billingCycles[0],
                            pricing_scheme: {
                              fixed_price: {
                                value: value === 'Starter' ? '49.99' : value === 'Growth' ? '99.99' : '199.99',
                                currency_code: formData.billingCycles[0].pricing_scheme.fixed_price.currency_code,
                              },
                            },
                          },
                        ],
                      });
                    }}
                  >
                    <SelectTrigger className="col-span-3">
                      <SelectValue placeholder="Select a tier" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Starter">Starter</SelectItem>
                      <SelectItem value="Growth">Growth</SelectItem>
                      <SelectItem value="Pro">Pro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="plan-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="plan-name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="plan-description" className="text-right">
                    Description
                  </Label>
                  <Input
                    id="plan-description"
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="col-span-3"
                  />
                </div>

                <Separator className="my-2" />
                <h3 className="text-lg font-medium">Billing Cycle</h3>

                {formData.billingCycles.map((cycle, index) => (
                  <div key={index} className="space-y-4 border p-4 rounded-md">
                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor={`price-${index}`} className="text-right">
                        Price
                      </Label>
                      <div className="col-span-3 flex items-center gap-2">
                        <span className="text-gray-500">$</span>
                        <Input
                          id={`price-${index}`}
                          type="number"
                          step="0.01"
                          value={cycle.pricing_scheme.fixed_price.value}
                          onChange={(e) => updateBillingCyclePrice(index, e.target.value)}
                        />
                        <Select
                          value={cycle.pricing_scheme.fixed_price.currency_code}
                          onValueChange={(value) => {
                            const updatedBillingCycles = [...formData.billingCycles];
                            updatedBillingCycles[index].pricing_scheme.fixed_price.currency_code = value;
                            setFormData({
                              ...formData,
                              billingCycles: updatedBillingCycles,
                            });
                          }}
                        >
                          <SelectTrigger className="w-24">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="grid grid-cols-4 items-center gap-4">
                      <Label htmlFor={`interval-${index}`} className="text-right">
                        Billing Interval
                      </Label>
                      <div className="col-span-3 flex items-center gap-2">
                        <Input
                          id={`interval-${index}`}
                          type="number"
                          min="1"
                          value={cycle.frequency.interval_count}
                          onChange={(e) => updateBillingCycleIntervalCount(index, parseInt(e.target.value))}
                          className="w-20"
                        />
                        <Select
                          value={cycle.frequency.interval_unit}
                          onValueChange={(value) => updateBillingCycleInterval(index, value)}
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DAY">Day(s)</SelectItem>
                            <SelectItem value="WEEK">Week(s)</SelectItem>
                            <SelectItem value="MONTH">Month(s)</SelectItem>
                            <SelectItem value="YEAR">Year(s)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={activeProvider === 'paypal' ? createPlan : createStripePrice}>
                  Create {activeProvider === 'paypal' ? 'Plan' : 'Price'}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Edit Subscription Plan</DialogTitle>
                <DialogDescription>
                  Update the plan details including pricing and billing
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-plan-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="edit-plan-name"
                    value={editFormData.name}
                    onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
                    className="col-span-3"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="edit-plan-description" className="text-right">
                    Description
                  </Label>
                  <Input
                    id="edit-plan-description"
                    value={editFormData.description}
                    onChange={(e) => setEditFormData({ ...editFormData, description: e.target.value })}
                    className="col-span-3"
                  />
                </div>

                {editFormData.billingCycles.length > 0 && (
                  <>
                    <Separator className="my-2" />
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-medium">Billing Cycle</h3>
                    </div>

                    <Alert>
                      <AlertCircle className="h-4 w-4" />
                      <AlertTitle>PayPal Pricing Update</AlertTitle>
                      <AlertDescription>
                        You can update the price of a plan, but billing intervals cannot be changed for existing plans.
                      </AlertDescription>
                    </Alert>

                    {editFormData.billingCycles.map((cycle, index) => (
                      <div key={index} className="space-y-4 border p-4 rounded-md">
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor={`edit-price-${index}`} className="text-right">
                            Price
                          </Label>
                          <div className="col-span-3 flex items-center gap-2">
                            <span className="text-gray-500">$</span>
                            <Input
                              id={`edit-price-${index}`}
                              type="number"
                              step="0.01"
                              value={cycle.pricing_scheme.fixed_price.value}
                              onChange={(e) => updateEditBillingCyclePrice(index, e.target.value)}
                            />
                            <Select
                              value={cycle.pricing_scheme.fixed_price.currency_code}
                              onValueChange={(value) => updateEditBillingCycleCurrency(index, value)}
                            >
                              <SelectTrigger className="w-24">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="USD">USD</SelectItem>
                                <SelectItem value="EUR">EUR</SelectItem>
                                <SelectItem value="GBP">GBP</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label htmlFor={`edit-interval-${index}`} className="text-right text-gray-600">
                            Billing Interval
                          </Label>
                          <div className="col-span-3 flex items-center gap-2">
                            <Input
                              id={`edit-interval-${index}`}
                              type="number"
                              min="1"
                              value={cycle.frequency.interval_count}
                              disabled
                              className="w-20 bg-gray-100"
                            />
                            <Input
                              value={cycle.frequency.interval_unit.toLowerCase() + '(s)'}
                              disabled
                              className="w-32 bg-gray-100"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={updatePlan}>Update Plan</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Provider Selection Tabs */}
      <Tabs value={activeProvider} onValueChange={(value) => setActiveProvider(value as 'paypal' | 'stripe')}>
        <TabsList className="grid w-full grid-cols-2 max-w-md">
          <TabsTrigger value="paypal" className="flex items-center gap-2">
            <svg className="h-4 w-4" viewBox="0 0 24 24" fill="currentColor">
              <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-1.12 7.106zm14.146-14.42a3.35 3.35 0 0 0-.607-.541c-.013.076-.026.175-.041.26-.93 4.778-4.005 7.201-9.138 7.201h-2.19a.9.9 0 0 0-.633.26.851.851 0 0 0-.24.9l-.72 4.56-.229 1.452a.566.566 0 0 0 .633.66h3.722a.641.641 0 0 0 .633-.74l.131-.828.81-5.123.052-.283a.641.641 0 0 1 .633-.74h.398c3.66 0 6.526-1.49 7.36-5.8.348-1.8.166-3.3-.698-4.478z"/>
            </svg>
            PayPal
          </TabsTrigger>
          <TabsTrigger value="stripe" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Stripe
          </TabsTrigger>
        </TabsList>
      </Tabs>

      <Tabs defaultValue="plans">
        <TabsList>
          <TabsTrigger value="plans">{activeProvider === 'paypal' ? 'Plans' : 'Prices'}</TabsTrigger>
          <TabsTrigger value="products">Products</TabsTrigger>
        </TabsList>
        <TabsContent value="plans" className="mt-6">
          <Card>
            <CardContent className="p-6">
              {loading ? (
                <div className="flex justify-center items-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : (activeProvider === 'paypal' ? plans : stripePrices).length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No {activeProvider === 'paypal' ? 'subscription plans' : 'prices'} found</p>
                  <Button className="mt-4" onClick={() => setCreateDialogOpen(true)}>
                    Create Your First {activeProvider === 'paypal' ? 'Plan' : 'Price'}
                  </Button>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>{activeProvider === 'paypal' ? 'Plan ID' : 'Price ID'}</TableHead>
                        <TableHead>{activeProvider === 'paypal' ? 'Description' : 'Amount'}</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {(activeProvider === 'paypal' ? plans : stripePrices).map((item) => (
                        <TableRow key={item.id}>
                          <TableCell className="font-medium">
                            {activeProvider === 'paypal'
                              ? (item as Plan).name
                              : (item as StripePrice).nickname || 'Unnamed Price'}
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <code className="bg-gray-100 px-2 py-1 rounded text-xs font-mono">{item.id}</code>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => {
                                  navigator.clipboard.writeText(item.id);
                                  toast({
                                    title: "Copied!",
                                    description: `${activeProvider === 'paypal' ? 'Plan' : 'Price'} ID copied to clipboard`,
                                    duration: 2000,
                                  });
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell>
                            {activeProvider === 'paypal'
                              ? (item as Plan).description
                              : `${((item as StripePrice).unit_amount / 100).toFixed(2)} ${(item as StripePrice).currency.toUpperCase()}`}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                activeProvider === 'paypal'
                                  ? (item as Plan).status === 'ACTIVE' ? 'success' : 'secondary'
                                  : (item as StripePrice).active ? 'success' : 'secondary'
                              }
                            >
                              {activeProvider === 'paypal'
                                ? (item as Plan).status
                                : (item as StripePrice).active ? 'ACTIVE' : 'INACTIVE'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {activeProvider === 'paypal'
                              ? new Date((item as Plan).create_time).toLocaleDateString()
                              : 'N/A'}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              {activeProvider === 'paypal' && (
                                <>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => openEditDialog(item as Plan)}
                                  >
                                    <Edit className="h-4 w-4" />
                                  </Button>
                                  {(item as Plan).status === 'ACTIVE' ? (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => deactivatePlan(item.id)}
                                    >
                                      Deactivate
                                    </Button>
                                  ) : (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      onClick={() => activatePlan(item.id)}
                                    >
                                      Activate
                                    </Button>
                                  )}
                                </>
                              )}
                              {activeProvider === 'stripe' && (
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    // For Stripe, we can copy the price ID
                                    navigator.clipboard.writeText(item.id);
                                    toast({
                                      title: "Copied!",
                                      description: "Price ID copied to clipboard",
                                      duration: 2000,
                                    });
                                  }}
                                >
                                  Copy ID
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
        <TabsContent value="products" className="mt-6">
          <Card>
            <CardContent className="p-6">
              {products.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-500">No products found</p>
                  <Button className="mt-4" onClick={() => setCreateProductDialogOpen(true)}>
                    Create Your First Product
                  </Button>
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Product ID</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Created</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {products.map((product) => (
                        <TableRow key={product.id}>
                          <TableCell className="font-medium">{product.name}</TableCell>
                          <TableCell>
                            <div className="flex items-center gap-2">
                              <code className="bg-gray-100 px-2 py-1 rounded text-xs font-mono">{product.id}</code>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="h-6 w-6"
                                onClick={() => {
                                  navigator.clipboard.writeText(product.id);
                                  toast({
                                    title: "Copied!",
                                    description: "Product ID copied to clipboard",
                                    duration: 2000,
                                  });
                                }}
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2"/><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2"/></svg>
                              </Button>
                            </div>
                          </TableCell>
                          <TableCell>{product.description}</TableCell>
                          <TableCell>{product.type}</TableCell>
                          <TableCell>{new Date(product.create_time).toLocaleDateString()}</TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SubscriptionPlanManager;
