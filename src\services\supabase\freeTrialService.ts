import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

/**
 * Check if the user has used their free trial
 */
export const hasUsedFreeTrial = async (userId: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('free_trial_used')
        .eq('user_id', userId)
        .single();

      if (error) throw error;
      return data?.free_trial_used || false;
    },
    `Failed to check free trial status for user ${userId}`,
    false
  );
};

/**
 * Mark the user's free trial as used
 */
export const markFreeTrialAsUsed = async (userId: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('profiles')
        .update({ free_trial_used: true })
        .eq('user_id', userId);

      if (error) throw error;
      return true;
    },
    `Failed to mark free trial as used for user ${userId}`,
    false
  );
};
