import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useCompanyContext } from '@/contexts/CompanyContext';
import {
  Users,
  UserPlus,
  Mail,
  Edit,
  Trash2,
  Search,
  Shield,
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowUpRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { useTeamMembers, useInviteTeamMember, useDeleteTeamMember, useTeamMemberLimit, useUpdateTeamMember } from '@/hooks/use-team';
import { useAuth } from '@/contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import { EnhancedTeamMember } from '@/services/supabase/team';

// Role descriptions
const roleDescriptions = {
  admin: 'Full access to all features and settings',
  recruiter: 'Can manage jobs, candidates, and evaluations',
  viewer: 'Read-only access to jobs and candidates'
};

const TeamManagement = () => {
  const { toast } = useToast();
  const { user } = useAuth();
  const navigate = useNavigate();

  // Get the active company ID from context
  const { activeCompanyId } = useCompanyContext();

  // Team member hooks
  const { data: teamMembers = [], isLoading: isLoadingTeamMembers } = useTeamMembers(activeCompanyId || '');
  const inviteTeamMemberMutation = useInviteTeamMember();
  const updateTeamMemberMutation = useUpdateTeamMember();
  const deleteTeamMemberMutation = useDeleteTeamMember();
  const { data: teamMemberLimit, isLoading: isLoadingLimit } = useTeamMemberLimit();

  const [searchQuery, setSearchQuery] = useState('');
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<EnhancedTeamMember | null>(null);
  const [newMember, setNewMember] = useState({
    name: '',
    email: '',
    role: 'recruiter'
  });

  // Check if team member limit is reached
  const isLimitReached = teamMemberLimit &&
    teamMemberLimit.currentUsage >= teamMemberLimit.limit &&
    teamMemberLimit.limit !== Infinity;

  // Filter team members based on search query
  const filteredMembers = teamMembers.filter(member =>
    (member.profiles?.full_name || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    (member.profiles?.email || '').toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.role.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle new member input change
  const handleNewMemberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewMember(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle role selection change
  const handleRoleChange = (value: string) => {
    setNewMember(prev => ({
      ...prev,
      role: value
    }));
  };

  // Handle edit member
  const handleEditMember = (member: any) => {
    setSelectedMember(member);
    setIsEditDialogOpen(true);
  };

  // Handle delete member
  const handleDeleteMember = (member: any) => {
    setSelectedMember(member);
    setIsDeleteDialogOpen(true);
  };

  // Add new team member
  const addTeamMember = async () => {
    if (!newMember.name || !newMember.email) {
      toast({
        title: 'Missing information',
        description: 'Please provide both name and email for the new team member.',
        variant: 'destructive',
      });
      return;
    }

    if (!activeCompanyId) {
      toast({
        title: 'No active company',
        description: 'Please select an active company before inviting team members.',
        variant: 'destructive',
      });
      return;
    }

    // Check if limit is reached
    if (isLimitReached) {
      toast({
        title: 'Team member limit reached',
        description: 'You have reached your team member limit. Please upgrade your plan to invite more team members.',
        variant: 'destructive',
      });
      return;
    }

    try {
      await inviteTeamMemberMutation.mutateAsync({
        user_id: null, // This will be set when the invitation is accepted
        company_id: activeCompanyId,
        role: newMember.role,
        status: 'pending',
        invited_by: user?.id || '',
        name: newMember.name,
        email: newMember.email
      });

      // Reset form and close dialog
      setNewMember({
        name: '',
        email: '',
        role: 'recruiter'
      });
      setIsAddDialogOpen(false);

      toast({
        title: 'Invitation sent',
        description: `An invitation has been sent to ${newMember.email}.`,
      });
    } catch (error: any) {
      toast({
        title: 'Failed to invite team member',
        description: error.message || 'An error occurred while inviting the team member.',
        variant: 'destructive',
      });
    }
  };

  // Update team member
  const updateTeamMember = async () => {
    if (!selectedMember) return;

    try {
      await updateTeamMemberMutation.mutateAsync({
        id: selectedMember.id,
        teamMember: {
          role: selectedMember.role,
          status: selectedMember.status
        }
      });

      setIsEditDialogOpen(false);

      toast({
        title: 'Member updated',
        description: `Team member's information has been updated.`,
      });
    } catch (error: any) {
      toast({
        title: 'Failed to update team member',
        description: error.message || 'An error occurred while updating the team member.',
        variant: 'destructive',
      });
    }
  };

  // Delete team member
  const deleteTeamMember = async () => {
    if (!selectedMember) return;

    try {
      await deleteTeamMemberMutation.mutateAsync({
        id: selectedMember.id,
        companyId: activeCompanyId || ''
      });

      setIsDeleteDialogOpen(false);

      toast({
        title: 'Member removed',
        description: `Team member has been removed from the team.`,
      });
    } catch (error: any) {
      toast({
        title: 'Failed to remove team member',
        description: error.message || 'An error occurred while removing the team member.',
        variant: 'destructive',
      });
    }
  };

  // Format last active time
  const formatLastActive = (lastActive: string | null) => {
    if (!lastActive) return 'Never';

    try {
      const date = new Date(lastActive);
      return date.toLocaleString();
    } catch (error) {
      return 'Unknown';
    }
  };

  // Get status badge color
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-500/20 text-green-500 hover:bg-green-500/30">Active</Badge>;
      case 'pending':
        return <Badge className="bg-amber-500/20 text-amber-500 hover:bg-amber-500/30">Pending</Badge>;
      case 'inactive':
        return <Badge className="bg-gray-500/20 text-gray-400 hover:bg-gray-500/30">Inactive</Badge>;
      default:
        return <Badge className="bg-gray-500/20 text-gray-400 hover:bg-gray-500/30">{status}</Badge>;
    }
  };

  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-4 w-4 text-purple-500" />;
      case 'recruiter':
        return <Users className="h-4 w-4 text-blue-500" />;
      case 'viewer':
        return <Search className="h-4 w-4 text-green-500" />;
      default:
        return <Users className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Team Management</h1>
            {activeCompanyId && (
              <p className="text-gray-600 text-sm mt-1">
                Manage team members for your company
              </p>
            )}
          </div>

          {!activeCompanyId && (
            <Alert className="bg-amber-50 border-amber-200 text-amber-800 mb-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Please select an active company to manage team members.</span>
                <Button
                  variant="outline"
                  size="sm"
                  className="ml-4 border-amber-300 text-amber-800 hover:bg-amber-100"
                  onClick={() => navigate('/dashboard/companies')}
                >
                  Select Company
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Usage information */}
          {teamMemberLimit && (
            <Card className="bg-white border-gray-200 shadow-sm mb-4 w-full">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium text-gray-800">Team Members</h3>
                    <p className="text-sm text-gray-600">
                      {teamMemberLimit.limit === Infinity ? (
                        <>
                          You are using {teamMemberLimit.currentUsage} team member{teamMemberLimit.currentUsage !== 1 ? 's' : ''} (unlimited)
                        </>
                      ) : (
                        <>
                          You are using {teamMemberLimit.currentUsage} of {teamMemberLimit.limit} team member{teamMemberLimit.limit !== 1 ? 's' : ''}
                        </>
                      )}
                    </p>
                  </div>
                  {teamMemberLimit.limit !== Infinity && (
                    <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div
                        className={`h-full rounded-full ${isLimitReached ? 'bg-red-500' : 'bg-primary-gradient'}`}
                        style={{ width: `${Math.min(100, (teamMemberLimit.currentUsage / teamMemberLimit.limit) * 100)}%` }}
                      ></div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}

          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button
                className="bg-recruiter-lightblue hover:bg-blue-500"
                disabled={isLimitReached || !activeCompanyId}
                title={
                  isLimitReached
                    ? 'You have reached your team member limit'
                    : !activeCompanyId
                      ? 'Please select an active company first'
                      : 'Invite a new team member'
                }
              >
                <UserPlus className="mr-2 h-4 w-4" /> Invite Team Member
              </Button>
            </DialogTrigger>
            <DialogContent className="bg-white border-gray-200 text-gray-800">
              <DialogHeader>
                <DialogTitle className="text-gray-800">Invite Team Member</DialogTitle>
                <DialogDescription className="text-gray-500">
                  Send an invitation to join your team.
                </DialogDescription>
              </DialogHeader>

              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="name" className="text-gray-700">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={newMember.name}
                    onChange={handleNewMemberChange}
                    placeholder="Enter name"
                    className="bg-white border-gray-200 text-gray-800"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="text-gray-700">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={newMember.email}
                    onChange={handleNewMemberChange}
                    placeholder="Enter email"
                    className="bg-white border-gray-200 text-gray-800"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="role" className="text-gray-700">Role</Label>
                  <Select value={newMember.role} onValueChange={handleRoleChange}>
                    <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent className="bg-white border-gray-200 text-gray-800">
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="recruiter">Recruiter</SelectItem>
                      <SelectItem value="viewer">Viewer</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-gray-500 text-sm mt-1">
                    {roleDescriptions[newMember.role as keyof typeof roleDescriptions]}
                  </p>
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  className="border-gray-200 text-gray-700 hover:bg-gray-100"
                  onClick={() => setIsAddDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  className="bg-primary hover:bg-primary/90"
                  onClick={addTeamMember}
                  disabled={isLimitReached || inviteTeamMemberMutation.isPending}
                >
                  {inviteTeamMemberMutation.isPending ? (
                    <div className="flex items-center">
                      <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                      Sending...
                    </div>
                  ) : (
                    'Send Invitation'
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>

        {isLimitReached && (
          <Alert className="bg-red-50 border-red-200 text-red-800 mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Team member limit reached</AlertTitle>
            <AlertDescription className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
              <span>You have reached your team member limit of {teamMemberLimit?.limit}. Upgrade your plan to invite more team members.</span>
              <Button
                variant="outline"
                size="sm"
                className="border-red-300 text-red-800 hover:bg-red-100"
                onClick={() => navigate('/dashboard/billing')}
              >
                <ArrowUpRight className="mr-2 h-4 w-4" /> Upgrade Plan
              </Button>
            </AlertDescription>
          </Alert>
        )}

        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg">Team Members</CardTitle>
            {activeCompanyId && (
              <CardDescription className="text-gray-500">
                Manage your team members and their access levels
              </CardDescription>
            )}
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search team members..."
                  className="bg-white border-gray-200 text-gray-800 pl-10"
                  value={searchQuery}
                  onChange={handleSearchChange}
                />
              </div>

              {isLoadingTeamMembers ? (
                <div className="flex flex-col items-center justify-center py-8">
                  <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full mb-4"></div>
                  <p className="text-gray-500">Loading team members...</p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 text-gray-500 font-medium">Name</th>
                        <th className="text-left py-3 px-4 text-gray-500 font-medium">Email</th>
                        <th className="text-left py-3 px-4 text-gray-500 font-medium">Role</th>
                        <th className="text-left py-3 px-4 text-gray-500 font-medium">Status</th>
                        <th className="text-left py-3 px-4 text-gray-500 font-medium">Last Active</th>
                        <th className="text-right py-3 px-4 text-gray-500 font-medium">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredMembers.length === 0 ? (
                        <tr>
                          <td colSpan={6} className="text-center py-8 text-gray-400">
                            {activeCompanyId ? 'No team members found' : 'Please select a company to view team members'}
                          </td>
                        </tr>
                      ) : (
                      filteredMembers.map(member => (
                        <tr key={member.id} className="border-b border-gray-200 hover:bg-gray-50">
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              <Avatar className="h-8 w-8 mr-3">
                                <AvatarImage src={member.profiles?.avatar_url || ''} alt={member.profiles?.full_name || 'Team Member'} />
                                <AvatarFallback>
                                  {member.status === 'pending' && !member.profiles?.full_name
                                    ? ((member.metadata && member.metadata.name) || 'TM').charAt(0)
                                    : (member.profiles?.full_name || 'TM').charAt(0)}
                                </AvatarFallback>
                              </Avatar>
                              <span className="text-gray-800">
                                {member.status === 'pending' && !member.profiles?.full_name
                                  ? ((member.metadata && member.metadata.name) || 'Invited User')
                                  : (member.profiles?.full_name || 'Unknown User')}
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-gray-600">
                            {member.status === 'pending' && !member.profiles?.email
                              ? ((member.metadata && member.metadata.email) || 'No email')
                              : (member.profiles?.email || 'No email')}
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex items-center">
                              {getRoleIcon(member.role)}
                              <span className="ml-2 text-gray-600 capitalize">{member.role}</span>
                            </div>
                          </td>
                          <td className="py-3 px-4">
                            {getStatusBadge(member.status)}
                          </td>
                          <td className="py-3 px-4 text-gray-600">
                            {formatLastActive(member.profiles?.last_sign_in_at)}
                          </td>
                          <td className="py-3 px-4 text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="icon"
                                className="text-gray-500 hover:text-primary hover:bg-gray-100"
                                onClick={() => handleEditMember(member)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="icon"
                                className="text-gray-500 hover:text-red-500 hover:bg-gray-100"
                                onClick={() => handleDeleteMember(member)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                  </table>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg">Pending Invitations</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoadingTeamMembers ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="animate-spin h-8 w-8 border-t-2 border-b-2 border-primary rounded-full mb-4"></div>
                <p className="text-gray-500">Loading invitations...</p>
              </div>
            ) : teamMembers.filter(member => member.status === 'pending').length === 0 ? (
              <div className="text-center py-8">
                <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800">No pending invitations</h3>
                <p className="text-gray-500 mt-1">
                  When you invite team members, they'll appear here until they accept.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {teamMembers
                  .filter(member => member.status === 'pending')
                  .map(member => (
                    <div
                      key={member.id}
                      className="bg-white p-4 rounded-lg border border-gray-200 flex flex-col sm:flex-row sm:items-center justify-between gap-4 hover:shadow-sm transition-all duration-200"
                    >
                      <div className="flex items-center">
                        <Avatar className="h-10 w-10 mr-3">
                          <AvatarImage src={member.profiles?.avatar_url || ''} alt={member.profiles?.full_name || 'Team Member'} />
                          <AvatarFallback>
                            {member.status === 'pending' && !member.profiles?.full_name
                              ? ((member.metadata && member.metadata.name) || 'TM').charAt(0)
                              : (member.profiles?.full_name || 'TM').charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <h3 className="text-gray-800 font-medium">
                            {member.status === 'pending' && !member.profiles?.full_name
                              ? ((member.metadata && member.metadata.name) || 'Invited User')
                              : (member.profiles?.full_name || 'Unknown User')}
                          </h3>
                          <p className="text-gray-500 text-sm">
                            {member.status === 'pending' && !member.profiles?.email
                              ? ((member.metadata && member.metadata.email) || 'No email')
                              : (member.profiles?.email || 'No email')}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center">
                        <Badge className="bg-amber-500/20 text-amber-500 hover:bg-amber-500/30 mr-4">
                          Pending
                        </Badge>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-gray-200 text-gray-700 hover:bg-gray-100"
                            onClick={() => {
                              // Resend invitation logic
                              toast({
                                title: 'Invitation resent',
                                description: `A new invitation has been sent to ${member.profiles?.email || (member.metadata && member.metadata.email) || 'the user'}.`,
                              });
                            }}
                          >
                            Resend
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            className="border-gray-200 text-gray-700 hover:bg-gray-100"
                            onClick={() => handleDeleteMember(member)}
                          >
                            Cancel
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))
                }
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Edit Member Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="bg-white border-gray-200 text-gray-800">
          <DialogHeader>
            <DialogTitle className="text-gray-800">Edit Team Member</DialogTitle>
            <DialogDescription className="text-gray-500">
              Update team member information and permissions.
            </DialogDescription>
          </DialogHeader>

          {selectedMember && (
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name" className="text-gray-700">Name (Read Only)</Label>
                <Input
                  id="edit-name"
                  value={
                    selectedMember.status === 'pending' && !selectedMember.profiles?.full_name
                      ? ((selectedMember.metadata && selectedMember.metadata.name) || 'Invited User')
                      : (selectedMember.profiles?.full_name || 'Unknown User')
                  }
                  disabled
                  className="bg-gray-50 border-gray-200 text-gray-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-email" className="text-gray-700">Email (Read Only)</Label>
                <Input
                  id="edit-email"
                  type="email"
                  value={
                    selectedMember.status === 'pending' && !selectedMember.profiles?.email
                      ? ((selectedMember.metadata && selectedMember.metadata.email) || 'No email')
                      : (selectedMember.profiles?.email || 'No email')
                  }
                  disabled
                  className="bg-gray-50 border-gray-200 text-gray-500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-role" className="text-gray-700">Role</Label>
                <Select
                  value={selectedMember.role}
                  onValueChange={(value) => setSelectedMember({...selectedMember, role: value})}
                >
                  <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200 text-gray-800">
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="recruiter">Recruiter</SelectItem>
                    <SelectItem value="viewer">Viewer</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-gray-500 text-sm mt-1">
                  {roleDescriptions[selectedMember.role as keyof typeof roleDescriptions]}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-status" className="text-gray-700">Status</Label>
                <Select
                  value={selectedMember.status}
                  onValueChange={(value) => setSelectedMember({...selectedMember, status: value})}
                >
                  <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-gray-200 text-gray-800">
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
              onClick={() => setIsEditDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              className="bg-primary hover:bg-primary/90"
              onClick={updateTeamMember}
            >
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Member Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="bg-white border-gray-200 text-gray-800">
          <DialogHeader>
            <DialogTitle className="text-gray-800">Remove Team Member</DialogTitle>
            <DialogDescription className="text-gray-500">
              Are you sure you want to remove this team member? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          {selectedMember && (
            <div className="py-4">
              <div className="flex items-center p-4 bg-gray-50 rounded-lg border border-gray-200">
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src={selectedMember.profiles?.avatar_url || ''} alt={selectedMember.profiles?.full_name || 'Team Member'} />
                  <AvatarFallback>
                    {selectedMember.status === 'pending' && !selectedMember.profiles?.full_name
                      ? ((selectedMember.metadata && selectedMember.metadata.name) || 'TM').charAt(0)
                      : (selectedMember.profiles?.full_name || 'TM').charAt(0)}
                  </AvatarFallback>
                </Avatar>
                <div>
                  <h3 className="text-gray-800 font-medium">
                    {selectedMember.status === 'pending' && !selectedMember.profiles?.full_name
                      ? ((selectedMember.metadata && selectedMember.metadata.name) || 'Invited User')
                      : (selectedMember.profiles?.full_name || 'Unknown User')}
                  </h3>
                  <p className="text-gray-500 text-sm">
                    {selectedMember.status === 'pending' && !selectedMember.profiles?.email
                      ? ((selectedMember.metadata && selectedMember.metadata.email) || 'No email')
                      : (selectedMember.profiles?.email || 'No email')}
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button
              variant="outline"
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={deleteTeamMember}
            >
              Remove
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

export default TeamManagement;


