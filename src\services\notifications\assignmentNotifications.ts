import { supabase } from '@/lib/supabase';
import { createNotification } from '@/services/supabase/notifications';
import { sendEmail } from '@/services/email/emailService';
import { AssignmentData } from '@/services/supabase/assignments';

/**
 * Notification interfaces
 */
export interface AssignmentNotificationData {
  candidateId: string;
  candidateName: string;
  assignments: AssignmentData[];
  assignedBy: string;
  assignedByName?: string;
}

export interface NotificationPreferences {
  emailNotifications: boolean;
  inAppNotifications: boolean;
  assignmentNotifications: boolean;
  statusChangeNotifications: boolean;
}

/**
 * Send notifications when candidates are assigned to companies
 */
export const sendAssignmentNotifications = async (
  notificationData: AssignmentNotificationData
): Promise<void> => {
  try {
    console.log('📧 Sending assignment notifications for:', notificationData.candidateName);

    // Group assignments by company to avoid duplicate notifications
    const companiesMap = new Map<string, AssignmentData[]>();
    
    notificationData.assignments.forEach(assignment => {
      const companyId = assignment.companyId;
      if (!companiesMap.has(companyId)) {
        companiesMap.set(companyId, []);
      }
      companiesMap.get(companyId)!.push(assignment);
    });

    // Send notifications to each company
    for (const [companyId, companyAssignments] of companiesMap) {
      await sendCompanyAssignmentNotification(
        companyId,
        notificationData.candidateId,
        notificationData.candidateName,
        companyAssignments,
        notificationData.assignedBy,
        notificationData.assignedByName
      );
    }

    console.log('✅ Assignment notifications sent successfully');
  } catch (error) {
    console.error('❌ Failed to send assignment notifications:', error);
    // Don't throw - notification failure shouldn't break the assignment process
  }
};

/**
 * Send notification to a specific company about new candidate assignment
 */
const sendCompanyAssignmentNotification = async (
  companyId: string,
  candidateId: string,
  candidateName: string,
  assignments: AssignmentData[],
  assignedBy: string,
  assignedByName?: string
): Promise<void> => {
  try {
    // Get company details and owner
    const { data: company, error: companyError } = await supabase
      .from('companies')
      .select(`
        id,
        name,
        user_id,
        profiles:user_id (
          full_name,
          user_id
        )
      `)
      .eq('id', companyId)
      .single();

    if (companyError || !company) {
      console.error('Company not found:', companyId);
      return;
    }

    // Get user's notification preferences
    const preferences = await getUserNotificationPreferences(company.user_id);
    
    if (!preferences.assignmentNotifications) {
      console.log('Assignment notifications disabled for user:', company.user_id);
      return;
    }

    // Create job titles list
    const jobTitles = assignments.map(a => a.jobTitle).join(', ');
    const jobCount = assignments.length;

    // Send in-app notification
    if (preferences.inAppNotifications) {
      await createNotification({
        user_id: company.user_id,
        title: 'New Candidate Assignment',
        message: `${candidateName} has been assigned to ${jobCount} job${jobCount > 1 ? 's' : ''}: ${jobTitles}`,
        type: 'info',
        link: `/dashboard/cvs/${candidateId}`
      });
    }

    // Send email notification
    if (preferences.emailNotifications) {
      await sendAssignmentEmail(
        company,
        candidateId,
        candidateName,
        assignments,
        assignedByName || 'System'
      );
    }

    console.log(`✅ Notifications sent to company: ${company.name}`);
  } catch (error) {
    console.error(`❌ Failed to send notification to company ${companyId}:`, error);
  }
};

/**
 * Send assignment email notification
 */
const sendAssignmentEmail = async (
  company: any,
  candidateId: string,
  candidateName: string,
  assignments: AssignmentData[],
  assignedByName: string
): Promise<void> => {
  try {
    // Get user email
    const { data: user, error: userError } = await supabase.auth.admin.getUserById(company.user_id);
    
    if (userError || !user.user?.email) {
      console.error('User email not found for company owner:', company.user_id);
      return;
    }

    const jobCount = assignments.length;
    const jobTitles = assignments.map(a => a.jobTitle).join(', ');
    const avgMatchScore = Math.round(
      assignments.reduce((sum, a) => sum + a.matchScore, 0) / assignments.length
    );

    const emailHtml = generateAssignmentEmailHTML(
      company.name,
      candidateName,
      assignments,
      avgMatchScore,
      assignedByName,
      candidateId
    );

    await sendEmail({
      to: user.user.email,
      subject: `New Candidate Assignment: ${candidateName} - ${company.name}`,
      html: emailHtml
    });

    console.log(`📧 Assignment email sent to: ${user.user.email}`);
  } catch (error) {
    console.error('Failed to send assignment email:', error);
  }
};

/**
 * Generate HTML for assignment email
 */
const generateAssignmentEmailHTML = (
  companyName: string,
  candidateName: string,
  assignments: AssignmentData[],
  avgMatchScore: number,
  assignedByName: string,
  candidateId: string
): string => {
  const jobRows = assignments.map(assignment => `
    <tr>
      <td style="padding: 8px; border-bottom: 1px solid #eee;">${assignment.jobTitle}</td>
      <td style="padding: 8px; border-bottom: 1px solid #eee; text-align: center;">${assignment.matchScore}%</td>
    </tr>
  `).join('');

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>New Candidate Assignment</title>
    </head>
    <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
      <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h1 style="color: #2563eb; margin: 0;">New Candidate Assignment</h1>
          <p style="margin: 10px 0 0 0; color: #666;">Sourcio.ai Platform</p>
        </div>

        <div style="background: white; padding: 20px; border-radius: 8px; border: 1px solid #e5e7eb;">
          <h2 style="color: #1f2937; margin-top: 0;">Hello ${companyName} Team,</h2>
          
          <p>A new candidate has been assigned to your job openings:</p>
          
          <div style="background: #f3f4f6; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <h3 style="margin: 0 0 10px 0; color: #374151;">Candidate Details</h3>
            <p style="margin: 5px 0;"><strong>Name:</strong> ${candidateName}</p>
            <p style="margin: 5px 0;"><strong>Average Match Score:</strong> <span style="color: ${avgMatchScore >= 80 ? '#059669' : avgMatchScore >= 70 ? '#d97706' : '#dc2626'}; font-weight: bold;">${avgMatchScore}%</span></p>
            <p style="margin: 5px 0;"><strong>Assigned by:</strong> ${assignedByName}</p>
          </div>

          <h3 style="color: #374151;">Job Assignments</h3>
          <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
            <thead>
              <tr style="background: #f9fafb;">
                <th style="padding: 12px; text-align: left; border-bottom: 2px solid #e5e7eb;">Job Title</th>
                <th style="padding: 12px; text-align: center; border-bottom: 2px solid #e5e7eb;">Match Score</th>
              </tr>
            </thead>
            <tbody>
              ${jobRows}
            </tbody>
          </table>

          <div style="margin: 30px 0; text-align: center;">
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/dashboard/cvs/${candidateId}" 
               style="background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
              View Candidate Profile
            </a>
          </div>

          <div style="background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin: 0; color: #92400e;">
              <strong>Next Steps:</strong> Review the candidate's profile, CV, and evaluation results. 
              You can update their status and schedule interviews directly from the platform.
            </p>
          </div>
        </div>

        <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
          <p style="color: #6b7280; font-size: 14px; margin: 0;">
            This email was sent by Sourcio.ai Platform<br>
            <a href="${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/dashboard/profile" style="color: #2563eb;">Manage notification preferences</a>
          </p>
        </div>
      </div>
    </body>
    </html>
  `;
};

/**
 * Get user notification preferences
 */
const getUserNotificationPreferences = async (userId: string): Promise<NotificationPreferences> => {
  try {
    const { data, error } = await supabase
      .from('notification_preferences')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error || !data) {
      // Return default preferences if none found
      return {
        emailNotifications: true,
        inAppNotifications: true,
        assignmentNotifications: true,
        statusChangeNotifications: true
      };
    }

    return {
      emailNotifications: data.email_notifications ?? true,
      inAppNotifications: data.in_app_notifications ?? true,
      assignmentNotifications: data.assignment_notifications ?? true,
      statusChangeNotifications: data.status_change_notifications ?? true
    };
  } catch (error) {
    console.error('Failed to fetch notification preferences:', error);
    // Return default preferences on error
    return {
      emailNotifications: true,
      inAppNotifications: true,
      assignmentNotifications: true,
      statusChangeNotifications: true
    };
  }
};

/**
 * Send bulk assignment summary notification
 */
export const sendBulkAssignmentSummary = async (
  assignedBy: string,
  totalAssignments: number,
  successfulAssignments: number,
  failedAssignments: number
): Promise<void> => {
  try {
    if (successfulAssignments > 0) {
      await createNotification({
        user_id: assignedBy,
        title: 'Bulk Assignment Complete',
        message: `Successfully assigned candidates to ${successfulAssignments} job${successfulAssignments > 1 ? 's' : ''}${failedAssignments > 0 ? `, ${failedAssignments} failed` : ''}`,
        type: failedAssignments > 0 ? 'warning' : 'success',
        link: '/dashboard/cvs'
      });
    }
  } catch (error) {
    console.error('Failed to send bulk assignment summary:', error);
  }
};
