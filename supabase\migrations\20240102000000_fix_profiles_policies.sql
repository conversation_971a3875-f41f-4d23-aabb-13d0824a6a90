-- First, drop all existing policies on the profiles table
DROP POLICY IF EXISTS "Platform admins can view all profiles" ON profiles;
DROP POLICY IF EXISTS "Users can view own profile" ON profiles;
DROP POLICY IF EXISTS "Platform admins can update any profile" ON profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON profiles;
DROP POLICY IF EXISTS "Public profiles are viewable by everyone." ON profiles;
DROP POLICY IF EXISTS "Users can insert their own profile." ON profiles;
DROP POLICY IF EXISTS "Users can update own profile." ON profiles;

-- Create a simpler policy structure that avoids recursion

-- 1. Allow anyone to read their own profile (no recursion)
CREATE POLICY "Users can read own profile"
ON profiles FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- 2. Allow users to update their own profile (no recursion)
CREATE POLICY "Users can update own profile"
ON profiles FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

-- 3. Allow users to insert their own profile (no recursion)
CREATE POLICY "Users can insert own profile"
ON profiles FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- 4. Create a special bypass policy for admins using a direct check
-- This avoids the recursion by not querying the profiles table again
CREATE POLICY "Admin bypass for all operations"
ON profiles FOR ALL
TO authenticated
USING (
  -- Check if the user's ID is in a list of known admin IDs
  -- Replace these IDs with your actual admin user IDs
  auth.uid() IN (
    '778d128d-606f-484e-b257-257390743df4'  -- Your admin ID
    -- Add more admin IDs here if needed, separated by commas
  )
);

-- Make sure RLS is enabled
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
