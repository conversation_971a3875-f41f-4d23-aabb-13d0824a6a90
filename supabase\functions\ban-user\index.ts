import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

serve(async (req) => {
  // Enable CORS
  if (req.method === 'OPTIONS') {
    return new Response('ok', {
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Authorization, Content-Type',
      }
    })
  }

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      return new Response(JSON.stringify({ error: 'Method not allowed' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 405,
      })
    }

    // Create a Supabase client with the service role key
    const supabaseAdmin = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SERVICE_ROLE_KEY') ?? '',
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
        }
      }
    )

    // Get the token from the Authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      return new Response(JSON.stringify({ error: 'Missing Authorization header' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    // Verify the user token
    const token = authHeader.replace('Bearer ', '')
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)

    if (authError || !user) {
      return new Response(JSON.stringify({ error: 'Unauthorized', details: authError }), {
        headers: { 'Content-Type': 'application/json' },
        status: 401,
      })
    }

    // Check if user is admin in profiles table
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('platform_admin')
      .eq('user_id', user.id)
      .single()

    if (profileError || !profile?.platform_admin) {
      return new Response(JSON.stringify({ error: 'Forbidden - Admin access required' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 403,
      })
    }

    // Get the user ID from the request body
    const { userId } = await req.json()

    if (!userId) {
      return new Response(JSON.stringify({ error: 'User ID is required' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 400,
      })
    }

    // Ban the user in Auth
    const { error: banError } = await supabaseAdmin.auth.admin.updateUserById(
      userId,
      { banned: true }
    )

    if (banError) {
      return new Response(JSON.stringify({ error: 'Failed to ban user', details: banError }), {
        headers: { 'Content-Type': 'application/json' },
        status: 500,
      })
    }

    // Also update the profiles table
    const { error: profileUpdateError } = await supabaseAdmin
      .from('profiles')
      .update({ banned: true })
      .eq('user_id', userId)

    if (profileUpdateError) {
      console.error('Failed to update profile banned status:', profileUpdateError)
      // Continue even if profile update fails
    }

    return new Response(JSON.stringify({ success: true, message: 'User banned successfully' }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      status: 200,
    })
  } catch (error) {
    return new Response(JSON.stringify({ error: 'Internal server error', details: error.message }), {
      headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
      },
      status: 500,
    })
  }
})
