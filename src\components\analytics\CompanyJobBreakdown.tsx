import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { CompanyJobEvaluation } from '@/services/supabase/dashboard';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Building, Briefcase, Search } from 'lucide-react';
import { Input } from '@/components/ui/input';

interface CompanyJobBreakdownProps {
  data: CompanyJobEvaluation[];
  title: string;
  description?: string;
}

export const CompanyJobBreakdown: React.FC<CompanyJobBreakdownProps> = ({
  data,
  title,
  description
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  
  // Filter data based on search term
  const filteredData = data.filter(item => 
    item.companyName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (item.jobTitle && item.jobTitle.toLowerCase().includes(searchTerm.toLowerCase()))
  );
  
  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 70) return 'text-blue-600';
    if (score >= 50) return 'text-amber-600';
    return 'text-red-600';
  };
  
  // Get progress color based on value
  const getProgressColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 70) return 'bg-blue-500';
    if (score >= 50) return 'bg-amber-500';
    return 'bg-red-500';
  };
  
  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader>
        <CardTitle className="text-gray-800 text-lg">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
        
        {/* Search input */}
        <div className="relative mt-2">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-500" />
          <Input
            placeholder="Search companies or jobs..."
            className="pl-8"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Company / Job</TableHead>
                <TableHead className="w-[100px] text-right">Evaluations</TableHead>
                <TableHead className="w-[180px] text-right">Avg. Score</TableHead>
                <TableHead className="w-[100px] text-right">Best Score</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredData.length > 0 ? (
                filteredData.map((item, index) => (
                  <TableRow key={index}>
                    <TableCell>
                      <div className="flex items-center">
                        {item.jobId ? (
                          <Briefcase className="mr-2 h-4 w-4 text-gray-500" />
                        ) : (
                          <Building className="mr-2 h-4 w-4 text-gray-500" />
                        )}
                        <div>
                          <div className="font-medium">
                            {item.jobTitle || item.companyName}
                          </div>
                          {item.jobTitle && (
                            <div className="text-sm text-gray-500">
                              {item.companyName}
                            </div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Badge variant="outline">{item.evaluationCount}</Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex flex-col items-end gap-1">
                        <span className={`text-sm font-medium ${getScoreColor(item.averageScore)}`}>
                          {item.averageScore}%
                        </span>
                        <Progress 
                          value={item.averageScore} 
                          className="h-2 w-[120px]" 
                          indicatorClassName={getProgressColor(item.averageScore)}
                        />
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <span className={`font-medium ${getScoreColor(item.highestScore)}`}>
                        {item.highestScore}%
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center py-6 text-gray-500">
                    {searchTerm ? 'No results found' : 'No evaluation data available'}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Summary */}
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4">
            <div>
              <p className="text-sm text-gray-500">Companies</p>
              <p className="text-xl font-semibold">
                {new Set(data.map(item => item.companyId)).size}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Jobs</p>
              <p className="text-xl font-semibold">
                {data.filter(item => item.jobId).length}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Total Evaluations</p>
              <p className="text-xl font-semibold">
                {data.reduce((sum, item) => sum + item.evaluationCount, 0)}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
