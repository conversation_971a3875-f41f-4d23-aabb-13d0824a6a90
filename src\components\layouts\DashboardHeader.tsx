import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import NotificationCenter from '@/components/notifications/NotificationCenter';
import CompanySwitcher from '@/components/company/CompanySwitcher';
import QuickActions from '@/components/dashboard/QuickActions';
import {
  Menu,
  ChevronDown,
  User,
  LogOut,
  HelpCircle,
  PanelLeft,
  Shield
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface DashboardHeaderProps {
  toggleSidebar: () => void;
}

export const DashboardHeader: React.FC<DashboardHeaderProps> = ({ toggleSidebar }) => {
  const { user, logout } = useAuth();
  const { hasRole } = usePermissions();

  // Get user initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="bg-header-gradient border-b border-gray-200 sticky top-0 z-10 shadow-sm animate-slide-in-top w-full">
      <div className="w-full px-6 py-4">
        <div className="flex items-center justify-between w-full">
          {/* Left section: Logo and toggle */}
          <div className="flex items-center">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleSidebar}
              className="mr-4 text-gray-500 hover:text-primary animate-slide-in-left"
            >
              <PanelLeft size={20} />
            </Button>

            <Link to="/dashboard" className="flex items-center">
              <img src="/logo-dark.png" alt="RecruitAI Logo" className="h-10" />
            </Link>
          </div>

          {/* Middle section: Company Switcher and Quick Actions */}
          <div className="hidden md:flex flex-1 items-center gap-4 mx-4 lg:mx-8">
            <CompanySwitcher />

            {/* Quick Actions */}
            <QuickActions className="flex-1" />
          </div>

          {/* Right section: Notifications and Profile */}
          <div className="flex items-center space-x-2 md:space-x-4">
            {/* Mobile company switcher */}
            <div className="md:hidden">
              <CompanySwitcher />
            </div>

            {/* Mobile quick actions */}
            <div className="md:hidden">
              <QuickActions />
            </div>

            {/* Mobile menu button */}
            <Button
              variant="ghost"
              size="icon"
              className="md:hidden text-gray-500 hover:text-primary"
            >
              <Menu size={20} />
            </Button>

            {/* Notifications */}
            <NotificationCenter />

            {/* User Profile */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="relative h-10 text-gray-700 hover:text-primary bg-white hover:bg-gray-50 rounded-full border border-gray-200 shadow-sm transition-all duration-200 px-2 md:px-3 flex items-center">
                  <Avatar className="h-8 w-8 mr-2 border border-gray-200">
                    <AvatarImage
                      src={user?.avatarUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.name || 'User')}&background=6366f1&color=fff&size=150`}
                      alt={user?.name || 'User'}
                      className="object-cover"
                    />
                    <AvatarFallback className="bg-primary text-white">
                      {user?.name ? getInitials(user.name) : 'U'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="hidden md:inline-block text-sm font-medium text-gray-700 max-w-[100px] lg:max-w-none truncate">
                    {user?.name}
                  </span>
                  <ChevronDown size={16} className="ml-1" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-56 bg-white border-gray-200 text-foreground shadow-lg rounded-lg animate-scale-in z-50">
                <DropdownMenuLabel className="text-gray-500 font-medium">
                  <div className="flex flex-col">
                    <span>My Account</span>
                    {user?.isPlatformAdmin && (
                      <span className="text-xs text-primary font-normal mt-1 flex items-center">
                        <Shield className="h-3 w-3 mr-1" />
                        Platform Administrator
                      </span>
                    )}
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator className="bg-gray-200" />
                <DropdownMenuItem className="cursor-pointer" asChild>
                  <Link to="/dashboard/profile">
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile & Settings</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem className="cursor-pointer" asChild>
                  <Link to="/dashboard/help">
                    <HelpCircle className="mr-2 h-4 w-4" />
                    <span>Help & Support</span>
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuSeparator className="bg-gray-200" />
                {hasRole('platform_admin') && (
                  <DropdownMenuItem className="cursor-pointer" asChild>
                    <Link to="/dashboard/admin">
                      <Shield className="mr-2 h-4 w-4 text-primary" />
                      <span>Platform Admin</span>
                    </Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator className="bg-gray-200" />
                <DropdownMenuItem
                  className="cursor-pointer text-red-500 hover:text-red-700 hover:bg-red-50 focus:bg-red-50 focus:text-red-700"
                  onClick={handleLogout}
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  <span>Log out</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </div>
    </header>
  );
};

