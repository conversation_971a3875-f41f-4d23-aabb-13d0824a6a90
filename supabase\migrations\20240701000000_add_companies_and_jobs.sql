-- Add 5 new companies and 2-3 jobs for each, assigned to the admin user

-- Get the admin user ID
DO $$
DECLARE
    admin_id UUID;
    company1_id UUID;
    company2_id UUID;
    company3_id UUID;
    company4_id UUID;
    company5_id UUID;
BEGIN
    -- Get the admin user ID
    SELECT id INTO admin_id FROM auth.users WHERE email = '<EMAIL>';

    -- Only proceed if we found the admin user
    IF admin_id IS NOT NULL THEN
        -- Company 1: Tech Innovations Inc.
        INSERT INTO public.companies (
            name,
            description,
            logo_url,
            website,
            industry,
            size,
            location,
            user_id
        )
        VALUES (
            'Tech Innovations Inc.',
            'A cutting-edge technology company focused on developing innovative software solutions for enterprise clients. We specialize in AI, machine learning, and cloud computing technologies.',
            'https://via.placeholder.com/150',
            'https://techinnovations-example.com',
            'Information Technology',
            '100-500 employees',
            'Boston, MA',
            admin_id
        )
        ON CONFLICT (id) DO NOTHING
        RETURNING id INTO STRICT company1_id;

        -- Jobs for Tech Innovations Inc.
        INSERT INTO public.jobs (
            title,
            description,
            requirements,
            location,
            salary_min,
            salary_max,
            job_type,
            experience_level,
            status,
            company_id,
            user_id
        )
        VALUES
        (
            'Senior Software Engineer',
            'We are looking for a Senior Software Engineer to join our development team. You will be responsible for designing, developing, and maintaining high-performance software applications. You will work closely with product managers, designers, and other engineers to deliver exceptional software solutions.',
            'Strong experience with Java, Python, or Go. Familiarity with cloud platforms (AWS, GCP, Azure). Experience with microservices architecture. Strong problem-solving skills and attention to detail.',
            'Boston, MA (Hybrid)',
            120000,
            160000,
            'Full-time',
            '5-8 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Tech Innovations Inc.' AND user_id = admin_id),
            admin_id
        ),
        (
            'Machine Learning Engineer',
            'Join our AI team to develop and implement machine learning models that solve complex business problems. You will work on cutting-edge projects involving natural language processing, computer vision, and predictive analytics.',
            'Masters or PhD in Computer Science, Machine Learning, or related field. Experience with TensorFlow, PyTorch, or similar frameworks. Strong programming skills in Python. Knowledge of deep learning techniques.',
            'Boston, MA or Remote',
            130000,
            180000,
            'Full-time',
            '3-7 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Tech Innovations Inc.' AND user_id = admin_id),
            admin_id
        ),
        (
            'DevOps Engineer',
            'We are seeking a DevOps Engineer to help us build and maintain our cloud infrastructure. You will be responsible for implementing CI/CD pipelines, managing cloud resources, and ensuring the reliability and security of our systems.',
            'Experience with AWS, Kubernetes, and Docker. Proficiency in infrastructure as code tools like Terraform or CloudFormation. Knowledge of CI/CD tools like Jenkins, GitLab CI, or GitHub Actions. Strong scripting skills in Bash, Python, or similar.',
            'Remote',
            110000,
            150000,
            'Full-time',
            '3-6 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Tech Innovations Inc.' AND user_id = admin_id),
            admin_id
        );

        -- Company 2: Global Finance Group
        INSERT INTO public.companies (
            name,
            description,
            logo_url,
            website,
            industry,
            size,
            location,
            user_id
        )
        VALUES (
            'Global Finance Group',
            'A leading financial services company providing investment management, banking, and advisory services to individuals and institutions worldwide. We combine innovative technology with financial expertise to deliver exceptional client experiences.',
            'https://via.placeholder.com/150',
            'https://globalfinance-example.com',
            'Financial Services',
            '1000+ employees',
            'New York, NY',
            admin_id
        )
        ON CONFLICT (id) DO NOTHING
        RETURNING id INTO STRICT company2_id;

        -- Jobs for Global Finance Group
        INSERT INTO public.jobs (
            title,
            description,
            requirements,
            location,
            salary_min,
            salary_max,
            job_type,
            experience_level,
            status,
            company_id,
            user_id
        )
        VALUES
        (
            'Financial Analyst',
            'We are looking for a Financial Analyst to join our investment team. You will be responsible for analyzing financial data, preparing reports, and providing insights to support investment decisions. This role requires a detail-oriented individual with strong analytical skills.',
            'Bachelor''s degree in Finance, Economics, or related field. Proficiency in financial modeling and analysis. Experience with financial reporting and forecasting. Strong Excel skills and familiarity with financial software.',
            'New York, NY',
            85000,
            110000,
            'Full-time',
            '2-5 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Global Finance Group' AND user_id = admin_id),
            admin_id
        ),
        (
            'Risk Management Specialist',
            'Join our risk management team to identify, assess, and mitigate financial risks. You will develop risk models, analyze market trends, and ensure compliance with regulatory requirements. This role is critical for maintaining the stability and integrity of our financial operations.',
            'Master''s degree in Finance, Risk Management, or related field. Knowledge of risk assessment methodologies and financial regulations. Experience with risk modeling and statistical analysis. Strong problem-solving and communication skills.',
            'New York, NY',
            95000,
            130000,
            'Full-time',
            '3-6 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Global Finance Group' AND user_id = admin_id),
            admin_id
        );

        -- Company 3: HealthTech Solutions
        INSERT INTO public.companies (
            name,
            description,
            logo_url,
            website,
            industry,
            size,
            location,
            user_id
        )
        VALUES (
            'HealthTech Solutions',
            'A healthcare technology company dedicated to improving patient outcomes through innovative digital solutions. We develop software and systems that streamline healthcare delivery, enhance patient engagement, and optimize clinical workflows.',
            'https://via.placeholder.com/150',
            'https://healthtech-example.com',
            'Healthcare Technology',
            '50-200 employees',
            'Chicago, IL',
            admin_id
        )
        ON CONFLICT (id) DO NOTHING
        RETURNING id INTO STRICT company3_id;

        -- Jobs for HealthTech Solutions
        INSERT INTO public.jobs (
            title,
            description,
            requirements,
            location,
            salary_min,
            salary_max,
            job_type,
            experience_level,
            status,
            company_id,
            user_id
        )
        VALUES
        (
            'Healthcare Software Developer',
            'We are seeking a Healthcare Software Developer to build and maintain our patient management platform. You will work on features that directly impact patient care and clinical workflows. This role offers an opportunity to make a meaningful difference in healthcare delivery.',
            'Experience with healthcare software development. Proficiency in JavaScript, React, and Node.js. Familiarity with healthcare data standards (HL7, FHIR). Knowledge of HIPAA compliance requirements.',
            'Chicago, IL',
            100000,
            140000,
            'Full-time',
            '3-6 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'HealthTech Solutions' AND user_id = admin_id),
            admin_id
        ),
        (
            'Clinical Data Analyst',
            'Join our data team to analyze clinical data and generate insights that improve healthcare outcomes. You will work with large healthcare datasets, develop analytical models, and create reports for healthcare providers and administrators.',
            'Background in healthcare or life sciences. Strong data analysis skills and experience with SQL, Python, or R. Knowledge of statistical methods and healthcare metrics. Ability to communicate complex findings to non-technical stakeholders.',
            'Chicago, IL or Remote',
            90000,
            120000,
            'Full-time',
            '2-5 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'HealthTech Solutions' AND user_id = admin_id),
            admin_id
        ),
        (
            'Product Manager - Healthcare',
            'We are looking for a Product Manager to lead the development of our healthcare applications. You will gather requirements, define product roadmaps, and work with cross-functional teams to deliver solutions that meet the needs of healthcare providers and patients.',
            'Experience in healthcare product management. Understanding of healthcare workflows and challenges. Strong communication and stakeholder management skills. Ability to translate clinical needs into product requirements.',
            'Chicago, IL',
            110000,
            150000,
            'Full-time',
            '4-8 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'HealthTech Solutions' AND user_id = admin_id),
            admin_id
        );

        -- Company 4: Creative Media Agency
        INSERT INTO public.companies (
            name,
            description,
            logo_url,
            website,
            industry,
            size,
            location,
            user_id
        )
        VALUES (
            'Creative Media Agency',
            'A full-service digital marketing and creative agency helping brands tell their stories and connect with their audiences. We combine strategic thinking, creative design, and digital expertise to deliver impactful marketing campaigns.',
            'https://via.placeholder.com/150',
            'https://creativemedia-example.com',
            'Marketing and Advertising',
            '20-100 employees',
            'Los Angeles, CA',
            admin_id
        )
        ON CONFLICT (id) DO NOTHING
        RETURNING id INTO STRICT company4_id;

        -- Jobs for Creative Media Agency
        INSERT INTO public.jobs (
            title,
            description,
            requirements,
            location,
            salary_min,
            salary_max,
            job_type,
            experience_level,
            status,
            company_id,
            user_id
        )
        VALUES
        (
            'Digital Marketing Specialist',
            'We are looking for a Digital Marketing Specialist to develop and implement marketing strategies for our clients. You will manage social media campaigns, email marketing, and digital advertising to drive engagement and conversions.',
            'Experience in digital marketing across multiple channels. Proficiency with marketing automation tools and analytics platforms. Knowledge of SEO/SEM principles. Creative mindset and strong communication skills.',
            'Los Angeles, CA',
            70000,
            95000,
            'Full-time',
            '2-4 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Creative Media Agency' AND user_id = admin_id),
            admin_id
        ),
        (
            'UX/UI Designer',
            'Join our creative team as a UX/UI Designer to create engaging and intuitive digital experiences. You will design user interfaces for websites, mobile apps, and digital products that balance aesthetic appeal with functional usability.',
            'Portfolio demonstrating strong UI/UX design skills. Proficiency with design tools like Figma, Adobe XD, or Sketch. Understanding of user-centered design principles. Experience designing for multiple platforms and devices.',
            'Los Angeles, CA or Remote',
            85000,
            115000,
            'Full-time',
            '3-6 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Creative Media Agency' AND user_id = admin_id),
            admin_id
        );

        -- Company 5: Sustainable Energy Innovations
        INSERT INTO public.companies (
            name,
            description,
            logo_url,
            website,
            industry,
            size,
            location,
            user_id
        )
        VALUES (
            'Sustainable Energy Innovations',
            'A renewable energy company developing innovative solutions for a sustainable future. We design and implement solar, wind, and energy storage systems for residential, commercial, and utility-scale applications.',
            'https://via.placeholder.com/150',
            'https://sustainableenergy-example.com',
            'Renewable Energy',
            '50-200 employees',
            'Denver, CO',
            admin_id
        )
        ON CONFLICT (id) DO NOTHING
        RETURNING id INTO STRICT company5_id;

        -- Jobs for Sustainable Energy Innovations
        INSERT INTO public.jobs (
            title,
            description,
            requirements,
            location,
            salary_min,
            salary_max,
            job_type,
            experience_level,
            status,
            company_id,
            user_id
        )
        VALUES
        (
            'Renewable Energy Engineer',
            'We are seeking a Renewable Energy Engineer to design and optimize solar and wind energy systems. You will conduct site assessments, perform energy modeling, and develop technical specifications for renewable energy installations.',
            'Degree in Electrical, Mechanical, or Environmental Engineering. Experience with renewable energy system design. Knowledge of energy modeling software and industry standards. Strong analytical and problem-solving skills.',
            'Denver, CO',
            95000,
            130000,
            'Full-time',
            '3-7 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Sustainable Energy Innovations' AND user_id = admin_id),
            admin_id
        ),
        (
            'Energy Storage Specialist',
            'Join our team as an Energy Storage Specialist to develop and implement battery storage solutions. You will analyze energy usage patterns, design storage systems, and optimize performance for maximum efficiency and reliability.',
            'Experience with battery storage technologies and energy management systems. Understanding of electrical systems and power electronics. Knowledge of grid integration and regulatory requirements. Strong technical and analytical skills.',
            'Denver, CO',
            100000,
            140000,
            'Full-time',
            '4-8 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Sustainable Energy Innovations' AND user_id = admin_id),
            admin_id
        ),
        (
            'Sustainability Consultant',
            'We are looking for a Sustainability Consultant to help clients reduce their environmental impact and transition to renewable energy. You will conduct energy audits, develop sustainability strategies, and provide guidance on green building practices.',
            'Background in environmental science, sustainability, or related field. Knowledge of green building standards (LEED, WELL). Experience with energy efficiency assessments and carbon footprint analysis. Strong communication and client management skills.',
            'Remote',
            80000,
            110000,
            'Full-time',
            '2-6 years',
            'active',
            (SELECT id FROM public.companies WHERE name = 'Sustainable Energy Innovations' AND user_id = admin_id),
            admin_id
        );
    ELSE
        RAISE EXCEPTION 'Admin user <NAME_EMAIL> not found';
    END IF;
END $$;
