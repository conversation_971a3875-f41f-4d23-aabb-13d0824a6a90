import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  Shield,
  Users,
  Eye,
  Save,
  Info,
  CheckCircle,
  XCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { useToast } from '@/hooks/use-toast';
import { usePermissions, ROLE_DESCRIPTIONS, RoleType } from '@/contexts/PermissionsContext';

// Define available roles for editing
const EDITABLE_ROLES: RoleType[] = ['admin', 'recruiter', 'viewer'];

const RolePermissions = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const { rolePermissions, updateRolePermissions, hasRole, isLoading } = usePermissions();

  const [activeRole, setActiveRole] = useState<RoleType>('admin');
  const [isEditing, setIsEditing] = useState(false);
  const [tempPermissions, setTempPermissions] = useState(rolePermissions);

  // Check if user has permission to access this page
  useEffect(() => {
    if (!isLoading && !hasRole(['admin', 'platform_admin'])) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to manage role permissions.',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }
  }, [isLoading, hasRole, navigate, toast]);

  // Update temp permissions when role permissions change
  useEffect(() => {
    setTempPermissions(rolePermissions);
  }, [rolePermissions]);

  // Start editing permissions
  const startEditing = () => {
    setTempPermissions(JSON.parse(JSON.stringify(rolePermissions)));
    setIsEditing(true);
  };

  // Cancel editing
  const cancelEditing = () => {
    setIsEditing(false);
  };

  // Save permissions
  const savePermissions = async () => {
    try {
      await updateRolePermissions(activeRole, tempPermissions[activeRole]);
      setIsEditing(false);

      toast({
        title: 'Permissions updated',
        description: `Permissions for ${activeRole} role have been updated.`,
      });
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update permissions. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Toggle permission
  const togglePermission = (section: string, permission: string) => {
    if (!isEditing) return;

    setTempPermissions(prev => {
      const newPermissions = JSON.parse(JSON.stringify(prev));
      newPermissions[activeRole][section][permission] = !newPermissions[activeRole][section][permission];
      return newPermissions;
    });
  };

  // Get permission icon
  const getPermissionIcon = (value: boolean) => {
    return value ? (
      <CheckCircle className="h-5 w-5 text-green-500" />
    ) : (
      <XCircle className="h-5 w-5 text-red-500" />
    );
  };

  // Get role icon
  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Shield className="h-5 w-5 text-purple-500" />;
      case 'recruiter':
        return <Users className="h-5 w-5 text-blue-500" />;
      case 'viewer':
        return <Eye className="h-5 w-5 text-green-500" />;
      default:
        return <Shield className="h-5 w-5 text-gray-400" />;
    }
  };

  // Show loading state
  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center h-[60vh]">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-12 w-12 animate-spin text-primary" />
            <p className="text-lg text-gray-600">Loading permissions...</p>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-2xl font-bold text-gray-800">Role Permissions</h1>

          {isEditing ? (
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="border-gray-700 text-white hover:bg-[#2a2f3d]"
                onClick={cancelEditing}
              >
                Cancel
              </Button>
              <Button
                className="bg-recruiter-lightblue hover:bg-blue-500"
                onClick={savePermissions}
              >
                <Save className="mr-2 h-4 w-4" /> Save Changes
              </Button>
            </div>
          ) : (
            <Button
              className="bg-recruiter-lightblue hover:bg-blue-500"
              onClick={startEditing}
            >
              Edit Permissions
            </Button>
          )}
        </div>

        <Tabs value={activeRole} onValueChange={(value) => setActiveRole(value as RoleType)}>
          <TabsList className="bg-white border-b border-gray-200 w-full justify-start">
            <TabsTrigger value="admin" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              <Shield className="mr-2 h-4 w-4 text-purple-500" /> Admin
            </TabsTrigger>
            <TabsTrigger value="recruiter" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              <Users className="mr-2 h-4 w-4 text-blue-500" /> Recruiter
            </TabsTrigger>
            <TabsTrigger value="viewer" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              <Eye className="mr-2 h-4 w-4 text-green-500" /> Viewer
            </TabsTrigger>
          </TabsList>

          {EDITABLE_ROLES.map(role => (
            <TabsContent key={role} value={role} className="mt-6">
              <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                <CardHeader>
                  <div className="flex items-center gap-2">
                    {getRoleIcon(role)}
                    <CardTitle className="text-gray-800 text-lg capitalize">{role} Role</CardTitle>
                  </div>
                  <CardDescription className="text-gray-500">
                    {ROLE_DESCRIPTIONS[role]}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <Table>
                      <TableHeader>
                        <TableRow className="border-gray-200">
                          <TableHead className="text-gray-500">Section</TableHead>
                          <TableHead className="text-gray-500">View</TableHead>
                          <TableHead className="text-gray-500">Create</TableHead>
                          <TableHead className="text-gray-500">Edit</TableHead>
                          <TableHead className="text-gray-500">Delete</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {Object.entries(isEditing ? tempPermissions[role] : rolePermissions[role]).map(([section, perms]) => (
                          <TableRow key={section} className="border-gray-200">
                            <TableCell className="font-medium text-gray-800 capitalize">
                              {section}
                            </TableCell>
                            <TableCell>
                              {perms.view !== undefined && (
                                <div
                                  className={`flex justify-center ${isEditing ? 'cursor-pointer' : ''}`}
                                  onClick={() => togglePermission(section, 'view')}
                                >
                                  {getPermissionIcon(perms.view)}
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              {perms.create !== undefined ? (
                                <div
                                  className={`flex justify-center ${isEditing ? 'cursor-pointer' : ''}`}
                                  onClick={() => togglePermission(section, 'create')}
                                >
                                  {getPermissionIcon(perms.create)}
                                </div>
                              ) : (
                                <div className="flex justify-center">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <AlertCircle className="h-5 w-5 text-gray-500" />
                                      </TooltipTrigger>
                                      <TooltipContent className="bg-white border-gray-200 text-gray-800">
                                        <p>Not applicable</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              {perms.edit !== undefined ? (
                                <div
                                  className={`flex justify-center ${isEditing ? 'cursor-pointer' : ''}`}
                                  onClick={() => togglePermission(section, 'edit')}
                                >
                                  {getPermissionIcon(perms.edit)}
                                </div>
                              ) : (
                                <div className="flex justify-center">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <AlertCircle className="h-5 w-5 text-gray-500" />
                                      </TooltipTrigger>
                                      <TooltipContent className="bg-white border-gray-200 text-gray-800">
                                        <p>Not applicable</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                              )}
                            </TableCell>
                            <TableCell>
                              {perms.delete !== undefined ? (
                                <div
                                  className={`flex justify-center ${isEditing ? 'cursor-pointer' : ''}`}
                                  onClick={() => togglePermission(section, 'delete')}
                                >
                                  {getPermissionIcon(perms.delete)}
                                </div>
                              ) : (
                                <div className="flex justify-center">
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger>
                                        <AlertCircle className="h-5 w-5 text-gray-500" />
                                      </TooltipTrigger>
                                      <TooltipContent className="bg-white border-gray-200 text-gray-800">
                                        <p>Not applicable</p>
                                      </TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                </div>
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>

                  {isEditing && (
                    <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                      <div className="flex items-start gap-3">
                        <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                        <div>
                          <h3 className="text-gray-800 font-medium">Editing Permissions</h3>
                          <p className="text-gray-500 text-sm mt-1">
                            Click on the permission icons to toggle them. Changes will only be applied after you save.
                          </p>
                        </div>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          ))}
        </Tabs>

        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg">Role Descriptions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-sm transition-all duration-200">
                <div className="flex items-center gap-2 mb-2">
                  <Shield className="h-5 w-5 text-purple-500" />
                  <h3 className="text-gray-800 font-medium">Admin</h3>
                </div>
                <p className="text-gray-600">
                  Administrators have full access to all features and settings of the platform. They can manage team members,
                  control billing, and configure system-wide settings. This role should be assigned to trusted individuals
                  who need complete control over the platform.
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-sm transition-all duration-200">
                <div className="flex items-center gap-2 mb-2">
                  <Users className="h-5 w-5 text-blue-500" />
                  <h3 className="text-gray-800 font-medium">Recruiter</h3>
                </div>
                <p className="text-gray-600">
                  Recruiters can manage the day-to-day recruitment operations. They can create and manage job postings,
                  process candidates, and run evaluations. They have limited access to team management and cannot access
                  billing or advanced settings. This role is ideal for team members who handle the recruitment process.
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg border border-gray-200 hover:shadow-sm transition-all duration-200">
                <div className="flex items-center gap-2 mb-2">
                  <Eye className="h-5 w-5 text-green-500" />
                  <h3 className="text-gray-800 font-medium">Viewer</h3>
                </div>
                <p className="text-gray-600">
                  Viewers have read-only access to jobs, candidates, and evaluations. They cannot create or modify any data
                  in the system. This role is suitable for stakeholders who need to monitor the recruitment process without
                  making changes, such as hiring managers or department heads.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default RolePermissions;
