import React, { useState, useEffect } from 'react';
import {
  BarChart3,
  Users,
  Briefcase,
  FileText,
  TrendingUp,
  Clock,
  CheckCircle2,
  XCircle
} from 'lucide-react';
import { ColorfulIcon } from '@/components/ui/colorful-icon';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { getJobs } from '@/services/supabase/jobs';
import { getCandidates, getCandidateStatistics } from '@/services/supabase/candidates';
import { useToast } from '@/hooks/use-toast';

interface StatCardProps {
  title: string;
  value: string | number;
  description?: string;
  icon: React.ElementType;
  color: 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'danger' | 'purple';
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const StatCard: React.FC<StatCardProps> = ({ title, value, description, icon, color, trend }) => {
  return (
    <Card className="bg-card-hover">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium text-gray-500">{title}</CardTitle>
        <ColorfulIcon icon={icon} color={color} size={18} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {description && <p className="text-xs text-gray-500 mt-1">{description}</p>}
        {trend && (
          <div className="flex items-center mt-2">
            {trend.isPositive ? (
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
            ) : (
              <TrendingUp className="h-4 w-4 text-red-500 mr-1 transform rotate-180" />
            )}
            <span className={trend.isPositive ? 'text-green-500 text-xs' : 'text-red-500 text-xs'}>
              {trend.isPositive ? '+' : '-'}{Math.abs(trend.value)}% from last month
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

interface ProgressCardProps {
  title: string;
  items: {
    label: string;
    value: number;
    color: string;
  }[];
  icon: React.ElementType;
  color: 'primary' | 'secondary' | 'success' | 'info' | 'warning' | 'danger' | 'purple';
}

const ProgressCard: React.FC<ProgressCardProps> = ({ title, items, icon, color }) => {
  return (
    <Card className="bg-card-hover">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium text-gray-500">{title}</CardTitle>
        <ColorfulIcon icon={icon} color={color} size={18} />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {items.map((item, index) => (
            <div key={index} className="space-y-1">
              <div className="flex justify-between text-xs">
                <span>{item.label}</span>
                <span className="font-medium">{item.value}%</span>
              </div>
              <Progress value={item.value} className={item.color} />
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

interface RecentActivityProps {
  activities: {
    id: string;
    title: string;
    time: string;
    status: 'completed' | 'pending' | 'failed';
  }[];
}

const RecentActivity: React.FC<RecentActivityProps> = ({ activities }) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle2 className="h-4 w-4 text-green-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-amber-500" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return null;
    }
  };

  return (
    <Card className="bg-card-hover">
      <CardHeader>
        <CardTitle>Recent Activity</CardTitle>
        <CardDescription>Your latest actions and updates</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-3 animate-fade-in">
              <div className="mt-0.5">{getStatusIcon(activity.status)}</div>
              <div className="space-y-1">
                <p className="text-sm font-medium leading-none">{activity.title}</p>
                <p className="text-xs text-gray-500">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export const DashboardOverview: React.FC = () => {
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<any[]>([]);
  const [progressItems, setProgressItems] = useState<any[]>([]);
  const [recentActivities, setRecentActivities] = useState<any[]>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);

        // Fetch jobs - getJobs now handles errors internally
        const jobs = await getJobs();
        const activeJobs = jobs.filter(job => job.status === 'active').length;

        // Fetch candidates - getCandidates now handles errors internally
        const candidates = await getCandidates();
        const totalCandidates = candidates.length;

        // Get candidate statistics with error handling
        let candidateStats;
        try {
          candidateStats = await getCandidateStatistics();
        } catch (error) {
          console.error('Error fetching candidate statistics:', error);
          candidateStats = {
            total: totalCandidates,
            new: 0,
            reviewing: 0,
            interviewed: 0,
            hired: 0,
            rejected: 0
          };
        }

        // Calculate CV processing rate
        const processedCVs = candidates.filter(c => c.evaluation_score).length;
        const matchRate = processedCVs > 0
          ? Math.round((candidates.filter(c => c.evaluation_score && c.evaluation_score > 70).length / processedCVs) * 100)
          : 0;

        // Calculate hiring rate
        const hiredCandidates = candidates.filter(c => c.status === 'hired').length;
        const hiringRate = totalCandidates > 0
          ? Math.round((hiredCandidates / totalCandidates) * 100)
          : 0;

        // Update stats
        setStats([
          {
            title: 'Total Candidates',
            value: totalCandidates.toString(),
            icon: Users,
            color: 'primary',
            trend: { value: 12, isPositive: true }, // Mock trend data
          },
          {
            title: 'Active Jobs',
            value: activeJobs.toString(),
            icon: Briefcase,
            color: 'success',
            trend: { value: 8, isPositive: true }, // Mock trend data
          },
          {
            title: 'CVs Processed',
            value: processedCVs.toString(),
            description: `${matchRate}% match rate`,
            icon: FileText,
            color: 'info',
          },
          {
            title: 'Hiring Rate',
            value: `${hiringRate}%`,
            description: `${hiredCandidates} hires total`,
            icon: BarChart3,
            color: 'warning',
            trend: { value: 5, isPositive: false }, // Mock trend data
          },
        ]);

        // Update progress items
        setProgressItems([
          {
            title: 'Recruitment Progress',
            icon: BarChart3,
            color: 'secondary',
            items: [
              { label: 'New', value: Math.round((candidateStats.new / candidateStats.total) * 100) || 0, color: 'bg-blue-500' },
              { label: 'Screening', value: Math.round((candidateStats.reviewing / candidateStats.total) * 100) || 0, color: 'bg-purple-500' },
              { label: 'Interviewed', value: Math.round((candidateStats.interviewed / candidateStats.total) * 100) || 0, color: 'bg-green-500' },
              { label: 'Hired', value: Math.round((candidateStats.hired / candidateStats.total) * 100) || 0, color: 'bg-amber-500' },
            ],
          },
          {
            title: 'Top Skills Demand',
            icon: BarChart3,
            color: 'purple',
            items: [
              { label: 'React', value: 85, color: 'bg-blue-500' },
              { label: 'Node.js', value: 70, color: 'bg-green-500' },
              { label: 'TypeScript', value: 65, color: 'bg-purple-500' },
              { label: 'Python', value: 45, color: 'bg-amber-500' },
            ],
          },
        ]);

        // Create recent activities from candidates and jobs
        const activities = [];

        // Add recent candidates
        const recentCandidates = candidates
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 2);

        for (const candidate of recentCandidates) {
          const job = jobs.find(j => j.id === candidate.job_id);
          activities.push({
            id: candidate.id,
            title: `New candidate ${candidate.name} applied for ${job?.title || 'a position'}`,
            time: formatTimeAgo(new Date(candidate.created_at)),
            status: 'completed',
          });
        }

        // Add recent jobs
        const recentJobs = jobs
          .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
          .slice(0, 2);

        for (const job of recentJobs) {
          activities.push({
            id: job.id,
            title: `New job posted: ${job.title}`,
            time: formatTimeAgo(new Date(job.created_at)),
            status: 'pending',
          });
        }

        setRecentActivities(activities);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load dashboard data. Using sample data instead.',
          variant: 'destructive'
        });

        // Fallback to sample data
        setStats([
    {
      title: 'Total Candidates',
      value: '1,234',
      icon: Users,
      color: 'primary',
      trend: { value: 12, isPositive: true },
    },
    {
      title: 'Active Jobs',
      value: '45',
      icon: Briefcase,
      color: 'success',
      trend: { value: 8, isPositive: true },
    },
    {
      title: 'CVs Processed',
      value: '892',
      description: '72% match rate',
      icon: FileText,
      color: 'info',
    },
    {
      title: 'Hiring Rate',
      value: '24%',
      description: '12 hires this month',
      icon: BarChart3,
      color: 'warning',
      trend: { value: 5, isPositive: false },
    },
  ]);
  setProgressItems([
    {
      title: 'Recruitment Progress',
      icon: BarChart3,
      color: 'secondary',
      items: [
        { label: 'Screening', value: 80, color: 'bg-blue-500' },
        { label: 'Interviews', value: 65, color: 'bg-purple-500' },
        { label: 'Offers', value: 40, color: 'bg-green-500' },
        { label: 'Onboarding', value: 25, color: 'bg-amber-500' },
      ],
    },
    {
      title: 'Top Skills Demand',
      icon: BarChart3,
      color: 'purple',
      items: [
        { label: 'React', value: 85, color: 'bg-blue-500' },
        { label: 'Node.js', value: 70, color: 'bg-green-500' },
        { label: 'TypeScript', value: 65, color: 'bg-purple-500' },
        { label: 'Python', value: 45, color: 'bg-amber-500' },
      ],
    },
  ]);
  setRecentActivities([
    {
      id: '1',
      title: 'New candidate applied for Frontend Developer',
      time: '2 hours ago',
      status: 'completed',
    },
    {
      id: '2',
      title: 'Interview scheduled with John Doe',
      time: '5 hours ago',
      status: 'pending',
    },
    {
      id: '3',
      title: 'CV evaluation for Marketing Manager',
      time: 'Yesterday',
      status: 'completed',
    },
    {
      id: '4',
      title: 'Job posting for UX Designer expired',
      time: '2 days ago',
      status: 'failed',
    },
  ]);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [toast]);

  // Helper function to format time ago
  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    }

    const diffInMinutes = Math.floor(diffInSeconds / 60);
    if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    }

    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) {
      return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    }

    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 30) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    const diffInMonths = Math.floor(diffInDays / 30);
    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-3xl font-bold tracking-tight">Dashboard</h2>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">Last updated: {new Date().toLocaleTimeString()}</span>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat, index) => (
          <div key={index} className={`animate-fade-in animate-delay-${index * 100}`}>
            <StatCard {...stat} />
          </div>
        ))}
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {progressItems.map((item, index) => (
          <div key={index} className={`animate-fade-in animate-delay-${index * 100}`}>
            <ProgressCard {...item} />
          </div>
        ))}
        <div className="animate-fade-in animate-delay-200">
          <RecentActivity activities={recentActivities} />
        </div>
      </div>
    </div>
  );
};
