import { Database } from './supabase';

// Application types from Supabase
export type Application = Database['public']['Tables']['applications']['Row'];
export type ApplicationInsert = Database['public']['Tables']['applications']['Insert'];
export type ApplicationUpdate = Database['public']['Tables']['applications']['Update'];

// Application status enum
export type ApplicationStatus = 'applied' | 'screening' | 'interviewed' | 'offer' | 'hired' | 'rejected';

// Extended application with related data
export interface ApplicationWithDetails extends Application {
  candidates?: {
    id: string;
    name: string;
    email: string;
    cv_url: string;
    status: string;
  };
  jobs?: {
    id: string;
    title: string;
    company_id: string;
    companies?: {
      id: string;
      name: string;
    };
  };
}

// Application creation payload
export interface CreateApplicationPayload {
  candidate_id: string;
  job_id: string;
  notes?: string;
}

// Bulk application creation payload
export interface BulkCreateApplicationPayload {
  candidate_ids: string[];
  job_id: string;
  notes?: string;
}

// Application statistics
export interface ApplicationStatistics {
  total: number;
  applied: number;
  screening: number;
  interviewed: number;
  offer: number;
  hired: number;
  rejected: number;
}

// Job applicant summary
export interface JobApplicantSummary {
  job_id: string;
  job_title: string;
  total_applicants: number;
  by_status: {
    applied: number;
    screening: number;
    interviewed: number;
    offer: number;
    hired: number;
    rejected: number;
  };
}
