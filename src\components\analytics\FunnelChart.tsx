import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ChartData } from '@/services/supabase/dashboard';

interface FunnelChartProps {
  data: ChartData[];
  title: string;
  description?: string;
  conversionRate?: number;
  totalCount?: number;
}

export const FunnelChart: React.FC<FunnelChartProps> = ({
  data,
  title,
  description,
  conversionRate,
  totalCount
}) => {
  const [animated, setAnimated] = useState(false);
  
  useEffect(() => {
    // Trigger animation after component mounts
    const timer = setTimeout(() => {
      setAnimated(true);
    }, 100);
    
    return () => clearTimeout(timer);
  }, []);
  
  // Find the maximum value for percentage calculation
  const maxValue = data.length > 0 ? data[0].value : 0;
  
  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader>
        <CardTitle className="text-gray-800 text-lg">{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {data.map((stage, index) => (
            <div key={index}>
              <div className="flex justify-between mb-1">
                <span className="text-sm text-gray-600">{stage.label}</span>
                <span className="text-sm text-gray-600">{stage.value}</span>
              </div>
              <div className="w-full bg-gray-100 rounded-full h-2.5">
                <div
                  className={`${stage.color} h-2.5 rounded-full transition-all duration-1000 ease-out`}
                  style={{ 
                    width: animated ? `${maxValue > 0 ? (stage.value / maxValue) * 100 : 0}%` : '0%',
                    transitionDelay: `${index * 100}ms`
                  }}
                />
              </div>
              {index < data.length - 1 && (
                <div className="flex justify-center my-2">
                  <div className="h-8 border-l-2 border-dashed border-gray-300"></div>
                </div>
              )}
            </div>
          ))}
        </div>
        
        {/* Summary statistics */}
        {(conversionRate !== undefined || totalCount !== undefined) && (
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-2 gap-4">
              {totalCount !== undefined && (
                <div>
                  <p className="text-sm text-gray-500">Total Candidates</p>
                  <p className="text-xl font-semibold">{totalCount}</p>
                </div>
              )}
              {conversionRate !== undefined && (
                <div>
                  <p className="text-sm text-gray-500">Conversion Rate</p>
                  <p className="text-xl font-semibold">{conversionRate}%</p>
                </div>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
