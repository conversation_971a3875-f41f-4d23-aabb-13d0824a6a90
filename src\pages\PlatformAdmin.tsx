import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/lib/supabase';
import {
  Shield,
  Users,
  Ban,
  CheckCircle,
  XCircle,
  Search,
  RefreshCw,
  AlertTriangle,
  CreditCard,
  Bot,
  Edit,
  Settings,
  Mail
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Label } from '@/components/ui/label';

interface UserData {
  id: string;
  email: string;
  full_name: string;
  subscription_tier: string;
  subscription_status: string;
  platform_admin: boolean;
  created_at: string;
  last_sign_in_at: string | null;
  banned: boolean;
}

const PlatformAdmin = () => {
  const { user } = useAuth();
  const { hasRole, isLoading: permissionsLoading } = usePermissions();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [users, setUsers] = useState<UserData[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [actionType, setActionType] = useState<'ban' | 'unban' | 'makeAdmin' | 'removeAdmin'>('ban');
  const [activeTab, setActiveTab] = useState('all');
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editFormData, setEditFormData] = useState({
    subscription_tier: '',
    subscription_status: '',
  });

  // Redirect if not a platform admin
  useEffect(() => {
    if (!permissionsLoading && user && !hasRole('platform_admin')) {
      toast({
        title: 'Access Denied',
        description: 'You do not have permission to access this page.',
        variant: 'destructive',
      });
      navigate('/dashboard');
    }
  }, [user, hasRole, permissionsLoading, navigate, toast]);

  // Fetch users using the Edge Function
  const fetchUsers = async () => {
    setLoading(true);
    try {
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      // Call the list-users Edge Function
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/list-users`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch users');
      }

      const { users } = await response.json();
      setUsers(users);
    } catch (error) {
      console.error('Error fetching users:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch users. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (!permissionsLoading && hasRole('platform_admin')) {
      fetchUsers();
    }
  }, [hasRole, permissionsLoading]);

  // Filter users based on search query and active tab
  const filteredUsers = users.filter(user => {
    const matchesSearch =
      user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      user.full_name.toLowerCase().includes(searchQuery.toLowerCase());

    if (activeTab === 'all') return matchesSearch;
    if (activeTab === 'admins') return matchesSearch && user.platform_admin;
    if (activeTab === 'banned') return matchesSearch && user.banned;

    return matchesSearch;
  });

  // Handle user actions
  const handleAction = async () => {
    if (!selectedUser) return;

    setLoading(true);
    try {
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      switch (actionType) {
        case 'ban':
          // Ban user using the Edge Function
          const banResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/ban-user`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId: selectedUser.id })
          });

          if (!banResponse.ok) {
            const errorData = await banResponse.json();
            throw new Error(errorData.error || 'Failed to ban user');
          }

          toast({
            title: 'User Banned',
            description: `${selectedUser.email} has been banned from the platform.`,
          });
          break;

        case 'unban':
          // Unban user using the Edge Function
          const unbanResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/unban-user`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId: selectedUser.id })
          });

          if (!unbanResponse.ok) {
            const errorData = await unbanResponse.json();
            throw new Error(errorData.error || 'Failed to unban user');
          }

          toast({
            title: 'User Unbanned',
            description: `${selectedUser.email} has been unbanned.`,
          });
          break;

        case 'makeAdmin':
          // Make user a platform admin using the Edge Function
          const makeAdminResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/update-admin-status`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId: selectedUser.id, isAdmin: true })
          });

          if (!makeAdminResponse.ok) {
            const errorData = await makeAdminResponse.json();
            throw new Error(errorData.error || 'Failed to grant admin privileges');
          }

          toast({
            title: 'Admin Privileges Granted',
            description: `${selectedUser.email} is now a platform administrator.`,
          });
          break;

        case 'removeAdmin':
          // Remove platform admin status using the Edge Function
          const removeAdminResponse = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/update-admin-status`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${session.access_token}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ userId: selectedUser.id, isAdmin: false })
          });

          if (!removeAdminResponse.ok) {
            const errorData = await removeAdminResponse.json();
            throw new Error(errorData.error || 'Failed to remove admin privileges');
          }

          toast({
            title: 'Admin Privileges Removed',
            description: `${selectedUser.email} is no longer a platform administrator.`,
          });
          break;
      }

      // Refresh user list
      fetchUsers();
    } catch (error) {
      console.error('Error performing action:', error);
      toast({
        title: 'Error',
        description: 'Failed to perform action. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
      setConfirmDialogOpen(false);
    }
  };

  // Format date
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Open confirmation dialog
  const openConfirmDialog = (user: UserData, action: 'ban' | 'unban' | 'makeAdmin' | 'removeAdmin') => {
    setSelectedUser(user);
    setActionType(action);
    setConfirmDialogOpen(true);
  };

  const openEditDialog = (user: UserData) => {
    setSelectedUser(user);
    setEditFormData({
      subscription_tier: user.subscription_tier || 'starter',
      subscription_status: user.subscription_status || 'active',
    });
    setEditDialogOpen(true);
  };

  const updateUserSubscription = async () => {
    if (!selectedUser) return;
    
    setLoading(true);
    try {
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      // Update the user's subscription using user_id instead of id
      const { error } = await supabase
        .from('profiles')
        .update({
          subscription_tier: editFormData.subscription_tier,
          subscription_status: editFormData.subscription_status,
        })
        .eq('user_id', selectedUser.id); // Changed from 'id' to 'user_id'

      if (error) {
        console.error('Database error:', error);
        throw error;
      }

      toast({
        title: 'Success',
        description: 'User subscription updated successfully',
      });

      setEditDialogOpen(false);
      setSelectedUser(null);
      fetchUsers();
    } catch (error) {
      console.error('Error updating user subscription:', error);
      toast({
        title: 'Error',
        description: 'Failed to update user subscription',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            <Shield className="h-6 w-6 text-primary" />
            <h1 className="text-2xl font-bold text-gray-800">Platform Administration</h1>
          </div>

          <Button
            variant="outline"
            className="border-gray-200 text-gray-700 hover:bg-gray-100"
            onClick={fetchUsers}
            disabled={loading}
          >
            <RefreshCw className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="bg-white border border-gray-200 w-full md:w-auto justify-start">
              <TabsTrigger value="all" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
                All Users
              </TabsTrigger>
              <TabsTrigger value="admins" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
                Admins
              </TabsTrigger>
              <TabsTrigger value="banned" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
                Banned Users
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="relative w-full md:w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search users..."
              className="pl-8 bg-white border-gray-200"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>

        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-gray-800 text-lg">User Management</CardTitle>
            <CardDescription>
              Manage users, assign admin privileges, and moderate platform access
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="rounded-md border border-gray-200">
              <Table>
                <TableHeader>
                  <TableRow className="bg-gray-50">
                    <TableHead className="w-[250px]">User</TableHead>
                    <TableHead>Subscription</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead>Last Login</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <RefreshCw className="h-6 w-6 animate-spin mx-auto text-gray-400" />
                        <p className="mt-2 text-gray-500">Loading users...</p>
                      </TableCell>
                    </TableRow>
                  ) : filteredUsers.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <Users className="h-6 w-6 mx-auto text-gray-400" />
                        <p className="mt-2 text-gray-500">No users found</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredUsers.map((userData) => (
                      <TableRow key={userData.id}>
                        <TableCell className="font-medium">
                          <div className="flex flex-col">
                            <span className="text-gray-800">{userData.full_name}</span>
                            <span className="text-gray-500 text-sm">{userData.email}</span>
                            {userData.platform_admin && (
                              <Badge className="bg-primary/10 text-primary hover:bg-primary/20 mt-1 w-fit">
                                Platform Admin
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={
                              userData.subscription_tier === 'pro'
                                ? 'bg-purple-100 text-purple-800 hover:bg-purple-200'
                                : userData.subscription_tier === 'growth'
                                  ? 'bg-blue-100 text-blue-800 hover:bg-blue-200'
                                  : 'bg-gray-100 text-gray-800 hover:bg-gray-200'
                            }
                          >
                            {userData.subscription_tier.charAt(0).toUpperCase() + userData.subscription_tier.slice(1)}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          {userData.banned ? (
                            <Badge variant="destructive">Banned</Badge>
                          ) : (
                            <Badge
                              className={
                                userData.subscription_status === 'active'
                                  ? 'bg-green-100 text-green-800 hover:bg-green-200'
                                  : 'bg-amber-100 text-amber-800 hover:bg-amber-200'
                              }
                            >
                              {userData.subscription_status.charAt(0).toUpperCase() + userData.subscription_status.slice(1)}
                            </Badge>
                          )}
                        </TableCell>
                        <TableCell>{formatDate(userData.created_at)}</TableCell>
                        <TableCell>{formatDate(userData.last_sign_in_at)}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex justify-end gap-2">
                            {userData.platform_admin ? (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-200 text-gray-700 hover:bg-gray-100"
                                onClick={() => openConfirmDialog(userData, 'removeAdmin')}
                              >
                                <Shield className="h-4 w-4 mr-1 text-primary" />
                                Remove Admin
                              </Button>
                            ) : (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-200 text-gray-700 hover:bg-gray-100"
                                onClick={() => openConfirmDialog(userData, 'makeAdmin')}
                              >
                                <Shield className="h-4 w-4 mr-1 text-primary" />
                                Make Admin
                              </Button>
                            )}

                            {userData.banned ? (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-200 text-green-600 hover:bg-green-50"
                                onClick={() => openConfirmDialog(userData, 'unban')}
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                Unban
                              </Button>
                            ) : (
                              <Button
                                variant="outline"
                                size="sm"
                                className="border-gray-200 text-red-600 hover:bg-red-50"
                                onClick={() => openConfirmDialog(userData, 'ban')}
                              >
                                <Ban className="h-4 w-4 mr-1" />
                                Ban
                              </Button>
                            )}

                            <Button
                              variant="outline"
                              size="sm"
                              className="border-gray-200 text-gray-700 hover:bg-gray-100"
                              onClick={() => openEditDialog(userData)}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </CardContent>
        </Card>

        {/* Admin Tools */}
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-gray-800 text-lg">Admin Tools</CardTitle>
            <CardDescription>
              Access administrative tools and settings
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <Button
                variant="outline"
                className="h-auto py-6 flex flex-col items-center justify-center gap-2 border-gray-200 hover:bg-gray-50"
                onClick={() => navigate('/dashboard/admin/subscription-plans')}
              >
                <CreditCard className="h-8 w-8 text-primary" />
                <div className="text-center">
                  <h3 className="font-medium">Subscription Plans</h3>
                  <p className="text-sm text-gray-500">Manage PayPal subscription plans</p>
                </div>
              </Button>

              <Button
                variant="outline"
                className="h-auto py-6 flex flex-col items-center justify-center gap-2 border-gray-200 hover:bg-gray-50"
                onClick={() => navigate('/dashboard/admin/ai-model-settings')}
              >
                <Bot className="h-8 w-8 text-primary" />
                <div className="text-center">
                  <h3 className="font-medium">AI Model Settings</h3>
                  <p className="text-sm text-gray-500">Configure AI providers and models</p>
                </div>
              </Button>

              <Button
                variant="outline"
                className="h-auto py-6 flex flex-col items-center justify-center gap-2 border-gray-200 hover:bg-gray-50"
                onClick={() => navigate('/dashboard/admin/plan-features')}
              >
                <Settings className="h-8 w-8 text-primary" />
                <div className="text-center">
                  <h3 className="font-medium">Plan Features</h3>
                  <p className="text-sm text-gray-500">Configure plan-based feature access</p>
                </div>
              </Button>

              <Button
                variant="outline"
                className="h-auto py-6 flex flex-col items-center justify-center gap-2 border-gray-200 hover:bg-gray-50"
                onClick={() => navigate('/dashboard/admin/email-templates')}
              >
                <Mail className="h-8 w-8 text-primary" />
                <div className="text-center">
                  <h3 className="font-medium">Email Templates</h3>
                  <p className="text-sm text-gray-500">Manage email templates and content</p>
                </div>
              </Button>

              {/* Add more admin tools here as needed */}
            </div>
          </CardContent>
        </Card>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-gray-800 text-lg flex items-center">
                <Users className="h-5 w-5 mr-2 text-blue-500" />
                Total Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-800">{users.length}</div>
              <p className="text-gray-500 text-sm">Registered accounts</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-gray-800 text-lg flex items-center">
                <Shield className="h-5 w-5 mr-2 text-purple-500" />
                Platform Admins
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-800">
                {users.filter(u => u.platform_admin).length}
              </div>
              <p className="text-gray-500 text-sm">Users with admin privileges</p>
            </CardContent>
          </Card>

          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader className="pb-2">
              <CardTitle className="text-gray-800 text-lg flex items-center">
                <Ban className="h-5 w-5 mr-2 text-red-500" />
                Banned Users
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-3xl font-bold text-gray-800">
                {users.filter(u => u.banned).length}
              </div>
              <p className="text-gray-500 text-sm">Accounts with restricted access</p>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onOpenChange={setConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>
              {actionType === 'ban' && 'Ban User'}
              {actionType === 'unban' && 'Unban User'}
              {actionType === 'makeAdmin' && 'Grant Admin Privileges'}
              {actionType === 'removeAdmin' && 'Remove Admin Privileges'}
            </DialogTitle>
            <DialogDescription>
              {actionType === 'ban' && 'This will prevent the user from accessing the platform.'}
              {actionType === 'unban' && 'This will restore the user\'s access to the platform.'}
              {actionType === 'makeAdmin' && 'This will grant the user full platform administration privileges.'}
              {actionType === 'removeAdmin' && 'This will remove the user\'s platform administration privileges.'}
            </DialogDescription>
          </DialogHeader>

          {selectedUser && (
            <div className="p-4 border border-gray-200 rounded-md bg-gray-50 mb-4">
              <div className="flex items-start gap-3">
                <AlertTriangle className="h-5 w-5 text-amber-500 mt-0.5" />
                <div>
                  <h3 className="text-gray-800 font-medium">Confirm Action</h3>
                  <p className="text-gray-600 text-sm mt-1">
                    {actionType === 'ban' && `Are you sure you want to ban ${selectedUser.email}?`}
                    {actionType === 'unban' && `Are you sure you want to unban ${selectedUser.email}?`}
                    {actionType === 'makeAdmin' && `Are you sure you want to make ${selectedUser.email} a platform administrator?`}
                    {actionType === 'removeAdmin' && `Are you sure you want to remove ${selectedUser.email}'s administrator privileges?`}
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="flex justify-between sm:justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setConfirmDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant={actionType === 'ban' ? 'destructive' : 'default'}
              onClick={handleAction}
              disabled={loading}
            >
              {loading && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
              {actionType === 'ban' && 'Ban User'}
              {actionType === 'unban' && 'Unban User'}
              {actionType === 'makeAdmin' && 'Grant Admin Access'}
              {actionType === 'removeAdmin' && 'Remove Admin Access'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit User Subscription</DialogTitle>
            <DialogDescription>
              Update subscription tier and status for {selectedUser?.email}
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="subscription-tier" className="text-right">
                Tier
              </Label>
              <Select
                value={editFormData.subscription_tier}
                onValueChange={(value) => setEditFormData({ ...editFormData, subscription_tier: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue placeholder="Select a subscription tier" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="starter">Starter</SelectItem>
                  <SelectItem value="growth">Growth</SelectItem>
                  <SelectItem value="pro">Pro</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="subscription-status" className="text-right">
                Status
              </Label>
              <Select
                value={editFormData.subscription_status}
                onValueChange={(value) => setEditFormData({ ...editFormData, subscription_status: value })}
              >
                <SelectTrigger className="col-span-3">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="expired">Expired</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={updateUserSubscription}>
              Update Subscription
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </DashboardLayout>
  );
};

export default PlatformAdmin;




