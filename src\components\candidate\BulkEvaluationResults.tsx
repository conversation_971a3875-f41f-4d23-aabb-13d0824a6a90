import React, { useState, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import {
  Trophy,
  Star,
  TrendingUp,
  TrendingDown,
  ChevronDown,
  ChevronUp,
  Filter,
  Download,
  Users,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from 'lucide-react';
import { BulkEvaluationResult, CandidateRanking } from '@/services/cv-processing/jobMatcher';

interface BulkEvaluationResultsProps {
  evaluationResult: BulkEvaluationResult;
  onCandidateSelect?: (candidateId: string) => void;
  onExportResults?: () => void;
}

type SortField = 'rank' | 'name' | 'overallScore' | 'recommendation';
type SortDirection = 'asc' | 'desc';
type FilterRecommendation = 'all' | 'Hire' | 'Consider' | 'Reject';

const BulkEvaluationResults: React.FC<BulkEvaluationResultsProps> = ({
  evaluationResult,
  onCandidateSelect,
  onExportResults,
}) => {
  const [sortField, setSortField] = useState<SortField>('rank');
  const [sortDirection, setSortDirection] = useState<SortDirection>('asc');
  const [filterRecommendation, setFilterRecommendation] = useState<FilterRecommendation>('all');
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());

  // Get recommendation badge variant
  const getRecommendationBadge = (recommendation: string) => {
    switch (recommendation) {
      case 'Hire':
        return <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          Hire
        </Badge>;
      case 'Consider':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800 border-yellow-200">
          <AlertTriangle className="w-3 h-3 mr-1" />
          Consider
        </Badge>;
      case 'Reject':
        return <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
          <XCircle className="w-3 h-3 mr-1" />
          Reject
        </Badge>;
      default:
        return <Badge variant="outline">{recommendation}</Badge>;
    }
  };

  // Get score color based on value
  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  // Sort and filter candidates
  const sortedAndFilteredCandidates = useMemo(() => {
    let filtered = evaluationResult.rankings;

    // Apply recommendation filter
    if (filterRecommendation !== 'all') {
      filtered = filtered.filter(candidate => candidate.recommendation === filterRecommendation);
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortField) {
        case 'rank':
          // Rank is based on position in original array (already sorted by score)
          aValue = evaluationResult.rankings.indexOf(a);
          bValue = evaluationResult.rankings.indexOf(b);
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'overallScore':
          aValue = a.overallScore;
          bValue = b.overallScore;
          break;
        case 'recommendation':
          aValue = a.recommendation;
          bValue = b.recommendation;
          break;
        default:
          return 0;
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return sorted;
  }, [evaluationResult.rankings, sortField, sortDirection, filterRecommendation]);

  // Toggle row expansion
  const toggleRowExpansion = (candidateId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(candidateId)) {
      newExpanded.delete(candidateId);
    } else {
      newExpanded.add(candidateId);
    }
    setExpandedRows(newExpanded);
  };

  // Handle sorting
  const handleSort = (field: SortField) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Total Candidates</p>
                <p className="text-2xl font-bold text-gray-900">{evaluationResult.totalCandidates}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Average Score</p>
                <p className="text-2xl font-bold text-gray-900">
                  {evaluationResult.evaluationSummary.averageScore}%
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-gray-600">Hire Recommendations</p>
                <p className="text-2xl font-bold text-green-600">
                  {evaluationResult.evaluationSummary.hireRecommendations}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Trophy className="h-5 w-5 text-yellow-500" />
              <div>
                <p className="text-sm font-medium text-gray-600">Top Candidate</p>
                <p className="text-lg font-bold text-gray-900">
                  {evaluationResult.topCandidate?.name || 'N/A'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <CardTitle className="text-lg font-semibold text-gray-900">
              Candidate Rankings
            </CardTitle>
            <div className="flex flex-col sm:flex-row gap-2">
              <Select value={filterRecommendation} onValueChange={(value: FilterRecommendation) => setFilterRecommendation(value)}>
                <SelectTrigger className="w-[180px]">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Filter by recommendation" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Recommendations</SelectItem>
                  <SelectItem value="Hire">Hire</SelectItem>
                  <SelectItem value="Consider">Consider</SelectItem>
                  <SelectItem value="Reject">Reject</SelectItem>
                </SelectContent>
              </Select>
              {onExportResults && (
                <Button variant="outline" onClick={onExportResults}>
                  <Download className="w-4 h-4 mr-2" />
                  Export Results
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Results Table */}
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-16">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('rank')}
                      className="font-semibold"
                    >
                      Rank
                      {sortField === 'rank' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('name')}
                      className="font-semibold"
                    >
                      Candidate Name
                      {sortField === 'name' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('overallScore')}
                      className="font-semibold"
                    >
                      Overall Score
                      {sortField === 'overallScore' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead>Skills</TableHead>
                  <TableHead>Experience</TableHead>
                  <TableHead>Education</TableHead>
                  <TableHead>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSort('recommendation')}
                      className="font-semibold"
                    >
                      Recommendation
                      {sortField === 'recommendation' && (
                        sortDirection === 'asc' ? <ChevronUp className="w-4 h-4 ml-1" /> : <ChevronDown className="w-4 h-4 ml-1" />
                      )}
                    </Button>
                  </TableHead>
                  <TableHead className="w-16">Details</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sortedAndFilteredCandidates.map((candidate, index) => {
                  const originalRank = evaluationResult.rankings.indexOf(candidate) + 1;
                  const isExpanded = expandedRows.has(candidate.candidateId);
                  
                  return (
                    <React.Fragment key={candidate.candidateId}>
                      <TableRow className="hover:bg-gray-50">
                        <TableCell className="font-medium">
                          <div className="flex items-center">
                            {originalRank === 1 && <Trophy className="w-4 h-4 text-yellow-500 mr-1" />}
                            #{originalRank}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="link"
                            className="p-0 h-auto font-medium text-blue-600 hover:text-blue-800"
                            onClick={() => onCandidateSelect?.(candidate.candidateId)}
                          >
                            {candidate.name}
                          </Button>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <span className={`font-semibold ${getScoreColor(candidate.overallScore)}`}>
                              {candidate.overallScore}%
                            </span>
                            <Progress value={candidate.overallScore} className="w-16 h-2" />
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className={getScoreColor(candidate.skillsScore)}>
                            {candidate.skillsScore}%
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className={getScoreColor(candidate.experienceScore)}>
                            {candidate.experienceScore}%
                          </span>
                        </TableCell>
                        <TableCell>
                          <span className={getScoreColor(candidate.educationScore)}>
                            {candidate.educationScore}%
                          </span>
                        </TableCell>
                        <TableCell>
                          {getRecommendationBadge(candidate.recommendation)}
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleRowExpansion(candidate.candidateId)}
                          >
                            {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                          </Button>
                        </TableCell>
                      </TableRow>
                      
                      {/* Expanded Row Details */}
                      {isExpanded && (
                        <TableRow>
                          <TableCell colSpan={8} className="bg-gray-50 p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <h4 className="font-semibold text-green-700 mb-2 flex items-center">
                                  <TrendingUp className="w-4 h-4 mr-1" />
                                  Strengths
                                </h4>
                                <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                                  {candidate.strengths.map((strength, idx) => (
                                    <li key={idx}>{strength}</li>
                                  ))}
                                </ul>
                              </div>
                              <div>
                                <h4 className="font-semibold text-red-700 mb-2 flex items-center">
                                  <TrendingDown className="w-4 h-4 mr-1" />
                                  Areas for Improvement
                                </h4>
                                <ul className="list-disc list-inside space-y-1 text-sm text-gray-700">
                                  {candidate.areasForImprovement.map((area, idx) => (
                                    <li key={idx}>{area}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {/* Show message if no candidates match filter */}
          {sortedAndFilteredCandidates.length === 0 && (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800">No candidates found</h3>
              <p className="text-gray-500 mt-1">
                Try adjusting your filters to see more results.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Error Display */}
      {evaluationResult.errors && evaluationResult.errors.length > 0 && (
        <Card className="border-red-200">
          <CardHeader>
            <CardTitle className="text-red-700 flex items-center">
              <AlertTriangle className="w-5 h-5 mr-2" />
              Evaluation Errors ({evaluationResult.errors.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {evaluationResult.errors.map((error, index) => (
                <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="font-medium text-red-800">{error.candidateName}</p>
                  <p className="text-sm text-red-600">{error.error}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default BulkEvaluationResults;
