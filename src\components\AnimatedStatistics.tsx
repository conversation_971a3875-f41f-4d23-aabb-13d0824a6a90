import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';

gsap.registerPlugin(ScrollTrigger);

interface StatItem {
  value: number;
  label: string;
  suffix?: string;
  prefix?: string;
  color: string;
  icon: React.ReactNode;
  description: string;
}

interface AnimatedStatisticsProps {
  stats: StatItem[];
}

export const AnimatedStatistics: React.FC<AnimatedStatisticsProps> = ({ stats }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [counters, setCounters] = useState<number[]>(stats.map(() => 0));

  useEffect(() => {
    if (!containerRef.current) return;

    const container = containerRef.current;
    
    // Create a GSAP timeline for the animation
    const tl = gsap.timeline({
      scrollTrigger: {
        trigger: container,
        start: 'top 80%',
        toggleActions: 'play none none none'
      }
    });
    
    // Animate each counter
    stats.forEach((stat, index) => {
      tl.to(
        counters,
        {
          duration: 2,
          ease: 'power2.out',
          onUpdate: () => {
            setCounters(prev => {
              const newCounters = [...prev];
              newCounters[index] = Math.round(gsap.utils.interpolate(0, stat.value, tl.progress()));
              return newCounters;
            });
          }
        },
        0
      );
    });
    
    return () => {
      if (tl.scrollTrigger) {
        tl.scrollTrigger.kill();
      }
      tl.kill();
    };
  }, [stats]);

  return (
    <div ref={containerRef} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
      {stats.map((stat, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: index * 0.1 }}
          className="bg-card-gradient rounded-xl p-8 border border-gray-800 hover:border-gray-700 transition-colors duration-300"
        >
          <div className={`w-16 h-16 rounded-full bg-${stat.color}/10 flex items-center justify-center mb-6`}>
            {stat.icon}
          </div>
          <h3 className="text-xl font-bold text-white mb-2">{stat.label}</h3>
          <p className="text-gray-300 mb-4">{stat.description}</p>
          <div className={`text-3xl font-bold text-${stat.color}`}>
            {stat.prefix}{counters[index]}{stat.suffix}
          </div>
        </motion.div>
      ))}
    </div>
  );
};
