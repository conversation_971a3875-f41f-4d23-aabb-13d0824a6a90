import { format } from 'date-fns';
import { ReportData, ReportType } from '../types';

/**
 * Helper function to generate an executive summary based on report type and data
 * @param reportData The report data
 * @param reportType The type of report
 * @returns The executive summary text
 */
export const generateExecutiveSummary = (reportData: ReportData, reportType: ReportType): string => {
  // If executive summary is provided, use it
  if (reportData.executiveSummary) {
    return reportData.executiveSummary;
  }

  // Otherwise, generate a summary based on the report type and data
  let summary = '';

  switch (reportType) {
    case 'recruitment_funnel':
      // Get the first and last stages of the funnel
      const firstStage = reportData.data[0];
      const lastStage = reportData.data[reportData.data.length - 1];

      if (firstStage && lastStage) {
        const totalApplications = firstStage.count || 0;
        const hiredCount = lastStage.count || 0;
        const conversionRate = totalApplications > 0 ? (hiredCount / totalApplications * 100).toFixed(1) : '0.0';

        summary = `This report analyzes your recruitment funnel from ${format(reportData.date, 'MMMM yyyy')}. `;
        summary += `You received ${totalApplications} applications and made ${hiredCount} hires, `;
        summary += `resulting in an overall conversion rate of ${conversionRate}%. `;

        if (parseFloat(conversionRate) < 10) {
          summary += 'Your conversion rate is below the industry average of 10-15%. ';
          summary += 'Consider reviewing your screening and interview processes to improve efficiency.';
        } else if (parseFloat(conversionRate) < 15) {
          summary += 'Your conversion rate is within the industry average range of 10-15%. ';
          summary += 'Continue monitoring for opportunities to improve.';
        } else {
          summary += 'Your conversion rate is above the industry average of 10-15%. ';
          summary += 'Your recruitment process is performing well.';
        }
      } else {
        summary = 'This report analyzes your recruitment funnel. No data is available for the selected period.';
      }
      break;

    case 'time_to_hire':
      if (reportData.data && reportData.data.length > 0) {
        // Calculate average time to hire
        const totalDays = reportData.data.reduce((sum, item) => sum + item.days, 0);
        const avgDays = Math.round(totalDays / reportData.data.length);

        // Find fastest and slowest positions
        let fastestPosition = { position: '', days: Number.MAX_SAFE_INTEGER };
        let slowestPosition = { position: '', days: 0 };

        reportData.data.forEach(item => {
          if (item.days < fastestPosition.days) {
            fastestPosition = { position: item.position, days: item.days };
          }
          if (item.days > slowestPosition.days) {
            slowestPosition = { position: item.position, days: item.days };
          }
        });

        summary = `This report analyzes your time-to-hire metrics from ${format(reportData.date, 'MMMM yyyy')}. `;
        summary += `Your average time to hire across all positions is ${avgDays} days. `;

        if (fastestPosition.position) {
          summary += `The fastest position to fill was ${fastestPosition.position} at ${fastestPosition.days} days, `;
        }

        if (slowestPosition.position) {
          summary += `while the slowest was ${slowestPosition.position} at ${slowestPosition.days} days. `;
        }

        if (avgDays > 30) {
          summary += 'Your average time to hire is above the industry benchmark of 30 days. ';
          summary += 'Consider streamlining your recruitment process to reduce time to hire.';
        } else {
          summary += 'Your average time to hire is within or below the industry benchmark of 30 days. ';
          summary += 'Your recruitment process is efficient in terms of time to hire.';
        }
      } else {
        summary = 'This report analyzes your time-to-hire metrics. No data is available for the selected period.';
      }
      break;

    case 'source_effectiveness':
      if (reportData.data && reportData.data.length > 0) {
        // Find best source by quality and conversion
        let bestQualitySource = { source: '', quality: 0 };
        let bestConversionSource = { source: '', conversion: 0 };
        let totalApplicants = 0;
        let totalHires = 0;

        reportData.data.forEach(item => {
          totalApplicants += item.applicants || 0;
          totalHires += item.hires || 0;

          // Check for best quality
          if (item.quality > bestQualitySource.quality) {
            bestQualitySource = { source: item.source, quality: item.quality };
          }

          // Check for best conversion
          const conversion = item.applicants > 0 ? (item.hires / item.applicants) * 100 : 0;
          if (conversion > bestConversionSource.conversion) {
            bestConversionSource = { source: item.source, conversion };
          }
        });

        summary = `This report analyzes the effectiveness of your recruitment sources from ${format(reportData.date, 'MMMM yyyy')}. `;
        summary += `You received ${totalApplicants} applications and made ${totalHires} hires across all sources. `;

        if (bestQualitySource.source) {
          summary += `${bestQualitySource.source} provided the highest quality candidates with a score of ${bestQualitySource.quality}%. `;
        }

        if (bestConversionSource.source) {
          summary += `${bestConversionSource.source} had the best conversion rate at ${bestConversionSource.conversion.toFixed(1)}%. `;
        }

        summary += 'Consider allocating more resources to your best-performing sources to optimize recruitment efficiency.';
      } else {
        summary = 'This report analyzes the effectiveness of your recruitment sources. No data is available for the selected period.';
      }
      break;

    default:
      summary = `This report provides an analysis of your ${reportType.replace(/_/g, ' ')} metrics.`;
      break;
  }

  return summary;
};
