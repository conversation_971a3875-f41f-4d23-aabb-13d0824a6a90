import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  ArrowLeft,
  FileText,
  BarChart3,
  Download,
  Share2,
  Loader2,
  ArrowUpRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { showRateLimitToast, isRateLimitError, getErrorMessage } from '@/components/ui/rate-limit-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyContext } from '@/contexts/CompanyContext';
import { supabase } from '@/lib/supabase';
import CVUploadCard from '@/components/cv/CVUploadCard';
import CVPreview from '@/components/cv/CVPreview.jsx';
import CVEvaluationResults from '@/components/cv/CVEvaluationResults';
import ReferralSourceSelector from '@/components/cv/ReferralSourceSelector';
import SubscriptionGuard from '@/components/guards/SubscriptionGuard';
import ReportExportButton from '@/components/reports/ReportExportButton';
import { useReportExport } from '@/hooks/use-reports';
import EvaluateModal from '@/components/cv/EvaluateModal';
import BulkCVProcessingList, { ProcessedFile } from '@/components/cv/BulkCVProcessingList';
import BulkEvaluationAssignment from '@/components/cv/BulkEvaluationAssignment';
import { assignCandidateToJobs, AssignmentData } from '@/services/supabase/assignments';
import { sendAssignmentNotifications, sendBulkAssignmentSummary } from '@/services/notifications/assignmentNotifications';

// Import CV processing services
import { processCVFile, ParsedCV } from '@/services/cv-processing/cvParser';
import { matchCandidate, rankCandidatesForJob, matchCandidateToAllCompanies } from '@/services/cv-processing/jobMatcher';
import { getSkillGapAnalysis } from '@/services/cv-processing/skillAnalyzer';

// Helper function to extract skill names from analysis text
const extractSkillNamesFromText = (text: string): string[] => {
  if (!text) return [];

  // Common skill keywords that might appear in analysis text
  const skillKeywords = [
    'experience', 'knowledge', 'proficiency', 'expertise', 'skills',
    'familiar with', 'background in', 'understanding of'
  ];

  // Try to extract skills from sentences containing skill keywords
  const skills: string[] = [];

  // Split by sentence
  const sentences = text.split(/[.!?]/).filter(s => s.trim().length > 0);

  for (const sentence of sentences) {
    // Check if sentence contains skill keywords
    if (skillKeywords.some(keyword => sentence.toLowerCase().includes(keyword))) {
      // Extract potential skill names (capitalized words or technical terms)
      const words = sentence.match(/\b([A-Z][a-z]+|[A-Z]+|[a-z]+\+\+|[a-z]#|[a-z]+\.js)\b/g) || [];

      // Add words that are likely skills (exclude common words)
      const commonWords = ['the', 'and', 'but', 'or', 'in', 'on', 'at', 'to', 'for', 'with', 'by'];
      words.forEach(word => {
        if (!commonWords.includes(word.toLowerCase()) && word.length > 2) {
          skills.push(word);
        }
      });
    }
  }

  // If no skills found with the above method, try a simpler approach
  if (skills.length === 0) {
    // Look for technical terms that are likely skills
    const techTerms = text.match(/\b(JavaScript|Python|Java|C\+\+|React|Angular|Vue|Node\.js|SQL|AWS|Azure|DevOps|UI\/UX|HTML|CSS)\b/g);
    if (techTerms) {
      skills.push(...techTerms);
    }
  }

  // Remove duplicates and return
  return [...new Set(skills)];
};

// Helper function to extract skill name from strength text
const extractSkillFromStrength = (strength: string): string => {
  if (!strength) return 'Unspecified Skill';

  // If strength is short, use it directly
  if (strength.length < 30) return strength;

  // Try to extract the main skill from the strength text
  const skillPhrases = [
    'Strong in', 'Expertise in', 'Proficient in', 'Skilled in',
    'Experience with', 'Knowledge of', 'Background in'
  ];

  for (const phrase of skillPhrases) {
    if (strength.includes(phrase)) {
      const afterPhrase = strength.split(phrase)[1];
      if (afterPhrase) {
        // Take the first few words after the phrase
        const words = afterPhrase.trim().split(' ').slice(0, 3).join(' ');
        return words;
      }
    }
  }

  // If no specific phrase found, take the first part of the strength
  return strength.split(',')[0].trim();
};

// Mock data for CV evaluation
const mockCandidateData = {
  candidateName: "John Doe",
  position: "Frontend Developer",
  overallMatch: 85,
  skills: [
    { name: "JavaScript", match: 90, required: true },
    { name: "React.js", match: 85, required: true },
    { name: "TypeScript", match: 75, required: false },
    { name: "HTML/CSS", match: 95, required: true },
    { name: "Node.js", match: 70, required: false },
    { name: "Git", match: 80, required: true }
  ],
  experience: [
    { title: "Senior Frontend Developer", company: "ABC Company", duration: "2 years", relevance: 90 },
    { title: "Frontend Developer", company: "XYZ Inc.", duration: "3 years", relevance: 85 }
  ],
  education: [
    { degree: "Bachelor of Science in Computer Science", institution: "University of Technology", year: "2017", relevance: 80 }
  ],
  strengths: [
    "Strong experience with modern JavaScript frameworks",
    "Excellent understanding of responsive design principles",
    "Proven track record of building complex web applications",
    "Good knowledge of frontend performance optimization"
  ],
  weaknesses: [
    "Limited experience with backend technologies",
    "No experience with GraphQL",
    "Could improve knowledge of testing frameworks"
  ]
};



interface EvaluationResult {
  candidateId: string;
  candidateName: string;
  position: string;
  overallMatch: number;
  cvUrl?: string; // URL to the uploaded CV file
  skills: Array<{ name: string; match: number; required: boolean }>;
  experience: Array<{ title: string; company: string; duration: string; relevance: number }>;
  education: Array<{ degree: string; institution: string; year: string; relevance: number }>;
  strengths: string[];
  weaknesses: string[];
  // Enhanced evaluation fields
  isCompanyEvaluation?: boolean;
  jobSpecificMatches?: any[];
  topMatchingJobs?: any[];
  // Cover letter fields
  coverLetterContent?: string | null;
  coverLetterUrl?: string | null;
  coverLetterScore?: number;
  coverLetterAnalysis?: string;
}

const CVEvaluation = () => {
  const navigate = useNavigate();
  const { jobId } = useParams<{ jobId: string }>();
  const { user } = useAuth();
  const { activeCompanyId } = useCompanyContext();
  const { toast } = useToast();
  const { availableFormats, subscriptionTier } = useReportExport();

  const [activeTab, setActiveTab] = useState('upload');
  const [selectedCandidateId, setSelectedCandidateId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [evaluationResult, setEvaluationResult] = useState<EvaluationResult | null>(null);

  // State for the evaluate modal
  const [isEvaluateModalOpen, setIsEvaluateModalOpen] = useState(false);
  const [uploadedCandidateId, setUploadedCandidateId] = useState<string>('');
  const [uploadedCandidateName, setUploadedCandidateName] = useState<string>('');

  // State for bulk upload
  const [processedFiles, setProcessedFiles] = useState<ProcessedFile[]>([]);
  const [isBulkMode, setIsBulkMode] = useState<boolean>(false);
  const [processingFile, setProcessingFile] = useState<File | null>(null);
  const [processingIndex, setProcessingIndex] = useState<number>(0);
  const [processedCandidates, setProcessedCandidates] = useState<{
    id: string;
    name: string;
    isEvaluated: boolean;
  }[]>([]);

  // State for referral source
  const [referralSource, setReferralSource] = useState<string>('Direct Upload');

  // State for evaluation progress
  const [evaluationProgress, setEvaluationProgress] = useState<{
    isEvaluating: boolean;
    step: string;
    progress: number;
  }>({
    isEvaluating: false,
    step: '',
    progress: 0
  });

  // State for assignment interface
  const [showAssignmentInterface, setShowAssignmentInterface] = useState(false);
  const [assignmentCandidateId, setAssignmentCandidateId] = useState<string>('');
  const [assignmentCandidateName, setAssignmentCandidateName] = useState<string>('');
  const [assignmentJobMatches, setAssignmentJobMatches] = useState<any[]>([]);

  // Prepare report data for export
  const getCandidateReportData = () => {
    if (!evaluationResult) return null;

    const date = new Date();

    return {
      title: `CV Evaluation Report - ${evaluationResult.candidateName}`,
      description: `Evaluation results for ${evaluationResult.candidateName} for the ${evaluationResult.position} position`,
      date,
      companyName: 'Your Company',
      data: [
        { category: 'Overall Match', value: `${evaluationResult.overallMatch}%` },
        ...evaluationResult.skills.map(skill => ({
          category: `Skill: ${skill.name}`,
          value: `${skill.match}%`,
          required: skill.required ? 'Required' : 'Optional'
        }))
      ],
      columns: [
        { header: 'Category', dataKey: 'category' },
        { header: 'Value', dataKey: 'value' },
        { header: 'Notes', dataKey: 'required' }
      ],
      summary: {
        'Candidate': evaluationResult.candidateName,
        'Position': evaluationResult.position,
        'Overall Match': `${evaluationResult.overallMatch}%`,
        'Key Strengths': evaluationResult.strengths.join(', '),
        'Areas for Improvement': evaluationResult.weaknesses.join(', ')
      }
    };
  };

  // Handle candidate assignment
  const handleCandidateAssignment = async (assignments: AssignmentData[]) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to assign candidates.',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);

      // Assign candidate to selected jobs
      const result = await assignCandidateToJobs(assignmentCandidateId, assignments);

      // Send notifications to companies
      await sendAssignmentNotifications({
        candidateId: assignmentCandidateId,
        candidateName: assignmentCandidateName,
        assignments,
        assignedBy: user.id,
        assignedByName: user.user_metadata?.name || user.email
      });

      // Send summary notification to the assigner
      await sendBulkAssignmentSummary(
        user.id,
        assignments.length,
        result.successful,
        result.failed
      );

      if (result.successful > 0) {
        toast({
          title: 'Assignment Successful',
          description: `${assignmentCandidateName} has been assigned to ${result.successful} job${result.successful > 1 ? 's' : ''}.`,
        });

        // Redirect to candidate profile
        setTimeout(() => {
          navigate(`/dashboard/cvs/${assignmentCandidateId}`);
        }, 1500);
      }

      if (result.failed > 0) {
        toast({
          title: 'Partial Assignment',
          description: `${result.failed} assignment${result.failed > 1 ? 's' : ''} failed. Please check the details.`,
          variant: 'destructive',
        });
      }

    } catch (error) {
      console.error('Assignment error:', error);
      toast({
        title: 'Assignment Failed',
        description: 'Failed to assign candidate. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Close assignment interface
  const handleCloseAssignment = () => {
    setShowAssignmentInterface(false);
    setAssignmentCandidateId('');
    setAssignmentCandidateName('');
    setAssignmentJobMatches([]);
  };

  // Fetch job details and candidates on mount
  useEffect(() => {
    if (jobId) {
      fetchJobDetails();
      fetchCandidates();
    } else {
      // If no job ID, we might want to fetch all candidates or do nothing
      console.log('No job ID provided, skipping job-specific data fetching');
    }
  }, [jobId]);

  // Fetch job details
  const fetchJobDetails = async () => {
    try {
      // In a real implementation, this would fetch job details from Supabase
      // For now, we'll use mock data
      console.log('Fetching job details for job ID:', jobId);
    } catch (error) {
      console.error('Error fetching job details:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch job details. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Fetch candidates
  const fetchCandidates = async () => {
    try {
      // In a real implementation, this would fetch candidates from Supabase
      // For now, we'll use mock data
      if (jobId) {
        console.log('Fetching candidates for job ID:', jobId);
        // Fetch candidates for a specific job
      } else {
        console.log('Fetching all candidates (no job ID specified)');
        // Fetch all candidates or recently uploaded ones
      }
    } catch (error) {
      console.error('Error fetching candidates:', error);
      toast({
        title: 'Error',
        description: 'Failed to fetch candidates. Please try again.',
        variant: 'destructive',
      });
    }
  };

  // Process a single CV file
  const processSingleCV = async (file: File, fileId: string): Promise<{
    candidateId: string;
    candidateName: string;
  } | null> => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to upload CVs.',
        variant: 'destructive',
      });
      return null;
    }

    try {
      // Update the file status to processing
      setProcessedFiles(prev =>
        prev.map(pf =>
          pf.id === fileId
            ? { ...pf, status: 'processing' as const, progress: 0 }
            : pf
        )
      );

      // Simulate processing progress
      const progressInterval = setInterval(() => {
        setProcessedFiles(prev => {
          const processingFile = prev.find(pf => pf.id === fileId);
          if (processingFile && processingFile.status === 'processing' && processingFile.progress < 90) {
            return prev.map(pf =>
              pf.id === fileId
                ? { ...pf, progress: pf.progress + Math.floor(Math.random() * 5) + 1 }
                : pf
            );
          }
          return prev;
        });
      }, 500);

      // Process the CV file with company ID for candidate pool
      // Pass activeCompanyId for candidate pool, jobId for direct job assignment
      const result = await processCVFile(file, activeCompanyId, user.id, jobId || null, referralSource);

      // Clear the progress interval
      clearInterval(progressInterval);

      // Update the file status to success
      setProcessedFiles(prev =>
        prev.map(pf =>
          pf.id === fileId
            ? {
                ...pf,
                status: 'success' as const,
                progress: 100,
                candidateId: result.candidate.id,
                candidateName: result.parsedCV.personalDetails.name || 'Candidate'
              }
            : pf
        )
      );

      return {
        candidateId: result.candidate.id,
        candidateName: result.parsedCV.personalDetails.name || 'Candidate'
      };
    } catch (error) {
      console.error('Error processing CV:', error);

      // Update the file status to error
      setProcessedFiles(prev =>
        prev.map(pf =>
          pf.id === fileId
            ? {
                ...pf,
                status: 'error' as const,
                progress: 0,
                error: 'Failed to process the CV. Please try again.'
              }
            : pf
        )
      );

      return null;
    }
  };

  // Handle multiple file upload completion
  const handleMultipleUploadComplete = async (files: File[]) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to upload CVs.',
        variant: 'destructive',
      });
      return;
    }

    // Set bulk mode
    setIsBulkMode(true);

    // Create initial file entries
    const initialFiles: ProcessedFile[] = files.map(file => ({
      id: Math.random().toString(36).substring(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      progress: 0,
      status: 'uploading' as const
    }));

    setProcessedFiles(initialFiles);

    // Process files sequentially to avoid overwhelming the API
    for (let i = 0; i < files.length; i++) {
      setProcessingIndex(i);
      setProcessingFile(files[i]);

      // Process the current file
      const result = await processSingleCV(files[i], initialFiles[i].id);

      // If successful, add to processed candidates
      if (result) {
        setProcessedCandidates(prev => [
          ...prev,
          {
            id: result.candidateId,
            name: result.candidateName,
            isEvaluated: false
          }
        ]);
      }
    }

    setProcessingFile(null);

    // Show toast notification
    toast({
      title: 'CV Processing Complete',
      description: `${files.length} CV${files.length > 1 ? 's' : ''} processed successfully.`,
    });
  };

  // Handle file upload completion
  const handleUploadComplete = async (file: File) => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'You must be logged in to upload CVs.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      // Process the CV file with company ID for candidate pool
      // Pass activeCompanyId for candidate pool, jobId for direct job assignment
      const result = await processCVFile(file, activeCompanyId, user.id, jobId || null, referralSource);

      // If we have a job ID, match the candidate to the job
      let matchResult = null;
      let skillGapAnalysis = null;
      let positionName = 'General Evaluation';

      try {
        if (jobId) {
          // Match the candidate to the job
          console.log('Matching candidate to specific job:', jobId);
          matchResult = await matchCandidate(result.candidate.id, jobId);

          // Get skill gap analysis
          skillGapAnalysis = await getSkillGapAnalysis(result.candidate.id, jobId);

          // Get job details to set position name
          positionName = 'Specific Job Position'; // This would come from job details
        } else {
          // Store the candidate ID and name for the evaluate modal
          setUploadedCandidateId(result.candidate.id);
          setUploadedCandidateName(result.parsedCV.personalDetails.name || 'Candidate');

          // Show the evaluate modal
          setIsEvaluateModalOpen(true);

          // Create a generic match result when no evaluation is requested
          // This will be updated if the user chooses to evaluate
          matchResult = {
            overallScore: 0, // No score without a job to match against
            skillsMatch: { score: 0, analysis: 'No evaluation performed' },
            experienceMatch: { score: 0, analysis: 'No evaluation performed' },
            educationMatch: { score: 0, analysis: 'No evaluation performed' },
            locationMatch: { score: 0, analysis: 'No evaluation performed' },
            strengths: [],
            gaps: [],
            recommendation: 'No evaluation performed'
          };
          positionName = 'No Evaluation';
        }
      } catch (matchError) {
        console.error('Error during candidate matching:', matchError);
        toast({
          title: 'Evaluation Warning',
          description: 'CV was processed but there was an issue with the evaluation. Basic information is still available.',
          variant: 'warning',
        });

        // Fallback match result
        matchResult = {
          overallScore: 0,
          skillsMatch: { score: 0, analysis: 'Evaluation failed' },
          experienceMatch: { score: 0, analysis: 'Evaluation failed' },
          educationMatch: { score: 0, analysis: 'Evaluation failed' },
          locationMatch: { score: 0, analysis: 'Evaluation failed' },
          strengths: [],
          gaps: [],
          recommendation: 'Evaluation failed'
        };
      }

      // Log the CV URL for debugging
      console.log('CV URL from upload result:', result.candidate.cv_url);

      // Format the evaluation result
      const formattedResult: EvaluationResult = {
        candidateId: result.candidate.id,
        candidateName: result.parsedCV.personalDetails.name,
        position: positionName, // Use the position name we determined earlier
        overallMatch: matchResult ? matchResult.overallScore : 0,
        cvUrl: result.candidate.cv_url, // Add the CV URL
        skills: (() => {
          // First, try to extract skills from the match result if available
          if (matchResult && matchResult.skillsMatch && matchResult.skillsMatch.analysis) {
            // Try to extract skill names from the analysis text
            const skillNames = extractSkillNamesFromText(matchResult.skillsMatch.analysis);
            if (skillNames.length > 0) {
              return skillNames.map(name => ({
                name,
                match: matchResult.skillsMatch.score || 70,
                required: false
              }));
            }
          }

          // If no skills from match result, try to use strengths
          if (matchResult && matchResult.strengths && matchResult.strengths.length > 0) {
            return matchResult.strengths.slice(0, 5).map((strength: string) => {
              // Extract skill name from strength text
              const skillName = extractSkillFromStrength(strength);
              return {
                name: skillName,
                match: 80, // Strengths are generally high match
                required: false
              };
            });
          }

          // If still no skills, use extracted skills from the CV
          if (result.extractedSkills && result.extractedSkills.technical) {
            return result.extractedSkills.technical.map((skill: any) => ({
              name: typeof skill === 'string' ? skill : skill.name,
              match: matchResult && matchResult.overallScore ? Math.round(matchResult.overallScore * 0.9) : 0,
              required: false
            }));
          }

          // Fallback if no skills are available
          return [
            { name: "No skills extracted", match: 0, required: false }
          ];
        })(),
        experience: result.parsedCV.workExperience ?
          result.parsedCV.workExperience.map((exp: any) => ({
            title: exp.title || 'Unknown Position',
            company: exp.company || 'Unknown Company',
            duration: `${exp.startDate || 'N/A'} - ${exp.endDate || 'Present'}`,
            relevance: jobId ? Math.floor(Math.random() * 30) + 70 : 0 // Simulated relevance score if job exists
          })) :
          // Fallback if work experience is missing
          [
            {
              title: "Software Developer",
              company: "Example Corp",
              duration: "2020 - Present",
              relevance: 90
            }
          ],
        education: result.parsedCV.education ?
          result.parsedCV.education.map((edu: any) => ({
            degree: edu.degree || 'Degree',
            institution: edu.institution || 'Institution',
            year: edu.startDate || 'N/A',
            relevance: jobId ? Math.floor(Math.random() * 30) + 70 : 0 // Simulated relevance score if job exists
          })) :
          // Fallback if education is missing
          [
            {
              degree: "Bachelor of Science in Computer Science",
              institution: "Example University",
              year: "2016",
              relevance: 85
            }
          ],
        strengths: matchResult && matchResult.strengths ?
          // Limit to 5 strengths for better display
          matchResult.strengths.slice(0, 5) :
          // Generate strengths based on match score if none available
          matchResult && matchResult.overallScore > 70 ?
            [`Strong overall match (${matchResult.overallScore}%)`] :
            [],

        weaknesses: matchResult && matchResult.gaps ?
          // Limit to 5 weaknesses for better display
          matchResult.gaps.slice(0, 5) :
          // Generate weaknesses based on match score if none available
          matchResult && matchResult.overallScore < 50 ?
            [`Lower overall match (${matchResult.overallScore}%)`] :
            []
      };

      // Store the evaluation result in state (will be useful if we need it later)
      setEvaluationResult(formattedResult);

      // Show toast notification about redirection
      toast({
        title: 'CV Processed Successfully',
        description: 'Redirecting to candidate profile...',
      });

      // Navigate to the candidate-specific page with the evaluation tab active
      navigate(`/dashboard/cvs/${result.candidate.id}?tab=evaluation`);
    } catch (error) {
      console.error('Error processing CV:', error);
      toast({
        title: 'Error',
        description: 'Failed to process the CV. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle evaluation of a single candidate from the bulk list
  const handleBulkItemEvaluate = (candidateId: string, candidateName: string) => {
    // Set the candidate ID and name for the evaluate modal
    setUploadedCandidateId(candidateId);
    setUploadedCandidateName(candidateName);

    // Show the evaluate modal
    setIsEvaluateModalOpen(true);
  };

  // Handle evaluation of all candidates in the bulk list
  const handleEvaluateAll = async () => {
    if (processedCandidates.length === 0) {
      toast({
        title: 'Error',
        description: 'No candidates available for evaluation.',
        variant: 'destructive'
      });
      return;
    }

    // Show confirmation toast
    toast({
      title: 'Bulk Evaluation',
      description: `Starting evaluation of ${processedCandidates.length} candidates...`,
    });

    // Set evaluation progress
    setEvaluationProgress({
      isEvaluating: true,
      step: 'Initializing bulk evaluation...',
      progress: 5
    });

    // Switch to the results tab to show progress
    setActiveTab('results');

    // Evaluate each candidate sequentially
    for (let i = 0; i < processedCandidates.length; i++) {
      const candidate = processedCandidates[i];

      if (candidate.isEvaluated) {
        continue; // Skip already evaluated candidates
      }

      try {
        // Update progress
        setEvaluationProgress({
          isEvaluating: true,
          step: `Evaluating candidate ${i + 1} of ${processedCandidates.length}...`,
          progress: Math.round((i / processedCandidates.length) * 90) + 5
        });

        // Evaluate the candidate against all companies (default mode for bulk)
        const matchResult = await matchCandidateToAllCompanies(candidate.id);

        // Mark the candidate as evaluated
        setProcessedCandidates(prev =>
          prev.map(pc =>
            pc.id === candidate.id
              ? { ...pc, isEvaluated: true }
              : pc
          )
        );

        // Update the file status to evaluated
        setProcessedFiles(prev =>
          prev.map(pf =>
            pf.candidateId === candidate.id
              ? { ...pf, isEvaluated: true }
              : pf
          )
        );
      } catch (error) {
        console.error(`Error evaluating candidate ${candidate.id}:`, error);

        // Show error toast but continue with other candidates
        toast({
          title: 'Evaluation Error',
          description: `Failed to evaluate ${candidate.name}. Continuing with other candidates.`,
          variant: 'destructive'
        });
      }
    }

    // Complete the evaluation
    setEvaluationProgress({
      isEvaluating: false,
      step: 'Bulk evaluation complete',
      progress: 100
    });

    // Show success toast
    toast({
      title: 'Bulk Evaluation Complete',
      description: `Successfully evaluated ${processedCandidates.length} candidates.`,
    });
  };

  // Handle evaluation from the modal
  const handleEvaluate = async (mode: string, selectedId?: string, coverLetter?: any) => {
    setIsEvaluateModalOpen(false);

    if (!uploadedCandidateId) {
      toast({
        title: 'Error',
        description: 'No candidate selected for evaluation.',
        variant: 'destructive'
      });
      return;
    }

    // If we're in bulk mode, update the candidate's evaluation status
    if (isBulkMode) {
      // Mark the candidate as evaluated in the processed candidates list
      setProcessedCandidates(prev =>
        prev.map(pc =>
          pc.id === uploadedCandidateId
            ? { ...pc, isEvaluated: true }
            : pc
        )
      );

      // Update the file status to evaluated
      setProcessedFiles(prev =>
        prev.map(pf =>
          pf.candidateId === uploadedCandidateId
            ? { ...pf, isEvaluated: true }
            : pf
          )
      );
    }

    setIsLoading(true);

    // Start evaluation progress
    setEvaluationProgress({
      isEvaluating: true,
      step: 'Initializing evaluation...',
      progress: 10
    });

    // Switch to the results tab to show progress
    setActiveTab('results');

    try {
      let matchResult;
      let positionName;

      if (mode === 'specific-job' && selectedId) {
        // Match to specific job
        setEvaluationProgress({
          isEvaluating: true,
          step: 'Matching candidate to specific job...',
          progress: 30
        });

        // Pass cover letter content if provided during evaluation
        const coverLetterText = coverLetter?.content || null;
        matchResult = await matchCandidate(uploadedCandidateId, selectedId, coverLetterText);
        positionName = 'Specific Job Position';

      } else if (mode === 'all-company-jobs' && selectedId) {
        // Match to all jobs in a company
        setEvaluationProgress({
          isEvaluating: true,
          step: 'Matching candidate to all jobs in company...',
          progress: 30
        });

        const { matchCandidateToCompany } = await import('@/services/cv-processing/jobMatcher');
        const coverLetterText = coverLetter?.content || null;
        matchResult = await matchCandidateToCompany(uploadedCandidateId, selectedId, coverLetterText);
        positionName = 'All Jobs in Company';

      } else if (mode === 'all-companies') {
        // Match to all companies
        setEvaluationProgress({
          isEvaluating: true,
          step: 'Matching candidate to all companies...',
          progress: 30
        });

        const coverLetterText = coverLetter?.content || null;
        matchResult = await matchCandidateToAllCompanies(uploadedCandidateId, coverLetterText);
        positionName = 'All Available Positions';

      } else {
        throw new Error('Invalid evaluation mode or missing selection');
      }

      // Processing results
      setEvaluationProgress({
        isEvaluating: true,
        step: 'Processing evaluation results...',
        progress: 70
      });

      // Use cover letter data provided during evaluation, if any
      let coverLetterContent = null;
      let coverLetterUrl = null;

      if (coverLetter) {
        if (coverLetter.content) {
          coverLetterContent = coverLetter.content;
        } else if (coverLetter.file) {
          // For file uploads during evaluation, we could upload them here
          // For now, we'll just note that a file was provided
          coverLetterUrl = 'evaluation-upload';
        }
      }

      // Update the evaluation result
      setEvaluationResult({
        candidateId: uploadedCandidateId,
        candidateName: uploadedCandidateName,
        position: positionName,
        overallMatch: matchResult.overallScore,
        skills: matchResult.skillsMatch?.skills || [],
        experience: matchResult.experienceMatch?.details || [],
        education: matchResult.educationMatch?.details || [],
        strengths: matchResult.strengths || [],
        weaknesses: matchResult.gaps || [],
        isCompanyEvaluation: mode !== 'specific-job',
        jobSpecificMatches: matchResult.jobSpecificMatches || [],
        topMatchingJobs: matchResult.topMatchingJobs || [],
        // Cover letter data from evaluation
        coverLetterContent: coverLetterContent,
        coverLetterUrl: coverLetterUrl,
        coverLetterScore: matchResult.coverLetterMatch?.score || undefined,
        coverLetterAnalysis: matchResult.coverLetterMatch?.analysis || undefined
      });

      // Finalizing
      setEvaluationProgress({
        isEvaluating: true,
        step: 'Finalizing evaluation...',
        progress: 90
      });

      // Short delay to show the final progress step
      await new Promise(resolve => setTimeout(resolve, 500));

      // Complete
      setEvaluationProgress({
        isEvaluating: false,
        step: 'Evaluation complete',
        progress: 100
      });

      // Count the number of evaluations performed (number of jobs evaluated)
      let evaluationCount = 1; // Default for specific-job mode

      if ((mode === 'all-company-jobs' || mode === 'all-companies') && matchResult.jobSpecificMatches) {
        evaluationCount = matchResult.jobSpecificMatches.length;
      }

      // Show assignment interface for bulk evaluations
      if (mode === 'all-companies' && matchResult.jobSpecificMatches && matchResult.jobSpecificMatches.length > 0) {
        setAssignmentCandidateId(uploadedCandidateId);
        setAssignmentCandidateName(uploadedCandidateName);
        setAssignmentJobMatches(matchResult.jobSpecificMatches);
        setShowAssignmentInterface(true);

        toast({
          title: 'Evaluation Complete',
          description: `Found ${evaluationCount} potential matches. Review and assign to companies.`,
        });
        return; // Don't redirect, show assignment interface
      }

      // Show toast notification about redirection with the number of evaluations performed
      toast({
        title: 'Evaluation Complete',
        description: `${evaluationCount} ${evaluationCount === 1 ? 'evaluation' : 'evaluations'} performed successfully. Redirecting to profile...`,
      });

      // Navigate to the candidate-specific page with the evaluation tab active
      navigate(`/dashboard/cvs/${uploadedCandidateId}?tab=evaluation`);
    } catch (error) {
      console.error('Error during evaluation:', error);

      // Check if it's a rate limiting error
      const rateLimit = isRateLimitError(error);
      const errorMessage = getErrorMessage(error);

      setEvaluationProgress({
        isEvaluating: false,
        step: rateLimit ? 'Rate limit exceeded' : 'Evaluation failed',
        progress: 0
      });

      if (rateLimit) {
        showRateLimitToast({
          message: errorMessage,
          onRetry: () => handleEvaluate(mode, selectedId)
        });
      } else {
        toast({
          title: 'Evaluation Error',
          description: errorMessage,
          variant: 'destructive'
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Evaluate Modal */}
        <EvaluateModal
          isOpen={isEvaluateModalOpen}
          onClose={() => setIsEvaluateModalOpen(false)}
          candidateId={uploadedCandidateId}
          candidateName={uploadedCandidateName}
          onEvaluate={handleEvaluate}
        />

        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
              onClick={() => navigate('/dashboard/cvs')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold text-gray-800">CV Evaluation</h1>
          </div>

          <div className="flex gap-2">
            {activeTab === 'results' && evaluationResult && (
              <ReportExportButton
                reportData={getCandidateReportData()}
                reportType="candidate_pipeline"
                variant="outline"
              />
            )}

            {evaluationResult && (
              <Button
                variant="outline"
                className="border-gray-200 text-gray-700 hover:bg-gray-100"
              >
                <Share2 className="mr-2 h-4 w-4" /> Share Results
              </Button>
            )}
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="bg-white border-b border-gray-200 w-full justify-start">
            <TabsTrigger value="upload" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              <FileText className="mr-2 h-4 w-4" /> Upload CVs
            </TabsTrigger>
            {evaluationResult && (
              <TabsTrigger value="results" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
                <BarChart3 className="mr-2 h-4 w-4" /> Evaluation Results
              </TabsTrigger>
            )}
          </TabsList>

          {/* Upload Tab */}
          <TabsContent value="upload" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div className="lg:col-span-2">
                {isLoading ? (
                  <div className="bg-white border-gray-200 rounded-lg p-6 shadow-lg flex flex-col items-center justify-center h-64">
                    <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                    <h3 className="text-lg font-medium text-gray-800">Processing CV...</h3>
                    <p className="text-gray-600 mt-2 text-center">
                      {jobId
                        ? "Our AI is analyzing the CV and matching it to the job requirements."
                        : "Our AI is analyzing the CV to extract skills and experience."
                      }
                      This may take a moment.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-6">
                    {/* Referral Source Selector */}
                    <ReferralSourceSelector
                      value={referralSource}
                      onChange={setReferralSource}
                    />

                    <SubscriptionGuard limitType="cv_uploads" useCompact={true}>
                      <CVUploadCard
                        onUploadComplete={handleUploadComplete}
                        onMultipleUploadComplete={handleMultipleUploadComplete}
                        enableBulkUpload={true}
                        maxFiles={10}
                        title={jobId ? "Upload CVs for Job Evaluation" : "Upload CVs to Your Database"}
                      />
                    </SubscriptionGuard>

                    {/* Show bulk processing list when in bulk mode */}
                    {isBulkMode && processedFiles.length > 0 && (
                      <div className="bg-white border-gray-200 rounded-lg p-6 shadow-lg">
                        <BulkCVProcessingList
                          files={processedFiles}
                          onEvaluate={handleBulkItemEvaluate}
                          onEvaluateAll={handleEvaluateAll}
                        />
                      </div>
                    )}
                  </div>
                )}
              </div>

              <div className="lg:col-span-1">
                <div className="bg-white border-gray-200 rounded-lg p-6 shadow-lg">
                  <h2 className="text-xl font-bold text-gray-800 mb-4">How It Works</h2>
                  <ol className="list-decimal list-inside text-gray-600 space-y-4">
                    <li className="pb-4 border-b border-gray-200">
                      <span className="font-medium text-gray-800">Upload CVs</span>
                      <p className="mt-1 text-sm">Upload candidate CVs in PDF, DOC, or DOCX format. You can upload multiple CVs at once for bulk processing and evaluation.</p>
                    </li>
                    <li className="pb-4 border-b border-gray-200">
                      <span className="font-medium text-gray-800">AI Analysis</span>
                      <p className="mt-1 text-sm">Our AI analyzes each CV, extracting skills, experience, education, and other relevant information.</p>
                    </li>
                    <li className="pb-4 border-b border-gray-200">
                      <span className="font-medium text-gray-800">Job Matching</span>
                      <p className="mt-1 text-sm">CVs are matched against the job requirements to calculate a match score and identify strengths and weaknesses.</p>
                    </li>
                    <li className="pb-4 border-b border-gray-200">
                      <span className="font-medium text-gray-800">Bulk Evaluation</span>
                      <p className="mt-1 text-sm">Evaluate multiple candidates at once with the "Evaluate All" button, or evaluate them individually as needed.</p>
                    </li>
                    <li>
                      <span className="font-medium text-gray-800">Compare Candidates</span>
                      <p className="mt-1 text-sm">Compare multiple candidates side-by-side to make informed hiring decisions based on objective criteria.</p>
                    </li>
                  </ol>
                </div>
              </div>
            </div>
          </TabsContent>

          {/* Results Tab */}
          <TabsContent value="results" className="mt-6">
            {evaluationProgress.isEvaluating ? (
              <div className="bg-white border-gray-200 rounded-lg p-8 shadow-lg flex flex-col items-center justify-center">
                <Loader2 className="h-12 w-12 text-primary animate-spin mb-4" />
                <h3 className="text-lg font-medium text-gray-800">{evaluationProgress.step}</h3>
                <div className="w-full max-w-md mt-4">
                  <Progress value={evaluationProgress.progress} className="h-2" />
                  <p className="text-right text-sm text-gray-500 mt-1">{evaluationProgress.progress}%</p>
                </div>
                <p className="text-gray-600 mt-4 text-center">
                  Our AI is analyzing the CV and evaluating the candidate. This may take a moment.
                </p>
              </div>
            ) : evaluationResult && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <CVPreview
                  url={evaluationResult.cvUrl || undefined}
                  fileName={evaluationResult.candidateName ? `${evaluationResult.candidateName.replace(/\s+/g, '_')}.pdf` : 'resume.pdf'}
                />
                <CVEvaluationResults {...evaluationResult} />
              </div>
            )}
          </TabsContent>
        </Tabs>

        {/* Assignment Interface Modal */}
        {showAssignmentInterface && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-6xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <BulkEvaluationAssignment
                  candidateId={assignmentCandidateId}
                  candidateName={assignmentCandidateName}
                  jobSpecificMatches={assignmentJobMatches}
                  onAssignment={handleCandidateAssignment}
                  onClose={handleCloseAssignment}
                />
              </div>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default CVEvaluation;
