import React from 'react';
import {
  <PERSON>alog,
  <PERSON>alogPortal,
  <PERSON>alog<PERSON><PERSON>lay,
  DialogTrigger,
  DialogClose
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { X } from 'lucide-react';

interface PartnerCompaniesProps {
  trigger?: React.ReactNode;
}

export const PartnerCompanies: React.FC<PartnerCompaniesProps> = ({
  trigger = <span className="text-white hover:text-recruiter-blue transition cursor-pointer">Partner Companies</span>
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        {trigger}
      </DialogTrigger>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0" />
        <div className="fixed left-[50%] top-[50%] z-50 w-full max-w-lg translate-x-[-50%] translate-y-[-50%] duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%]">
          <div className="bg-white rounded-md shadow-lg">
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="text-lg font-medium text-gray-800">Our Partner Companies</h2>
              <DialogClose className="p-2 text-gray-400 rounded-md hover:bg-gray-100">
                <X className="w-5 h-5" />
              </DialogClose>
            </div>

          {/* Content */}
          <div className="space-y-6 p-4 mt-3">
            <p className="text-gray-600 leading-relaxed">
              We've successfully deployed our AI-powered recruitment tool to over 60+ clients across South Africa and 5 leading companies in Mauritius.
            </p>

            {/* Flags section */}
            <div className="flex items-center -space-x-2 overflow-hidden">
              {/* South African Flag */}
              <img
                src="https://www.southafrica-usa.net/consulate/images/national_flag_450px.jpg"
                alt="South African Flag"
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              {/* Mauritius Flag */}
              <img
                src="https://media.istockphoto.com/id/1469816175/vector/mauritius-flag-vector.jpg?s=612x612&w=0&k=20&c=75nya0gIFjQawozuok_fGvqj3IFGPUc5VaYZXg2CORg="
                alt="Mauritius Flag"
                className="w-10 h-10 rounded-full border-2 border-white"
              />
              <p className="text-sm text-gray-500 font-medium translate-x-5">Join 65+ companies</p>
            </div>

            {/* Customer Success */}
            <div>
              <h2 className="text-gray-800 text-xl font-bold">Our Latest Customer Success</h2>
              <li className="duration-150 hover:border-white hover:rounded-xl hover:bg-gray-50 list-none mt-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-x-3">
                    <div className="bg-white w-14 h-14 border rounded-full flex items-center justify-center">
                      <img
                        src="https://expertability.net/wp-content/uploads/2020/09/Expertability-Logo.png"
                        className="w-10 h-10 object-contain"
                        alt="Expertability.net"
                      />
                    </div>
                    <div>
                      <span className="block text-sm text-sky-600 font-medium">Expertability.net</span>
                      <h3 className="text-base text-gray-800 font-semibold mt-1">Premier Recruitment Agency</h3>
                      <div className="flex gap-4 mt-2">
                        <a href="https://expertability.net" target="_blank" rel="noopener noreferrer" className="text-sm text-sky-500 hover:text-sky-600">
                          Visit Website
                        </a>
                        <a href="https://cv.expertability.net" target="_blank" rel="noopener noreferrer" className="text-sm text-sky-500 hover:text-sky-600">
                          Access Platform
                        </a>
                      </div>
                    </div>
                  </div>
                  <p className="text-gray-600 sm:text-sm">
                    A leading recruitment powerhouse in Mauritius, specializing in executive search and talent acquisition.
                    Their recruitment process has been transformed through our AI-powered solution, resulting in faster
                    placements and better candidate matches.
                  </p>
                  <div className="text-sm text-gray-600 flex items-center gap-6">
                    <span className="flex items-center gap-2">
                      <svg className="w-5 h-5 text-gray-500" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" clipRule="evenodd" d="M6 6V5C6 3.34315 7.34315 2 9 2H11C12.6569 2 14 3.34315 14 5V6H16C17.1046 6 18 6.89543 18 8V11.5708C15.5096 12.4947 12.8149 12.9999 10 12.9999C7.18514 12.9999 4.49037 12.4947 2 11.5707V8C2 6.89543 2.89543 6 4 6H6ZM8 5C8 4.44772 8.44772 4 9 4H11C11.5523 4 12 4.44772 12 5V6H8V5ZM9 10C9 9.44772 9.44772 9 10 9H10.01C10.5623 9 11.01 9.44772 11.01 10C11.01 10.5523 10.5623 11 10.01 11H10C9.44772 11 9 10.5523 9 10Z" fill="#9CA3AF"></path>
                        <path d="M2 13.6923V16C2 17.1046 2.89543 18 4 18H16C17.1046 18 18 17.1046 18 16V13.6923C15.4872 14.5404 12.7964 14.9999 10 14.9999C7.20363 14.9999 4.51279 14.5404 2 13.6923Z" fill="#9CA3AF"></path>
                      </svg>
                      <span>Enterprise Partner</span>
                    </span>
                    <span className="flex items-center gap-2">
                      <svg className="w-5 h-5 text-gray-500" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path fillRule="evenodd" clipRule="evenodd" d="M5.05025 4.05025C7.78392 1.31658 12.2161 1.31658 14.9497 4.05025C17.6834 6.78392 17.6834 11.2161 14.9497 13.9497L10 18.8995L5.05025 13.9497C2.31658 11.2161 2.31658 6.78392 5.05025 4.05025ZM10 11C11.1046 11 12 10.1046 12 9C12 7.89543 11.1046 7 10 7C8.89543 7 8 7.89543 8 9C8 10.1046 8.89543 11 10 11Z" fill="#9CA3AF"></path>
                      </svg>
                      <span>Mauritius</span>
                    </span>
                  </div>
                </div>
              </li>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center gap-3 p-4 border-t">
            <DialogClose className="px-6 py-2 text-white bg-sky-600 rounded-md outline-none ring-offset-2 ring-sky-600 focus:ring-2 hover:bg-sky-700">
              Close
            </DialogClose>
          </div>
          </div>
        </div>
      </DialogPortal>
    </Dialog>
  );
};
