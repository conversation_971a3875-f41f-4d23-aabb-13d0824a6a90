import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  BarChart3,
  TrendingUp,
  Users,
  Clock,
  Calendar,
  Download,
  FileText,
  Briefcase,
  CheckCircle,
  XCircle,
  Filter,
  ArrowUpRight,
  AlertCircle,
  LineChart,
  PieChart,
  Building,
  ChevronDown
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useCompanyContext } from '@/contexts/CompanyContext';
import { useNavigate } from 'react-router-dom';
import ReportExportButton from '@/components/reports/ReportExportButton';
import { useReportExport } from '@/hooks/use-reports';
import { FunnelChart } from '@/components/analytics/FunnelChart';
import { TimelineChart } from '@/components/analytics/TimelineChart';
import { CompanyJobBreakdown } from '@/components/analytics/CompanyJobBreakdown';
import { AnimatedBarChart } from '@/components/dashboard/AnimatedBarChart';
import {
  useEvaluationFunnelData,
  useEvaluationTimelineData,
  useEvaluationByCompanyJob,
  useMatchQualityDistribution,
  useTopSkills,
  useTimeToHireData,
  useSourceEffectivenessData,
  useRecruitmentFunnelData,
  useTimeBreakdownData,
  useSourceMetricsData
} from '@/hooks/use-dashboard';

// No more mock data needed - using real data from hooks

const Analytics = () => {
  const { user } = useAuth();
  const { hasRole } = usePermissions();
  const { activeCompanyId, activeCompany } = useCompanyContext();
  const navigate = useNavigate();
  const { availableFormats, availableReportTypes, subscriptionTier } = useReportExport();
  const [selectedTab, setSelectedTab] = useState('overview');
  const [timePeriod, setTimePeriod] = useState('30days');
  const [timelinePeriod, setTimelinePeriod] = useState<'week' | 'month' | 'year'>('month');

  // Fetch real data using our hooks
  const { data: evaluationFunnelData, isLoading: funnelLoading } = useEvaluationFunnelData();
  const { data: evaluationTimelineData, isLoading: timelineLoading } = useEvaluationTimelineData(timelinePeriod);
  const { data: companyJobData, isLoading: companyJobLoading } = useEvaluationByCompanyJob();
  const { data: matchQualityData, isLoading: matchQualityLoading } = useMatchQualityDistribution();
  const { data: topSkillsData, isLoading: topSkillsLoading } = useTopSkills(8);
  const { data: timeToHireData, isLoading: timeToHireLoading } = useTimeToHireData();
  const { data: sourceEffectivenessData, isLoading: sourceEffectivenessLoading } = useSourceEffectivenessData();
  const { data: recruitmentFunnelData, isLoading: recruitmentFunnelLoading } = useRecruitmentFunnelData();
  const { data: timeBreakdownData, isLoading: timeBreakdownLoading } = useTimeBreakdownData();
  const { data: sourceMetricsData, isLoading: sourceMetricsLoading } = useSourceMetricsData();

  // Prepare report data for export
  const getReportData = (reportType: string) => {
    const date = new Date();
    const companyName = activeCompany?.name || 'Your Company';

    // Calculate common metrics used in multiple report types
    // Calculate average match score
    const totalEvaluations = matchQualityData?.reduce((sum, item) => sum + item.value, 0) || 0;
    const avgMatchScore = matchQualityData && totalEvaluations > 0 ?
      Math.round(matchQualityData.reduce((sum, item) => sum + (item.value * (
        item.label.includes('90') ? 95 :
        item.label.includes('70') ? 80 :
        item.label.includes('50') ? 60 : 40
      )), 0) / totalEvaluations) : 0;

    // Get top skills
    const topSkillsList = topSkillsData?.slice(0, 5).map(skill => skill.label).join(', ') || 'No skill data available';

    switch (reportType) {
      case 'comprehensive':
        // Create a comprehensive report with data from all sections
        const recruitmentData = getReportData('recruitment');
        const timeData = getReportData('time');
        const sourcesData = getReportData('sources');
        const evaluationData = getReportData('evaluation');
        const overviewData = getReportData('default');

        return {
          title: 'Comprehensive Recruitment Analytics Report',
          description: 'Complete analysis of all recruitment metrics',
          date,
          companyName,
          data: [], // Main data will be in sections
          columns: [], // Main columns will be in sections
          summary: {
            ...overviewData.summary,
          },
          executiveSummary: 'This comprehensive report provides a complete overview of all recruitment analytics, including recruitment funnel, time to hire, source effectiveness, and evaluation analytics.',
          recommendations: [
            'Review all sections of this report to get a holistic view of your recruitment process.',
            'Identify correlations between different metrics to optimize your recruitment strategy.',
            'Focus on improving the weakest areas identified in each section.',
            'Use this comprehensive data to make data-driven decisions about your recruitment process.',
            'Share relevant sections with stakeholders to align recruitment goals across the organization.'
          ],
          // Add sections for the comprehensive report
          sections: [
            {
              title: 'Recruitment Funnel Analysis',
              description: 'Detailed analysis of the recruitment pipeline stages',
              data: recruitmentData.data,
              columns: recruitmentData.columns,
              type: 'recruitment_funnel'
            },
            {
              title: 'Time to Hire Analysis',
              description: 'Analysis of hiring timeframes by position',
              data: timeData.data,
              columns: timeData.columns,
              type: 'time_to_hire'
            },
            {
              title: 'Source Effectiveness Analysis',
              description: 'Analysis of recruitment sources and their effectiveness',
              data: sourcesData.data,
              columns: sourcesData.columns,
              type: 'source_effectiveness'
            },
            {
              title: 'Evaluation Analytics',
              description: 'Analysis of candidate evaluations and match quality',
              data: evaluationData.data,
              columns: evaluationData.columns,
              type: 'evaluation_analytics'
            }
          ]
        };
      case 'recruitment':
        return {
          title: 'Recruitment Funnel Report',
          description: 'Detailed analysis of the recruitment pipeline stages',
          date,
          companyName,
          data: recruitmentFunnelData?.stages.map(item => ({
            stage: item.stage,
            count: item.count,
            percentage: recruitmentFunnelData.stages[0].count > 0
              ? ((item.count / recruitmentFunnelData.stages[0].count) * 100).toFixed(1) + '%'
              : '0%'
          })) || [],
          columns: [
            { header: 'Stage', dataKey: 'stage' },
            { header: 'Count', dataKey: 'count' },
            { header: 'Percentage', dataKey: 'percentage' }
          ],
          summary: {
            'Total Applications': recruitmentFunnelData?.stages[0].count || 0,
            'Conversion Rate': recruitmentFunnelData?.overallConversion + '%' || '0%',
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          filters: {
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          executiveSummary: `This report analyzes your recruitment funnel for ${getTimePeriodLabel(timePeriod).toLowerCase()}. ${
            recruitmentFunnelData?.stages[0].count ?
            `You received ${recruitmentFunnelData.stages[0].count} applications and made ${recruitmentFunnelData.stages[4]?.count || 0} hires, resulting in an overall conversion rate of ${recruitmentFunnelData.overallConversion}%.` :
            'No recruitment data is available for this period.'
          }`,
          recommendations: [
            'Focus on improving the conversion from Interview to Offer stages.',
            'Regularly review your recruitment process for bottlenecks and inefficiencies.',
            'Consider implementing structured interviews to improve candidate evaluation consistency.',
            'Track and analyze reasons for candidate dropoffs at each stage.',
            'Set up automated follow-ups to reduce candidate ghosting.'
          ]
        };
      case 'time':
        // Calculate average, fastest, and slowest positions from real data
        let avgDays = 0;
        let fastestPosition = { position: '', days: Number.MAX_SAFE_INTEGER };
        let slowestPosition = { position: '', days: 0 };

        if (timeToHireData && timeToHireData.length > 0) {
          // Calculate average
          avgDays = Math.round(
            timeToHireData.reduce((sum, item) => sum + item.days, 0) / timeToHireData.length
          );

          // Find fastest and slowest
          timeToHireData.forEach(item => {
            if (item.days < fastestPosition.days) {
              fastestPosition = { position: item.position, days: item.days };
            }
            if (item.days > slowestPosition.days) {
              slowestPosition = { position: item.position, days: item.days };
            }
          });
        }

        return {
          title: 'Time to Hire Report',
          description: 'Analysis of hiring timeframes by position',
          date,
          companyName,
          data: timeToHireData?.map(item => ({
            position: item.position,
            days: item.days,
            status: item.days <= 20 ? 'Good' : item.days <= 25 ? 'Average' : 'Slow'
          })) || [],
          columns: [
            { header: 'Position', dataKey: 'position' },
            { header: 'Days', dataKey: 'days' },
            { header: 'Status', dataKey: 'status' }
          ],
          summary: {
            'Average Time to Hire': timeToHireData?.length > 0 ? `${avgDays} days` : 'No data',
            'Fastest Position': fastestPosition.position ? `${fastestPosition.position} (${fastestPosition.days} days)` : 'No data',
            'Slowest Position': slowestPosition.position ? `${slowestPosition.position} (${slowestPosition.days} days)` : 'No data',
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          filters: {
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          executiveSummary: `This report analyzes your time-to-hire metrics for ${getTimePeriodLabel(timePeriod).toLowerCase()}. ${
            timeToHireData && timeToHireData.length > 0 ?
            `Your average time to hire across all positions is ${avgDays} days. The fastest position to fill was ${fastestPosition.position} at ${fastestPosition.days} days, while the slowest was ${slowestPosition.position} at ${slowestPosition.days} days.` :
            'No time-to-hire data is available for this period.'
          }`,
          recommendations: [
            slowestPosition.position ? `Review the hiring process for ${slowestPosition.position}, which takes the longest to fill at ${slowestPosition.days} days.` : 'Establish baseline metrics for time-to-hire for each position.',
            'Implement a more efficient screening process to reduce time-to-hire.',
            'Consider using pre-employment assessments to quickly identify qualified candidates.',
            'Streamline the interview scheduling process to reduce delays.',
            'Set clear timelines for each stage of the recruitment process.'
          ]
        };
      case 'sources':
        // Calculate best source by quality, conversion, and most applicants
        let bestQualitySource = { source: '', quality: 0 };
        let bestConversionSource = { source: '', conversion: 0 };
        let mostApplicantsSource = { source: '', applicants: 0 };

        if (sourceEffectivenessData && sourceEffectivenessData.length > 0) {
          sourceEffectivenessData.forEach(item => {
            // Check for best quality
            if (item.quality > bestQualitySource.quality) {
              bestQualitySource = { source: item.source, quality: item.quality };
            }

            // Check for best conversion
            const conversion = (item.hires / item.applicants) * 100;
            if (conversion > bestConversionSource.conversion) {
              bestConversionSource = { source: item.source, conversion };
            }

            // Check for most applicants
            if (item.applicants > mostApplicantsSource.applicants) {
              mostApplicantsSource = { source: item.source, applicants: item.applicants };
            }
          });
        }

        // Calculate total applicants and hires
        const totalApplicants = sourceEffectivenessData?.reduce((sum, item) => sum + item.applicants, 0) || 0;
        const totalHires = sourceEffectivenessData?.reduce((sum, item) => sum + item.hires, 0) || 0;
        const overallConversion = totalApplicants > 0 ? ((totalHires / totalApplicants) * 100).toFixed(1) + '%' : '0%';

        return {
          title: 'Source Effectiveness Report',
          description: 'Analysis of recruitment sources and their effectiveness',
          date,
          companyName,
          data: sourceEffectivenessData?.map(item => {
            return {
              source: item.source,
              applicants: item.applicants,
              hires: item.hires,
              conversion: ((item.hires / item.applicants) * 100).toFixed(1) + '%',
              quality: item.quality + '%'
            };
          }) || [],
          columns: [
            { header: 'Source', dataKey: 'source' },
            { header: 'Applicants', dataKey: 'applicants' },
            { header: 'Hires', dataKey: 'hires' },
            { header: 'Conversion', dataKey: 'conversion' },
            { header: 'Quality', dataKey: 'quality' }
          ],
          summary: {
            'Total Applicants': totalApplicants,
            'Total Hires': totalHires,
            'Overall Conversion Rate': overallConversion,
            'Best Source (Quality)': bestQualitySource.source ? `${bestQualitySource.source} (${bestQualitySource.quality}%)` : 'No data',
            'Best Source (Conversion)': bestConversionSource.source ? `${bestConversionSource.source} (${bestConversionSource.conversion.toFixed(1)}%)` : 'No data',
            'Most Applicants': mostApplicantsSource.source ? `${mostApplicantsSource.source} (${mostApplicantsSource.applicants})` : 'No data',
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          filters: {
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          executiveSummary: `This report analyzes the effectiveness of your recruitment sources for ${getTimePeriodLabel(timePeriod).toLowerCase()}. ${
            sourceEffectivenessData && sourceEffectivenessData.length > 0 ?
            `You received ${totalApplicants} applications and made ${totalHires} hires across all sources. ${bestQualitySource.source} provided the highest quality candidates with a score of ${bestQualitySource.quality}%. ${bestConversionSource.source} had the best conversion rate at ${bestConversionSource.conversion.toFixed(1)}%.` :
            'No source effectiveness data is available for this period.'
          }`,
          recommendations: [
            bestConversionSource.source ? `Allocate more resources to ${bestConversionSource.source}, which has the best conversion rate at ${bestConversionSource.conversion.toFixed(1)}%.` : 'Establish a diverse mix of recruitment sources to reach a wider pool of candidates.',
            'Track cost per hire for each source to optimize your recruitment budget.',
            'Regularly review the quality of hires from each source.',
            'Implement source-specific application processes to improve candidate quality.',
            'Develop targeted employer branding for your most effective sources.'
          ]
        };
      case 'evaluation':
        return {
          title: 'Evaluation Analytics Report',
          description: 'Analysis of candidate evaluations and match quality',
          date,
          companyName,
          data: [
            { metric: 'Total Candidates', value: evaluationFunnelData?.totalCandidates || 0 },
            { metric: 'Evaluated Candidates', value: evaluationFunnelData?.stages[1]?.value || 0 },
            { metric: 'Good Matches (70%+)', value: evaluationFunnelData?.stages[2]?.value || 0 },
            { metric: 'Average Match Score', value: `${avgMatchScore}%` },
            { metric: 'Conversion Rate', value: `${evaluationFunnelData?.conversionRate || 0}%` },
            { metric: 'Top Skills', value: topSkillsList }
          ],
          columns: [
            { header: 'Metric', dataKey: 'metric' },
            { header: 'Value', dataKey: 'value' }
          ],
          summary: {
            'Total Candidates': evaluationFunnelData?.totalCandidates || 0,
            'Evaluated Candidates': evaluationFunnelData?.stages[1]?.value || 0,
            'Good Matches (70%+)': evaluationFunnelData?.stages[2]?.value || 0,
            'Average Match Score': `${avgMatchScore}%`,
            'Top Skills': topSkillsList,
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          filters: {
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          executiveSummary: `This report analyzes your candidate evaluations for ${getTimePeriodLabel(timePeriod).toLowerCase()}. ${
            evaluationFunnelData?.totalCandidates ?
            `You have evaluated ${evaluationFunnelData.stages[1]?.value || 0} out of ${evaluationFunnelData.totalCandidates} candidates, with ${evaluationFunnelData.stages[2]?.value || 0} candidates scoring 70% or higher. The average match score across all evaluations is ${avgMatchScore}%.` :
            'No evaluation data is available for this period.'
          }`,
          recommendations: [
            'Focus on candidates with the highest match scores for priority interviews.',
            'Review evaluation criteria regularly to ensure they align with job requirements.',
            'Consider implementing skills-based assessments to complement CV evaluations.',
            'Track correlation between match scores and actual job performance.',
            'Use evaluation data to refine job descriptions and recruitment targeting.'
          ]
        };
      default: // overview
        // Combine data from multiple sources for a comprehensive overview
        const totalCandidates = evaluationFunnelData?.totalCandidates || 0;
        const evaluatedCandidates = evaluationFunnelData?.stages[1]?.value || 0;
        const goodMatches = evaluationFunnelData?.stages[2]?.value || 0;
        const totalApplications = recruitmentFunnelData?.stages[0]?.count || 0;
        const totalHired = recruitmentFunnelData?.stages[4]?.count || 0;
        const avgTimeToHire = timeToHireData && timeToHireData.length > 0 ?
          Math.round(timeToHireData.reduce((sum, item) => sum + item.days, 0) / timeToHireData.length) : 0;

        return {
          title: 'Recruitment Analytics Overview',
          description: 'Summary of key recruitment metrics',
          date,
          companyName,
          data: [
            { metric: 'Total Candidates', value: totalCandidates.toString(), change: `${evaluatedCandidates} evaluated` },
            { metric: 'Good Matches (70%+)', value: goodMatches.toString(), change: evaluatedCandidates > 0 ? `${Math.round((goodMatches / evaluatedCandidates) * 100)}% of evaluated` : '0%' },
            { metric: 'Total Applications', value: totalApplications.toString(), change: `${totalHired} hired` },
            { metric: 'Avg. Time to Hire', value: avgTimeToHire > 0 ? `${avgTimeToHire} days` : 'No data', change: '' },
            { metric: 'Avg. Match Score', value: `${avgMatchScore}%`, change: '' },
            { metric: 'Top Skills', value: topSkillsList, change: '' }
          ],
          columns: [
            { header: 'Metric', dataKey: 'metric' },
            { header: 'Value', dataKey: 'value' },
            { header: 'Additional Info', dataKey: 'change' }
          ],
          summary: {
            'Total Candidates': totalCandidates,
            'Evaluated Candidates': evaluatedCandidates,
            'Good Matches (70%+)': goodMatches,
            'Total Applications': totalApplications,
            'Total Hired': totalHired,
            'Avg. Time to Hire': avgTimeToHire > 0 ? `${avgTimeToHire} days` : 'No data',
            'Avg. Match Score': `${avgMatchScore}%`,
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          filters: {
            'Time Period': getTimePeriodLabel(timePeriod)
          },
          executiveSummary: `This report provides an overview of your recruitment analytics for ${getTimePeriodLabel(timePeriod).toLowerCase()}. ${
            totalCandidates > 0 || totalApplications > 0 ?
            `You have ${totalCandidates} candidates in your database, with ${evaluatedCandidates} evaluated and ${goodMatches} good matches. Your recruitment funnel shows ${totalApplications} applications with ${totalHired} hires, and an average time to hire of ${avgTimeToHire > 0 ? avgTimeToHire : 'N/A'} days.` :
            'No recruitment data is available for this period.'
          }`,
          recommendations: [
            'Focus on candidates with high match scores to improve hiring efficiency.',
            'Review your recruitment funnel for bottlenecks, especially between interview and offer stages.',
            'Consider implementing skills-based assessments to complement CV evaluations.',
            'Track correlation between match scores and actual job performance.',
            'Optimize your recruitment sources based on candidate quality and conversion rates.'
          ]
        };
    }
  };

  // Helper function to get time period label
  const getTimePeriodLabel = (period: string) => {
    switch (period) {
      case '7days': return 'Last 7 days';
      case '30days': return 'Last 30 days';
      case '90days': return 'Last 90 days';
      case 'year': return 'Last year';
      case 'custom': return 'Custom range';
      default: return 'Last 30 days';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Analytics & Reporting</h1>
          <p className="text-gray-600 text-sm mt-1">
            Insights and metrics about your recruitment activities
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" className="border-gray-200 text-gray-700 hover:bg-gray-100">
            <Filter className="mr-2 h-4 w-4" /> Filter
          </Button>

          {/* Export Dropdown Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="default" className="bg-primary-gradient text-white hover:bg-primary/90">
                <Download className="mr-2 h-4 w-4" /> Export <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>Export Options</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                <ReportExportButton
                  reportData={getReportData(selectedTab === 'overview' ? 'default' : selectedTab)}
                  reportType={
                    selectedTab === 'overview' ? 'recruitment_funnel' :
                    selectedTab === 'recruitment' ? 'recruitment_funnel' :
                    selectedTab === 'time' ? 'time_to_hire' :
                    selectedTab === 'sources' ? 'source_effectiveness' :
                    'evaluation_analytics'
                  }
                  variant="ghost"
                  className="w-full justify-start px-0"
                >
                  <FileText className="mr-2 h-4 w-4" /> Export Current Tab
                </ReportExportButton>
              </DropdownMenuItem>
              <DropdownMenuItem>
                <ReportExportButton
                  reportData={getReportData('comprehensive')}
                  reportType="comprehensive"
                  variant="ghost"
                  className="w-full justify-start px-0"
                >
                  <FileText className="mr-2 h-4 w-4" /> Export All Data
                </ReportExportButton>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>


        </div>
      </div>

      {/* Subscription tier alert for Starter users who are not admins */}
      {subscriptionTier === 'starter' && !hasRole('platform_admin') && (
        <Alert className="bg-amber-50 border-amber-200 text-amber-800">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Export features limited</AlertTitle>
          <AlertDescription className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
            <span>Your current plan doesn't include report exports. Upgrade to Growth or Pro to export reports in PDF and Excel formats.</span>
            <Button
              variant="outline"
              size="sm"
              className="border-amber-300 text-amber-800 hover:bg-amber-100"
              onClick={() => navigate('/dashboard/billing')}
            >
              <ArrowUpRight className="mr-2 h-4 w-4" /> Upgrade Plan
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Time period selector */}
      <div className="flex justify-end">
        <div className="w-40">
          <Select
            value={timePeriod}
            onValueChange={(value) => setTimePeriod(value)}
          >
            <SelectTrigger className="bg-white border-gray-200 text-gray-800">
              <SelectValue placeholder="Time Period" />
            </SelectTrigger>
            <SelectContent className="bg-white border-gray-200 text-gray-800">
              <SelectItem value="7days">Last 7 days</SelectItem>
              <SelectItem value="30days">Last 30 days</SelectItem>
              <SelectItem value="90days">Last 90 days</SelectItem>
              <SelectItem value="year">Last year</SelectItem>
              <SelectItem value="custom">Custom range</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[
          {
            title: 'Total Candidates',
            value: evaluationFunnelData?.totalCandidates.toString() || '0',
            icon: FileText,
            change: `${evaluationFunnelData?.stages[0]?.value || 0} total`,
            trend: 'up'
          },
          {
            title: 'Evaluated Candidates',
            value: evaluationFunnelData?.stages[1]?.value.toString() || '0',
            icon: BarChart3,
            change: `${evaluationFunnelData?.stages[1]?.value && evaluationFunnelData?.totalCandidates > 0 ? Math.round((evaluationFunnelData.stages[1].value / evaluationFunnelData.totalCandidates) * 100) : 0}% of total`,
            trend: 'up'
          },
          {
            title: 'Good Matches (70%+)',
            value: evaluationFunnelData?.stages[2]?.value.toString() || '0',
            icon: TrendingUp,
            change: `${evaluationFunnelData?.stages[2]?.value && evaluationFunnelData?.stages[1]?.value > 0 ? Math.round((evaluationFunnelData.stages[2].value / evaluationFunnelData.stages[1].value) * 100) : 0}% of evaluated`,
            trend: 'up'
          },
          {
            title: 'Avg. Match Score',
            value: `${matchQualityData && matchQualityData.reduce((sum, item) => sum + item.value, 0) > 0 ?
              Math.round(matchQualityData.reduce((sum, item) => sum + (item.value * (
                item.label.includes('90') ? 95 :
                item.label.includes('70') ? 80 :
                item.label.includes('50') ? 60 : 40
              )), 0) / matchQualityData.reduce((sum, item) => sum + item.value, 0)) : 0}%`,
            icon: PieChart,
            change: 'Based on all evaluations',
            trend: 'up'
          },
        ].map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index} className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 bg-card-hover">
              <CardHeader className="flex flex-row items-center justify-between pb-2">
                <CardTitle className="text-gray-800 text-sm font-medium">{stat.title}</CardTitle>
                <Icon className="h-4 w-4 text-gray-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-primary">{stat.value}</div>
                <p className="text-xs text-gray-500 mt-1 flex items-center">
                  {stat.trend === 'up' ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1 rotate-180" />
                  )}
                  {stat.change}
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Tabs for different reports */}
      <Tabs
        defaultValue="overview"
        className="mt-6"
        value={selectedTab}
        onValueChange={(value) => setSelectedTab(value)}
      >
        <TabsList className="bg-white border-b border-gray-200 w-full justify-start">
          <TabsTrigger value="overview" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Overview
          </TabsTrigger>
          <TabsTrigger value="recruitment" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Recruitment Funnel
          </TabsTrigger>
          <TabsTrigger value="time" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Time to Hire
          </TabsTrigger>
          <TabsTrigger value="sources" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Source Effectiveness
          </TabsTrigger>
          <TabsTrigger value="evaluation" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
            Evaluation Analytics
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            {/* Evaluation Funnel */}
            <FunnelChart
              data={evaluationFunnelData?.stages || []}
              title="Evaluation Funnel"
              description="Progression of candidates through the evaluation process"
              conversionRate={evaluationFunnelData?.conversionRate}
              totalCount={evaluationFunnelData?.totalCandidates}
            />

            {/* Time to Hire by Position */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Time to Hire by Position</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {timeToHireData && timeToHireData.length > 0 ? (
                    timeToHireData.map((position, index) => (
                      <div key={index}>
                        <div className="flex justify-between mb-1">
                          <span className="text-sm text-gray-600">{position.position}</span>
                          <span className="text-sm text-gray-600">{position.days} days</span>
                        </div>
                        <div className="w-full bg-gray-100 rounded-full h-2.5">
                          <div
                            className="bg-primary h-2.5 rounded-full"
                            style={{ width: `${(position.days / 30) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      No time to hire data available yet
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Source Effectiveness */}
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 mt-6">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg">Source Effectiveness</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left text-gray-600">
                  <thead className="text-xs uppercase bg-gray-100 text-gray-500">
                    <tr>
                      <th scope="col" className="px-6 py-3">Source</th>
                      <th scope="col" className="px-6 py-3">Applicants</th>
                      <th scope="col" className="px-6 py-3">Hires</th>
                      <th scope="col" className="px-6 py-3">Conversion Rate</th>
                      <th scope="col" className="px-6 py-3">Quality Score</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sourceEffectivenessData && sourceEffectivenessData.length > 0 ? (
                      sourceEffectivenessData.map((source, index) => (
                        <tr key={index} className="border-b border-gray-200">
                          <td className="px-6 py-4 font-medium text-gray-800">{source.source}</td>
                          <td className="px-6 py-4">{source.applicants}</td>
                          <td className="px-6 py-4">{source.hires}</td>
                          <td className="px-6 py-4">
                            {source.applicants > 0 ? ((source.hires / source.applicants) * 100).toFixed(1) : '0.0'}%
                          </td>
                          <td className="px-6 py-4">
                            <div className="flex items-center">
                              <div className="w-full bg-gray-100 rounded-full h-2.5 mr-2">
                                <div
                                  className={`${
                                    source.quality >= 90 ? 'bg-green-500' :
                                    source.quality >= 80 ? 'bg-blue-500' :
                                    source.quality >= 70 ? 'bg-amber-500' :
                                    'bg-red-500'
                                  } h-2.5 rounded-full`}
                                  style={{ width: `${source.quality}%` }}
                                ></div>
                              </div>
                              <span>{source.quality}%</span>
                            </div>
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={5} className="px-6 py-4 text-center text-gray-500">
                          No source effectiveness data available yet
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Recruitment Funnel Tab */}
        <TabsContent value="recruitment">
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 mt-6">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg">Detailed Recruitment Funnel</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center space-y-8 py-4">
                {recruitmentFunnelData?.stages.map((stage, index) => (
                  <div key={index} className="w-full max-w-md">
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-800 font-medium">{stage.stage}</span>
                      <span className="text-gray-800 font-medium">{stage.count}</span>
                    </div>
                    <div className="w-full bg-gray-100 rounded-full h-4">
                      <div
                        className={`${stage.color} h-4 rounded-full flex items-center justify-center text-xs text-white`}
                        style={{ width: recruitmentFunnelData.stages[0].count > 0
                          ? `${(stage.count / recruitmentFunnelData.stages[0].count) * 100}%`
                          : '0%' }}
                      >
                        {recruitmentFunnelData.stages[0].count > 0
                          ? ((stage.count / recruitmentFunnelData.stages[0].count) * 100).toFixed(0)
                          : 0}%
                      </div>
                    </div>
                    {index < recruitmentFunnelData.stages.length - 1 && (
                      <div className="flex justify-center my-2">
                        <div className="h-8 border-l-2 border-dashed border-gray-300"></div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
              <div className="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h4 className="text-gray-800 font-medium mb-2">Conversion Rates</h4>
                <div className="space-y-2">
                  {recruitmentFunnelData?.conversionRates.map((rate, index) => (
                    <div key={index} className="flex justify-between text-gray-600">
                      <span>{rate.fromStage} to {rate.toStage}</span>
                      <span className="font-medium">{rate.rate}%</span>
                    </div>
                  ))}
                  <div className="flex justify-between pt-2 border-t border-gray-300">
                    <span className="text-gray-700 font-medium">Overall Conversion</span>
                    <span className="text-gray-700 font-medium">{recruitmentFunnelData?.overallConversion || 0}%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Time to Hire Tab */}
        <TabsContent value="time">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Time to Hire by Position</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {timeToHireData && timeToHireData.length > 0 ? (
                    timeToHireData.map((position, index) => (
                      <div key={index}>
                        <div className="flex justify-between mb-2">
                          <span className="text-gray-800">{position.position}</span>
                          <span className="text-gray-800 font-medium">{position.days} days</span>
                        </div>
                        <div className="w-full bg-gray-100 rounded-full h-4">
                          <div
                            className="bg-primary h-4 rounded-full flex items-center justify-end pr-2 text-xs text-white"
                            style={{ width: `${(position.days / 30) * 100}%` }}
                          >
                            {position.days}d
                          </div>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      No time to hire data available yet
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Time Breakdown by Stage</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {timeBreakdownData?.stages.map((stage, index) => (
                    <div key={index}>
                      <div className="flex justify-between mb-1">
                        <span className="text-sm text-gray-600">{stage.stage}</span>
                        <span className="text-sm text-gray-600">{stage.days} days</span>
                      </div>
                      <div className="w-full bg-gray-100 rounded-full h-2.5">
                        <div
                          className={`${stage.color} h-2.5 rounded-full`}
                          style={{ width: `${(stage.days / 10) * 100}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                  <div className="pt-4 border-t border-gray-200 mt-4">
                    <div className="flex justify-between">
                      <span className="text-gray-800 font-medium">Average Total Time</span>
                      <span className="text-gray-800 font-medium">{timeBreakdownData?.totalDays || 0} days</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Source Effectiveness Tab */}
        <TabsContent value="sources">
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 mt-6">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg">Source Effectiveness Analysis</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm text-left text-gray-600">
                  <thead className="text-xs uppercase bg-gray-100 text-gray-500">
                    <tr>
                      <th scope="col" className="px-6 py-3">Source</th>
                      <th scope="col" className="px-6 py-3">Applicants</th>
                      <th scope="col" className="px-6 py-3">Screened</th>
                      <th scope="col" className="px-6 py-3">Interviewed</th>
                      <th scope="col" className="px-6 py-3">Offers</th>
                      <th scope="col" className="px-6 py-3">Hires</th>
                      <th scope="col" className="px-6 py-3">Conversion</th>
                      <th scope="col" className="px-6 py-3">Quality</th>
                    </tr>
                  </thead>
                  <tbody>
                    {sourceEffectivenessData && sourceEffectivenessData.length > 0 ? (
                      sourceEffectivenessData.map((source, index) => {
                        // Generate some derived data for the additional columns
                        const screened = Math.round(source.applicants * 0.6);
                        const interviewed = Math.round(screened * 0.5);
                        const offers = Math.round(interviewed * 0.4);

                        return (
                          <tr key={index} className="border-b border-gray-200">
                            <td className="px-6 py-4 font-medium text-gray-800">{source.source}</td>
                            <td className="px-6 py-4">{source.applicants}</td>
                            <td className="px-6 py-4">{screened}</td>
                            <td className="px-6 py-4">{interviewed}</td>
                            <td className="px-6 py-4">{offers}</td>
                            <td className="px-6 py-4">{source.hires}</td>
                            <td className="px-6 py-4">
                              {source.applicants > 0 ? ((source.hires / source.applicants) * 100).toFixed(1) : '0.0'}%
                            </td>
                            <td className="px-6 py-4">
                              <div className="flex items-center">
                                <div className="w-full bg-gray-100 rounded-full h-2.5 mr-2">
                                  <div
                                    className={`${
                                      source.quality >= 90 ? 'bg-green-500' :
                                      source.quality >= 80 ? 'bg-blue-500' :
                                      source.quality >= 70 ? 'bg-amber-500' :
                                      'bg-red-500'
                                    } h-2.5 rounded-full`}
                                    style={{ width: `${source.quality}%` }}
                                  ></div>
                                </div>
                                <span>{source.quality}%</span>
                              </div>
                            </td>
                          </tr>
                        );
                      })
                    ) : (
                      <tr>
                        <td colSpan={8} className="px-6 py-4 text-center text-gray-500">
                          No source effectiveness data available yet
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h4 className="text-gray-800 font-medium mb-4">Cost per Hire by Source</h4>
                  <div className="space-y-3">
                    {sourceMetricsData && sourceMetricsData.costPerHire && sourceMetricsData.costPerHire.length > 0 ? (
                      sourceMetricsData.costPerHire.map((item, index) => (
                        <div key={index} className="flex justify-between text-gray-600">
                          <span>{item.source}</span>
                          <span className="font-medium">${item.cost}</span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-2 text-gray-500">
                        No cost per hire data available yet
                      </div>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                  <h4 className="text-gray-800 font-medium mb-4">Time to Hire by Source</h4>
                  <div className="space-y-3">
                    {sourceMetricsData && sourceMetricsData.timeToHire && sourceMetricsData.timeToHire.length > 0 ? (
                      sourceMetricsData.timeToHire.map((item, index) => (
                        <div key={index} className="flex justify-between text-gray-600">
                          <span>{item.source}</span>
                          <span className="font-medium">{item.days} days</span>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-2 text-gray-500">
                        No time to hire data available yet
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Evaluation Analytics Tab */}
        <TabsContent value="evaluation">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">
            {/* Evaluation Funnel */}
            <FunnelChart
              data={evaluationFunnelData?.stages || []}
              title="Evaluation Funnel"
              description="Progression of candidates through the evaluation process"
              conversionRate={evaluationFunnelData?.conversionRate}
              totalCount={evaluationFunnelData?.totalCandidates}
            />

            {/* Match Quality Distribution */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Match Quality Distribution</CardTitle>
                <CardDescription>Distribution of evaluation scores across all candidates</CardDescription>
              </CardHeader>
              <CardContent>
                <AnimatedBarChart data={matchQualityData || []} />
              </CardContent>
            </Card>
          </div>

          {/* Timeline Chart */}
          <div className="mt-6">
            <TimelineChart
              evaluations={evaluationTimelineData?.evaluations || []}
              candidates={evaluationTimelineData?.candidates || []}
              title="Evaluation Timeline"
              description="CV uploads and evaluations over time"
              period={timelinePeriod}
              onPeriodChange={setTimelinePeriod}
            />
          </div>

          {/* Top Skills */}
          <div className="mt-6">
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Top Skills in Candidate Pool</CardTitle>
                <CardDescription>Most common skills found in candidate CVs</CardDescription>
              </CardHeader>
              <CardContent>
                <AnimatedBarChart data={topSkillsData || []} />
              </CardContent>
            </Card>
          </div>

          {/* Company/Job Breakdown */}
          <div className="mt-6">
            <CompanyJobBreakdown
              data={companyJobData || []}
              title="Evaluation by Company & Job"
              description="Breakdown of evaluations by company and job position"
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Wrap the Analytics component with the DashboardLayout
const AnalyticsWithLayout = () => (
  <DashboardLayout>
    <Analytics />
  </DashboardLayout>
);

export default AnalyticsWithLayout;

