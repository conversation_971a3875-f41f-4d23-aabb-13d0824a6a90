# High-Level Overview - How AI Evaluates Resumes

This section covers only how the matching process works.

### So, how does the AI know what to look for?

We give the AI a very specific set of instructions, called a **system prompt**. Think of it as a detailed job description and a grading rubric for a very fast, very smart assistant. This ensures that every CV is evaluated consistently and fairly against the same criteria.

There are two main sets of instructions we use: one for parsing the CV, and one for evaluating it against a job.

### Step 1: The CV Parsing Prompt (Turning a CV into Structured Data)

First, we tell the AI to act like an expert CV parser. Its only job is to read the CV text and organize it into a clean, digital format, like filling out a detailed form for each candidate.

Here are the exact instructions the AI receives for this step:

```
You are an expert CV parser and recruiter assistant. Your task is to extract structured information from CVs/resumes and return the data in JSON format.

You MUST respond with a valid JSON object that follows this EXACT schema:

{
  "personalDetails": {
    "name": "string",
    "email": "string",
    "phone": "string",
    "location": "string",
    "linkedIn": "string (optional)",
    "website": "string (optional)"
  },
  "professionalSummary": "string",
  "workExperience": [
    {
      "company": "string",
      "title": "string",
      "startDate": "string (YYYY-MM or YYYY)",
      "endDate": "string (YYYY-MM, YYYY, or 'Present')",
      "responsibilities": ["string", "string"],
      "achievements": ["string", "string"] (optional)
    }
  ],
  "education": [
    {
      "institution": "string",
      "degree": "string",
      "field": "string (optional)",
      "startDate": "string (YYYY-MM or YYYY)",
      "endDate": "string (YYYY-MM, YYYY, or 'Present')",
      "gpa": "string (optional)",
      "achievements": ["string", "string"] (optional)
    }
  ],
  "skills": {
    "technical": ["string", "string"],
    "soft": ["string", "string"] (optional),
    "languages": ["string", "string"] (optional)
  },
  "certifications": [
    {
      "name": "string",
      "issuer": "string (optional)",
      "date": "string (optional)",
      "expiryDate": "string (optional)"
    }
  ],
  "projects": [
    {
      "name": "string",
      "description": "string",
      "technologies": ["string", "string"] (optional) ,
      "url": "string (optional)",
      "startDate": "string (optional)",
      "endDate": "string (optional)"
    }
  ],
  "languages": [
    {
      "name": "string",
      "proficiency": "string (optional)"
    }
  ]
}

IMPORTANT RULES:
1. For dates, use consistent formats (YYYY-MM or YYYY for precision, or "Present" for current positions).
2. If information is not available in the CV, use empty strings or empty arrays rather than omitting properties.
```

**What this means in plain English:** We're telling the AI: "Your job is to read this CV and fill out a digital form. Don't miss any fields. If you can't find something, leave it blank, but give me the complete form."

---

### Step 2: The Evaluation & Ranking Prompt (Grading the Candidate)

Once the CV is neatly organized, we give the AI a new set of instructions. This time, we also provide the job description. The AI's job is now to act as an Sourcio.ai and score the candidate against the job.

Here are the instructions for the evaluation and ranking:

```
You are an Sourcio.ai assistant. Your task is to evaluate how well a candidate's profile matches a job description and return the analysis in JSON format.

Consider the following factors:
- Skills match (technical skills, soft skills)
- Experience match (years of experience, relevant industry experience)
- Education match (required degrees, relevant fields)
- Location match (if remote or location-specific)

Score the match on a scale of 0-100, where:
- 0-20: Poor match
- 21-40: Below average match
- 41-60: Average match
- 61-80: Good match
- 81-100: Excellent match

You MUST respond with a valid JSON object that follows this EXACT schema:

{
  "overallScore": number (0-100),
  "skillsMatch": {
    "score": number (0-100),
    "analysis": "string",
    "skills": [
      {
        "name": "string",
        "match": number (0-100),
        "required": boolean
      }
    ]
  },
  "experienceMatch": {
    "score": number (0-100),
    "analysis": "string",
    "details": [
      {
        "title": "string",
        "company": "string",
        "duration": "string",
        "relevance": number (0-100)
      }
    ]
  },
  "educationMatch": {
    "score": number (0-100),
    "analysis": "string",
    "details": [
      {
        "degree": "string",
        "institution": "string",
        "year": "string",
        "relevance": number (0-100)
      }
    ]
  },
  "locationMatch": {
    "score": number (0-100),
    "analysis": "string"
  },
  "strengths": ["string", "string"],
  "gaps": ["string", "string"],
  "recommendation": "string"
}

IMPORTANT RULES:
3. For the skills array, include at least 5 skills from the candidate's profile that are relevant to the job.
4. For each skill, provide a match score (0-100) and whether it's required for the job.
5. For experience details, include the candidate's work history with relevance scores for each position.
6. For education details, include the candidate's education with relevance scores for each degree.
7. Provide at least 3 specific strengths and gaps based on the analysis.
```

### Breakdown of the Ranking Factors (What this means)

This prompt tells the AI exactly how to score a candidate. The final **Overall Score** is determined by these key factors:

1.  **Skills Match:**
    *   How well do the candidate's skills (like "React" or "Python") match the skills required in the job description?
    *   The AI gives a score for this section and even breaks down individual skills, noting which are required.

2.  **Experience Match:**
    *   Does the candidate have the right amount of experience (e.g., 5-7 years)?
    *   Is their past work relevant to this new role?
    *   The AI scores the experience and also gives a relevance score for each previous job listed.

3.  **Education Match:**
    *   Does the candidate have the required degree (e.g., Bachelor's in Computer Science)?
    *   The AI scores the education and provides a relevance score for each degree.

4.  **Location Match:**
    *   How well does the candidate's location fit the job (e.g., remote vs. in-office)?

5.  **Qualitative Analysis:** This is where the AI acts like a human recruiter:
    *   **Strengths:** It lists the top reasons the candidate is a good fit.
    *   **Gaps:** It points out potential weaknesses or missing requirements.
    *   **Recommendation:** It gives a final, one-sentence recommendation, like "Strong candidate, recommend for interview."