import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';

export interface InterviewParticipant {
  id: string;
  interview_id: string;
  user_id: string;
  role: 'interviewer' | 'candidate' | 'observer';
  status: 'invited' | 'confirmed' | 'declined' | 'no_response';
  created_at: string;
  updated_at: string;
  // Enhanced fields
  name?: string;
  email?: string;
}

/**
 * Add participants to an interview
 */
export const addInterviewParticipants = async (
  interviewId: string,
  participantUserIds: string[],
  role: 'interviewer' | 'observer' = 'interviewer'
): Promise<InterviewParticipant[]> => {
  return safeDbOperation(
    async () => {
      const participants = participantUserIds.map(userId => ({
        interview_id: interviewId,
        user_id: userId,
        role,
        status: 'invited' as const
      }));

      const { data, error } = await supabase
        .from('interview_participants')
        .insert(participants)
        .select();

      if (error) throw error;
      return data;
    },
    'Failed to add interview participants',
    []
  );
};

/**
 * Get participants for an interview
 */
export const getInterviewParticipants = async (
  interviewId: string
): Promise<InterviewParticipant[]> => {
  return safeDbOperation(
    async () => {
      // Get all participants for the interview
      const { data: participantsData, error: participantsError } = await supabase
        .from('interview_participants')
        .select('*')
        .eq('interview_id', interviewId);

      if (participantsError) throw participantsError;

      // Get unique user IDs
      const userIds = [...new Set(participantsData.map(p => p.user_id))];

      // Get profile information for all users
      const { data: profilesData, error: profilesError } = await supabase
        .from('profiles')
        .select('user_id, full_name, email')
        .in('user_id', userIds);

      // Don't throw error if profiles not found
      const profiles = profilesError ? [] : profilesData;

      // Map participants with profile information
      return participantsData.map(participant => {
        const profile = profiles.find(p => p.user_id === participant.user_id);
        return {
          ...participant,
          name: profile?.full_name || null,
          email: profile?.email || null
        };
      });
    },
    `Failed to fetch participants for interview ${interviewId}`,
    []
  );
};

/**
 * Update participant status
 */
export const updateParticipantStatus = async (
  participantId: string,
  status: 'confirmed' | 'declined' | 'no_response'
): Promise<void> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('interview_participants')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', participantId);

      if (error) throw error;
    },
    'Failed to update participant status'
  );
};

/**
 * Remove participant from interview
 */
export const removeInterviewParticipant = async (
  participantId: string
): Promise<void> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('interview_participants')
        .delete()
        .eq('id', participantId);

      if (error) throw error;
    },
    'Failed to remove interview participant'
  );
};