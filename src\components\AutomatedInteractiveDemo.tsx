import React, { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  FileText, Users, Building, BarChart2, Search, Plus,
  ChevronRight, Bell, Settings, User, Zap, Check, AlertCircle
} from 'lucide-react';

// Dashboard Header Component
const DashboardHeader: React.FC = () => {
  return (
    <div className="bg-white p-4 rounded-t-xl border-b border-gray-200 flex justify-between items-center">
      <div className="flex items-center">
        <div className="bg-indigo-600 w-8 h-8 rounded flex items-center justify-center mr-3">
          <Users size={16} className="text-white" />
        </div>
        <h3 className="text-gray-800 font-medium">Sourcio.ai Dashboard</h3>
      </div>

      <div className="flex items-center space-x-4">
        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center cursor-pointer hover:bg-gray-200">
          <Bell size={16} className="text-gray-600" />
        </div>
        <div className="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center cursor-pointer hover:bg-gray-200">
          <Settings size={16} className="text-gray-600" />
        </div>
        <div className="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center cursor-pointer">
          <User size={16} className="text-white" />
        </div>
      </div>
    </div>
  );
};

// Dashboard Sidebar Component
const DashboardSidebar: React.FC<{ activeTab: string }> = ({ activeTab }) => {
  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: <BarChart2 size={18} /> },
    { id: 'candidates', label: 'Candidates', icon: <Users size={18} /> },
    { id: 'companies', label: 'Companies', icon: <Building size={18} /> },
    { id: 'evaluations', label: 'Evaluations', icon: <FileText size={18} /> },
  ];

  return (
    <div className="bg-gray-50 p-4 w-56 border-r border-gray-200 h-full">
      {tabs.map((tab) => (
        <div
          key={tab.id}
          className={`flex items-center p-3 rounded-lg mb-2 ${
            activeTab === tab.id
              ? 'bg-indigo-600 text-white'
              : 'text-gray-600 hover:bg-gray-100'
          }`}
        >
          <div className="mr-3">{tab.icon}</div>
          <span>{tab.label}</span>
        </div>
      ))}
    </div>
  );
};

// Tooltip Component
const Tooltip: React.FC<{
  isVisible: boolean;
  text: string;
  position: { top: number; left: number };
  step: number;
  totalSteps: number;
}> = ({ isVisible, text, position, step, totalSteps }) => {
  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.3 }}
      className="absolute z-50 bg-white border border-gray-200 text-gray-800 p-4 rounded-lg shadow-lg max-w-xs"
      style={{ top: position.top, left: position.left }}
    >
      <div className="flex items-center mb-2">
        <div className="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center mr-2">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="w-3 h-3 border-2 border-indigo-500 border-t-transparent rounded-full"
          />
        </div>
        <span className="text-sm font-medium text-indigo-600">Auto-Demo in Progress</span>
      </div>
      <div className="mb-2">{text}</div>
      <div className="flex justify-between items-center text-xs text-gray-500">
        <span>Step {step} of {totalSteps}</span>
        <span className="text-indigo-500">Watch the demo unfold...</span>
      </div>
    </motion.div>
  );
};

// CV Evaluation Modal
const CVEvaluationModal: React.FC<{ isOpen: boolean }> = ({ isOpen }) => {
  if (!isOpen) return null;

  return (
    <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-xl overflow-hidden max-w-4xl w-full max-h-[80vh] shadow-2xl"
      >
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 rounded-lg bg-blue-100 flex items-center justify-center mr-3">
              <Zap size={20} className="text-blue-600" />
            </div>
            <h2 className="text-xl font-bold text-gray-800">CV Evaluation</h2>
          </div>

          <div className="flex items-center">
            <div className="px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full text-xs font-medium flex items-center">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                className="w-3 h-3 border-2 border-indigo-500 border-t-transparent rounded-full mr-2"
              />
              Auto-Demo in Progress
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-0 h-[calc(80vh-76px)]">
          <div className="bg-white p-6 overflow-y-auto border-r border-gray-200">
            <h3 className="text-gray-800 font-medium mb-4">Candidate Profile</h3>
            <div className="bg-gray-50 rounded-lg p-4 mb-6 border border-gray-200">
              <div className="flex items-center mb-4">
                <div className="w-12 h-12 rounded-full bg-indigo-500 flex items-center justify-center mr-3">
                  <span className="text-white font-bold">JS</span>
                </div>
                <div>
                  <h3 className="text-gray-800 font-medium">John Smith</h3>
                  <p className="text-gray-600 text-sm">Senior Developer</p>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <p className="text-gray-500 text-xs">Experience</p>
                  <p className="text-gray-800">8 years</p>
                </div>
                <div>
                  <p className="text-gray-500 text-xs">Education</p>
                  <p className="text-gray-800">MSc Computer Science</p>
                </div>
                <div>
                  <p className="text-gray-500 text-xs">Skills</p>
                  <div className="flex flex-wrap gap-2 mt-1">
                    <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs">React</span>
                    <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-xs">Node.js</span>
                    <span className="px-2 py-1 bg-purple-100 text-purple-600 rounded text-xs">TypeScript</span>
                    <span className="px-2 py-1 bg-yellow-100 text-yellow-600 rounded text-xs">AWS</span>
                    <span className="px-2 py-1 bg-pink-100 text-pink-600 rounded text-xs">GraphQL</span>
                  </div>
                </div>
              </div>
            </div>

            <h3 className="text-gray-800 font-medium mb-4">CV Content</h3>
            <div className="bg-gray-50 rounded-lg p-4 text-gray-700 text-sm h-64 overflow-y-auto border border-gray-200">
              <p className="mb-2"><span className="font-bold">John Smith</span></p>
              <p className="mb-4 text-gray-600">Senior Software Developer</p>

              <p className="mb-2 text-gray-600 font-medium">PROFESSIONAL SUMMARY</p>
              <p className="mb-4">Experienced software developer with 8 years of experience in React and TypeScript development. Proficient in building scalable web applications using Node.js and GraphQL. Experience with cloud deployment on AWS.</p>

              <p className="mb-2 text-gray-600 font-medium">EXPERIENCE</p>
              <p className="font-medium">Senior Developer - TechCorp Inc.</p>
              <p className="text-gray-600 mb-1">2018 - Present</p>
              <ul className="list-disc list-inside mb-4 space-y-1">
                <li>Led development of customer-facing web applications using React and TypeScript</li>
                <li>Implemented GraphQL APIs for improved data fetching</li>
                <li>Deployed and maintained applications on AWS</li>
              </ul>

              <p className="font-medium">Developer - WebSolutions Ltd.</p>
              <p className="text-gray-600 mb-1">2015 - 2018</p>
              <ul className="list-disc list-inside mb-4 space-y-1">
                <li>Developed backend services using Node.js</li>
                <li>Created frontend components with React</li>
              </ul>
            </div>
          </div>

          <div className="bg-white p-6 overflow-y-auto">
            <h3 className="text-gray-800 font-medium mb-4">Evaluation Results</h3>

            <div className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
              <div className="flex justify-between items-center mb-3">
                <span className="text-gray-800">Match Score</span>
                <span className="text-2xl font-bold text-indigo-600">
                  85%
                </span>
              </div>

              <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden mb-4">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: '85%' }}
                  transition={{ duration: 1.5, delay: 0.5 }}
                  className="h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full"
                ></motion.div>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Skills Match</span>
                  <span className="text-green-600">90%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Experience Match</span>
                  <span className="text-green-600">85%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Education Match</span>
                  <span className="text-yellow-600">75%</span>
                </div>
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg mb-6 border border-gray-200">
              <h4 className="text-gray-800 font-medium mb-3">AI Insights</h4>
              <p className="text-gray-700 text-sm mb-3">
                This candidate is a strong match for the Senior Developer position at TechCorp Inc. Their React and TypeScript skills align perfectly with the company's tech stack requirements.
              </p>
              <div className="space-y-2">
                <div className="flex items-start">
                  <Check size={16} className="text-green-600 mr-2 mt-0.5" />
                  <p className="text-gray-700 text-sm">Strong frontend development skills with React ecosystem</p>
                </div>
                <div className="flex items-start">
                  <Check size={16} className="text-green-600 mr-2 mt-0.5" />
                  <p className="text-gray-700 text-sm">Experience exceeds the minimum requirements by 3 years</p>
                </div>
                <div className="flex items-start">
                  <AlertCircle size={16} className="text-amber-600 mr-2 mt-0.5" />
                  <p className="text-gray-700 text-sm">Cloud deployment skills (AWS) could be stronger for this role</p>
                </div>
              </div>
            </div>

            <div className="flex gap-4">
              <button className="flex-1 py-2 bg-indigo-600 hover:bg-indigo-700 text-white rounded-lg transition-colors">
                Contact Candidate
              </button>
              <button className="flex-1 py-2 bg-gray-200 hover:bg-gray-300 text-gray-800 rounded-lg transition-colors">
                Save Evaluation
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

// Main Component
export const AutomatedInteractiveDemo: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [tooltipInfo, setTooltipInfo] = useState({
    isVisible: false,
    text: '',
    position: { top: 0, left: 0 },
    step: 0,
    totalSteps: 5
  });
  const [showEvaluation, setShowEvaluation] = useState(false);

  // Function to start the demo sequence
  const startDemoSequence = () => {
    // Reset state
    setActiveTab('dashboard');
    setShowEvaluation(false);
    setTooltipInfo({
      isVisible: false,
      text: '',
      position: { top: 0, left: 0 },
      step: 0,
      totalSteps: 5
    });

    let timeouts: NodeJS.Timeout[] = [];

    // Initial tooltip
    const t1 = setTimeout(() => {
      setTooltipInfo({
        isVisible: true,
        text: 'Welcome to the Sourcio.ai Dashboard! This is where you can manage all your recruitment activities.',
        position: { top: 80, left: 300 },
        step: 1,
        totalSteps: 5
      });
    }, 1000);
    timeouts.push(t1);

    // Switch to candidates tab
    const t2 = setTimeout(() => {
      setTooltipInfo({
        isVisible: true,
        text: 'Let\'s check the candidates in our system.',
        position: { top: 150, left: 200 },
        step: 2,
        totalSteps: 5
      });
    }, 4000);
    timeouts.push(t2);

    const t3 = setTimeout(() => {
      setActiveTab('candidates');
    }, 6000);
    timeouts.push(t3);

    // Show tooltip for candidate evaluation
    const t4 = setTimeout(() => {
      setTooltipInfo({
        isVisible: true,
        text: 'Let\'s evaluate this candidate against a job position.',
        position: { top: 240, left: 400 },
        step: 3,
        totalSteps: 5
      });
    }, 8000);
    timeouts.push(t4);

    // Show evaluation modal
    const t5 = setTimeout(() => {
      setShowEvaluation(true);
      setTooltipInfo({
        isVisible: false,
        text: '',
        position: { top: 0, left: 0 },
        step: 4,
        totalSteps: 5
      });
    }, 11000);
    timeouts.push(t5);

    // Hide evaluation modal and show final tooltip
    const t6 = setTimeout(() => {
      setShowEvaluation(false);
      setTooltipInfo({
        isVisible: true,
        text: 'Demo complete! Click the "Restart Demo" button to see it again.',
        position: { top: 450, left: 300 },
        step: 5,
        totalSteps: 5
      });
    }, 20000);
    timeouts.push(t6);

    return timeouts;
  };

  // Store timeouts in a ref so we can clear them
  const timeoutsRef = useRef<NodeJS.Timeout[]>([]);

  // Automated demo sequence
  useEffect(() => {
    timeoutsRef.current = startDemoSequence();

    // Clean up
    return () => {
      timeoutsRef.current.forEach(timeout => clearTimeout(timeout));
    };
  }, []);

  // Function to restart the demo
  const handleRestartDemo = () => {
    // Clear existing timeouts
    timeoutsRef.current.forEach(timeout => clearTimeout(timeout));

    // Start new sequence
    timeoutsRef.current = startDemoSequence();
  };

  return (
    <section className="py-8 bg-recruiter-darknavy">
      <div className="container mx-auto px-6">
        <div className="max-w-5xl mx-auto relative">
          {/* Demo Progress Bar and Controls */}
          <div className="absolute -top-16 left-0 right-0 z-10 flex items-center justify-between">
            <div className="flex items-center space-x-3 bg-white rounded-lg p-2 shadow-md border border-gray-200 flex-1 mr-4">
              <div className="px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full text-xs font-medium flex items-center">
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                  className="w-3 h-3 border-2 border-indigo-500 border-t-transparent rounded-full mr-2"
                />
                Auto-Demo
              </div>

              <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
                <motion.div
                  className="h-full bg-indigo-600 rounded-full"
                  initial={{ width: '0%' }}
                  animate={{
                    width: `${(tooltipInfo.step / tooltipInfo.totalSteps) * 100}%`
                  }}
                  transition={{ duration: 0.5 }}
                />
              </div>

              <div className="text-xs text-gray-600 font-medium whitespace-nowrap">
                Step {tooltipInfo.step} of {tooltipInfo.totalSteps}
              </div>
            </div>

            <button
              onClick={handleRestartDemo}
              className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center text-sm"
            >
              Restart Demo
            </button>
          </div>

          {/* Dashboard Simulator */}
          <div className="bg-white rounded-xl overflow-hidden border border-gray-200 shadow-2xl">
            <DashboardHeader />

            <div className="flex h-[500px]">
              <DashboardSidebar activeTab={activeTab} />

              {/* Dashboard Content */}
              <div className="flex-1 overflow-auto bg-white">
                {activeTab === 'dashboard' && (
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h2 className="text-xl font-bold text-gray-800">Dashboard Overview</h2>
                      <div className="px-3 py-1 bg-indigo-100 text-indigo-600 rounded-full text-xs font-medium flex items-center">
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                          className="w-3 h-3 border-2 border-indigo-500 border-t-transparent rounded-full mr-2"
                        />
                        Auto-Demo in Progress
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 mb-6">
                      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-600">Active Candidates</span>
                          <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center">
                            <Users size={18} className="text-indigo-600" />
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-gray-800">128</div>
                        <div className="text-xs text-green-600">+12% from last month</div>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-600">Companies</span>
                          <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center">
                            <Building size={18} className="text-blue-600" />
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-gray-800">24</div>
                        <div className="text-xs text-green-600">+3 new this month</div>
                      </div>
                      <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-600">Evaluations</span>
                          <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center">
                            <FileText size={18} className="text-purple-600" />
                          </div>
                        </div>
                        <div className="text-2xl font-bold text-gray-800">342</div>
                        <div className="text-xs text-green-600">+28% from last month</div>
                      </div>
                    </div>

                    <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm mb-6">
                      <h3 className="text-gray-800 font-medium mb-3">Recent Activity</h3>
                      <div className="space-y-3">
                        <div className="flex items-center p-2 rounded hover:bg-gray-50">
                          <div className="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <FileText size={14} className="text-blue-600" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-800">New CV uploaded: John Smith</div>
                            <div className="text-xs text-gray-500">2 minutes ago</div>
                          </div>
                        </div>
                        <div className="flex items-center p-2 rounded hover:bg-gray-50">
                          <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <Building size={14} className="text-green-600" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-800">New company added: TechCorp Inc.</div>
                            <div className="text-xs text-gray-500">1 hour ago</div>
                          </div>
                        </div>
                        <div className="flex items-center p-2 rounded hover:bg-gray-50">
                          <div className="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                            <Users size={14} className="text-purple-600" />
                          </div>
                          <div>
                            <div className="text-sm text-gray-800">Candidate matched: Sarah Johnson</div>
                            <div className="text-xs text-gray-500">3 hours ago</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'candidates' && (
                  <div className="p-6">
                    <div className="flex justify-between items-center mb-6">
                      <h2 className="text-xl font-bold text-gray-800">Candidates</h2>
                      <div className="flex space-x-3">
                        <div className="relative">
                          <input
                            type="text"
                            placeholder="Search candidates..."
                            className="bg-white text-gray-700 pl-9 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-1 focus:ring-indigo-500 w-64"
                          />
                          <Search size={16} className="text-gray-500 absolute left-3 top-2.5" />
                        </div>
                        <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center">
                          <Plus size={16} className="mr-2" />
                          Add Candidate
                        </button>
                      </div>
                    </div>

                    <div className="bg-white rounded-lg overflow-hidden border border-gray-200">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-gray-50 border-b border-gray-200">
                            <th className="text-left p-4 text-gray-600 font-medium">Name</th>
                            <th className="text-left p-4 text-gray-600 font-medium">Position</th>
                            <th className="text-left p-4 text-gray-600 font-medium">Skills</th>
                            <th className="text-left p-4 text-gray-600 font-medium">Match Score</th>
                            <th className="text-left p-4 text-gray-600 font-medium">Actions</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr className={`border-t border-gray-200 ${tooltipInfo.position.top === 240 ? 'bg-indigo-50' : 'hover:bg-gray-50'}`}>
                            <td className="p-4">
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center mr-3">
                                  <span className="text-white text-sm font-medium">JS</span>
                                </div>
                                <span className="text-gray-800">John Smith</span>
                              </div>
                            </td>
                            <td className="p-4 text-gray-700">Senior Developer</td>
                            <td className="p-4">
                              <div className="flex space-x-1">
                                <span className="px-2 py-1 bg-blue-100 text-blue-600 rounded text-xs">React</span>
                                <span className="px-2 py-1 bg-green-100 text-green-600 rounded text-xs">Node.js</span>
                              </div>
                            </td>
                            <td className="p-4">
                              <div className="flex items-center">
                                <div className="w-16 bg-gray-200 h-2 rounded-full mr-2">
                                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                                </div>
                                <span className="text-green-600">85%</span>
                              </div>
                            </td>
                            <td className="p-4">
                              <button className="text-indigo-600 hover:text-indigo-800">
                                View Profile
                              </button>
                            </td>
                          </tr>
                          <tr className="border-t border-gray-200 hover:bg-gray-50">
                            <td className="p-4">
                              <div className="flex items-center">
                                <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                                  <span className="text-white text-sm font-medium">SJ</span>
                                </div>
                                <span className="text-gray-800">Sarah Johnson</span>
                              </div>
                            </td>
                            <td className="p-4 text-gray-700">UX Designer</td>
                            <td className="p-4">
                              <div className="flex space-x-1">
                                <span className="px-2 py-1 bg-purple-100 text-purple-600 rounded text-xs">Figma</span>
                                <span className="px-2 py-1 bg-yellow-100 text-yellow-600 rounded text-xs">UI/UX</span>
                              </div>
                            </td>
                            <td className="p-4">
                              <div className="flex items-center">
                                <div className="w-16 bg-gray-200 h-2 rounded-full mr-2">
                                  <div className="bg-green-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                                </div>
                                <span className="text-green-600">92%</span>
                              </div>
                            </td>
                            <td className="p-4">
                              <button className="text-indigo-600 hover:text-indigo-800">
                                View Profile
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Interactive Tooltip */}
          <Tooltip
            isVisible={tooltipInfo.isVisible}
            text={tooltipInfo.text}
            position={tooltipInfo.position}
            step={tooltipInfo.step}
            totalSteps={tooltipInfo.totalSteps}
          />

          {/* CV Evaluation Modal */}
          <CVEvaluationModal isOpen={showEvaluation} />
        </div>
      </div>
    </section>
  );
};
