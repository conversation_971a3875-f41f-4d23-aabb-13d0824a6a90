import React from 'react';
import { toast } from '@/hooks/use-toast';
import { ToastAction } from '@/components/ui/toast';
import { AlertCircle } from 'lucide-react';

interface RateLimitToastProps {
  onRetry: () => void;
  message?: string;
}

/**
 * Display a toast notification for rate limiting errors with a retry button
 */
export function showRateLimitToast({ onRetry, message }: RateLimitToastProps) {
  return toast({
    title: (
      <div className="flex items-center gap-2">
        <AlertCircle className="h-4 w-4 text-amber-500" />
        <span>AI Service Busy</span>
      </div>
    ),
    description: message || 'The AI service is currently experiencing high demand. Please try again in a few moments.',
    variant: 'destructive',
    action: (
      <ToastAction altText="Retry" onClick={onRetry}>
        Retry
      </ToastAction>
    ),
  });
}

/**
 * Check if an error is a rate limiting error
 */
export function isRateLimitError(error: any): boolean {
  return (
    error.status === 429 ||
    error.code === 429 ||
    (error.message && error.message.toLowerCase().includes('rate limit')) ||
    (error.userMessage && error.userMessage.toLowerCase().includes('high demand'))
  );
}

/**
 * Get a user-friendly error message for different error types
 */
export function getErrorMessage(error: any): string {
  if (isRateLimitError(error)) {
    return error.userMessage || 'The AI service is currently experiencing high demand. Please try again in a few moments.';
  }
  
  if (error.userMessage) {
    return error.userMessage;
  }
  
  if (error.message) {
    // Clean up error message for display
    const message = error.message.replace(/^Error: /, '');
    return message.charAt(0).toUpperCase() + message.slice(1);
  }
  
  return 'An unexpected error occurred. Please try again.';
}
