import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { AlertCircle, Upload, Briefcase, Building2, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { useCurrentUsage, useSubscriptionLimits } from '@/hooks/use-usage';

const UsageDashboard: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const { data: usage, isLoading: isLoadingUsage } = useCurrentUsage();
  const { data: limits, isLoading: isLoadingLimits } = useSubscriptionLimits();

  // Calculate percentages
  const cvPercentage = usage && limits ? (usage.cv_uploads_count / limits.cv_upload_limit) * 100 : 0;
  const jobsPercentage = usage && limits ? (usage.active_jobs_count / limits.job_posting_limit) * 100 : 0;
  const companiesPercentage = usage && limits ? (usage.company_profiles_count / limits.company_profile_limit) * 100 : 0;
  const teamPercentage = usage && limits ? (usage.team_members_count / limits.team_member_limit) * 100 : 0;

  // Check if any limit is approaching (>80%)
  const isApproachingLimit =
    cvPercentage > 80 ||
    jobsPercentage > 80 ||
    companiesPercentage > 80 ||
    teamPercentage > 80;

  // Check if any limit is reached (100%)
  const isLimitReached =
    cvPercentage >= 100 ||
    jobsPercentage >= 100 ||
    companiesPercentage >= 100 ||
    teamPercentage >= 100;

  if (isLoadingUsage || isLoadingLimits) {
    return (
      <Card className="bg-white border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="text-gray-800 text-lg">Subscription Usage</CardTitle>
          <CardDescription>Loading usage data...</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (!usage || !limits) {
    return (
      <Card className="bg-white border-gray-200 shadow-sm">
        <CardHeader>
          <CardTitle className="text-gray-800 text-lg">Subscription Usage</CardTitle>
          <CardDescription>Unable to load usage data</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card className="bg-white border-gray-200 shadow-sm">
      <CardHeader>
        <CardTitle className="text-gray-800 text-lg">Subscription Usage</CardTitle>
        <CardDescription>Your current usage for this billing period</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Show warning if approaching limit */}
        {isApproachingLimit && !isLimitReached && (
          <Alert variant="warning" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              You're approaching your subscription limits. Consider upgrading your plan for more resources.
            </AlertDescription>
          </Alert>
        )}

        {/* Show error if limit reached */}
        {isLimitReached && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              You've reached one or more of your subscription limits. Please upgrade your plan to continue using all features.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* CV Uploads */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Upload className="h-4 w-4 mr-2 text-blue-500" />
                <span className="text-sm font-medium">CV Uploads</span>
              </div>
              <span className="text-sm text-gray-500">
                {usage.cv_uploads_count} / {limits.cv_upload_limit}
                {limits.cv_upload_limit > 999 && " (Unlimited)"}
              </span>
            </div>
            <Progress
              value={cvPercentage > 100 ? 100 : cvPercentage}
              className="h-2"
              indicatorClassName={
                cvPercentage >= 100
                  ? 'bg-red-500'
                  : cvPercentage > 80
                    ? 'bg-amber-500'
                    : 'bg-green-500'
              }
            />
          </div>

          {/* Active Jobs */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Briefcase className="h-4 w-4 mr-2 text-indigo-500" />
                <span className="text-sm font-medium">Active Jobs</span>
              </div>
              <span className="text-sm text-gray-500">
                {usage.active_jobs_count} / {limits.job_posting_limit}
                {limits.job_posting_limit > 999 && " (Unlimited)"}
              </span>
            </div>
            <Progress
              value={jobsPercentage > 100 ? 100 : jobsPercentage}
              className="h-2"
              indicatorClassName={
                jobsPercentage >= 100
                  ? 'bg-red-500'
                  : jobsPercentage > 80
                    ? 'bg-amber-500'
                    : 'bg-green-500'
              }
            />
          </div>

          {/* Company Profiles */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Building2 className="h-4 w-4 mr-2 text-purple-500" />
                <span className="text-sm font-medium">Company Profiles</span>
              </div>
              <span className="text-sm text-gray-500">
                {usage.company_profiles_count} / {limits.company_profile_limit}
                {limits.company_profile_limit > 999 && " (Unlimited)"}
              </span>
            </div>
            <Progress
              value={companiesPercentage > 100 ? 100 : companiesPercentage}
              className="h-2"
              indicatorClassName={
                companiesPercentage >= 100
                  ? 'bg-red-500'
                  : companiesPercentage > 80
                    ? 'bg-amber-500'
                    : 'bg-green-500'
              }
            />
          </div>

          {/* Team Members */}
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2 text-teal-500" />
                <span className="text-sm font-medium">Team Members</span>
              </div>
              <span className="text-sm text-gray-500">
                {usage.team_members_count} / {limits.team_member_limit}
                {limits.team_member_limit > 999 && " (Unlimited)"}
              </span>
            </div>
            <Progress
              value={teamPercentage > 100 ? 100 : teamPercentage}
              className="h-2"
              indicatorClassName={
                teamPercentage >= 100
                  ? 'bg-red-500'
                  : teamPercentage > 80
                    ? 'bg-amber-500'
                    : 'bg-green-500'
              }
            />
          </div>
        </div>

        {/* Upgrade button */}
        {(isApproachingLimit || isLimitReached) && (
          <div className="mt-4 text-center">
            <Button
              onClick={() => navigate('/dashboard/pricing')}
              className="bg-recruiter-lightblue hover:bg-blue-600"
            >
              Upgrade Plan
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default UsageDashboard;
