import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  Edit,
  ArrowLeft,
  Calendar,
  DollarSign,
  MapPin,
  Briefcase,
  Building,
  Clock,
  Users,
  Share2,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  FileText,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { useJob, useUpdateJob } from '@/hooks/use-jobs';
import { useJobApplications } from '@/hooks/use-applications';
import { JobSocialShare } from "@/components/JobSocialShare";

// Mock job data
const mockJob = {
  id: '1',
  title: 'Frontend Developer',
  department: 'Engineering',
  location: 'San Francisco, CA',
  locationType: 'Hybrid',
  type: 'Full-time',
  experience: '3-5 years',
  salary: {
    min: '90000',
    max: '120000',
    currency: 'USD',
    period: 'yearly',
    showSalary: true
  },
  description: 'We are looking for a skilled Frontend Developer to join our engineering team. You will be responsible for building user interfaces for our web applications, ensuring they are responsive, accessible, and provide an excellent user experience.',
  requirements: [
    'Bachelor\'s degree in Computer Science or related field',
    '3-5 years of experience in frontend development',
    'Proficiency in JavaScript, TypeScript, React, and modern CSS',
    'Experience with responsive design and cross-browser compatibility',
    'Knowledge of frontend build tools and testing frameworks',
    'Strong problem-solving skills and attention to detail'
  ],
  responsibilities: [
    'Develop user interfaces using React and TypeScript',
    'Collaborate with designers to implement UI/UX designs',
    'Write clean, maintainable, and efficient code',
    'Optimize applications for maximum speed and scalability',
    'Participate in code reviews and contribute to team standards',
    'Stay up-to-date with emerging trends and technologies'
  ],
  benefits: [
    'Competitive salary and equity package',
    'Health, dental, and vision insurance',
    'Flexible work schedule and remote options',
    '401(k) matching',
    'Professional development budget',
    'Generous vacation policy'
  ],
  applicationDeadline: '2023-12-15',
  postedDate: '2023-09-15',
  status: 'active',
  applicants: 18,
  views: 245
};

// Mock applicants data
const mockApplicants = [
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    appliedDate: '2023-09-18',
    status: 'screening',
    matchScore: 92,
    avatar: 'https://i.pravatar.cc/150?u=1'
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    appliedDate: '2023-09-20',
    status: 'interview',
    matchScore: 88,
    avatar: 'https://i.pravatar.cc/150?u=2'
  },
  {
    id: '3',
    name: 'Michael Johnson',
    email: '<EMAIL>',
    appliedDate: '2023-09-22',
    status: 'new',
    matchScore: 78,
    avatar: 'https://i.pravatar.cc/150?u=3'
  },
  {
    id: '4',
    name: 'Emily Davis',
    email: '<EMAIL>',
    appliedDate: '2023-09-25',
    status: 'rejected',
    matchScore: 65,
    avatar: 'https://i.pravatar.cc/150?u=4'
  },
  {
    id: '5',
    name: 'David Wilson',
    email: '<EMAIL>',
    appliedDate: '2023-09-28',
    status: 'new',
    matchScore: 85,
    avatar: 'https://i.pravatar.cc/150?u=5'
  }
];

// Helper function to extract department from job title
const getDepartmentFromTitle = (title: string): string => {
  if (title.toLowerCase().includes('developer') || title.toLowerCase().includes('engineer')) {
    return 'Engineering';
  } else if (title.toLowerCase().includes('design')) {
    return 'Design';
  } else if (title.toLowerCase().includes('product')) {
    return 'Product';
  } else if (title.toLowerCase().includes('market')) {
    return 'Marketing';
  } else if (title.toLowerCase().includes('sales')) {
    return 'Sales';
  } else if (title.toLowerCase().includes('hr') || title.toLowerCase().includes('human resources')) {
    return 'HR';
  } else if (title.toLowerCase().includes('finance') || title.toLowerCase().includes('account')) {
    return 'Finance';
  } else {
    return 'Other';
  }
};

// Status badge color mapping
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'active':
      return 'bg-green-500/20 text-green-500 hover:bg-green-500/30';
    case 'draft':
      return 'bg-amber-500/20 text-amber-500 hover:bg-amber-500/30';
    case 'closed':
      return 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30';
    case 'new':
      return 'bg-blue-500/20 text-blue-500 hover:bg-blue-500/30';
    case 'screening':
      return 'bg-amber-500/20 text-amber-500 hover:bg-amber-500/30';
    case 'interview':
      return 'bg-purple-500/20 text-purple-500 hover:bg-purple-500/30';
    case 'rejected':
      return 'bg-red-500/20 text-red-500 hover:bg-red-500/30';
    default:
      return 'bg-gray-500/20 text-gray-400 hover:bg-gray-500/30';
  }
};

const JobDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();

  // Fetch the real job data using the useJob hook
  const { data: job, isLoading, error } = useJob(id || '');
  const { mutate: updateJob } = useUpdateJob();

  // Fetch job applications (candidates assigned to this job)
  const { data: applications = [], isLoading: candidatesLoading } = useJobApplications(id || '');

  // Use state for job status that initializes from the fetched job
  const [jobStatus, setJobStatus] = useState<'active' | 'closed' | 'draft'>('active');

  // Update local state when job data is loaded
  useEffect(() => {
    if (job) {
      setJobStatus(job.status as 'active' | 'closed' | 'draft');
    }
  }, [job]);



  // Handle status change
  const handleStatusChange = (newStatus: 'active' | 'closed' | 'draft') => {
    if (!id || !job) return;

    // Update local state
    setJobStatus(newStatus);

    // Update job in the database
    updateJob(
      {
        id,
        job: { status: newStatus }
      },
      {
        onSuccess: () => {
          toast({
            title: 'Status updated',
            description: `Job status has been changed to ${newStatus}.`,
          });
        },
        onError: (error) => {
          // Revert local state on error
          setJobStatus(job.status as 'active' | 'closed' | 'draft');
          toast({
            title: 'Error',
            description: 'Failed to update job status.',
            variant: 'destructive'
          });
        }
      }
    );
  };

  // Format currency
  const formatCurrency = (amount: string, currency: string) => {
    const formatter = new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      maximumFractionDigits: 0
    });

    return formatter.format(Number(amount));
  };

  // Format salary range - adapted to work with real job data structure
  const formatSalaryRange = () => {
    if (!job) return 'Not available';

    // Handle real job data which might not have the same structure as mock data
    const salaryMin = job.salary_min;
    const salaryMax = job.salary_max;

    if (!salaryMin && !salaryMax) return 'Not disclosed';

    const min = salaryMin ? formatCurrency(salaryMin.toString(), 'USD') : '';
    const max = salaryMax ? formatCurrency(salaryMax.toString(), 'USD') : '';

    if (min && max) {
      return `${min} - ${max} /year`;
    } else if (min) {
      return `From ${min} /year`;
    } else if (max) {
      return `Up to ${max} /year`;
    }

    return 'Not disclosed';
  };

  // Set page title
  useEffect(() => {
    if (job) {
      document.title = `${job.title} - Job Details - Sourcio.ai`;
    } else {
      document.title = 'Job Details - Sourcio.ai';
    }
  }, [job]);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
              onClick={() => navigate('/dashboard/jobs')}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold text-gray-800">Job Details</h1>
          </div>

          {!isLoading && job && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="border-gray-200 text-gray-700 hover:bg-gray-100"
                onClick={() => navigate(`/dashboard/jobs/edit/${id}`)}
              >
                <Edit className="mr-2 h-4 w-4" /> Edit Job
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className="bg-recruiter-lightblue hover:bg-blue-500">
                    {jobStatus === 'active' ? (
                      <>
                        <Eye className="mr-2 h-4 w-4" /> Active
                      </>
                    ) : jobStatus === 'draft' ? (
                      <>
                        <FileText className="mr-2 h-4 w-4" /> Draft
                      </>
                    ) : (
                      <>
                        <EyeOff className="mr-2 h-4 w-4" /> Closed
                      </>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="bg-white border-gray-200 text-gray-800">
                  <DropdownMenuLabel className="text-gray-500 font-medium">Change Status</DropdownMenuLabel>
                  <DropdownMenuSeparator className="bg-gray-200" />
                  {jobStatus !== 'active' && (
                    <DropdownMenuItem
                      className="hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleStatusChange('active')}
                    >
                      <Eye className="mr-2 h-4 w-4 text-green-500" /> Mark as Active
                    </DropdownMenuItem>
                  )}
                  {jobStatus !== 'draft' && (
                    <DropdownMenuItem
                      className="hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleStatusChange('draft')}
                    >
                      <FileText className="mr-2 h-4 w-4 text-amber-500" /> Mark as Draft
                    </DropdownMenuItem>
                  )}
                  {jobStatus !== 'closed' && (
                    <DropdownMenuItem
                      className="hover:bg-gray-100 cursor-pointer"
                      onClick={() => handleStatusChange('closed')}
                    >
                      <EyeOff className="mr-2 h-4 w-4 text-gray-400" /> Mark as Closed
                    </DropdownMenuItem>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>

        {/* Loading state */}
        {isLoading && (
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardContent className="p-6 flex justify-center items-center min-h-[200px]">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-gray-500">Loading job details...</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error state */}
        {error && !isLoading && (
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-red-500 mb-2">Error Loading Job</h3>
                <p className="text-gray-600">We couldn't load the job details. The job may have been deleted or you don't have permission to view it.</p>
                <Button
                  className="mt-4"
                  onClick={() => navigate('/dashboard/jobs')}
                >
                  Return to Job Listings
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Job Overview - Only show when job data is loaded */}
        {!isLoading && job && (
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-6">
                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <h2 className="text-2xl font-bold text-gray-800">{job.title}</h2>
                    <Badge className={getStatusColor(jobStatus)}>
                      {jobStatus.charAt(0).toUpperCase() + jobStatus.slice(1)}
                    </Badge>
                  </div>

                  <div className="flex flex-wrap gap-4 text-gray-600 mb-4">
                    {/* Use department from job data or derive it from title */}
                    <div className="flex items-center">
                      <Building className="mr-2 h-4 w-4 text-gray-500" />
                      {job.department || getDepartmentFromTitle(job.title)}
                    </div>
                    <div className="flex items-center">
                      <MapPin className="mr-2 h-4 w-4 text-gray-500" />
                      {job.location}
                    </div>
                    <div className="flex items-center">
                      <Briefcase className="mr-2 h-4 w-4 text-gray-500" />
                      {job.job_type}
                    </div>
                    <div className="flex items-center">
                      <Clock className="mr-2 h-4 w-4 text-gray-500" />
                      {job.experience_level}
                    </div>
                  </div>

                  <div className="flex flex-wrap gap-4 text-gray-600">
                    <div className="flex items-center">
                      <DollarSign className="mr-2 h-4 w-4 text-gray-500" />
                      {formatSalaryRange()}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                      Posted: {new Date(job.created_at).toLocaleDateString()}
                    </div>
                    {job.closing_date && (
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-4 w-4 text-gray-500" />
                        Deadline: {new Date(job.closing_date).toLocaleDateString()}
                      </div>
                    )}
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row md:flex-col gap-4">
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 text-center">
                    <div className="text-3xl font-bold text-recruiter-blue">{applications.length}</div>
                    <div className="text-sm text-gray-500">Applicants</div>
                  </div>
                  <div className="bg-gray-50 p-4 rounded-lg border border-gray-200 text-center">
                    <div className="text-3xl font-bold text-recruiter-blue">{job.views || 0}</div>
                    <div className="text-sm text-gray-500">Views</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tabs for job details and applicants - Only show when job data is loaded */}
        {!isLoading && job && (
          <Tabs defaultValue="details">
            <TabsList className="bg-white border-b border-gray-200 w-full justify-start">
              <TabsTrigger value="details" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
                Job Details
              </TabsTrigger>
              <TabsTrigger value="applicants" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
                Applicants ({applications.length})
              </TabsTrigger>
            </TabsList>

            {/* Job Details Tab */}
            <TabsContent value="details">
              <div className="grid grid-cols-1 gap-6">
                {/* Description */}
                <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <CardHeader>
                    <CardTitle className="text-gray-800 text-lg">Job Description</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-gray-600 whitespace-pre-line">{job.description}</p>
                  </CardContent>
                </Card>

                {/* Requirements */}
                <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                  <CardHeader>
                    <CardTitle className="text-gray-800 text-lg">Requirements</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {/* Handle requirements as a string or array */}
                    {typeof job.requirements === 'string' ? (
                      <p className="text-gray-600 whitespace-pre-line">{job.requirements}</p>
                    ) : Array.isArray(job.requirements) ? (
                      <ul className="list-disc list-inside text-gray-600 space-y-2">
                        {job.requirements.map((requirement, index) => (
                          <li key={index}>{requirement}</li>
                        ))}
                      </ul>
                    ) : (
                      <p className="text-gray-600">No requirements specified</p>
                    )}
                  </CardContent>
                </Card>

                {/* Only show responsibilities and benefits if they exist in the data */}
                {job.responsibilities && (
                  <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardHeader>
                      <CardTitle className="text-gray-800 text-lg">Responsibilities</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {typeof job.responsibilities === 'string' ? (
                        <p className="text-gray-600 whitespace-pre-line">{job.responsibilities}</p>
                      ) : Array.isArray(job.responsibilities) ? (
                        <ul className="list-disc list-inside text-gray-600 space-y-2">
                          {job.responsibilities.map((responsibility, index) => (
                            <li key={index}>{responsibility}</li>
                          ))}
                        </ul>
                      ) : null}
                    </CardContent>
                  </Card>
                )}

                {job.benefits && (
                  <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                    <CardHeader>
                      <CardTitle className="text-gray-800 text-lg">Benefits</CardTitle>
                    </CardHeader>
                    <CardContent>
                      {typeof job.benefits === 'string' ? (
                        <p className="text-gray-600 whitespace-pre-line">{job.benefits}</p>
                      ) : Array.isArray(job.benefits) ? (
                        <ul className="list-disc list-inside text-gray-600 space-y-2">
                          {job.benefits.map((benefit, index) => (
                            <li key={index}>{benefit}</li>
                          ))}
                        </ul>
                      ) : null}
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>

            {/* Applicants Tab */}
            <TabsContent value="applicants">
              <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-gray-800 text-lg">Applicants ({applications.length})</CardTitle>
                  <Button className="bg-recruiter-lightblue hover:bg-blue-500">
                    <Share2 className="mr-2 h-4 w-4" /> Export Applicants
                  </Button>
                </CardHeader>
                <CardContent>
                  {candidatesLoading ? (
                    <div className="text-center py-8">
                      <p className="text-gray-500">Loading applicants...</p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {applications.length === 0 ? (
                        <div className="text-center py-8">
                          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <h3 className="text-lg font-medium text-gray-800">No applicants yet</h3>
                          <p className="text-gray-500 mt-1">
                            When candidates are assigned to this job, they will appear here.
                          </p>
                        </div>
                      ) : (
                        applications.map((application) => {
                          const candidate = application.candidates;
                          if (!candidate) return null;

                          return (
                            <div
                              key={application.id}
                              className="bg-white p-4 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors shadow-sm hover:shadow-md"
                            >
                              <div className="flex flex-col md:flex-row md:items-center gap-4">
                                <div className="flex items-start gap-3">
                                  <Avatar className="h-10 w-10">
                                    <AvatarImage src={candidate.avatar_url} alt={candidate.name} />
                                    <AvatarFallback>{candidate.name ? candidate.name.charAt(0) : 'C'}</AvatarFallback>
                                  </Avatar>
                                  <div>
                                    <h3 className="text-gray-800 font-medium">{candidate.name || 'Unnamed Candidate'}</h3>
                                    <p className="text-gray-500 text-sm">{candidate.email || 'No email provided'}</p>
                                    <div className="flex items-center mt-1">
                                      <Calendar className="h-3 w-3 text-gray-400 mr-1" />
                                      <span className="text-xs text-gray-500">
                                        Applied {new Date(application.applied_at).toLocaleDateString()}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              
                              <div className="flex items-center gap-4 md:ml-auto">
                                {candidate.evaluation_score && (
                                  <div className="text-center">
                                    <div className="text-sm font-medium text-gray-800">
                                      {candidate.evaluation_score}%
                                    </div>
                                    <div className="text-xs text-gray-500">Match</div>
                                  </div>
                                )}
                                
                                <Badge 
                                  variant={
                                    candidate.status === 'new' ? 'secondary' :
                                    candidate.status === 'screening' ? 'default' :
                                    candidate.status === 'interview' ? 'default' :
                                    candidate.status === 'rejected' ? 'destructive' :
                                    'secondary'
                                  }
                                  className="capitalize"
                                >
                                  {candidate.status}
                                </Badge>
                                
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => window.open(candidate.cv_url, '_blank')}
                                >
                                  <FileText className="h-4 w-4 mr-1" />
                                  View CV
                                </Button>
                              </div>
                            </div>
                          </div>
                          );
                        })
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}
      </div>
    </DashboardLayout>
  );
};

export default JobDetails;







