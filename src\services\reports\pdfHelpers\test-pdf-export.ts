import { ReportData, ReportType } from '../types';
import { generatePdfReport, downloadPdfReport } from '../generators/pdfGenerator';

/**
 * Test function to generate a sample PDF report
 */
export const testPdfExport = (reportType: ReportType = 'recruitment_funnel'): void => {
  // Create sample report data based on report type
  let reportData: ReportData;

  switch (reportType) {
    case 'time_to_hire':
      reportData = {
        title: 'Time to Hire Report',
        description: 'Analysis of hiring timeframes by position',
        date: new Date(),
        companyName: 'Test Company',
        data: [
          { position: 'Software Engineer', days: 28, status: 'Average' },
          { position: 'Product Manager', days: 35, status: 'Slow' },
          { position: 'UX Designer', days: 21, status: 'Good' },
          { position: 'Data Scientist', days: 42, status: 'Slow' },
          { position: 'Marketing Specialist', days: 18, status: 'Good' }
        ],
        columns: [
          { header: 'Position', dataKey: 'position' },
          { header: 'Days', dataKey: 'days' },
          { header: 'Status', dataKey: 'status' }
        ],
        summary: {
          'Average Time to Hire': '28.8 days',
          'Fastest Position': 'Marketing Specialist (18 days)',
          'Slowest Position': 'Data Scientist (42 days)'
        },
        filters: {
          'Time Period': 'Last 90 days',
          'Department': 'All'
        },
        executiveSummary: 'This report analyzes your time-to-hire metrics from the last 90 days. Your average time to hire across all positions is 28.8 days. The fastest position to fill was Marketing Specialist at 18 days, while the slowest was Data Scientist at 42 days. Your average time to hire is within the industry benchmark of 30 days. Your recruitment process is efficient in terms of time to hire.',
        recommendations: [
          'Review the hiring process for Data Scientist, which takes the longest to fill at 42 days.',
          'Implement a more efficient screening process to reduce time-to-hire.',
          'Consider using pre-employment assessments to quickly identify qualified candidates.',
          'Streamline the interview scheduling process to reduce delays.',
          'Set clear timelines for each stage of the recruitment process.'
        ],
        historicalData: [
          {
            period: 'Previous Quarter',
            data: [
              { position: 'Software Engineer', days: 32, status: 'Slow' },
              { position: 'Product Manager', days: 38, status: 'Slow' },
              { position: 'UX Designer', days: 25, status: 'Average' },
              { position: 'Data Scientist', days: 45, status: 'Slow' },
              { position: 'Marketing Specialist', days: 20, status: 'Good' }
            ]
          }
        ],
        benchmarkData: [
          {
            name: 'Industry Average Time to Hire',
            value: 30,
            description: 'Average number of days from job posting to offer acceptance across the recruitment industry.'
          },
          {
            name: 'Top Quartile Time to Hire',
            value: 21,
            description: 'Time to hire achieved by the top 25% of companies in the recruitment industry.'
          }
        ]
      };
      break;

    case 'source_effectiveness':
      reportData = {
        title: 'Source Effectiveness Report',
        description: 'Analysis of recruitment sources and their effectiveness',
        date: new Date(),
        companyName: 'Test Company',
        data: [
          { source: 'LinkedIn', applicants: 120, hires: 15, conversion: '12.5%', quality: '85%' },
          { source: 'Indeed', applicants: 200, hires: 18, conversion: '9.0%', quality: '75%' },
          { source: 'Referrals', applicants: 50, hires: 12, conversion: '24.0%', quality: '92%' },
          { source: 'Company Website', applicants: 80, hires: 8, conversion: '10.0%', quality: '80%' },
          { source: 'Job Fairs', applicants: 40, hires: 5, conversion: '12.5%', quality: '78%' }
        ],
        columns: [
          { header: 'Source', dataKey: 'source' },
          { header: 'Applicants', dataKey: 'applicants' },
          { header: 'Hires', dataKey: 'hires' },
          { header: 'Conversion', dataKey: 'conversion' },
          { header: 'Quality', dataKey: 'quality' }
        ],
        summary: {
          'Total Applicants': 490,
          'Total Hires': 58,
          'Overall Conversion Rate': '11.8%',
          'Best Source (Quality)': 'Referrals (92%)',
          'Best Source (Conversion)': 'Referrals (24.0%)'
        },
        filters: {
          'Time Period': 'Last 6 months',
          'Department': 'All'
        },
        executiveSummary: 'This report analyzes the effectiveness of your recruitment sources from the last 6 months. You received 490 applications and made 58 hires across all sources. Referrals provided the highest quality candidates with a score of 92%. Referrals had the best conversion rate at 24.0%. Consider allocating more resources to your best-performing sources to optimize recruitment efficiency.',
        recommendations: [
          'Allocate more resources to Referrals, which has the best combination of conversion rate (24.0%) and candidate quality (92%).',
          'Reconsider your investment in Indeed, which has a low conversion rate (9.0%) despite high volume.',
          'Diversify your recruitment sources to reach a wider pool of candidates.',
          'Track cost per hire for each source to optimize your recruitment budget.',
          'Regularly review the quality of hires from each source.'
        ],
        historicalData: [
          {
            period: 'Previous 6 months',
            data: [
              { source: 'LinkedIn', applicants: 100, hires: 12, conversion: '12.0%', quality: '82%' },
              { source: 'Indeed', applicants: 180, hires: 15, conversion: '8.3%', quality: '73%' },
              { source: 'Referrals', applicants: 40, hires: 10, conversion: '25.0%', quality: '90%' },
              { source: 'Company Website', applicants: 70, hires: 7, conversion: '10.0%', quality: '78%' },
              { source: 'Job Fairs', applicants: 35, hires: 4, conversion: '11.4%', quality: '75%' }
            ]
          }
        ],
        benchmarkData: [
          {
            name: 'Industry Average Conversion Rate',
            value: 10,
            description: 'Average conversion rate from application to hire across the recruitment industry.'
          },
          {
            name: 'Top Source Conversion Rate',
            value: 20,
            description: 'Conversion rate for the top-performing recruitment source across the industry.'
          }
        ]
      };
      break;

    case 'candidate_pipeline':
      reportData = {
        title: 'Candidate Pipeline Report',
        description: 'Analysis of candidate flow through recruitment pipeline',
        date: new Date(),
        companyName: 'Test Company',
        data: [
          { stage: 'New Applications', count: 150, age: 3, status: 'Active' },
          { stage: 'Resume Screening', count: 120, age: 5, status: 'Active' },
          { stage: 'Phone Interview', count: 80, age: 8, status: 'Active' },
          { stage: 'Technical Assessment', count: 60, age: 12, status: 'Active' },
          { stage: 'Onsite Interview', count: 40, age: 15, status: 'Active' },
          { stage: 'Offer Stage', count: 25, age: 18, status: 'Active' },
          { stage: 'Hired', count: 20, age: 0, status: 'Closed' }
        ],
        columns: [
          { header: 'Stage', dataKey: 'stage' },
          { header: 'Count', dataKey: 'count' },
          { header: 'Avg. Age (days)', dataKey: 'age' },
          { header: 'Status', dataKey: 'status' }
        ],
        summary: {
          'Total in Pipeline': 475,
          'Conversion Rate': '13.3%',
          'Avg. Time in Pipeline': '10.1 days'
        },
        filters: {
          'Time Period': 'Current',
          'Department': 'All'
        },
        executiveSummary: 'This report provides an analysis of your current candidate pipeline. You have 475 candidates at various stages, with a conversion rate of 13.3% from application to hire. The average time candidates spend in the pipeline is 10.1 days. The largest drop-off occurs between Phone Interview and Technical Assessment stages.',
        recommendations: [
          'Focus on improving the conversion from Phone Interview to Technical Assessment stages.',
          'Reduce the average age of candidates in the Onsite Interview stage (currently 15 days).',
          'Implement a more efficient screening process to move candidates through the pipeline faster.',
          'Set up automated reminders for candidates in the pipeline for more than 10 days.',
          'Consider implementing a fast-track process for high-potential candidates.'
        ],
        historicalData: [
          {
            period: 'Previous Month',
            data: [
              { stage: 'New Applications', count: 140, age: 3, status: 'Active' },
              { stage: 'Resume Screening', count: 110, age: 6, status: 'Active' },
              { stage: 'Phone Interview', count: 75, age: 9, status: 'Active' },
              { stage: 'Technical Assessment', count: 55, age: 13, status: 'Active' },
              { stage: 'Onsite Interview', count: 35, age: 16, status: 'Active' },
              { stage: 'Offer Stage', count: 22, age: 19, status: 'Active' },
              { stage: 'Hired', count: 18, age: 0, status: 'Closed' }
            ]
          }
        ],
        benchmarkData: [
          {
            name: 'Industry Average Pipeline Conversion',
            value: 12,
            description: 'Average percentage of candidates who progress from application to hire.'
          },
          {
            name: 'Industry Average Pipeline Age',
            value: 12,
            description: 'Average number of days candidates spend in the recruitment pipeline.'
          }
        ]
      };
      break;

    case 'skill_analysis':
      reportData = {
        title: 'Skill Analysis Report',
        description: 'Analysis of skills in candidate pool and job requirements',
        date: new Date(),
        companyName: 'Test Company',
        data: [
          { skill: 'JavaScript', demand: 85, supply: 70, gap: 15 },
          { skill: 'React', demand: 80, supply: 65, gap: 15 },
          { skill: 'Node.js', demand: 75, supply: 60, gap: 15 },
          { skill: 'Python', demand: 70, supply: 75, gap: -5 },
          { skill: 'Data Analysis', demand: 65, supply: 50, gap: 15 },
          { skill: 'Machine Learning', demand: 60, supply: 40, gap: 20 },
          { skill: 'UI/UX Design', demand: 55, supply: 45, gap: 10 },
          { skill: 'Project Management', demand: 50, supply: 60, gap: -10 }
        ],
        columns: [
          { header: 'Skill', dataKey: 'skill' },
          { header: 'Demand (%)', dataKey: 'demand' },
          { header: 'Supply (%)', dataKey: 'supply' },
          { header: 'Gap', dataKey: 'gap' }
        ],
        summary: {
          'Top Demand Skill': 'JavaScript (85%)',
          'Top Supply Skill': 'Python (75%)',
          'Largest Skill Gap': 'Machine Learning (20%)',
          'Skills in Surplus': 'Python, Project Management'
        },
        filters: {
          'Time Period': 'Last 3 months',
          'Department': 'All'
        },
        executiveSummary: 'This report analyzes the skills in your candidate pool compared to job requirements over the last 3 months. The largest skill gaps are in Machine Learning (20%), JavaScript (15%), React (15%), and Node.js (15%). You have a surplus of candidates with Python and Project Management skills. Consider focusing your recruitment efforts on candidates with Machine Learning and JavaScript skills.',
        recommendations: [
          'Focus recruitment efforts on candidates with Machine Learning skills, which has the largest gap at 20%.',
          'Consider training existing employees in JavaScript and React to address these skill gaps.',
          'Leverage the surplus of Python and Project Management skills for cross-training opportunities.',
          'Develop a skills development program to address the most common skill gaps.',
          'Partner with educational institutions to develop talent pipelines for high-demand skills.'
        ],
        historicalData: [
          {
            period: 'Previous Quarter',
            data: [
              { skill: 'JavaScript', demand: 80, supply: 65, gap: 15 },
              { skill: 'React', demand: 75, supply: 60, gap: 15 },
              { skill: 'Node.js', demand: 70, supply: 55, gap: 15 },
              { skill: 'Python', demand: 65, supply: 70, gap: -5 },
              { skill: 'Data Analysis', demand: 60, supply: 45, gap: 15 },
              { skill: 'Machine Learning', demand: 55, supply: 35, gap: 20 },
              { skill: 'UI/UX Design', demand: 50, supply: 40, gap: 10 },
              { skill: 'Project Management', demand: 45, supply: 55, gap: -10 }
            ]
          }
        ],
        benchmarkData: [
          {
            name: 'Industry Average Skill Gap',
            value: 12,
            description: 'Average percentage gap between skill demand and supply across the industry.'
          },
          {
            name: 'Top Industry Skill Gap',
            value: 18,
            description: 'Largest skill gap commonly seen across the industry.'
          }
        ]
      };
      break;

    case 'recruitment_funnel':
    default:
      reportData = {
        title: 'Recruitment Funnel Report',
        description: 'Detailed analysis of the recruitment pipeline stages',
        date: new Date(),
        companyName: 'Test Company',
        data: [
          { stage: 'Applications', count: 100, percentage: '100%' },
          { stage: 'Screening', count: 75, percentage: '75%' },
          { stage: 'Interview', count: 50, percentage: '50%' },
          { stage: 'Offer', count: 25, percentage: '25%' },
          { stage: 'Hire', count: 15, percentage: '15%' }
        ],
        columns: [
          { header: 'Stage', dataKey: 'stage' },
          { header: 'Count', dataKey: 'count' },
          { header: 'Percentage', dataKey: 'percentage' }
        ],
        summary: {
          'Total Applications': 100,
          'Total Hires': 15,
          'Conversion Rate': '15%'
        },
        filters: {
          'Time Period': 'Last 30 days',
          'Department': 'All'
        },
        executiveSummary: 'This report analyzes your recruitment funnel for the last 30 days. You received 100 applications and made 15 hires, resulting in an overall conversion rate of 15%. Your conversion rate is within the industry average range of 10-15%. Continue monitoring for opportunities to improve.',
        recommendations: [
          'Focus on improving the conversion from Interview to Offer, which has the lowest rate at 50%.',
          'Regularly review your recruitment process for bottlenecks and inefficiencies.',
          'Consider implementing structured interviews to improve candidate evaluation consistency.',
          'Track and analyze reasons for candidate dropoffs at each stage.',
          'Set up automated follow-ups to reduce candidate ghosting.'
        ],
        historicalData: [
          {
            period: 'Previous Month',
            data: [
              { stage: 'Applications', count: 90, percentage: '100%' },
              { stage: 'Screening', count: 65, percentage: '72%' },
              { stage: 'Interview', count: 40, percentage: '44%' },
              { stage: 'Offer', count: 20, percentage: '22%' },
              { stage: 'Hire', count: 12, percentage: '13%' }
            ]
          },
          {
            period: 'Two Months Ago',
            data: [
              { stage: 'Applications', count: 80, percentage: '100%' },
              { stage: 'Screening', count: 60, percentage: '75%' },
              { stage: 'Interview', count: 35, percentage: '44%' },
              { stage: 'Offer', count: 18, percentage: '23%' },
              { stage: 'Hire', count: 10, percentage: '13%' }
            ]
          }
        ],
        benchmarkData: [
          {
            name: 'Industry Average Conversion Rate',
            value: 12.5,
            description: 'Average conversion rate from application to hire across the recruitment industry.'
          },
          {
            name: 'Industry Average Time to Hire',
            value: 30,
            description: 'Average number of days from job posting to offer acceptance across the recruitment industry.'
          },
          {
            name: 'Top Quartile Conversion Rate',
            value: 18,
            description: 'Conversion rate achieved by the top 25% of companies in the recruitment industry.'
          }
        ]
      };
      break;
  }

  // Generate and download the PDF report
  downloadPdfReport(reportData, reportType);
};
