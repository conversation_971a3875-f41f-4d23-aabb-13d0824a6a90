import { sendEmail } from './emailService';
import { TemplateService } from './templateService';
import { env } from '@/lib/env';

/**
 * Candidate notification interfaces
 */
export interface CandidateNotificationData {
  candidateEmail: string;
  candidateName: string;
  jobTitle: string;
  companyName: string;
  applicationId?: string;
  jobId?: string;
}

export interface StatusUpdateData extends CandidateNotificationData {
  oldStatus: string;
  newStatus: string;
  message?: string;
  nextSteps?: string;
}

/**
 * Send application received confirmation email to candidate
 */
export const sendApplicationReceivedEmail = async (
  data: CandidateNotificationData
): Promise<void> => {
  try {
    if (!env.ENABLE_EMAIL_NOTIFICATIONS) {
      console.log('Email notifications disabled, skipping application received email');
      return;
    }

    // Try to use database template first, fallback to hardcoded template
    const templateResult = await TemplateService.renderApplicationReceived({
      companyName: data.companyName,
      candidateName: data.candidateName,
      jobTitle: data.jobTitle
    });

    const emailContent = templateResult || {
      subject: `Application Received: ${data.jobTitle} at ${data.companyName}`,
      content: generateApplicationReceivedHTML(data)
    };

    await sendEmail({
      to: data.candidateEmail,
      subject: emailContent.subject,
      html: emailContent.content,
      companyName: data.companyName
    });

    console.log(`✅ Application received email sent to: ${data.candidateEmail}`);
  } catch (error) {
    console.error('Failed to send application received email:', error);
    // Don't throw - notification failure shouldn't break the application process
  }
};

/**
 * Send status update email to candidate
 */
export const sendCandidateStatusUpdateEmail = async (
  data: StatusUpdateData
): Promise<void> => {
  try {
    console.log(`📧 Attempting to send status update email to: ${data.candidateEmail}`);
    console.log(`📧 Email notifications enabled: ${env.ENABLE_EMAIL_NOTIFICATIONS}`);

    if (!env.ENABLE_EMAIL_NOTIFICATIONS) {
      console.log('❌ Email notifications disabled, skipping status update email');
      return;
    }

    const subject = getStatusUpdateSubject(data.newStatus, data.jobTitle, data.companyName);

    console.log(`📧 Sending email with subject: ${subject}`);

    await sendEmail({
      to: data.candidateEmail,
      subject,
      html: generateStatusUpdateHTML(data),
      companyName: data.companyName
    });

    console.log(`✅ Status update email sent successfully to: ${data.candidateEmail} (${data.oldStatus} → ${data.newStatus})`);
  } catch (error) {
    console.error('❌ Failed to send status update email:', error);
    console.error('Error details:', error);
    // Don't throw - notification failure shouldn't break the status update process
  }
};

/**
 * Send rejection email to candidate
 */
export const sendRejectionEmail = async (
  data: CandidateNotificationData & { message?: string }
): Promise<void> => {
  try {
    console.log(`📧 Attempting to send rejection email to: ${data.candidateEmail}`);

    if (!env.ENABLE_EMAIL_NOTIFICATIONS) {
      console.log('❌ Email notifications disabled, skipping rejection email');
      return;
    }

    await sendEmail({
      to: data.candidateEmail,
      subject: `Application Update: ${data.jobTitle} at ${data.companyName}`,
      html: generateRejectionHTML(data)
    });

    console.log(`✅ Rejection email sent successfully to: ${data.candidateEmail}`);
  } catch (error) {
    console.error('❌ Failed to send rejection email:', error);
    console.error('Error details:', error);
  }
};

/**
 * Send offer email to candidate
 */
export const sendOfferEmail = async (
  data: CandidateNotificationData & { message?: string; nextSteps?: string }
): Promise<void> => {
  try {
    console.log(`📧 Attempting to send offer email to: ${data.candidateEmail}`);

    if (!env.ENABLE_EMAIL_NOTIFICATIONS) {
      console.log('❌ Email notifications disabled, skipping offer email');
      return;
    }

    await sendEmail({
      to: data.candidateEmail,
      subject: `Great News! Job Offer: ${data.jobTitle} at ${data.companyName}`,
      html: generateOfferHTML(data),
      companyName: data.companyName
    });

    console.log(`✅ Offer email sent successfully to: ${data.candidateEmail}`);
  } catch (error) {
    console.error('❌ Failed to send offer email:', error);
    console.error('Error details:', error);
  }
};

/**
 * Get appropriate subject line for status updates
 */
const getStatusUpdateSubject = (status: string, jobTitle: string, companyName: string): string => {
  const statusSubjects = {
    'screening': `Application Update: ${jobTitle} at ${companyName}`,
    'interview': `Interview Invitation: ${jobTitle} at ${companyName}`,
    'offer': `Great News! Job Offer: ${jobTitle} at ${companyName}`,
    'hired': `Congratulations! Welcome to ${companyName}`,
    'rejected': `Application Update: ${jobTitle} at ${companyName}`
  };

  return statusSubjects[status as keyof typeof statusSubjects] || 
         `Application Update: ${jobTitle} at ${companyName}`;
};

/**
 * Generate application received email HTML
 */
const generateApplicationReceivedHTML = (data: CandidateNotificationData): string => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #2563eb; margin: 0;">${data.companyName}</h1>
      </div>

      <h2 style="color: #2563eb; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
        Application Received
      </h2>

      <p style="font-size: 16px; line-height: 1.6;">Dear ${data.candidateName},</p>

      <p style="font-size: 16px; line-height: 1.6;">
        Thank you for your interest in the <strong>${data.jobTitle}</strong> position at <strong>${data.companyName}</strong>.
      </p>

      <p style="font-size: 16px; line-height: 1.6;">
        We have successfully received your application and our team will review it carefully.
        You can expect to hear from us within the next few business days regarding the next steps.
      </p>

      <div style="background: #f8fafc; border-left: 4px solid #2563eb; padding: 15px; margin: 20px 0;">
        <h3 style="color: #2563eb; margin: 0 0 10px 0;">What happens next?</h3>
        <ul style="margin: 0; padding-left: 20px;">
          <li>Our recruitment team will review your application</li>
          <li>If your profile matches our requirements, we'll contact you for the next steps</li>
          <li>You'll receive email updates about your application status</li>
        </ul>
      </div>

      <p style="font-size: 16px; line-height: 1.6;">
        If you have any questions about your application or the position, please don't hesitate to reach out.
      </p>

      <p style="font-size: 16px; line-height: 1.6;">
        Best regards,<br>
        <strong>The Recruitment Team</strong><br>
        ${data.companyName}
      </p>

      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${data.companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};

/**
 * Generate status update email HTML
 */
const generateStatusUpdateHTML = (data: StatusUpdateData): string => {
  const statusInfo = getStatusInfo(data.newStatus);

  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: ${statusInfo.color}; margin: 0;">${data.companyName}</h1>
      </div>

      <h2 style="color: ${statusInfo.color}; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
        ${statusInfo.title}
      </h2>

      <p style="font-size: 16px; line-height: 1.6;">Dear ${data.candidateName},</p>

      <p style="font-size: 16px; line-height: 1.6;">
        We have an update regarding your application for the <strong>${data.jobTitle}</strong> position at <strong>${data.companyName}</strong>.
      </p>

      <div style="background: ${statusInfo.bgColor}; border-left: 4px solid ${statusInfo.color}; padding: 15px; margin: 20px 0;">
        <h3 style="color: ${statusInfo.color}; margin: 0 0 10px 0;">Status Update</h3>
        <p style="margin: 0; font-size: 16px;">
          Your application status has been updated to: <strong>${statusInfo.displayName}</strong>
        </p>
      </div>

      ${data.message ? `
        <div style="background: #f8fafc; padding: 15px; margin: 20px 0; border-radius: 5px;">
          <h4 style="margin: 0 0 10px 0; color: #374151;">Message from the recruitment team:</h4>
          <p style="margin: 0; font-style: italic;">"${data.message}"</p>
        </div>
      ` : ''}

      ${data.nextSteps ? `
        <div style="background: #f0f9ff; border-left: 4px solid #2563eb; padding: 15px; margin: 20px 0;">
          <h3 style="color: #2563eb; margin: 0 0 10px 0;">Next Steps</h3>
          <p style="margin: 0;">${data.nextSteps}</p>
        </div>
      ` : ''}

      <p style="font-size: 16px; line-height: 1.6;">
        ${statusInfo.description}
      </p>

      <p style="font-size: 16px; line-height: 1.6;">
        Best regards,<br>
        <strong>The Recruitment Team</strong><br>
        ${data.companyName}
      </p>

      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${data.companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};

/**
 * Generate rejection email HTML
 */
const generateRejectionHTML = (data: CandidateNotificationData & { message?: string }): string => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #dc2626; margin: 0;">${data.companyName}</h1>
      </div>

      <h2 style="color: #dc2626; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
        Application Update
      </h2>

      <p style="font-size: 16px; line-height: 1.6;">Dear ${data.candidateName},</p>

      <p style="font-size: 16px; line-height: 1.6;">
        Thank you for your interest in the <strong>${data.jobTitle}</strong> position at <strong>${data.companyName}</strong>
        and for taking the time to apply.
      </p>

      <p style="font-size: 16px; line-height: 1.6;">
        After careful consideration, we have decided to move forward with other candidates whose experience
        more closely matches our current requirements.
      </p>

      ${data.message ? `
        <div style="background: #fef2f2; border-left: 4px solid #dc2626; padding: 15px; margin: 20px 0;">
          <h3 style="color: #dc2626; margin: 0 0 10px 0;">Feedback</h3>
          <p style="margin: 0;">${data.message}</p>
        </div>
      ` : ''}

      <p style="font-size: 16px; line-height: 1.6;">
        We encourage you to continue checking our career opportunities as we frequently post new positions
        that might be a better fit for your background and skills.
      </p>

      <p style="font-size: 16px; line-height: 1.6;">
        We appreciate your interest in joining our team and wish you the best in your job search.
      </p>

      <p style="font-size: 16px; line-height: 1.6;">
        Best regards,<br>
        <strong>The Recruitment Team</strong><br>
        ${data.companyName}
      </p>

      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${data.companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};

/**
 * Generate offer email HTML
 */
const generateOfferHTML = (data: CandidateNotificationData & { message?: string; nextSteps?: string }): string => {
  return `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: #ffffff; padding: 20px;">
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="color: #059669; margin: 0;">${data.companyName}</h1>
      </div>

      <h2 style="color: #059669; border-bottom: 2px solid #e5e7eb; padding-bottom: 10px;">
        🎉 Congratulations! Job Offer
      </h2>

      <p style="font-size: 16px; line-height: 1.6;">Dear ${data.candidateName},</p>

      <p style="font-size: 16px; line-height: 1.6;">
        We are delighted to extend an offer for the <strong>${data.jobTitle}</strong> position at <strong>${data.companyName}</strong>!
      </p>

      <div style="background: #f0fdf4; border-left: 4px solid #059669; padding: 15px; margin: 20px 0;">
        <h3 style="color: #059669; margin: 0 0 10px 0;">🎊 Great News!</h3>
        <p style="margin: 0; font-size: 16px;">
          After reviewing your application and interview performance, we believe you would be an excellent addition to our team.
        </p>
      </div>

      ${data.message ? `
        <div style="background: #f8fafc; padding: 15px; margin: 20px 0; border-radius: 5px;">
          <h4 style="margin: 0 0 10px 0; color: #374151;">Message from the hiring team:</h4>
          <p style="margin: 0; font-style: italic;">"${data.message}"</p>
        </div>
      ` : ''}

      ${data.nextSteps ? `
        <div style="background: #f0f9ff; border-left: 4px solid #2563eb; padding: 15px; margin: 20px 0;">
          <h3 style="color: #2563eb; margin: 0 0 10px 0;">Next Steps</h3>
          <p style="margin: 0;">${data.nextSteps}</p>
        </div>
      ` : `
        <div style="background: #f0f9ff; border-left: 4px solid #2563eb; padding: 15px; margin: 20px 0;">
          <h3 style="color: #2563eb; margin: 0 0 10px 0;">Next Steps</h3>
          <p style="margin: 0;">
            Our HR team will be in touch with you shortly with detailed offer information including
            compensation, benefits, and start date. Please keep an eye on your email and phone.
          </p>
        </div>
      `}

      <p style="font-size: 16px; line-height: 1.6;">
        We look forward to welcoming you to the team and are excited about the contributions you'll make to our organization.
      </p>

      <p style="font-size: 16px; line-height: 1.6;">
        Best regards,<br>
        <strong>The Recruitment Team</strong><br>
        ${data.companyName}
      </p>

      <div style="border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;">
        <p style="color: #6b7280; font-size: 12px; margin: 0;">
          This is an automated message from ${data.companyName}. Please do not reply to this email.
        </p>
      </div>
    </div>
  `;
};

/**
 * Get status information for email styling and content
 */
const getStatusInfo = (status: string) => {
  const statusMap = {
    'applied': {
      title: 'Application Received',
      displayName: 'Application Received',
      color: '#2563eb',
      bgColor: '#f0f9ff',
      description: 'Your application is being reviewed by our recruitment team.'
    },
    'screening': {
      title: 'Application Under Review',
      displayName: 'Under Review',
      color: '#d97706',
      bgColor: '#fffbeb',
      description: 'Your application has passed the initial screening and is being reviewed in detail.'
    },
    'interview': {
      title: 'Interview Stage',
      displayName: 'Interview Scheduled',
      color: '#7c3aed',
      bgColor: '#faf5ff',
      description: 'Congratulations! You have been selected for an interview. You should receive interview details separately.'
    },
    'offer': {
      title: 'Job Offer',
      displayName: 'Offer Extended',
      color: '#059669',
      bgColor: '#f0fdf4',
      description: 'Congratulations! We are pleased to extend a job offer. Our HR team will contact you with details.'
    },
    'hired': {
      title: 'Welcome to the Team!',
      displayName: 'Hired',
      color: '#059669',
      bgColor: '#f0fdf4',
      description: 'Welcome aboard! We are excited to have you join our team.'
    },
    'rejected': {
      title: 'Application Update',
      displayName: 'Not Selected',
      color: '#dc2626',
      bgColor: '#fef2f2',
      description: 'Thank you for your interest. While we have decided to move forward with other candidates, we encourage you to apply for future opportunities.'
    }
  };

  return statusMap[status as keyof typeof statusMap] || {
    title: 'Application Update',
    displayName: status.charAt(0).toUpperCase() + status.slice(1),
    color: '#6b7280',
    bgColor: '#f9fafb',
    description: 'Your application status has been updated.'
  };
};
