import React, { useState, useEffect } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/contexts/PermissionsContext';
import { useToast } from '@/hooks/use-toast';
import {
  Bot,
  Save,
  AlertCircle,
  Info,
  Check,
  ArrowLeft,
  TestTube
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useNavigate } from 'react-router-dom';
import { AI_PROVIDERS, AIProvider } from '@/types/aiSettings';
import {
  getActiveAISettings,
  upsertAISettings,
  testAISettings
} from '@/services/supabase/aiSettings';
import { clearAIConfigCache } from '@/lib/aiConfig';

const AIModelSettings = () => {
  const { user } = useAuth();
  const { hasRole } = usePermissions();
  const navigate = useNavigate();
  const { toast } = useToast();

  // State for form values
  const [selectedProvider, setSelectedProvider] = useState<AIProvider>('groq');
  const [selectedModel, setSelectedModel] = useState('llama-3.3-70b-versatile');
  const [apiKey, setApiKey] = useState('');
  const [hasExistingApiKey, setHasExistingApiKey] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const [isLoadingSettings, setIsLoadingSettings] = useState(true);
  const [isTesting, setIsTesting] = useState(false);

  // Load current settings on component mount
  useEffect(() => {
    loadCurrentSettings();
  }, []);

  const loadCurrentSettings = async (preferredProvider?: AIProvider) => {
    setIsLoadingSettings(true);
    try {
      // Check all providers for active settings
      const providers: AIProvider[] = ['groq', 'openai', 'anthropic', 'gemini'];
      const allSettings: Array<{ provider: AIProvider; data: any; updatedAt: string }> = [];

      for (const provider of providers) {
        const { data, error } = await getActiveAISettings(provider);
        if (data && !error && data.api_key !== 'PLACEHOLDER_API_KEY') {
          allSettings.push({ provider, data, updatedAt: data.updated_at });
        }
      }

      let selectedSettings = null;

      if (preferredProvider) {
        // If a preferred provider is specified, use it if it has active settings
        selectedSettings = allSettings.find(s => s.provider === preferredProvider);
      }

      if (!selectedSettings && allSettings.length > 0) {
        // Otherwise, use the most recently updated settings
        selectedSettings = allSettings.sort((a, b) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        )[0];
      }

      if (selectedSettings) {
        setSelectedProvider(selectedSettings.provider);
        setSelectedModel(selectedSettings.data.model);
        setApiKey(''); // Don't show the actual API key for security reasons
        setHasExistingApiKey(selectedSettings.data.api_key.length > 0);
      } else {
        // Set default values if no settings found
        setSelectedProvider('groq');
        setSelectedModel('llama-3.3-70b-versatile');
        setApiKey('');
        setHasExistingApiKey(false);
      }
    } catch (error) {
      console.error('Error loading AI settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to load current AI settings.',
        variant: 'destructive',
      });
    } finally {
      setIsLoadingSettings(false);
    }
  };

  // Handle provider change
  const handleProviderChange = async (value: string) => {
    const provider = value as AIProvider;
    setSelectedProvider(provider);

    try {
      // Check if there are existing settings for this provider
      const { data, error } = await getActiveAISettings(provider);

      if (data && !error && data.api_key !== 'PLACEHOLDER_API_KEY') {
        // Load existing settings for this provider
        setSelectedModel(data.model);
        setApiKey(''); // Don't show the actual API key for security reasons
        setHasExistingApiKey(data.api_key.length > 0);
      } else {
        // Set default values for this provider
        setSelectedModel(AI_PROVIDERS[provider].models[0].id);
        setApiKey('');
        setHasExistingApiKey(false);
      }
    } catch (error) {
      console.error('Error loading settings for provider:', provider, error);
      // Fallback to defaults
      setSelectedModel(AI_PROVIDERS[provider].models[0].id);
      setApiKey('');
      setHasExistingApiKey(false);
    }
  };

  // Handle test API connection
  const handleTestConnection = async () => {
    let keyToTest = apiKey.trim();

    // If no key entered but we have an existing key, get it from the database
    if (!keyToTest && hasExistingApiKey) {
      try {
        const { data } = await getActiveAISettings(selectedProvider);
        if (data && data.api_key !== 'PLACEHOLDER_API_KEY') {
          keyToTest = data.api_key;
        }
      } catch (error) {
        console.error('Error fetching existing API key for test:', error);
      }
    }

    if (!keyToTest) {
      toast({
        title: 'Error',
        description: 'Please enter an API key to test the connection.',
        variant: 'destructive',
      });
      return;
    }

    setIsTesting(true);
    try {
      const result = await testAISettings(selectedProvider, keyToTest, selectedModel);

      if (result.success) {
        toast({
          title: 'Connection successful',
          description: 'API key and model configuration are valid.',
        });
      } else {
        toast({
          title: 'Connection failed',
          description: result.error || 'Failed to validate API settings.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Test failed',
        description: 'An error occurred while testing the connection.',
        variant: 'destructive',
      });
    } finally {
      setIsTesting(false);
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!apiKey.trim() && !hasExistingApiKey) {
      toast({
        title: 'Error',
        description: 'Please enter an API key.',
        variant: 'destructive',
      });
      return;
    }

    setIsLoading(true);

    try {
      let keyToSave = apiKey.trim();

      // If no new key entered but we have an existing key, keep the existing one
      if (!keyToSave && hasExistingApiKey) {
        const { data: existingData } = await getActiveAISettings(selectedProvider);
        if (existingData && existingData.api_key !== 'PLACEHOLDER_API_KEY') {
          keyToSave = existingData.api_key;
        }
      }

      const { data, error } = await upsertAISettings(selectedProvider, {
        model: selectedModel,
        api_key: keyToSave,
        is_active: true,
        metadata: {
          description: `${AI_PROVIDERS[selectedProvider].name} configuration`,
          updated_via: 'admin_interface'
        }
      });

      if (error) {
        throw new Error(error);
      }

      // Clear the AI config cache to force reload of new settings
      clearAIConfigCache();

      toast({
        title: 'Settings saved',
        description: 'AI model settings have been updated successfully.',
      });

      setIsSaved(true);
      setTimeout(() => setIsSaved(false), 3000);

      // Reload settings to show updated data, preferring the provider we just saved
      await loadCurrentSettings(selectedProvider);
    } catch (error) {
      console.error('Error saving AI settings:', error);
      toast({
        title: 'Error',
        description: 'There was an error saving the settings. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Redirect if not an admin
  if (user && !hasRole('platform_admin')) {
    navigate('/dashboard');
    return null;
  }

  return (
    <DashboardLayout>
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => navigate('/dashboard/admin')}
              className="flex items-center gap-1"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Admin Panel
            </Button>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">AI Model Settings</h1>
        </div>

        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Important</AlertTitle>
          <AlertDescription>
            Changing the AI provider or model will affect all CV evaluations across the platform.
            Make sure to test the new configuration before using it in production.
          </AlertDescription>
        </Alert>

        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center gap-2">
              <Bot className="h-5 w-5 text-primary" />
              AI Provider Configuration
            </CardTitle>
            <CardDescription>
              Configure which AI provider and model to use for CV evaluations
            </CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingSettings ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                  <p className="text-sm text-gray-500">Loading current settings...</p>
                </div>
              </div>
            ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="provider">AI Provider</Label>
                  <Select
                    value={selectedProvider}
                    onValueChange={handleProviderChange}
                  >
                    <SelectTrigger id="provider">
                      <SelectValue placeholder="Select AI provider" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(AI_PROVIDERS).map(([key, provider]) => (
                        <SelectItem key={key} value={key}>
                          {provider.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-gray-500">
                    {AI_PROVIDERS[selectedProvider].description}
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="model">Model</Label>
                  <Select
                    value={selectedModel}
                    onValueChange={setSelectedModel}
                  >
                    <SelectTrigger id="model">
                      <SelectValue placeholder="Select model" />
                    </SelectTrigger>
                    <SelectContent>
                      {AI_PROVIDERS[selectedProvider].models.map((model) => (
                        <SelectItem key={model.id} value={model.id}>
                          {model.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="apiKey">API Key</Label>
                  <div className="flex gap-2">
                    <Input
                      id="apiKey"
                      type="password"
                      value={apiKey}
                      onChange={(e) => {
                        setApiKey(e.target.value);
                        // If user starts typing, we no longer have the existing key
                        if (e.target.value.length > 0) {
                          setHasExistingApiKey(false);
                        }
                      }}
                      placeholder={hasExistingApiKey ? '••••••••••••••••••••••' : 'Enter API key'}
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleTestConnection}
                      disabled={isTesting || (!apiKey.trim() && !hasExistingApiKey)}
                      className="px-3"
                    >
                      {isTesting ? (
                        <>Testing...</>
                      ) : (
                        <>
                          <TestTube className="h-4 w-4" />
                        </>
                      )}
                    </Button>
                  </div>
                  <div className="space-y-1">
                    {hasExistingApiKey && (
                      <p className="text-sm text-green-600 flex items-center gap-1">
                        <Check className="h-3 w-3" />
                        API key is configured and saved
                      </p>
                    )}
                    <p className="text-sm text-gray-500">
                      {selectedProvider === 'groq' && 'Get your API key from the GROQ console: https://console.groq.com/keys'}
                      {selectedProvider === 'anthropic' && 'Get your API key from the Anthropic console: https://console.anthropic.com/keys'}
                      {selectedProvider === 'openai' && 'Get your API key from the OpenAI dashboard: https://platform.openai.com/api-keys'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleTestConnection}
                  disabled={isTesting || !apiKey.trim()}
                >
                  {isTesting ? (
                    <>Testing...</>
                  ) : (
                    <>
                      <TestTube className="mr-2 h-4 w-4" />
                      Test Connection
                    </>
                  )}
                </Button>
                <Button
                  type="submit"
                  className="bg-primary hover:bg-primary/90"
                  disabled={isLoading || isLoadingSettings}
                >
                  {isLoading ? (
                    <>Saving...</>
                  ) : isSaved ? (
                    <>
                      <Check className="mr-2 h-4 w-4" />
                      Saved
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Settings
                    </>
                  )}
                </Button>
              </div>
            </form>
            )}
          </CardContent>
        </Card>

        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center gap-2">
              <Info className="h-5 w-5 text-blue-500" />
              About AI Providers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="groq">
              <TabsList className="grid grid-cols-4 mb-4">
                <TabsTrigger value="groq">GROQ</TabsTrigger>
                <TabsTrigger value="anthropic">Anthropic</TabsTrigger>
                <TabsTrigger value="openai">OpenAI</TabsTrigger>
                <TabsTrigger value="gemini">Gemini</TabsTrigger>
              </TabsList>

              <TabsContent value="groq" className="space-y-4">
                <h3 className="font-medium text-lg">GROQ API</h3>
                <p>GROQ provides high-performance inference for Llama models. It's optimized for speed and cost-effectiveness.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Recommended model: Llama 3.3 70B Versatile</li>
                  <li>Good balance of performance and cost</li>
                  <li>Excellent for CV parsing and job matching</li>
                </ul>
                <p className="text-sm text-gray-500 mt-4">
                  Learn more at <a href="https://groq.com" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">groq.com</a>
                </p>
              </TabsContent>

              <TabsContent value="anthropic" className="space-y-4">
                <h3 className="font-medium text-lg">Anthropic Claude</h3>
                <p>Anthropic's Claude models are known for their natural language understanding and safety features.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Recommended model: Claude 3 Opus for highest quality, Claude 3 Haiku for speed</li>
                  <li>Excellent at understanding complex instructions</li>
                  <li>Strong at extracting structured information from text</li>
                </ul>
                <p className="text-sm text-gray-500 mt-4">
                  Learn more at <a href="https://anthropic.com" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">anthropic.com</a>
                </p>
              </TabsContent>

              <TabsContent value="openai" className="space-y-4">
                <h3 className="font-medium text-lg">OpenAI GPT</h3>
                <p>OpenAI's GPT models are versatile and powerful for a wide range of language tasks.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Recommended model: GPT-4o for best performance, GPT-3.5 Turbo for cost efficiency</li>
                  <li>Excellent general-purpose capabilities</li>
                  <li>Good at understanding context and generating structured outputs</li>
                </ul>
                <p className="text-sm text-gray-500 mt-4">
                  Learn more at <a href="https://openai.com" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">openai.com</a>
                </p>
              </TabsContent>

              <TabsContent value="gemini" className="space-y-4">
                <h3 className="font-medium text-lg">Google Gemini</h3>
                <p>Google's Gemini models offer advanced multimodal AI capabilities with strong reasoning and structured output generation.</p>
                <ul className="list-disc pl-5 space-y-1">
                  <li>Recommended model: Gemini 2.5 Pro for best performance, Gemini 1.5 Flash for speed</li>
                  <li>Excellent at structured data extraction and JSON generation</li>
                  <li>Strong reasoning capabilities for complex evaluations</li>
                  <li>Native support for structured output schemas</li>
                </ul>
                <p className="text-sm text-gray-500 mt-4">
                  Learn more at <a href="https://ai.google.dev" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">ai.google.dev</a>
                </p>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default AIModelSettings;
