import React, { useEffect, useRef } from 'react';
import Typed from 'typed.js';

interface TypedHeadlineProps {
  strings: string[];
  className?: string;
  speed?: number;
  loop?: boolean;
  startDelay?: number;
}

export const TypedHeadline: React.FC<TypedHeadlineProps> = ({
  strings,
  className = "text-5xl font-bold text-white mb-6",
  speed = 50,
  loop = true,
  startDelay = 300
}) => {
  const el = useRef<HTMLSpanElement>(null);
  const typed = useRef<Typed | null>(null);

  useEffect(() => {
    if (el.current) {
      typed.current = new Typed(el.current, {
        strings,
        typeSpeed: speed,
        backSpeed: 30,
        loop,
        startDelay,
        backDelay: 1500,
        smartBackspace: true,
        showCursor: true,
        cursorChar: '|',
      });
    }

    return () => {
      if (typed.current) {
        typed.current.destroy();
      }
    };
  }, [strings, speed, loop, startDelay]);

  return (
    <div className={className}>
      <span ref={el} className="inline-block"></span>
    </div>
  );
};
