import { Interview } from '@/services/supabase/interviews';
import { InterviewParticipant } from '@/services/supabase/interview-participants';

export interface CalendarEvent {
  title: string;
  description: string;
  startDate: Date;
  endDate: Date;
  location?: string;
  attendees: string[];
  organizer: string;
}

/**
 * Generate ICS calendar file content
 */
export const generateICSContent = (event: CalendarEvent): string => {
  const formatDate = (date: Date): string => {
    return date.toISOString().replace(/[-:]/g, '').split('.')[0] + 'Z';
  };

  const escapeText = (text: string): string => {
    return text.replace(/[,;\\]/g, '\\$&').replace(/\n/g, '\\n');
  };

  const uid = `interview-${Date.now()}@expertrecruiter.com`;
  const timestamp = formatDate(new Date());

  let icsContent = [
    'BEGIN:VCALENDAR',
    'VERSION:2.0',
    'PRODID:-//Sourcio.ai//Interview Scheduler//EN',
    'CALSCALE:GREGORIAN',
    'METHOD:REQUEST',
    'BEGIN:VEVENT',
    `UID:${uid}`,
    `DTSTAMP:${timestamp}`,
    `DTSTART:${formatDate(event.startDate)}`,
    `DTEND:${formatDate(event.endDate)}`,
    `SUMMARY:${escapeText(event.title)}`,
    `DESCRIPTION:${escapeText(event.description)}`,
    `ORGANIZER:MAILTO:${event.organizer}`,
    'STATUS:CONFIRMED',
    'SEQUENCE:0'
  ];

  if (event.location) {
    icsContent.push(`LOCATION:${escapeText(event.location)}`);
  }

  // Add attendees
  event.attendees.forEach(email => {
    icsContent.push(`ATTENDEE;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE:MAILTO:${email}`);
  });

  icsContent.push('END:VEVENT');
  icsContent.push('END:VCALENDAR');

  return icsContent.join('\r\n');
};

/**
 * Create calendar event from interview data
 */
export const createInterviewCalendarEvent = (
  interview: Interview,
  participants: InterviewParticipant[],
  candidateEmail: string,
  organizerEmail: string
): CalendarEvent => {
  const startDate = new Date(interview.scheduled_at);
  const endDate = new Date(startDate.getTime() + interview.duration_minutes * 60000);

  const attendees = [
    candidateEmail,
    ...participants.filter(p => p.email).map(p => p.email!)
  ];

  let location = interview.location || '';
  if (interview.interview_type === 'video' && !location) {
    location = 'Video Call - Link will be provided';
  } else if (interview.interview_type === 'phone' && !location) {
    location = 'Phone Interview';
  }

  const description = [
    `Interview for: ${interview.job_title}`,
    `Candidate: ${interview.candidate_name}`,
    `Type: ${interview.interview_type.charAt(0).toUpperCase() + interview.interview_type.slice(1)}`,
    `Duration: ${interview.duration_minutes} minutes`,
    location && `Location: ${location}`,
    interview.notes && `Notes: ${interview.notes}`
  ].filter(Boolean).join('\n');

  return {
    title: `Interview: ${interview.candidate_name} - ${interview.job_title}`,
    description,
    startDate,
    endDate,
    location,
    attendees,
    organizer: organizerEmail
  };
};

/**
 * Generate ICS file for interview
 */
export const generateInterviewICS = (
  interview: Interview,
  participants: InterviewParticipant[],
  candidateEmail: string,
  organizerEmail: string
): string => {
  const event = createInterviewCalendarEvent(interview, participants, candidateEmail, organizerEmail);
  return generateICSContent(event);
};