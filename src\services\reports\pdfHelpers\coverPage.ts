import { jsPDF } from 'jspdf';
import { format } from 'date-fns';
import { ReportData, ReportType } from '../types';

/**
 * Helper function to add a cover page to the PDF report
 * @param doc The PDF document
 * @param reportData The report data
 * @param reportType The type of report
 */
export const addCoverPage = (doc: jsPDF, reportData: ReportData, reportType: ReportType): void => {
  // Create a new page for the cover
  doc.addPage();
  doc.setPage(1);

  // Set background color for the cover page
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;

  // Add a subtle background color
  doc.setFillColor(245, 247, 250);
  doc.rect(0, 0, pageWidth, pageHeight, 'F');

  // Add a decorative header bar
  doc.setFillColor(41, 128, 185);
  doc.rect(0, 0, pageWidth, 40, 'F');

  // Add report type label
  doc.setFontSize(12);
  doc.setTextColor(255, 255, 255);
  let reportTypeLabel = '';
  switch (reportType) {
    case 'recruitment_funnel':
      reportTypeLabel = 'RECRUITMENT FUNNEL ANALYSIS';
      break;
    case 'time_to_hire':
      reportTypeLabel = 'TIME TO HIRE ANALYSIS';
      break;
    case 'source_effectiveness':
      reportTypeLabel = 'SOURCE EFFECTIVENESS ANALYSIS';
      break;
    case 'candidate_pipeline':
      reportTypeLabel = 'CANDIDATE PIPELINE ANALYSIS';
      break;
    case 'skill_analysis':
      reportTypeLabel = 'SKILL ANALYSIS REPORT';
      break;
    case 'comprehensive':
      reportTypeLabel = 'COMPREHENSIVE RECRUITMENT ANALYTICS';
      break;
  }
  doc.text(reportTypeLabel, pageWidth / 2, 25, { align: 'center' });

  // Add company logo if available
  if (reportData.companyLogo) {
    try {
      // Determine image format from the data URL or use the provided format
      let format = 'PNG';
      if (typeof reportData.companyLogo === 'string' && reportData.companyLogo.startsWith('data:')) {
        const matches = reportData.companyLogo.match(/^data:image\/([a-zA-Z0-9]+);base64,/);
        if (matches && matches.length > 1) {
          format = matches[1].toUpperCase();
          // JPEG format in jsPDF is 'JPEG' not 'JPG'
          if (format === 'JPG') format = 'JPEG';
        }
      }

      // Calculate logo dimensions while maintaining aspect ratio
      const logoSize = 60;
      const logoX = pageWidth / 2 - logoSize / 2;
      const logoY = 60;

      // Add the image to the PDF
      doc.addImage(
        reportData.companyLogo,
        format,
        logoX,
        logoY,
        logoSize,
        logoSize,
        undefined,
        'FAST'
      );

      // Add a subtle border around the logo
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.5);
      doc.rect(logoX, logoY, logoSize, logoSize);
    } catch (error) {
      console.error('Error adding logo to PDF:', error);
      // Add a placeholder for the logo
      const logoSize = 60;
      const logoX = pageWidth / 2 - logoSize / 2;
      const logoY = 60;

      doc.setFillColor(245, 247, 250);
      doc.setDrawColor(200, 200, 200);
      doc.setLineWidth(0.5);
      doc.rect(logoX, logoY, logoSize, logoSize, 'FD');

      doc.setFontSize(10);
      doc.setTextColor(150);
      doc.text('Logo', pageWidth / 2, logoY + logoSize / 2, { align: 'center' });
    }
  }

  // Add report title
  doc.setFontSize(24);
  doc.setTextColor(41, 128, 185);
  const titleY = reportData.companyLogo ? 150 : 100;
  const titleLines = doc.splitTextToSize(reportData.title, pageWidth - 40);
  doc.text(titleLines, pageWidth / 2, titleY, { align: 'center' });

  // Add company name
  if (reportData.companyName) {
    doc.setFontSize(16);
    doc.setTextColor(100);
    doc.text(reportData.companyName, pageWidth / 2, titleY + titleLines.length * 10 + 15, { align: 'center' });
  }

  // Add date
  doc.setFontSize(12);
  doc.setTextColor(100);
  const dateY = titleY + titleLines.length * 10 + (reportData.companyName ? 30 : 15);
  doc.text(`Generated on: ${format(reportData.date, 'PPP')}`, pageWidth / 2, dateY, { align: 'center' });

  // Add a decorative footer
  doc.setFillColor(41, 128, 185);
  doc.rect(0, pageHeight - 20, pageWidth, 20, 'F');

  // Add footer text
  doc.setFontSize(10);
  doc.setTextColor(255, 255, 255);
  doc.text('Sourcio.ai Platform', pageWidth / 2, pageHeight - 10, { align: 'center' });
};
