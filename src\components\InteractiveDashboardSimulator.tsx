import React, { useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { FileText, Users, Building, BarChart2, Search, Plus, ChevronRight, Bell, Settings, User } from 'lucide-react';

// Dashboard Header Component
const DashboardHeader: React.FC = () => {
  return (
    <div className="bg-gray-900 p-4 rounded-t-xl border-b border-gray-700 flex justify-between items-center">
      <div className="flex items-center">
        <div className="bg-indigo-600 w-8 h-8 rounded flex items-center justify-center mr-3">
          <Users size={16} className="text-white" />
        </div>
        <h3 className="text-white font-medium">Sourcio.ai Dashboard</h3>
      </div>
      
      <div className="flex items-center space-x-4">
        <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center cursor-pointer hover:bg-gray-700">
          <Bell size={16} className="text-gray-400" />
        </div>
        <div className="w-8 h-8 rounded-full bg-gray-800 flex items-center justify-center cursor-pointer hover:bg-gray-700">
          <Settings size={16} className="text-gray-400" />
        </div>
        <div className="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center cursor-pointer">
          <User size={16} className="text-white" />
        </div>
      </div>
    </div>
  );
};

// Dashboard Sidebar Component
const DashboardSidebar: React.FC<{ activeTab: string; setActiveTab: (tab: string) => void }> = ({ 
  activeTab, 
  setActiveTab 
}) => {
  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: <BarChart2 size={18} /> },
    { id: 'candidates', label: 'Candidates', icon: <Users size={18} /> },
    { id: 'companies', label: 'Companies', icon: <Building size={18} /> },
    { id: 'evaluations', label: 'Evaluations', icon: <FileText size={18} /> },
  ];
  
  return (
    <div className="bg-gray-900 p-4 w-56 border-r border-gray-700 h-full">
      {tabs.map((tab) => (
        <div 
          key={tab.id}
          className={`flex items-center p-3 rounded-lg cursor-pointer mb-2 ${
            activeTab === tab.id 
              ? 'bg-indigo-600 text-white' 
              : 'text-gray-400 hover:bg-gray-800'
          }`}
          onClick={() => setActiveTab(tab.id)}
        >
          <div className="mr-3">{tab.icon}</div>
          <span>{tab.label}</span>
        </div>
      ))}
    </div>
  );
};

// Dashboard Content Component
const DashboardContent: React.FC<{ activeTab: string }> = ({ activeTab }) => {
  // Placeholder content for different tabs
  const renderContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return (
          <div className="p-6">
            <h2 className="text-xl font-bold text-white mb-4">Dashboard Overview</h2>
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="bg-gray-800 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">Active Candidates</span>
                  <Users size={18} className="text-indigo-400" />
                </div>
                <div className="text-2xl font-bold text-white">128</div>
                <div className="text-xs text-green-400">+12% from last month</div>
              </div>
              <div className="bg-gray-800 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">Companies</span>
                  <Building size={18} className="text-blue-400" />
                </div>
                <div className="text-2xl font-bold text-white">24</div>
                <div className="text-xs text-green-400">+3 new this month</div>
              </div>
              <div className="bg-gray-800 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-gray-400">Evaluations</span>
                  <FileText size={18} className="text-purple-400" />
                </div>
                <div className="text-2xl font-bold text-white">342</div>
                <div className="text-xs text-green-400">+28% from last month</div>
              </div>
            </div>
            
            <div className="bg-gray-800 p-4 rounded-lg mb-6">
              <h3 className="text-white font-medium mb-3">Recent Activity</h3>
              <div className="space-y-3">
                <div className="flex items-center p-2 rounded hover:bg-gray-700">
                  <div className="w-8 h-8 rounded-full bg-blue-500/20 flex items-center justify-center mr-3">
                    <FileText size={14} className="text-blue-400" />
                  </div>
                  <div>
                    <div className="text-sm text-white">New CV uploaded: John Smith</div>
                    <div className="text-xs text-gray-400">2 minutes ago</div>
                  </div>
                </div>
                <div className="flex items-center p-2 rounded hover:bg-gray-700">
                  <div className="w-8 h-8 rounded-full bg-green-500/20 flex items-center justify-center mr-3">
                    <Building size={14} className="text-green-400" />
                  </div>
                  <div>
                    <div className="text-sm text-white">New company added: TechCorp Inc.</div>
                    <div className="text-xs text-gray-400">1 hour ago</div>
                  </div>
                </div>
                <div className="flex items-center p-2 rounded hover:bg-gray-700">
                  <div className="w-8 h-8 rounded-full bg-purple-500/20 flex items-center justify-center mr-3">
                    <Users size={14} className="text-purple-400" />
                  </div>
                  <div>
                    <div className="text-sm text-white">Candidate matched: Sarah Johnson</div>
                    <div className="text-xs text-gray-400">3 hours ago</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      case 'candidates':
        return (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-white">Candidates</h2>
              <div className="flex space-x-3">
                <div className="relative">
                  <input 
                    type="text" 
                    placeholder="Search candidates..." 
                    className="bg-gray-800 text-gray-300 pl-9 pr-4 py-2 rounded-lg focus:outline-none focus:ring-1 focus:ring-indigo-500 w-64"
                  />
                  <Search size={16} className="text-gray-400 absolute left-3 top-2.5" />
                </div>
                <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center">
                  <Plus size={16} className="mr-2" />
                  Add Candidate
                </button>
              </div>
            </div>
            
            <div className="bg-gray-800 rounded-lg overflow-hidden">
              <table className="w-full">
                <thead>
                  <tr className="bg-gray-900">
                    <th className="text-left p-4 text-gray-400 font-medium">Name</th>
                    <th className="text-left p-4 text-gray-400 font-medium">Position</th>
                    <th className="text-left p-4 text-gray-400 font-medium">Skills</th>
                    <th className="text-left p-4 text-gray-400 font-medium">Match Score</th>
                    <th className="text-left p-4 text-gray-400 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-t border-gray-700 hover:bg-gray-700">
                    <td className="p-4">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center mr-3">
                          <span className="text-white text-sm font-medium">JS</span>
                        </div>
                        <span className="text-white">John Smith</span>
                      </div>
                    </td>
                    <td className="p-4 text-gray-300">Senior Developer</td>
                    <td className="p-4">
                      <div className="flex space-x-1">
                        <span className="px-2 py-1 bg-blue-500/20 text-blue-400 rounded text-xs">React</span>
                        <span className="px-2 py-1 bg-green-500/20 text-green-400 rounded text-xs">Node.js</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-700 h-2 rounded-full mr-2">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                        </div>
                        <span className="text-green-400">85%</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <button className="text-indigo-400 hover:text-indigo-300">
                        View Profile
                      </button>
                    </td>
                  </tr>
                  <tr className="border-t border-gray-700 hover:bg-gray-700">
                    <td className="p-4">
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                          <span className="text-white text-sm font-medium">SJ</span>
                        </div>
                        <span className="text-white">Sarah Johnson</span>
                      </div>
                    </td>
                    <td className="p-4 text-gray-300">UX Designer</td>
                    <td className="p-4">
                      <div className="flex space-x-1">
                        <span className="px-2 py-1 bg-purple-500/20 text-purple-400 rounded text-xs">Figma</span>
                        <span className="px-2 py-1 bg-yellow-500/20 text-yellow-400 rounded text-xs">UI/UX</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <div className="flex items-center">
                        <div className="w-16 bg-gray-700 h-2 rounded-full mr-2">
                          <div className="bg-green-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                        </div>
                        <span className="text-green-400">92%</span>
                      </div>
                    </td>
                    <td className="p-4">
                      <button className="text-indigo-400 hover:text-indigo-300">
                        View Profile
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        );
      case 'companies':
        return (
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-white">Companies</h2>
              <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center">
                <Plus size={16} className="mr-2" />
                Add Company
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-800 p-4 rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center mr-3">
                    <span className="text-white font-bold">T</span>
                  </div>
                  <div>
                    <h3 className="text-white font-medium">TechCorp Inc.</h3>
                    <p className="text-gray-400 text-sm">Technology</p>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-400">Open Positions</span>
                    <span className="text-white">4</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Candidates Matched</span>
                    <span className="text-white">12</span>
                  </div>
                </div>
                <button className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center">
                  View Company <ChevronRight size={14} className="ml-1" />
                </button>
              </div>
              
              <div className="bg-gray-800 p-4 rounded-lg">
                <div className="flex items-center mb-4">
                  <div className="w-12 h-12 rounded-full bg-green-500 flex items-center justify-center mr-3">
                    <span className="text-white font-bold">I</span>
                  </div>
                  <div>
                    <h3 className="text-white font-medium">InnovateSoft</h3>
                    <p className="text-gray-400 text-sm">Software Development</p>
                  </div>
                </div>
                <div className="mb-4">
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-gray-400">Open Positions</span>
                    <span className="text-white">6</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-400">Candidates Matched</span>
                    <span className="text-white">18</span>
                  </div>
                </div>
                <button className="text-indigo-400 hover:text-indigo-300 text-sm flex items-center">
                  View Company <ChevronRight size={14} className="ml-1" />
                </button>
              </div>
            </div>
          </div>
        );
      case 'evaluations':
        return (
          <div className="p-6">
            <h2 className="text-xl font-bold text-white mb-6">Evaluations</h2>
            
            <div className="bg-gray-800 p-4 rounded-lg mb-6">
              <h3 className="text-white font-medium mb-4">Recent Evaluations</h3>
              
              <div className="space-y-4">
                <div className="bg-gray-700 p-3 rounded-lg">
                  <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center mr-3">
                        <span className="text-white text-sm font-medium">JS</span>
                      </div>
                      <div>
                        <div className="text-white">John Smith</div>
                        <div className="text-xs text-gray-400">Senior Developer</div>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-500 flex items-center justify-center mr-2">
                        <span className="text-white text-sm font-medium">T</span>
                      </div>
                      <div className="text-right">
                        <div className="text-white">TechCorp Inc.</div>
                        <div className="text-xs text-gray-400">Frontend Lead</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400 text-sm">Match Score</span>
                    <span className="text-green-400 font-medium">85%</span>
                  </div>
                  
                  <div className="w-full bg-gray-600 h-2 rounded-full">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
                  </div>
                  
                  <div className="flex justify-end mt-3">
                    <button className="text-indigo-400 hover:text-indigo-300 text-sm">
                      View Details
                    </button>
                  </div>
                </div>
                
                <div className="bg-gray-700 p-3 rounded-lg">
                  <div className="flex justify-between items-center mb-3">
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-purple-500 flex items-center justify-center mr-3">
                        <span className="text-white text-sm font-medium">SJ</span>
                      </div>
                      <div>
                        <div className="text-white">Sarah Johnson</div>
                        <div className="text-xs text-gray-400">UX Designer</div>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-green-500 flex items-center justify-center mr-2">
                        <span className="text-white text-sm font-medium">I</span>
                      </div>
                      <div className="text-right">
                        <div className="text-white">InnovateSoft</div>
                        <div className="text-xs text-gray-400">Senior Designer</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-gray-400 text-sm">Match Score</span>
                    <span className="text-green-400 font-medium">92%</span>
                  </div>
                  
                  <div className="w-full bg-gray-600 h-2 rounded-full">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '92%' }}></div>
                  </div>
                  
                  <div className="flex justify-end mt-3">
                    <button className="text-indigo-400 hover:text-indigo-300 text-sm">
                      View Details
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        );
      default:
        return null;
    }
  };
  
  return (
    <div className="flex-1 overflow-auto">
      {renderContent()}
    </div>
  );
};

// Tooltip Component
const Tooltip: React.FC<{ 
  isVisible: boolean; 
  text: string; 
  position: { top: number; left: number }; 
  onNext: () => void;
  step: number;
  totalSteps: number;
}> = ({ isVisible, text, position, onNext, step, totalSteps }) => {
  if (!isVisible) return null;
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      className="absolute z-50 bg-indigo-600 text-white p-4 rounded-lg shadow-lg max-w-xs"
      style={{ top: position.top, left: position.left }}
    >
      <div className="mb-3">{text}</div>
      <div className="flex justify-between items-center">
        <div className="text-xs text-indigo-200">
          Step {step} of {totalSteps}
        </div>
        <button 
          onClick={onNext}
          className="bg-white text-indigo-600 px-3 py-1 rounded text-sm font-medium"
        >
          Next
        </button>
      </div>
    </motion.div>
  );
};

// Main Component
export const InteractiveDashboardSimulator: React.FC = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;
  
  // Tooltip positions for each step
  const tooltipPositions = [
    { top: 80, left: 100 },  // Dashboard overview
    { top: 150, left: 300 }, // Candidates tab
    { top: 200, left: 400 }, // Companies tab
    { top: 250, left: 200 }, // Evaluations tab
  ];
  
  // Tooltip text for each step
  const tooltipTexts = [
    "Welcome to the Sourcio.ai Dashboard! Here you can see an overview of your recruitment activities.",
    "Click on the Candidates tab to view and manage all your candidates.",
    "The Companies tab lets you manage client companies and their job requirements.",
    "In the Evaluations tab, you can see how candidates match with different companies and positions."
  ];
  
  const handleNextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
      
      // Change active tab based on the step
      if (currentStep === 1) setActiveTab('candidates');
      if (currentStep === 2) setActiveTab('companies');
      if (currentStep === 3) setActiveTab('evaluations');
    } else {
      // Reset to first step
      setCurrentStep(1);
      setActiveTab('dashboard');
    }
  };
  
  return (
    <section className="py-16 bg-recruiter-darknavy">
      <div className="container mx-auto px-6">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-white mb-4">Test Drive the Platform</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Experience how our platform works with this interactive dashboard simulator
          </p>
        </div>
        
        <div className="max-w-5xl mx-auto relative">
          {/* Dashboard Simulator */}
          <div className="bg-gray-800 rounded-xl overflow-hidden border border-gray-700 shadow-2xl">
            <DashboardHeader />
            
            <div className="flex h-[500px]">
              <DashboardSidebar activeTab={activeTab} setActiveTab={setActiveTab} />
              <DashboardContent activeTab={activeTab} />
            </div>
          </div>
          
          {/* Interactive Tooltip */}
          <Tooltip 
            isVisible={true}
            text={tooltipTexts[currentStep - 1]}
            position={tooltipPositions[currentStep - 1]}
            onNext={handleNextStep}
            step={currentStep}
            totalSteps={totalSteps}
          />
        </div>
      </div>
    </section>
  );
};
