import React, { useRef, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { useGSAP } from '@gsap/react';
import gsap from 'gsap';
import { Link } from 'react-router-dom';
import { ArrowRight } from 'lucide-react';
import { HeroScene3D } from './HeroScene3D';

// Floating CV Component
const FloatingCV = ({ delay = 0 }) => {
  const cvRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (cvRef.current) {
      gsap.fromTo(
        cvRef.current,
        {
          y: '+=20',
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );

      // Floating animation
      gsap.to(cvRef.current, {
        y: '+=10',
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
    }
  }, [delay]);

  return (
    <div
      ref={cvRef}
      className="absolute w-48 h-64 bg-white rounded-lg shadow-xl p-4 flex flex-col"
      style={{ opacity: 0 }}
    >
      <div className="w-full h-4 bg-blue-500 rounded mb-3"></div>
      <div className="w-3/4 h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-1/2 h-3 bg-gray-200 rounded mb-4"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded mb-3"></div>
      <div className="w-1/2 h-6 bg-indigo-500 rounded mb-3"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-2/3 h-2 bg-gray-200 rounded"></div>
      <div className="mt-auto flex justify-between">
        <div className="w-8 h-8 rounded-full bg-green-500"></div>
        <div className="w-12 h-4 bg-blue-500 rounded"></div>
      </div>
    </div>
  );
};

// Floating Company Profile Component
const FloatingCompany = ({ delay = 0 }) => {
  const companyRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (companyRef.current) {
      gsap.fromTo(
        companyRef.current,
        {
          y: '+=20',
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );

      // Floating animation
      gsap.to(companyRef.current, {
        y: '+=10',
        duration: 2.5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: 0.5,
      });
    }
  }, [delay]);

  return (
    <div
      ref={companyRef}
      className="absolute w-56 h-72 bg-white rounded-lg shadow-xl p-4 flex flex-col"
      style={{ opacity: 0 }}
    >
      <div className="flex items-center mb-4">
        <div className="w-12 h-12 rounded-full bg-purple-500 mr-3"></div>
        <div>
          <div className="w-32 h-4 bg-gray-800 rounded mb-1"></div>
          <div className="w-24 h-3 bg-gray-400 rounded"></div>
        </div>
      </div>
      <div className="w-full h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-full h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-3/4 h-3 bg-gray-200 rounded mb-4"></div>

      <div className="grid grid-cols-2 gap-2 mb-4">
        <div className="h-8 bg-blue-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-blue-500 rounded"></div>
        </div>
        <div className="h-8 bg-green-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-green-500 rounded"></div>
        </div>
        <div className="h-8 bg-purple-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-purple-500 rounded"></div>
        </div>
        <div className="h-8 bg-yellow-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-yellow-500 rounded"></div>
        </div>
      </div>

      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded"></div>

      <div className="mt-auto flex justify-end">
        <div className="w-20 h-6 bg-indigo-500 rounded"></div>
      </div>
    </div>
  );
};

// Floating Match Result Component
const FloatingMatch = ({ delay = 0 }) => {
  const matchRef = useRef<HTMLDivElement>(null);

  useGSAP(() => {
    if (matchRef.current) {
      gsap.fromTo(
        matchRef.current,
        {
          y: '+=20',
          opacity: 0,
        },
        {
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );

      // Floating animation
      gsap.to(matchRef.current, {
        y: '+=10',
        duration: 2.2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: 0.3,
      });

      // Animate the progress bar
      gsap.fromTo(
        matchRef.current.querySelector('.progress-fill'),
        { width: '0%' },
        {
          width: '85%',
          duration: 1.5,
          delay: delay + 0.5,
          ease: 'power2.out',
        }
      );
    }
  }, [delay]);

  return (
    <div
      ref={matchRef}
      className="absolute w-64 h-48 bg-white rounded-lg shadow-xl p-4 flex flex-col"
      style={{ opacity: 0 }}
    >
      <div className="flex justify-between items-center mb-4">
        <div className="w-32 h-5 bg-gray-800 rounded"></div>
        <div className="w-12 h-12 rounded-full bg-indigo-500 flex items-center justify-center text-white font-bold">
          85%
        </div>
      </div>

      <div className="w-full h-4 bg-gray-200 rounded-full mb-4 overflow-hidden">
        <div className="progress-fill h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full" style={{ width: '0%' }}></div>
      </div>

      <div className="grid grid-cols-2 gap-2 mb-3">
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
      </div>

      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded"></div>

      <div className="mt-auto flex justify-end">
        <div className="w-24 h-6 bg-green-500 rounded"></div>
      </div>
    </div>
  );
};

// Main ParallaxHero Component
export const ParallaxHero: React.FC = () => {
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], ["0%", "50%"]);
  const opacity = useTransform(scrollYProgress, [0, 0.5], [1, 0]);

  // Particle animation effect
  useEffect(() => {
    if (!containerRef.current) return;

    const createParticle = () => {
      const particle = document.createElement('div');
      particle.className = 'absolute rounded-full bg-white opacity-0';

      // Random size between 2px and 6px
      const size = Math.random() * 4 + 2;
      particle.style.width = `${size}px`;
      particle.style.height = `${size}px`;

      // Random position within the hero section
      const posX = Math.random() * 100;
      const posY = Math.random() * 100;
      particle.style.left = `${posX}%`;
      particle.style.top = `${posY}%`;

      // Add to DOM
      containerRef.current?.appendChild(particle);

      // Animate with CSS
      particle.animate(
        [
          { opacity: 0, transform: 'translateY(0)' },
          { opacity: 0.8, transform: 'translateY(-20px)' },
          { opacity: 0, transform: 'translateY(-40px)' },
        ],
        {
          duration: 3000 + Math.random() * 2000,
          easing: 'ease-out',
        }
      );

      // Remove after animation
      setTimeout(() => {
        particle.remove();
      }, 5000);
    };

    // Create particles at intervals
    const interval = setInterval(() => {
      for (let i = 0; i < 3; i++) {
        createParticle();
      }
    }, 500);

    return () => clearInterval(interval);
  }, []);

  return (
    <div
      ref={containerRef}
      className="relative h-screen overflow-hidden bg-gradient-to-b from-recruiter-navy to-recruiter-darknavy"
    >
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-[300px] -left-[300px] w-[600px] h-[600px] rounded-full bg-blue-500/10 blur-3xl"></div>
        <div className="absolute -bottom-[200px] -right-[200px] w-[500px] h-[500px] rounded-full bg-purple-500/10 blur-3xl"></div>
        <motion.div
          className="absolute top-1/4 right-1/4 w-[300px] h-[300px] rounded-full bg-indigo-500/5 blur-xl"
          animate={{
            x: [0, 30, 0],
            y: [0, 15, 0],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        ></motion.div>
      </div>

      {/* 3D Canvas */}
      <HeroScene3D />

      {/* Floating UI Elements */}
      <div className="absolute inset-0 z-10 pointer-events-none">
        <div className="relative w-full h-full">
          <div className="absolute top-[20%] left-[15%]">
            <FloatingCV delay={0.5} />
          </div>
          <div className="absolute top-[30%] right-[15%]">
            <FloatingCompany delay={0.8} />
          </div>
          <div className="absolute bottom-[25%] left-[25%]">
            <FloatingMatch delay={1.1} />
          </div>
        </div>
      </div>

      {/* Content */}
      <motion.div
        className="relative z-20 container mx-auto px-6 h-full flex flex-col justify-center items-center text-center"
        style={{ y, opacity }}
      >
        <motion.h1
          className="text-5xl md:text-6xl font-bold text-white mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          AI-Powered CV Evaluation<br />Platform for Agencies
        </motion.h1>

        <motion.p
          className="text-xl text-gray-300 mb-8 max-w-3xl"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          Upload CVs, match against company requirements, and deliver better candidates to your clients.
          Our intelligent system helps agencies evaluate talent more effectively.
        </motion.p>

        <motion.div
          className="flex flex-col sm:flex-row items-center justify-center gap-4"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="w-full sm:w-auto"
          >
            <Link
              to="/signup"
              className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white py-4 px-8 rounded-lg flex items-center justify-center transition-all duration-300 w-full shadow-lg shadow-blue-500/20"
            >
              Start Evaluating Candidates <ArrowRight size={16} className="ml-2" />
            </Link>
          </motion.div>

          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="w-full sm:w-auto"
          >
            <Link
              to="/login"
              className="bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white py-4 px-8 rounded-lg flex items-center justify-center transition-colors w-full"
            >
              Sign In
            </Link>
          </motion.div>
        </motion.div>

        <motion.div
          className="absolute bottom-10 left-1/2 transform -translate-x-1/2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <div className="w-8 h-12 border-2 border-white/30 rounded-full flex justify-center">
            <motion.div
              className="w-1.5 h-3 bg-white rounded-full mt-2"
              animate={{
                y: [0, 6, 0],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};
