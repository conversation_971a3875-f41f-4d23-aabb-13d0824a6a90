import { ParsedCV } from '@/services/cv-processing/cvParser';

/**
 * Calculate total years of experience from work experience array
 */
export const calculateTotalExperience = (workExperience: any[]): number => {
  if (!workExperience || workExperience.length === 0) {
    console.log('📊 Experience Calculation: No work experience found');
    return 0;
  }

  console.log('📊 Experience Calculation: Processing work experience:', workExperience);

  const totalYears = workExperience.reduce((total: number, job: any, index: number) => {
    try {
      const startYear = job.startDate ? new Date(job.startDate).getFullYear() : 0;
      const endYear = job.endDate && job.endDate.toLowerCase() !== 'present' 
        ? new Date(job.endDate).getFullYear() 
        : new Date().getFullYear();
      
      const years = endYear - startYear;
      const validYears = years > 0 ? years : 0;
      
      console.log(`📊 Job ${index + 1}: ${job.title || 'Unknown'} at ${job.company || 'Unknown'}`);
      console.log(`   Start: ${job.startDate} (${startYear}) | End: ${job.endDate} (${endYear})`);
      console.log(`   Duration: ${validYears} years`);
      
      return total + validYears;
    } catch (error) {
      console.warn('❌ Error calculating experience for job:', job, error);
      return total;
    }
  }, 0);

  console.log(`📊 Total Experience Calculated: ${totalYears} years`);
  return totalYears;
};

/**
 * Extract minimum experience requirement from job description
 */
export const extractMinimumExperience = (jobDescription: string): number | null => {
  if (!jobDescription) {
    console.log('📋 Experience Requirement: No job description provided');
    return null;
  }

  const text = jobDescription.toLowerCase();
  console.log('📋 Experience Requirement: Analyzing job description for minimum experience...');
  
  // Common patterns for experience requirements
  const patterns = [
    /(\d+)\+?\s*years?\s*of\s*experience/,
    /minimum\s*(\d+)\s*years?/,
    /at\s*least\s*(\d+)\s*years?/,
    /(\d+)-\d+\s*years?\s*experience/,
    /(\d+)\s*to\s*\d+\s*years?\s*experience/,
    /(\d+)\s*years?\s*minimum/,
    /experience:\s*(\d+)\+?\s*years?/
  ];

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      const years = parseInt(match[1], 10);
      if (years > 0 && years <= 20) { // Reasonable range
        console.log(`📋 Experience Requirement Found: ${years} years (pattern: ${pattern})`);
        return years;
      }
    }
  }

  console.log('📋 Experience Requirement: No minimum experience requirement found in job description');
  return null;
};

