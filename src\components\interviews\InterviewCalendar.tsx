import React, { useState } from 'react';
import { Calendar } from '@/components/ui/calendar';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Interview } from '@/services/supabase/interviews';
import { format, isSameDay } from 'date-fns';
import { cn } from '@/lib/utils';
import { formatTimeInTimezone, getTimezoneByValue } from '@/lib/timezones';
import { CalendarClock } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface InterviewCalendarProps {
  interviews: Interview[];
  onDateSelect: (date: Date) => void;
  onInterviewSelect: (interview: Interview) => void;
}

const InterviewCalendar: React.FC<InterviewCalendarProps> = ({
  interviews,
  onDateSelect,
  onInterviewSelect
}) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());

  // Handle date selection
  const handleDateSelect = (date: Date | undefined) => {
    if (date) {
      setSelectedDate(date);
      onDateSelect(date);
    }
  };

  // Get interviews for the selected date
  const interviewsForSelectedDate = selectedDate
    ? interviews.filter(interview => 
        isSameDay(new Date(interview.scheduled_at), selectedDate)
      )
    : [];

  // Function to determine if a date has interviews
  const hasInterviews = (date: Date) => {
    return interviews.some(interview => 
      isSameDay(new Date(interview.scheduled_at), date)
    );
  };

  // Custom day render function to show dots for dates with interviews
  const renderDay = (day: Date) => {
    const hasInterviewsOnDay = hasInterviews(day);
    
    return (
      <div className="relative">
        <div>{day.getDate()}</div>
        {hasInterviewsOnDay && (
          <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-primary rounded-full" />
        )}
      </div>
    );
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      <Card className="md:col-span-1">
        <CardHeader>
          <CardTitle>Calendar</CardTitle>
        </CardHeader>
        <CardContent>
          <Calendar
            mode="single"
            selected={selectedDate}
            onSelect={handleDateSelect}
            className="rounded-md border"
            components={{
              Day: ({ date, ...props }) => (
                <div
                  {...props}
                  className={cn(
                    props.className,
                    hasInterviews(date) && 'font-bold relative'
                  )}
                >
                  {renderDay(date)}
                </div>
              ),
            }}
          />
        </CardContent>
      </Card>

      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>
            {selectedDate ? format(selectedDate, 'MMMM d, yyyy') : 'No date selected'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {interviewsForSelectedDate.length > 0 ? (
            <div className="space-y-4">
              {interviewsForSelectedDate.map(interview => (
                <div
                  key={interview.id}
                  className="p-4 border rounded-md hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => onInterviewSelect(interview)}
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{interview.candidate_name}</h3>
                      <p className="text-sm text-gray-500">{interview.job_title}</p>
                    </div>
                    <Badge
                      variant={
                        interview.status === 'scheduled' ? 'default' :
                        interview.status === 'completed' ? 'secondary' :
                        interview.status === 'cancelled' ? 'destructive' : 'outline'
                      }
                    >
                      {interview.status.charAt(0).toUpperCase() + interview.status.slice(1)}
                    </Badge>
                  </div>
                  <div className="mt-2 flex items-center text-sm">
                    <span className="text-gray-700">
                      {format(new Date(interview.scheduled_at), 'h:mm a')}
                      {interview.timezone && (
                        <span className="text-gray-500 ml-1">
                          ({getTimezoneByValue(interview.timezone)?.label.split(' ')[0] || interview.timezone})
                        </span>
                      )} •
                      {interview.duration_minutes} min •
                      {interview.interview_type.charAt(0).toUpperCase() + interview.interview_type.slice(1)}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <CalendarClock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800">
                {interviews.length === 0 ? 'No interviews scheduled yet' : 'No interviews for this date'}
              </h3>
              <p className="text-gray-500 mt-1 mb-4">
                {interviews.length === 0 
                  ? 'Start by scheduling your first interview with a candidate.'
                  : 'Select a different date or schedule a new interview.'
                }
              </p>
              <Button onClick={() => window.location.href = '/dashboard/interviews'}>
                <CalendarClock className="h-4 w-4 mr-2" />
                Schedule Interview
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default InterviewCalendar;

