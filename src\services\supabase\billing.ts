import { supabase } from '@/lib/supabase';

/**
 * Billing information interface
 */
export interface BillingInfo {
  id: string;
  user_id: string;
  company_name: string | null;
  address_line1: string;
  address_line2: string | null;
  city: string;
  state: string;
  postal_code: string;
  country: string;
  tax_id: string | null;
  billing_email: string;
  phone: string | null;
  created_at: string;
  updated_at: string;
}

/**
 * Get billing information for a user
 */
export const getBillingInfo = async (userId: string): Promise<BillingInfo | null> => {
  try {
    const { data, error } = await supabase
      .from('billing_info')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      // If no billing info found, return null
      if (error.code === 'PGRST116') {
        return null;
      }
      console.error('Error fetching billing info:', error);
      throw error;
    }

    return data;
  } catch (error) {
    console.error('Error in getBillingInfo:', error);
    return null;
  }
};

/**
 * Create or update billing information
 */
export const updateBillingInfo = async (
  userId: string,
  billingData: Omit<BillingInfo, 'id' | 'user_id' | 'created_at' | 'updated_at'>
): Promise<BillingInfo | null> => {
  try {
    // Check if billing info already exists
    const existingInfo = await getBillingInfo(userId);

    if (existingInfo) {
      // Update existing billing info
      const { data, error } = await supabase
        .from('billing_info')
        .update({
          ...billingData,
          updated_at: new Date().toISOString(),
        })
        .eq('user_id', userId)
        .select()
        .single();

      if (error) {
        console.error('Error updating billing info:', error);
        throw error;
      }

      return data;
    } else {
      // Create new billing info
      const { data, error } = await supabase
        .from('billing_info')
        .insert({
          user_id: userId,
          ...billingData,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating billing info:', error);
        throw error;
      }

      return data;
    }
  } catch (error) {
    console.error('Error in updateBillingInfo:', error);
    throw error;
  }
};

/**
 * Delete billing information
 */
export const deleteBillingInfo = async (userId: string): Promise<boolean> => {
  try {
    const { error } = await supabase
      .from('billing_info')
      .delete()
      .eq('user_id', userId);

    if (error) {
      console.error('Error deleting billing info:', error);
      throw error;
    }

    return true;
  } catch (error) {
    console.error('Error in deleteBillingInfo:', error);
    throw error;
  }
};


