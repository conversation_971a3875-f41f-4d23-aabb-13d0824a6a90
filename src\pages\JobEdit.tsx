import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import {
  Save,
  X,
  Plus,
  Trash2,
  Calendar,
  DollarSign,
  MapPin,
  Briefcase,
  Building,
  Clock,
  ArrowLeft,
  Loader2
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useJob, useUpdateJob } from '@/hooks/use-jobs';
import { useCompanyContext } from '@/contexts/CompanyContext';

// Mock job data (same as in JobDetails.tsx)
const mockJob = {
  id: '1',
  title: 'Frontend Developer',
  department: 'Engineering',
  location: 'San Francisco, CA',
  locationType: 'hybrid',
  type: 'full-time',
  experience: '3-5 years',
  salary: {
    min: '90000',
    max: '120000',
    currency: 'USD',
    period: 'yearly',
    showSalary: true
  },
  description: 'We are looking for a skilled Frontend Developer to join our engineering team. You will be responsible for building user interfaces for our web applications, ensuring they are responsive, accessible, and provide an excellent user experience.',
  requirements: [
    'Bachelor\'s degree in Computer Science or related field',
    '3-5 years of experience in frontend development',
    'Proficiency in JavaScript, TypeScript, React, and modern CSS',
    'Experience with responsive design and cross-browser compatibility',
    'Knowledge of frontend build tools and testing frameworks',
    'Strong problem-solving skills and attention to detail'
  ],
  responsibilities: [
    'Develop user interfaces using React and TypeScript',
    'Collaborate with designers to implement UI/UX designs',
    'Write clean, maintainable, and efficient code',
    'Optimize applications for maximum speed and scalability',
    'Participate in code reviews and contribute to team standards',
    'Stay up-to-date with emerging trends and technologies'
  ],
  benefits: [
    'Competitive salary and equity package',
    'Health, dental, and vision insurance',
    'Flexible work schedule and remote options',
    '401(k) matching',
    'Professional development budget',
    'Generous vacation policy'
  ],
  applicationDeadline: '2023-12-15',
  postedDate: '2023-09-15',
  status: 'active',
  applicants: 18,
  views: 245
};

// Job type interface adapted for real data structure
interface JobFormData {
  title: string;
  department?: string;
  location: string;
  locationType?: string;
  job_type: string;
  experience_level: string;
  salary_min?: string | number | null;
  salary_max?: string | number | null;
  salary_currency?: string;
  salary_period?: string;
  show_salary?: boolean;
  description: string;
  requirements: string | string[];
  responsibilities?: string | string[];
  benefits?: string | string[];
  closing_date?: string;
  status: 'draft' | 'active' | 'closed';
  company_id: string;
}

const JobEdit = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const { activeCompanyId } = useCompanyContext();

  // Fetch the real job data using the useJob hook
  const { data: job, isLoading, error } = useJob(id || '');
  const { mutate: updateJob } = useUpdateJob();

  // Initialize form state
  const [formData, setFormData] = useState<JobFormData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Update form data when job is loaded
  useEffect(() => {
    if (job) {
      // Parse requirements, responsibilities, and benefits if they're stored as JSON strings
      let parsedRequirements = job.requirements;
      let parsedResponsibilities = job.responsibilities;
      let parsedBenefits = job.benefits;

      try {
        if (typeof job.requirements === 'string') {
          const parsed = JSON.parse(job.requirements);
          if (parsed.text) {
            parsedRequirements = parsed.text;
          }
        }
      } catch (e) {
        // If parsing fails, keep the original string
        console.log('Failed to parse requirements JSON, using as string');
      }

      try {
        if (typeof job.responsibilities === 'string') {
          parsedResponsibilities = JSON.parse(job.responsibilities);
        }
      } catch (e) {
        // If parsing fails, keep the original string
        console.log('Failed to parse responsibilities JSON, using as string');
      }

      try {
        if (typeof job.benefits === 'string') {
          parsedBenefits = JSON.parse(job.benefits);
        }
      } catch (e) {
        // If parsing fails, keep the original string
        console.log('Failed to parse benefits JSON, using as string');
      }

      // Convert string requirements to array if needed
      if (typeof parsedRequirements === 'string') {
        parsedRequirements = parsedRequirements.split('\n').filter(item => item.trim() !== '');
      }

      // Convert string responsibilities to array if needed
      if (typeof parsedResponsibilities === 'string') {
        parsedResponsibilities = parsedResponsibilities.split('\n').filter(item => item.trim() !== '');
      }

      // Convert string benefits to array if needed
      if (typeof parsedBenefits === 'string') {
        parsedBenefits = parsedBenefits.split('\n').filter(item => item.trim() !== '');
      }

      // Set form data with the job data
      setFormData({
        title: job.title,
        department: job.department || '',
        location: job.location,
        locationType: job.location_type || 'onsite',
        job_type: job.job_type,
        experience_level: job.experience_level,
        salary_min: job.salary_min || '',
        salary_max: job.salary_max || '',
        salary_currency: job.salary_currency || 'USD',
        salary_period: job.salary_period || 'yearly',
        show_salary: job.show_salary || false,
        description: job.description,
        requirements: parsedRequirements || [],
        responsibilities: parsedResponsibilities || [],
        benefits: parsedBenefits || [],
        closing_date: job.closing_date || '',
        status: job.status as 'draft' | 'active' | 'closed',
        company_id: job.company_id
      });
    }
  }, [job]);

  // Handle input change for simple fields
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    if (!formData) return;

    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle select change
  const handleSelectChange = (value: string, name: string) => {
    if (!formData) return;

    setFormData({
      ...formData,
      [name]: value
    });
  };

  // Handle checkbox change
  const handleCheckboxChange = (checked: boolean, name: string) => {
    if (!formData) return;

    setFormData({
      ...formData,
      [name]: checked
    });
  };

  // Handle array fields (requirements, responsibilities, benefits)
  const handleArrayItemChange = (index: number, value: string, field: 'requirements' | 'responsibilities' | 'benefits') => {
    if (!formData) return;

    // Ensure the field is an array
    const currentArray = Array.isArray(formData[field])
      ? formData[field] as string[]
      : [(formData[field] || '').toString()];

    const newArray = [...currentArray];
    newArray[index] = value;

    setFormData({
      ...formData,
      [field]: newArray
    });
  };

  // Add new item to array fields
  const addArrayItem = (field: 'requirements' | 'responsibilities' | 'benefits') => {
    if (!formData) return;

    // Ensure the field is an array
    const currentArray = Array.isArray(formData[field])
      ? formData[field] as string[]
      : [(formData[field] || '').toString()];

    setFormData({
      ...formData,
      [field]: [...currentArray, '']
    });
  };

  // Remove item from array fields
  const removeArrayItem = (index: number, field: 'requirements' | 'responsibilities' | 'benefits') => {
    if (!formData) return;

    // Ensure the field is an array
    const currentArray = Array.isArray(formData[field])
      ? formData[field] as string[]
      : [(formData[field] || '').toString()];

    if (currentArray.length > 1) {
      const newArray = [...currentArray];
      newArray.splice(index, 1);

      setFormData({
        ...formData,
        [field]: newArray
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData || !id) return;

    setIsSubmitting(true);

    try {
      // Prepare the data for submission - only include fields that exist in the database
      const jobUpdate = {
        title: formData.title,
        description: formData.description,
        location: formData.locationType === 'remote'
          ? `Remote${formData.location ? ` - ${formData.location}` : ''}`
          : formData.location,
        job_type: formData.job_type,
        experience_level: formData.experience_level,
        status: formData.status,
        company_id: formData.company_id,
      };

      // Add optional fields if they exist
      // Note: Based on the database schema, only these fields exist in the jobs table
      if (formData.salary_min) jobUpdate['salary_min'] = formData.salary_min;
      if (formData.salary_max) jobUpdate['salary_max'] = formData.salary_max;

      // Handle requirements field - this is the only array field that exists in the database
      if (formData.requirements) {
        // If requirements is an array, convert it to a string
        if (Array.isArray(formData.requirements)) {
          jobUpdate['requirements'] = formData.requirements.join('\n');
        } else {
          jobUpdate['requirements'] = formData.requirements;
        }
      }

      // Note: responsibilities and benefits fields don't exist in the database schema
      // We're storing them in the requirements field as part of the job description

      // Call the updateJob mutation
      updateJob(
        {
          id,
          job: jobUpdate
        },
        {
          onSuccess: () => {
            toast({
              title: 'Job updated',
              description: 'Your job posting has been updated successfully.',
            });

            // Navigate back to job details
            navigate(`/dashboard/jobs/${id}`);
          },
          onError: (error) => {
            toast({
              title: 'Error',
              description: 'There was an error updating the job. Please try again.',
              variant: 'destructive',
            });
            console.error('Error updating job:', error);
          },
          onSettled: () => {
            setIsSubmitting(false);
          }
        }
      );
    } catch (error) {
      toast({
        title: 'Error',
        description: 'There was an error updating the job. Please try again.',
        variant: 'destructive',
      });
      console.error('Error preparing job update:', error);
      setIsSubmitting(false);
    }
  };

  // Handle cancel
  const handleCancel = () => {
    navigate(`/dashboard/jobs/${id}`);
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="icon"
              className="border-gray-200 text-gray-700 hover:bg-gray-100"
              onClick={() => navigate(`/dashboard/jobs/${id}`)}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold text-gray-800">Edit Job</h1>
          </div>

          {!isLoading && formData && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                className="border-gray-200 text-gray-700 hover:bg-gray-100"
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                <X className="mr-2 h-4 w-4" /> Cancel
              </Button>
              <Button
                className="bg-recruiter-lightblue hover:bg-blue-500"
                onClick={handleSubmit}
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <div className="flex items-center">
                    <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                    Saving...
                  </div>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" /> Save Changes
                  </>
                )}
              </Button>
            </div>
          )}
        </div>

        {/* Loading state */}
        {isLoading && (
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardContent className="p-6 flex justify-center items-center min-h-[200px]">
              <div className="flex flex-col items-center gap-2">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
                <p className="text-gray-500">Loading job details...</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Error state */}
        {error && !isLoading && (
          <Card className="bg-white border-gray-200 shadow-sm">
            <CardContent className="p-6">
              <div className="text-center">
                <h3 className="text-lg font-medium text-red-500 mb-2">Error Loading Job</h3>
                <p className="text-gray-600">We couldn't load the job details. The job may have been deleted or you don't have permission to edit it.</p>
                <Button
                  className="mt-4"
                  onClick={() => navigate('/dashboard/jobs')}
                >
                  Return to Job Listings
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {!isLoading && !error && formData && (
          <form onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 gap-6">
            {/* Basic Information */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Basic Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="title" className="text-gray-700">Job Title <span className="text-red-500">*</span></Label>
                  <Input
                    id="title"
                    name="title"
                    value={formData.title}
                    onChange={handleInputChange}
                    placeholder="e.g. Frontend Developer"
                    className="bg-white border-gray-200 text-gray-800"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="department" className="text-gray-700">Department</Label>
                    <Input
                      id="department"
                      name="department"
                      value={formData.department}
                      onChange={handleInputChange}
                      placeholder="e.g. Engineering"
                      className="bg-white border-gray-200 text-gray-800"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="location" className="text-gray-700">
                      Location
                      {formData.locationType === 'remote' && (
                        <span className="text-sm text-gray-500 ml-2">(Optional for remote jobs)</span>
                      )}
                    </Label>
                    <div className="flex gap-2">
                      <div className="flex-1">
                        <Input
                          id="location"
                          name="location"
                          value={formData.location}
                          onChange={handleInputChange}
                          placeholder={
                            formData.locationType === 'remote'
                              ? "e.g. Preferred time zone or 'Worldwide'"
                              : "e.g. New York, NY"
                          }
                          className="bg-white border-gray-200 text-gray-800"
                        />
                        {formData.locationType === 'remote' && (
                          <p className="text-xs text-gray-500 mt-1">
                            For remote jobs, you can specify time zone preferences or leave blank for worldwide
                          </p>
                        )}
                      </div>
                      <Select
                        value={formData.locationType || 'onsite'}
                        onValueChange={(value) => handleSelectChange(value, 'locationType')}
                      >
                        <SelectTrigger className="w-[140px] bg-white border-gray-200 text-gray-800">
                          <SelectValue placeholder="Location Type" />
                        </SelectTrigger>
                        <SelectContent className="bg-white border-gray-200 text-gray-800">
                          <SelectItem value="onsite">On-site</SelectItem>
                          <SelectItem value="remote">Remote</SelectItem>
                          <SelectItem value="hybrid">Hybrid</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="type" className="text-gray-700">Employment Type</Label>
                    <Select
                      value={formData.job_type}
                      onValueChange={(value) => handleSelectChange(value, 'job_type')}
                    >
                      <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                        <SelectValue placeholder="Employment Type" />
                      </SelectTrigger>
                      <SelectContent className="bg-white border-gray-200 text-gray-800">
                        <SelectItem value="full-time">Full-time</SelectItem>
                        <SelectItem value="part-time">Part-time</SelectItem>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="internship">Internship</SelectItem>
                        <SelectItem value="temporary">Temporary</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="experience" className="text-gray-700">Experience Level</Label>
                    <Input
                      id="experience_level"
                      name="experience_level"
                      value={formData.experience_level}
                      onChange={handleInputChange}
                      placeholder="e.g. 3-5 years"
                      className="bg-white border-gray-200 text-gray-800"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label className="text-gray-700">Salary Range</Label>
                  <div className="flex items-center gap-2 mb-2">
                    <Checkbox
                      id="show_salary"
                      checked={formData.show_salary || false}
                      onCheckedChange={(checked) =>
                        handleCheckboxChange(checked as boolean, 'show_salary')
                      }
                    />
                    <Label htmlFor="show_salary" className="text-gray-600 text-sm">
                      Display salary range in job posting
                    </Label>
                  </div>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <Select
                        value={formData.salary_currency || 'USD'}
                        onValueChange={(value) => handleSelectChange(value, 'salary_currency')}
                      >
                        <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                          <SelectValue placeholder="Currency" />
                        </SelectTrigger>
                        <SelectContent className="bg-white border-gray-200 text-gray-800">
                          <SelectItem value="USD">USD ($)</SelectItem>
                          <SelectItem value="EUR">EUR (€)</SelectItem>
                          <SelectItem value="GBP">GBP (£)</SelectItem>
                          <SelectItem value="CAD">CAD ($)</SelectItem>
                          <SelectItem value="AUD">AUD ($)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Input
                        id="salary_min"
                        name="salary_min"
                        value={formData.salary_min || ''}
                        onChange={handleInputChange}
                        placeholder="Min"
                        className="bg-white border-gray-200 text-gray-800"
                        type="number"
                        min="0"
                      />
                    </div>
                    <div>
                      <Input
                        id="salary_max"
                        name="salary_max"
                        value={formData.salary_max || ''}
                        onChange={handleInputChange}
                        placeholder="Max"
                        className="bg-white border-gray-200 text-gray-800"
                        type="number"
                        min="0"
                      />
                    </div>
                    <div>
                      <Select
                        value={formData.salary_period || 'yearly'}
                        onValueChange={(value) => handleSelectChange(value, 'salary_period')}
                      >
                        <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                          <SelectValue placeholder="Period" />
                        </SelectTrigger>
                        <SelectContent className="bg-white border-gray-200 text-gray-800">
                          <SelectItem value="yearly">Per Year</SelectItem>
                          <SelectItem value="monthly">Per Month</SelectItem>
                          <SelectItem value="hourly">Per Hour</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="closing_date" className="text-gray-700">Application Deadline</Label>
                  <Input
                    id="closing_date"
                    name="closing_date"
                    value={formData.closing_date || ''}
                    onChange={handleInputChange}
                    type="date"
                    className="bg-white border-gray-200 text-gray-800"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="status" className="text-gray-700">Job Status</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value: 'draft' | 'active' | 'closed') => handleSelectChange(value, 'status')}
                  >
                    <SelectTrigger className="bg-white border-gray-200 text-gray-800">
                      <SelectValue placeholder="Status" />
                    </SelectTrigger>
                    <SelectContent className="bg-white border-gray-200 text-gray-800">
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="closed">Closed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            {/* Job Description */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Job Description</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="description" className="text-gray-700">Description <span className="text-red-500">*</span></Label>
                  <Textarea
                    id="description"
                    name="description"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="Provide a detailed description of the job..."
                    className="bg-white border-gray-200 text-gray-800 min-h-[150px]"
                    required
                  />
                </div>
              </CardContent>
            </Card>

            {/* Requirements */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Requirements</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.requirements.map((requirement, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={requirement}
                      onChange={(e) => handleArrayItemChange(index, e.target.value, 'requirements')}
                      placeholder={`Requirement ${index + 1}`}
                      className="bg-white border-gray-200 text-gray-800 flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => removeArrayItem(index, 'requirements')}
                      disabled={formData.requirements.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  className="border-gray-200 text-gray-700 hover:bg-gray-100"
                  onClick={() => addArrayItem('requirements')}
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Requirement
                </Button>
              </CardContent>
            </Card>

            {/* Responsibilities */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Responsibilities</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.responsibilities.map((responsibility, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={responsibility}
                      onChange={(e) => handleArrayItemChange(index, e.target.value, 'responsibilities')}
                      placeholder={`Responsibility ${index + 1}`}
                      className="bg-white border-gray-200 text-gray-800 flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => removeArrayItem(index, 'responsibilities')}
                      disabled={formData.responsibilities.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  className="border-gray-200 text-gray-700 hover:bg-gray-100"
                  onClick={() => addArrayItem('responsibilities')}
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Responsibility
                </Button>
              </CardContent>
            </Card>

            {/* Benefits */}
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardHeader>
                <CardTitle className="text-gray-800 text-lg">Benefits</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {formData.benefits.map((benefit, index) => (
                  <div key={index} className="flex gap-2">
                    <Input
                      value={benefit}
                      onChange={(e) => handleArrayItemChange(index, e.target.value, 'benefits')}
                      placeholder={`Benefit ${index + 1}`}
                      className="bg-white border-gray-200 text-gray-800 flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="icon"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => removeArrayItem(index, 'benefits')}
                      disabled={formData.benefits.length <= 1}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
                <Button
                  type="button"
                  variant="outline"
                  className="border-gray-200 text-gray-700 hover:bg-gray-100"
                  onClick={() => addArrayItem('benefits')}
                >
                  <Plus className="mr-2 h-4 w-4" /> Add Benefit
                </Button>
              </CardContent>
            </Card>
          </div>
          </form>
        )}
      </div>
    </DashboardLayout>
  );
};

export default JobEdit;
