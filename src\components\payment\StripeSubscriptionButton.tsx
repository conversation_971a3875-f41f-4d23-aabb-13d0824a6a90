import React, { useState } from 'react';
import { useStripeScriptContext } from './StripeScriptProvider';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface StripeSubscriptionButtonProps {
  priceId: string;
  onSuccess?: (details: any) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
  className?: string;
  children?: React.ReactNode;
  userId?: string;
  tier?: string;
}

const StripeSubscriptionButton: React.FC<StripeSubscriptionButtonProps> = ({
  priceId,
  onSuccess,
  onError,
  onCancel,
  className = '',
  children,
  userId,
  tier,
}) => {
  const { isLoaded, failedToLoad, reload, stripe } = useStripeScriptContext();
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const handleSubscribe = async () => {
    if (!stripe) {
      const error = new Error('Stripe is not loaded');
      if (onError) onError(error);
      toast({
        title: 'Error',
        description: 'Stripe is not loaded. Please try again.',
        variant: 'destructive',
      });
      return;
    }

    if (!priceId) {
      const error = new Error('Price ID is required');
      if (onError) onError(error);
      toast({
        title: 'Error',
        description: 'Subscription plan not configured.',
        variant: 'destructive',
      });
      return;
    }

    setIsProcessing(true);

    try {
      // Create checkout session via our edge function
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/stripe-checkout`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          priceId,
          userId,
          tier,
          successUrl: `${window.location.origin}/dashboard?subscription=success`,
          cancelUrl: `${window.location.origin}/dashboard/pricing?subscription=cancelled`,
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      const { error } = await stripe.redirectToCheckout({
        sessionId: data.sessionId,
      });

      if (error) {
        throw new Error(error.message);
      }

      // If we reach here, the redirect was successful
      if (onSuccess) {
        onSuccess({
          sessionId: data.sessionId,
          status: 'REDIRECTED',
          priceId,
        });
      }
    } catch (error) {
      console.error('Error creating Stripe checkout session:', error);
      
      if (onError) onError(error);
      
      toast({
        title: 'Subscription error',
        description: 'There was a problem processing your subscription. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Show loading state while Stripe is loading
  if (!isLoaded && !failedToLoad) {
    return (
      <Button disabled className={`w-full ${className}`}>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        Loading Stripe...
      </Button>
    );
  }

  // Show error state if Stripe failed to load
  if (failedToLoad) {
    return (
      <Button onClick={reload} variant="outline" className={`w-full ${className}`}>
        Retry Loading Stripe
      </Button>
    );
  }

  // Show processing state
  if (isProcessing) {
    return (
      <Button disabled className={`w-full ${className}`}>
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        Processing...
      </Button>
    );
  }

  return (
    <Button 
      onClick={handleSubscribe}
      className={`w-full bg-[#635bff] hover:bg-[#5a54e6] text-white ${className}`}
    >
      {children || 'Subscribe with Stripe'}
    </Button>
  );
};

export default StripeSubscriptionButton;
