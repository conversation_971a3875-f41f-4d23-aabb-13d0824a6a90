import React, { useState } from 'react';
import { AlertCircle, CheckCircle2 } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/ui/use-toast';
import { useAuth } from '@/contexts/AuthContext';

interface EmailVerificationReminderProps {
  email: string;
  onVerified?: () => void;
}

/**
 * A component that displays a reminder to verify email and allows resending the verification email
 */
export function EmailVerificationReminder({ email, onVerified }: EmailVerificationReminderProps) {
  const { toast } = useToast();
  const { resendVerificationEmail, checkEmailVerification } = useAuth();
  const [isResending, setIsResending] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [resendSuccess, setResendSuccess] = useState(false);
  const [resendCount, setResendCount] = useState(0);

  // Handle resend verification email
  const handleResendVerification = async () => {
    if (isResending) return;

    setIsResending(true);
    try {
      await resendVerificationEmail(email);
      setResendSuccess(true);
      setResendCount(prev => prev + 1);

      toast({
        title: 'Verification email sent',
        description: `We've sent a verification email to ${email}. Please check your inbox.`,
      });

      // Reset success message after 5 seconds
      setTimeout(() => {
        setResendSuccess(false);
      }, 5000);
    } catch (error) {
      toast({
        title: 'Failed to send verification email',
        description: error instanceof Error ? error.message : 'Please try again later',
        variant: 'destructive',
      });
    } finally {
      setIsResending(false);
    }
  };

  // Handle check verification status
  const handleCheckVerification = async () => {
    if (isChecking) return;

    setIsChecking(true);
    try {
      const isVerified = await checkEmailVerification(email);

      if (isVerified) {
        toast({
          title: 'Email verified',
          description: 'Your email has been verified successfully.',
        });
        onVerified?.();
      } else {
        toast({
          title: 'Email not verified',
          description: 'Please check your inbox and click the verification link.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      toast({
        title: 'Failed to check verification status',
        description: error instanceof Error ? error.message : 'Please try again later',
        variant: 'destructive',
      });
    } finally {
      setIsChecking(false);
    }
  };

  return (
    <Alert variant="destructive" className="mb-6 w-full">
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>Email verification required</AlertTitle>
      <AlertDescription className="mt-2 w-full">
        <p className="mb-3">
          We've sent a verification email to <strong className="break-all">{email}</strong>.
          Please check your inbox and click the verification link to activate your account.
        </p>

        {resendSuccess && (
          <Alert variant="success" className="mb-3">
            <CheckCircle2 className="h-4 w-4" />
            <AlertTitle>Verification email sent</AlertTitle>
            <AlertDescription>
              Please check your inbox and spam folder.
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 mt-3 w-full">
          <Button
            variant="outline"
            onClick={handleResendVerification}
            disabled={isResending || isChecking || resendCount >= 3}
            className="w-full text-xs sm:text-sm px-2 sm:px-4"
            size="sm"
          >
            {isResending ? (
              <>
                <div className="mr-1 h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                Sending...
              </>
            ) : (
              <>Resend verification</>
            )}
          </Button>

          <Button
            variant="default"
            onClick={handleCheckVerification}
            disabled={isResending || isChecking}
            className="w-full text-xs sm:text-sm px-2 sm:px-4"
            size="sm"
          >
            {isChecking ? (
              <>
                <div className="mr-1 h-3 w-3 animate-spin rounded-full border-2 border-current border-t-transparent"></div>
                Checking...
              </>
            ) : (
              <>I've verified my email</>
            )}
          </Button>
        </div>

        {resendCount >= 3 && (
          <p className="text-sm mt-2">
            You've reached the maximum number of resend attempts.
            Please check your spam folder or contact support if you still haven't received the email.
          </p>
        )}
      </AlertDescription>
    </Alert>
  );
}
