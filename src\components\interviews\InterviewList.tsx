import React, { useState } from 'react';
import { format } from 'date-fns';
import { getTimezoneByValue } from '@/lib/timezones';
import { 
  CalendarClock, 
  Search, 
  Filter, 
  ChevronDown, 
  Phone, 
  Video, 
  MapPin,
  User
} from 'lucide-react';
import { Interview } from '@/services/supabase/interviews';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface InterviewListProps {
  interviews: Interview[];
  isLoading: boolean;
  onInterviewSelect: (interview: Interview) => void;
  onScheduleNew: () => void;
}

const InterviewList: React.FC<InterviewListProps> = ({
  interviews,
  isLoading,
  onInterviewSelect,
  onScheduleNew
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [typeFilter, setTypeFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date-asc');

  // Filter and sort interviews
  const filteredInterviews = interviews
    .filter(interview => {
      // Apply search filter
      const searchLower = searchQuery.toLowerCase();
      const matchesSearch = 
        interview.candidate_name?.toLowerCase().includes(searchLower) ||
        interview.job_title?.toLowerCase().includes(searchLower) ||
        interview.company_name?.toLowerCase().includes(searchLower);
      
      // Apply status filter
      const matchesStatus = statusFilter === 'all' || interview.status === statusFilter;
      
      // Apply type filter
      const matchesType = typeFilter === 'all' || interview.interview_type === typeFilter;
      
      return matchesSearch && matchesStatus && matchesType;
    })
    .sort((a, b) => {
      // Apply sorting
      switch (sortBy) {
        case 'date-asc':
          return new Date(a.scheduled_at).getTime() - new Date(b.scheduled_at).getTime();
        case 'date-desc':
          return new Date(b.scheduled_at).getTime() - new Date(a.scheduled_at).getTime();
        case 'candidate-asc':
          return (a.candidate_name || '').localeCompare(b.candidate_name || '');
        case 'candidate-desc':
          return (b.candidate_name || '').localeCompare(a.candidate_name || '');
        default:
          return 0;
      }
    });

  // Render interview type icon
  const renderTypeIcon = (type: string) => {
    switch (type) {
      case 'phone':
        return <Phone className="h-4 w-4 text-gray-500" />;
      case 'video':
        return <Video className="h-4 w-4 text-gray-500" />;
      case 'in-person':
        return <MapPin className="h-4 w-4 text-gray-500" />;
      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Interviews</CardTitle>
        <Button onClick={onScheduleNew}>
          <CalendarClock className="h-4 w-4 mr-2" />
          Schedule New
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Search and filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Search interviews..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="rescheduled">Rescheduled</SelectItem>
                </SelectContent>
              </Select>

              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-[130px]">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="phone">Phone</SelectItem>
                  <SelectItem value="video">Video</SelectItem>
                  <SelectItem value="in-person">In-person</SelectItem>
                </SelectContent>
              </Select>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="gap-1">
                    <Filter className="h-4 w-4" />
                    Sort
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[200px]">
                  <DropdownMenuLabel>Sort by</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuGroup>
                    <DropdownMenuItem onClick={() => setSortBy('date-asc')}>
                      Date (Ascending)
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy('date-desc')}>
                      Date (Descending)
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy('candidate-asc')}>
                      Candidate (A-Z)
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => setSortBy('candidate-desc')}>
                      Candidate (Z-A)
                    </DropdownMenuItem>
                  </DropdownMenuGroup>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Interviews list */}
          <div className="space-y-4">
            {isLoading ? (
              // Loading skeletons
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex justify-between">
                    <Skeleton className="h-6 w-1/3" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <div className="mt-2 space-y-2">
                    <Skeleton className="h-4 w-1/2" />
                    <Skeleton className="h-4 w-1/4" />
                  </div>
                </div>
              ))
            ) : filteredInterviews.length > 0 ? (
              filteredInterviews.map(interview => (
                <div
                  key={interview.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 cursor-pointer transition-colors"
                  onClick={() => onInterviewSelect(interview)}
                >
                  <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2">
                    <div>
                      <h3 className="font-medium flex items-center">
                        <User className="h-4 w-4 mr-2 text-gray-500" />
                        {interview.candidate_name}
                      </h3>
                      <p className="text-sm text-gray-500">{interview.job_title}</p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge
                        variant={
                          interview.status === 'scheduled' ? 'default' :
                          interview.status === 'completed' ? 'secondary' :
                          interview.status === 'cancelled' ? 'destructive' : 'outline'
                        }
                        className="capitalize"
                      >
                        {interview.status}
                      </Badge>
                      <div className="flex items-center gap-1 text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                        {renderTypeIcon(interview.interview_type)}
                        <span className="capitalize">{interview.interview_type}</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-2 flex items-center text-sm text-gray-500">
                    <CalendarClock className="h-4 w-4 mr-1" />
                    <span>
                      {format(new Date(interview.scheduled_at), 'MMM d, yyyy')} at {format(new Date(interview.scheduled_at), 'h:mm a')}
                      {interview.timezone && (
                        <span className="ml-1">
                          ({getTimezoneByValue(interview.timezone)?.label.split(' ')[0] || interview.timezone})
                        </span>
                      )}
                    </span>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                {searchQuery || statusFilter !== 'all' || typeFilter !== 'all' ? (
                  <p>No interviews match your filters</p>
                ) : (
                  <p>No interviews scheduled yet</p>
                )}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default InterviewList;
