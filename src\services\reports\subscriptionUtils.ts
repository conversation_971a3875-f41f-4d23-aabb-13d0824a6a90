import { ReportFormat, ReportType } from './types';

/**
 * Check if a user can export reports in the specified format based on their subscription tier
 */
export const canExportReportFormat = (
  format: ReportFormat,
  subscriptionTier: 'starter' | 'growth' | 'pro'
): boolean => {
  switch (subscriptionTier) {
    case 'starter':
      // Starter can only view reports in the UI, no exports
      return false;
    case 'growth':
      // Growth can export PDF and Excel
      return true;
    case 'pro':
      // Pro can export all formats
      return true;
    default:
      return false;
  }
};

/**
 * Get available report formats based on subscription tier
 */
export const getAvailableReportFormats = (
  subscriptionTier: 'starter' | 'growth' | 'pro'
): ReportFormat[] => {
  switch (subscriptionTier) {
    case 'starter':
      return [];
    case 'growth':
      return ['pdf', 'excel'];
    case 'pro':
      return ['pdf', 'excel'];
    default:
      return [];
  }
};

/**
 * Get available report types based on subscription tier
 */
export const getAvailableReportTypes = (
  subscriptionTier: 'starter' | 'growth' | 'pro'
): ReportType[] => {
  const baseReports: ReportType[] = ['recruitment_funnel', 'time_to_hire'];

  switch (subscriptionTier) {
    case 'starter':
      return baseReports;
    case 'growth':
      return [...baseReports, 'source_effectiveness'];
    case 'pro':
      return [...baseReports, 'source_effectiveness', 'candidate_pipeline', 'skill_analysis', 'comprehensive'];
    default:
      return baseReports;
  }
};
