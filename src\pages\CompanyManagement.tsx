import React, { useState, useRef } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { useCompanyContext } from '@/contexts/CompanyContext';
import {
  Plus,
  Building,
  Edit,
  Trash2,
  CheckCircle,
  Globe,
  MapPin,
  Users,
  Briefcase,
  Upload,
  X,
  Image
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useCompanies, useCreateCompany, useDeleteCompany, useUploadCompanyLogo } from '@/hooks/use-companies';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import SubscriptionGuard from '@/components/guards/SubscriptionGuard';
import { useUsageLimit } from '@/contexts/UsageContext';

interface CompanyFormData {
  name: string;
  description: string;
  industry: string;
  size: string;
  location: string;
  website: string;
  logo_url?: string;
}

const CompanyManagement = () => {
  const { toast } = useToast();
  const { data: companies, isLoading } = useCompanies();
  const createCompanyMutation = useCreateCompany();
  const deleteCompanyMutation = useDeleteCompany();
  const uploadLogoMutation = useUploadCompanyLogo();
  const { data: usageLimit } = useUsageLimit('company_profiles');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [formData, setFormData] = useState<CompanyFormData>({
    name: '',
    description: '',
    industry: '',
    size: '',
    location: '',
    website: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle logo file selection
  const handleLogoSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
      if (!validTypes.includes(file.type)) {
        toast({
          title: 'Invalid file type',
          description: 'Please upload a valid image file (JPEG, PNG, GIF, WEBP)',
          variant: 'destructive',
        });
        return;
      }

      // Validate file size (max 2MB)
      if (file.size > 2 * 1024 * 1024) {
        toast({
          title: 'File too large',
          description: 'Logo image must be less than 2MB',
          variant: 'destructive',
        });
        return;
      }

      setLogoFile(file);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setLogoPreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  // Remove logo preview
  const removeLogoPreview = () => {
    setLogoFile(null);
    setLogoPreview(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleCreateCompany = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // First create the company
      const newCompany = await createCompanyMutation.mutateAsync({
        name: formData.name,
        description: formData.description,
        industry: formData.industry,
        size: formData.size,
        location: formData.location,
        website: formData.website
      });

      // If we have a logo file, upload it and update the company
      if (logoFile && newCompany?.id) {
        const logoUrl = await uploadLogoMutation.mutateAsync({
          companyId: newCompany.id,
          file: logoFile
        });

        // Update the company with the logo URL
        if (logoUrl) {
          // The company is already updated in the database, no need to call an update mutation
          // Just update the local state if needed
        }
      }

      // Reset form and close dialog
      setFormData({
        name: '',
        description: '',
        industry: '',
        size: '',
        location: '',
        website: ''
      });
      setLogoFile(null);
      setLogoPreview(null);
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error creating company:', error);
    }
  };

  const handleDeleteCompany = async (id: string) => {
    if (window.confirm('Are you sure you want to delete this company? This action cannot be undone.')) {
      try {
        await deleteCompanyMutation.mutateAsync(id);
        toast({
          title: 'Company deleted',
          description: 'The company has been successfully deleted.',
        });
      } catch (error) {
        console.error('Error deleting company:', error);
      }
    }
  };

  const { activeCompanyId, setActiveCompanyId } = useCompanyContext();

  const handleSetActiveCompany = (id: string) => {
    // Set the active company ID in the context (which also updates localStorage)
    setActiveCompanyId(id);

    toast({
      title: 'Company activated',
      description: 'This company is now your active company for all operations.',
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-800">Company Management</h1>
          <p className="text-gray-600 mt-1">
            Create and manage your company profiles
          </p>
        </div>
        <SubscriptionGuard limitType="company_profiles" useToast={true}>
          <Dialog
            open={isDialogOpen}
            onOpenChange={(open) => {
              setIsDialogOpen(open);
              if (!open) {
                // Reset form when dialog is closed
                setLogoFile(null);
                setLogoPreview(null);
                setFormData({
                  name: '',
                  description: '',
                  industry: '',
                  size: '',
                  location: '',
                  website: ''
                });
              }
            }}
          >
            <DialogTrigger asChild>
              <Button className="bg-primary-gradient text-white hover:shadow-md transition-all">
                <Plus className="mr-2 h-4 w-4" /> Create New Company
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[550px]">
              <DialogHeader>
                <DialogTitle>Create New Company</DialogTitle>
                <DialogDescription>
                  Fill in the details below to create a new company profile.
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleCreateCompany}>
                <div className="grid gap-4 py-4">
                  {/* Company Logo Upload */}
                  <div className="space-y-2">
                    <Label className="text-gray-700">Company Logo</Label>
                    <div className="flex flex-col items-center p-4 border border-dashed border-gray-300 rounded-lg bg-gray-50">
                      {logoPreview ? (
                        <div className="relative mb-4">
                          <Avatar className="h-24 w-24">
                            <AvatarImage src={logoPreview} alt="Logo preview" />
                            <AvatarFallback className="bg-primary-gradient text-white">
                              <Image className="h-8 w-8" />
                            </AvatarFallback>
                          </Avatar>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full border-gray-300"
                            onClick={removeLogoPreview}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <div className="mb-4 bg-gray-200 h-24 w-24 rounded-full flex items-center justify-center">
                          <Image className="h-8 w-8 text-gray-500" />
                        </div>
                      )}

                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleLogoSelect}
                        className="hidden"
                        accept="image/jpeg,image/png,image/gif,image/webp"
                      />

                      <Button
                        type="button"
                        variant="outline"
                        className="border-gray-300 text-gray-700 hover:bg-gray-100"
                        onClick={() => fileInputRef.current?.click()}
                      >
                        <Upload className="mr-2 h-4 w-4" /> Upload Logo
                      </Button>
                      <p className="text-xs text-gray-500 mt-2">Recommended size: 400x400px (Max: 2MB)</p>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-gray-700">Company Name <span className="text-red-500">*</span></Label>
                    <Input
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="e.g. Acme Corporation"
                      className="bg-white border-gray-200 text-gray-800"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description" className="text-gray-700">Description</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Brief description of your company"
                      className="bg-white border-gray-200 text-gray-800 min-h-[100px]"
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="industry" className="text-gray-700">Industry</Label>
                      <Input
                        id="industry"
                        name="industry"
                        value={formData.industry}
                        onChange={handleInputChange}
                        placeholder="e.g. Technology"
                        className="bg-white border-gray-200 text-gray-800"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="size" className="text-gray-700">Company Size</Label>
                      <Input
                        id="size"
                        name="size"
                        value={formData.size}
                        onChange={handleInputChange}
                        placeholder="e.g. 1-50 employees"
                        className="bg-white border-gray-200 text-gray-800"
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="location" className="text-gray-700">Location</Label>
                      <Input
                        id="location"
                        name="location"
                        value={formData.location}
                        onChange={handleInputChange}
                        placeholder="e.g. New York, NY"
                        className="bg-white border-gray-200 text-gray-800"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="website" className="text-gray-700">Website</Label>
                      <Input
                        id="website"
                        name="website"
                        value={formData.website}
                        onChange={handleInputChange}
                        placeholder="e.g. https://example.com"
                        className="bg-white border-gray-200 text-gray-800"
                      />
                    </div>
                  </div>
                </div>
                <DialogFooter>
                  <Button type="submit" disabled={createCompanyMutation.isPending}>
                    {createCompanyMutation.isPending ? (
                      <div className="flex items-center">
                        <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                        Creating...
                      </div>
                    ) : (
                      'Create Company'
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </DialogContent>
          </Dialog>
        </SubscriptionGuard>
      </div>

      {/* Usage information */}
      <Card className="bg-white border-gray-200 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium text-gray-800">Company Profiles</h3>
              <p className="text-sm text-gray-600">
                {usageLimit?.data ? (
                  <>
                    You are using {usageLimit.data.currentUsage} of {usageLimit.data.limit === Infinity ? 'unlimited' : usageLimit.data.limit} company profiles
                  </>
                ) : (
                  'Loading usage information...'
                )}
              </p>
            </div>
            {usageLimit?.data && usageLimit.data.limit !== Infinity && (
              <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-primary-gradient rounded-full"
                  style={{ width: `${Math.min(100, (usageLimit.data.currentUsage / usageLimit.data.limit) * 100)}%` }}
                ></div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Company listings */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {isLoading ? (
          // Loading skeletons
          Array.from({ length: 3 }).map((_, index) => (
            <Card key={index} className="bg-white border-gray-200 shadow-sm">
              <CardContent className="p-6">
                <div className="flex items-center space-x-4 mb-4">
                  <Skeleton className="h-12 w-12 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-40" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : companies && companies.length > 0 ? (
          companies.map((company) => (
            <Card
              key={company.id}
              className={`bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 ${
                company.id === activeCompanyId ? 'ring-2 ring-primary ring-offset-2' : ''
              }`}
            >
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-12 w-12">
                      {company.logo_url ? (
                        <AvatarImage src={company.logo_url} alt={company.name} />
                      ) : (
                        <AvatarFallback className="bg-primary-gradient text-white">
                          {company.name.charAt(0)}
                        </AvatarFallback>
                      )}
                    </Avatar>
                    <div>
                      <h3 className="font-medium text-gray-800">{company.name}</h3>
                      {company.id === activeCompanyId && (
                        <Badge className="bg-green-500/20 text-green-600 hover:bg-green-500/30 mt-1">
                          <CheckCircle className="mr-1 h-3 w-3" /> Active
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-2 mb-4">
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {company.description || 'No description provided'}
                  </p>

                  {company.industry && (
                    <div className="flex items-center text-xs text-gray-500">
                      <Briefcase className="mr-1 h-3 w-3" />
                      <span>{company.industry}</span>
                    </div>
                  )}

                  {company.location && (
                    <div className="flex items-center text-xs text-gray-500">
                      <MapPin className="mr-1 h-3 w-3" />
                      <span>{company.location}</span>
                    </div>
                  )}

                  {company.website && (
                    <div className="flex items-center text-xs text-gray-500">
                      <Globe className="mr-1 h-3 w-3" />
                      <a
                        href={company.website.startsWith('http') ? company.website : `https://${company.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:underline truncate max-w-[200px]"
                      >
                        {company.website}
                      </a>
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-gray-200 text-gray-700 hover:bg-gray-100"
                      onClick={() => window.location.href = `/dashboard/company/${company.id}`}
                    >
                      <Edit className="mr-1 h-3 w-3" /> Edit
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-gray-200 text-red-600 hover:bg-red-50 hover:text-red-700"
                      onClick={() => handleDeleteCompany(company.id)}
                      disabled={deleteCompanyMutation.isPending}
                    >
                      <Trash2 className="mr-1 h-3 w-3" /> Delete
                    </Button>
                  </div>

                  {company.id !== activeCompanyId && (
                    <Button
                      variant="default"
                      size="sm"
                      className="bg-primary-gradient text-white"
                      onClick={() => handleSetActiveCompany(company.id)}
                    >
                      <CheckCircle className="mr-1 h-3 w-3" /> Set Active
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        ) : (
          <Card className="bg-white border-gray-200 p-8 text-center col-span-full">
            <Building className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">No Companies Found</h3>
            <p className="text-gray-600 mb-6">
              You haven't created any company profiles yet. Create your first company to get started.
            </p>
            <SubscriptionGuard limitType="company_profiles" useCompact={true}>
              <Button
                className="bg-primary-gradient text-white hover:shadow-md transition-all mx-auto"
                onClick={() => setIsDialogOpen(true)}
              >
                <Plus className="mr-2 h-4 w-4" /> Create New Company
              </Button>
            </SubscriptionGuard>
          </Card>
        )}
      </div>
    </div>
  );
};

// Wrap the CompanyManagement component with the DashboardLayout
const CompanyManagementWithLayout = () => (
  <DashboardLayout>
    <CompanyManagement />
  </DashboardLayout>
);

export default CompanyManagementWithLayout;
