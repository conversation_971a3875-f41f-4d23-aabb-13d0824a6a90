/**
 * Utility for handling API retries with exponential backoff
 */

// Default retry configuration
export const DEFAULT_RETRY_CONFIG = {
  maxRetries: 3,
  initialDelayMs: 1000, // 1 second
  maxDelayMs: 10000, // 10 seconds
  backoffFactor: 2, // Exponential backoff factor
};

// Error types that should trigger a retry
export const RETRYABLE_ERROR_TYPES = [
  'rate_limit_exceeded', // GROQ rate limit
  'timeout', // Request timeout
  'service_unavailable', // 503 errors
  'server_error', // 5xx errors
  'network_error', // Network connectivity issues
];

/**
 * Check if an error is retryable
 */
export function isRetryableError(error: any): boolean {
  // Check for rate limiting (429) errors
  if (error.status === 429 || error.code === 429) {
    return true;
  }

  // Check for specific error types in the error object
  if (error.type && RETRYABLE_ERROR_TYPES.includes(error.type)) {
    return true;
  }

  // Check for error messages that indicate rate limiting
  const errorMessage = error.message?.toLowerCase() || '';
  if (
    errorMessage.includes('rate limit') ||
    errorMessage.includes('too many requests') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('service unavailable') ||
    errorMessage.includes('server error')
  ) {
    return true;
  }

  return false;
}

/**
 * Calculate delay for exponential backoff
 */
export function calculateBackoffDelay(
  retryCount: number,
  { initialDelayMs, maxDelayMs, backoffFactor }: typeof DEFAULT_RETRY_CONFIG
): number {
  // Calculate exponential backoff: initialDelay * (backoffFactor ^ retryCount)
  const delay = initialDelayMs * Math.pow(backoffFactor, retryCount);
  
  // Add some randomness (jitter) to prevent all clients retrying simultaneously
  const jitter = Math.random() * 0.3 + 0.85; // Random value between 0.85 and 1.15
  
  // Return the delay, capped at maxDelayMs
  return Math.min(delay * jitter, maxDelayMs);
}

/**
 * Execute a function with retry logic
 */
export async function withRetry<T>(
  fn: () => Promise<T>,
  config = DEFAULT_RETRY_CONFIG
): Promise<T> {
  let retryCount = 0;

  while (true) {
    try {
      return await fn();
    } catch (error) {
      // Check if we should retry
      if (retryCount >= config.maxRetries || !isRetryableError(error)) {
        throw error; // Don't retry, just throw the error
      }

      // Calculate delay for exponential backoff
      const delayMs = calculateBackoffDelay(retryCount, config);
      
      // Log the retry attempt
      console.warn(
        `API call failed with error: ${error.message}. Retrying in ${delayMs}ms (attempt ${retryCount + 1}/${
          config.maxRetries
        })`
      );

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delayMs));
      
      // Increment retry counter
      retryCount++;
    }
  }
}

/**
 * Get a user-friendly error message based on the error, with Gemini-specific handling
 */
export function getUserFriendlyErrorMessage(error: any): string {
  const errorMessage = error.message?.toLowerCase() || '';

  // Check for Gemini-specific errors
  if (errorMessage.includes('gemini api error')) {
    if (errorMessage.includes('quota exceeded') || errorMessage.includes('resource exhausted')) {
      return 'Gemini API quota exceeded. Please try again later or check your API limits.';
    }
    if (errorMessage.includes('invalid api key') || errorMessage.includes('authentication')) {
      return 'Invalid Gemini API key. Please check your API key configuration.';
    }
    if (errorMessage.includes('model not found')) {
      return 'The selected Gemini model is not available. Please try a different model.';
    }
  }

  // Check for rate limiting errors
  if (error.status === 429 || error.code === 429 || 
      errorMessage.includes('rate limit') || errorMessage.includes('too many requests')) {
    return 'The AI service is currently experiencing high demand. The system will automatically retry your request.';
  }

  // Check for timeout errors
  if (errorMessage.includes('timeout') || errorMessage.includes('deadline exceeded')) {
    return 'The request timed out. The system will automatically retry your request.';
  }

  // Check for service unavailable errors
  if (error.status === 503 || error.code === 503 || 
      errorMessage.includes('service unavailable') || errorMessage.includes('unavailable')) {
    return 'The AI service is temporarily unavailable. The system will automatically retry your request.';
  }

  // Check for server errors
  if (error.status >= 500 || error.code >= 500 || errorMessage.includes('internal error')) {
    return 'The AI service encountered an internal error. The system will automatically retry your request.';
  }

  // Default error message
  return 'An error occurred while processing your request. The system will automatically retry if possible.';
}

// Enhanced retry configuration for Gemini
export const GEMINI_RETRY_CONFIG = {
  maxRetries: 10,
  initialDelayMs: 5000, // 5 seconds
  maxDelayMs: 30000, // 30 seconds max
  backoffFactor: 1.2, // Slower exponential backoff for Gemini
};

// Gemini-specific error types that should trigger a retry
export const GEMINI_RETRYABLE_ERROR_TYPES = [
  'rate_limit_exceeded',
  'timeout',
  'service_unavailable',
  'server_error',
  'network_error',
  'internal_error',
  'quota_exceeded',
  'resource_exhausted',
  'unavailable',
  'deadline_exceeded'
];

/**
 * Check if a Gemini error is retryable
 */
export function isGeminiRetryableError(error: any): boolean {
  // Check for rate limiting (429) errors
  if (error.status === 429 || error.code === 429) {
    return true;
  }

  // Check for server errors (5xx)
  if (error.status >= 500 || error.code >= 500) {
    return true;
  }

  // Check for specific Gemini error types
  if (error.type && GEMINI_RETRYABLE_ERROR_TYPES.includes(error.type)) {
    return true;
  }

  // Check for Gemini-specific error messages
  const errorMessage = error.message?.toLowerCase() || '';
  if (
    errorMessage.includes('rate limit') ||
    errorMessage.includes('quota exceeded') ||
    errorMessage.includes('resource exhausted') ||
    errorMessage.includes('unavailable') ||
    errorMessage.includes('deadline exceeded') ||
    errorMessage.includes('internal error') ||
    errorMessage.includes('service unavailable') ||
    errorMessage.includes('timeout') ||
    errorMessage.includes('too many requests') ||
    errorMessage.includes('server error') ||
    errorMessage.includes('gemini api error')
  ) {
    return true;
  }

  return false;
}

/**
 * Execute a function with Gemini-specific retry logic
 */
export async function withGeminiRetry<T>(
  fn: () => Promise<T>,
  config = GEMINI_RETRY_CONFIG
): Promise<T> {
  let retryCount = 0;

  while (true) {
    try {
      return await fn();
    } catch (error) {
      // Check if we should retry
      if (retryCount >= config.maxRetries || !isGeminiRetryableError(error)) {
        console.error(`Gemini API call failed after ${retryCount} retries:`, error);
        throw error; // Don't retry, just throw the error
      }

      // Calculate delay for exponential backoff
      const delayMs = calculateBackoffDelay(retryCount, config);
      
      // Log the retry attempt with more details for Gemini
      console.warn(
        `Gemini API call failed with error: ${error.message}. Retrying in ${delayMs}ms (attempt ${retryCount + 1}/${
          config.maxRetries
        }). Error details:`, error
      );

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delayMs));
      
      // Increment retry counter
      retryCount++;
    }
  }
}


