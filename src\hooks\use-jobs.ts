/**
 * React Query hooks for job data
 */

import { useAuth } from '@/contexts/AuthContext';
import { useEnhancedQuery, useEnhancedMutation, queryKeys } from '@/lib/queryUtils';
import * as jobService from '@/services/supabase/jobs';
import { Job, JobInsert, JobUpdate, JobRealtimeEvent } from '@/services/supabase/jobs';
import { BulkEvaluationResult } from '@/services/cv-processing/jobMatcher';
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/lib/supabase';
import { getPublicJob, getPublicJobs } from '@/services/supabase/jobs';

/**
 * Hook to fetch all jobs
 */
export function useJobs(companyId?: string) {
  return useEnhancedQuery<Job[]>(
    companyId ? queryKeys.jobs.byCompany(companyId) : queryKeys.jobs.all,
    () => jobService.getJobs(companyId),
    {
      fallbackData: [],
      errorMessage: companyId
        ? `Failed to load jobs for company ${companyId}`
        : 'Failed to load jobs',
    }
  );
}

/**
 * Hook to fetch a specific job
 */
export function useJob(id: string) {
  return useEnhancedQuery<Job | null>(
    queryKeys.jobs.byId(id),
    () => jobService.getJob(id),
    {
      enabled: !!id,
      fallbackData: null,
      errorMessage: `Failed to load job with ID ${id}`,
    }
  );
}

/**
 * Hook to fetch all public jobs (no authentication required)
 */
export function usePublicJobs() {
  return useEnhancedQuery<Job[]>(
    ['public-jobs'],
    () => getPublicJobs(),
    {
      fallbackData: [],
      errorMessage: 'Failed to load public jobs',
    }
  );
}

/**
 * Hook to fetch a public job (no authentication required)
 */
export function usePublicJob(id: string) {
  return useEnhancedQuery<Job | null>(
    ['public-job', id],
    () => getPublicJob(id),
    {
      enabled: !!id,
      fallbackData: null,
      errorMessage: `Failed to load job with ID ${id}`,
    }
  );
}

/**
 * Hook to create a new job
 */
export function useCreateJob() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useEnhancedMutation<Job, JobInsert>(
    (job) => jobService.createJob({
      ...job,
      user_id: job.user_id || user?.id,
    }),
    {
      errorMessage: 'Failed to create job',
      successMessage: 'Job created successfully',
      // Use optimistic updates for better UX
      onMutate: async (newJob) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: queryKeys.jobs.all });

        // Get the company ID if it exists
        const companyId = newJob.company_id;

        // Create a temporary ID for the optimistic job
        const tempId = `temp-${Date.now()}`;

        // Create the optimistic job
        const optimisticJob: Job = {
          id: tempId,
          user_id: newJob.user_id || user?.id || '',
          title: newJob.title,
          description: newJob.description || '',
          requirements: newJob.requirements || '',
          location: newJob.location || '',
          salary_range: newJob.salary_range || '',
          job_type: newJob.job_type || '',
          experience_level: newJob.experience_level || '',
          status: newJob.status || 'draft',
          company_id: companyId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        };

        // Snapshot the previous value
        const previousJobs = queryClient.getQueryData<Job[]>(queryKeys.jobs.all) || [];

        // Optimistically update the cache
        queryClient.setQueryData<Job[]>(
          queryKeys.jobs.all,
          [optimisticJob, ...previousJobs]
        );

        // If we have a company ID, also update the company-specific cache
        if (companyId) {
          const previousCompanyJobs = queryClient.getQueryData<Job[]>(
            queryKeys.jobs.byCompany(companyId)
          ) || [];

          queryClient.setQueryData<Job[]>(
            queryKeys.jobs.byCompany(companyId),
            [optimisticJob, ...previousCompanyJobs]
          );
        }

        // Return the context with the previous jobs
        return { previousJobs, companyId, tempId };
      },
      // If the mutation fails, roll back to the previous value
      onError: (err, newJob, context: any) => {
        if (context) {
          queryClient.setQueryData(
            queryKeys.jobs.all,
            context.previousJobs
          );

          if (context.companyId) {
            const previousCompanyJobs = context.previousJobs.filter(
              (job: Job) => job.company_id === context.companyId
            );

            queryClient.setQueryData(
              queryKeys.jobs.byCompany(context.companyId),
              previousCompanyJobs
            );
          }
        }
      },
      // Always refetch after error or success
      onSettled: (data, error, variables) => {
        queryClient.invalidateQueries({ queryKey: queryKeys.jobs.all });

        if (variables.company_id) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.jobs.byCompany(variables.company_id)
          });
        }

        // Invalidate job statistics
        queryClient.invalidateQueries({ queryKey: ['jobStatistics'] });
      },
    }
  );
}

/**
 * Hook to update a job
 */
export function useUpdateJob() {
  return useEnhancedMutation<Job, { id: string; job: JobUpdate }>(
    ({ id, job }) => jobService.updateJob(id, job),
    {
      errorMessage: 'Failed to update job',
      successMessage: 'Job updated successfully',
      invalidateQueries: [queryKeys.jobs.all],
      onSuccess: (data, variables) => {
        // Also invalidate the specific job query
        return [queryKeys.jobs.byId(variables.id)];
      },
    }
  );
}

/**
 * Hook to delete a job
 */
export function useDeleteJob() {
  return useEnhancedMutation<boolean, string>(
    (id) => jobService.deleteJob(id),
    {
      errorMessage: 'Failed to delete job',
      successMessage: 'Job deleted successfully',
      invalidateQueries: [queryKeys.jobs.all],
    }
  );
}

/**
 * Hook to fetch job statistics
 */
export function useJobStatistics(companyId?: string) {
  return useEnhancedQuery(
    ['jobStatistics', companyId],
    () => jobService.getJobStatistics(companyId),
    {
      fallbackData: { total: 0, active: 0, draft: 0, closed: 0 },
      errorMessage: 'Failed to load job statistics',
    }
  );
}

/**
 * Hook to subscribe to real-time job updates
 *
 * This hook automatically subscribes to job updates and updates the React Query cache
 * when jobs are created, updated, or deleted.
 */
export function useJobSubscription(companyId?: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!user?.id) return;

    // Subscribe to job updates
    const subscription = jobService.subscribeToJobs(
      user.id,
      companyId,
      (event: JobRealtimeEvent, job: Job) => {
        // Handle different event types
        switch (event) {
          case 'INSERT':
            // Add the new job to the cache
            queryClient.setQueryData<Job[]>(
              companyId ? queryKeys.jobs.byCompany(companyId) : queryKeys.jobs.all,
              (oldJobs = []) => [job, ...oldJobs]
            );

            // Invalidate job statistics
            queryClient.invalidateQueries({ queryKey: ['jobStatistics', companyId] });
            break;

          case 'UPDATE':
            // Update the job in the cache
            queryClient.setQueryData<Job[]>(
              companyId ? queryKeys.jobs.byCompany(companyId) : queryKeys.jobs.all,
              (oldJobs = []) => oldJobs.map(oldJob =>
                oldJob.id === job.id ? job : oldJob
              )
            );

            // Update the specific job in the cache
            queryClient.setQueryData(
              queryKeys.jobs.byId(job.id),
              job
            );

            // Invalidate job statistics if status changed
            queryClient.invalidateQueries({ queryKey: ['jobStatistics', companyId] });
            break;

          case 'DELETE':
            // Remove the job from the cache
            queryClient.setQueryData<Job[]>(
              companyId ? queryKeys.jobs.byCompany(companyId) : queryKeys.jobs.all,
              (oldJobs = []) => oldJobs.filter(oldJob => oldJob.id !== job.id)
            );

            // Remove the specific job from the cache
            queryClient.removeQueries({ queryKey: queryKeys.jobs.byId(job.id) });

            // Invalidate job statistics
            queryClient.invalidateQueries({ queryKey: ['jobStatistics', companyId] });
            break;
        }
      }
    );

    // Cleanup subscription on unmount
    return () => {
      subscription.unsubscribe();
    };
  }, [user?.id, companyId, queryClient]);
}

/**
 * Hook to perform bulk evaluation of all candidates for a specific job
 */
export function useBulkEvaluateCandidates() {
  return useEnhancedMutation<BulkEvaluationResult, string>(
    async (jobId: string) => {
      // Get the current session
      const { data: { session } } = await supabase.auth.getSession();

      if (!session) {
        throw new Error('No active session');
      }

      // Call the bulk evaluation Edge Function
      const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/bulk-evaluate-candidates/${jobId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || errorData.message || 'Failed to evaluate candidates');
      }

      const result = await response.json();
      return result.data;
    },
    {
      errorMessage: 'Failed to evaluate candidates for job',
      successMessage: 'Candidates evaluated successfully',
      // Invalidate related queries after successful evaluation
      invalidateQueries: [
        queryKeys.candidates.all,
        ['candidateStatistics'],
        ['evaluations']
      ],
    }
  );
}

