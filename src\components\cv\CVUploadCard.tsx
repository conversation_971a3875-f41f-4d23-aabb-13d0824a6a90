import React, { useState, useRef, useEffect } from 'react';
import {
  Upload,
  File,
  X,
  Check,
  Trash2,
  FileText,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/contexts/AuthContext';
import { useUsageLimit } from '@/hooks/use-usage';
import { useFreeTrial } from '@/hooks/use-free-trial';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
  url?: string;
}

interface CVUploadCardProps {
  onUploadComplete?: (file: File) => void;
  onMultipleUploadComplete?: (files: File[]) => void;
  enableBulkUpload?: boolean;
  maxFiles?: number;
  title?: string;
}

const CVUploadCard: React.FC<CVUploadCardProps> = ({
  onUploadComplete,
  onMultipleUploadComplete,
  enableBulkUpload = false,
  maxFiles = 10,
  title = "Upload CVs"
}) => {
  const { toast } = useToast();
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragging, setIsDragging] = useState(false);

  // Get CV upload usage limit
  const { data: usageLimit, isLoading: isLoadingUsage } = useUsageLimit('cv_uploads');

  // Get free trial status
  const { hasUsedFreeTrial, isLoading: isLoadingFreeTrial } = useFreeTrial();

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      addFiles(Array.from(files));
    }
  };

  // Handle drag events
  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    const files = e.dataTransfer.files;
    if (files) {
      addFiles(Array.from(files));
    }
  };

  // Add files to the upload list
  const addFiles = (files: File[]) => {
    if (files.length === 0) return;

    // Filter for PDF, DOC, DOCX files
    const validTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];

    const validFiles = files.filter(file => validTypes.includes(file.type));

    if (validFiles.length !== files.length) {
      toast({
        title: 'Invalid file type',
        description: 'Only PDF, DOC, and DOCX files are allowed.',
        variant: 'destructive',
      });

      if (validFiles.length === 0) return;
    }

    // Check if user has reached their CV upload limit
    if (usageLimit && usageLimit.hasReachedLimit) {
      toast({
        title: 'Upload limit reached',
        description: `You have reached your monthly limit of ${usageLimit.limit} CV uploads. Please upgrade your plan to upload more CVs.`,
        variant: 'destructive',
      });
      return;
    }

    // Limit the number of files to maxFiles
    const filesToProcess = enableBulkUpload
      ? validFiles.slice(0, maxFiles)
      : [validFiles[0]]; // If bulk upload is disabled, only take the first file

    if (enableBulkUpload && validFiles.length > maxFiles) {
      toast({
        title: 'Too many files',
        description: `Only the first ${maxFiles} files will be processed.`,
        variant: 'warning',
      });
    }

    // Create file objects for the UI
    const newFiles = filesToProcess.map(file => ({
      id: Math.random().toString(36).substring(2, 9),
      name: file.name,
      size: file.size,
      type: file.type,
      progress: 0,
      status: 'uploading' as const,
    }));

    // If bulk upload is enabled, add all files to the list
    // Otherwise, replace the current list with the new file
    if (enableBulkUpload) {
      setUploadedFiles(prev => [...prev, ...newFiles]);
    } else {
      setUploadedFiles(newFiles);
    }

    // Start upload simulation for each file
    newFiles.forEach((newFile, index) => {
      simulateFileUpload(newFile.id, filesToProcess[index]);
    });

    // If multiple upload callback is provided and bulk upload is enabled, use it
    if (enableBulkUpload && onMultipleUploadComplete && filesToProcess.length > 1) {
      // We'll call this after all files have been "uploaded" in the simulateFileUpload function
      // For now, just store the files for later use
      window.setTimeout(() => {
        onMultipleUploadComplete(filesToProcess);
      }, 500);
    }
  };

  // Simulate file upload progress
  const simulateFileUpload = (fileId: string, originalFile: File) => {
    let progress = 0;
    const interval = setInterval(() => {
      progress += Math.floor(Math.random() * 10) + 5;

      if (progress >= 100) {
        progress = 100;
        clearInterval(interval);

        setUploadedFiles(prev => {
          const updatedFiles = prev.map(file =>
            file.id === fileId
              ? { ...file, progress: 100, status: 'success', url: `/mock-cv-${fileId}.pdf` }
              : file
          );

          // Check if all files are uploaded
          const allUploaded = updatedFiles.every(file => file.status === 'success');

          // If this is a single file upload or if all files are uploaded in bulk mode
          if (!enableBulkUpload && onUploadComplete) {
            // Call onUploadComplete with the original File object for single file mode
            onUploadComplete(originalFile);
          } else if (enableBulkUpload && allUploaded && onUploadComplete && updatedFiles.length === 1) {
            // If there's only one file in bulk mode, still use the single file callback
            onUploadComplete(originalFile);
          }

          return updatedFiles;
        });
      } else {
        setUploadedFiles(prev =>
          prev.map(file =>
            file.id === fileId
              ? { ...file, progress }
              : file
          )
        );
      }
    }, 300);
  };

  // Remove file from the list
  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(file => file.id !== fileId));
  };

  // Format file size
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader>
        <CardTitle className="text-gray-800 text-lg">{title}</CardTitle>
        {!hasUsedFreeTrial && !isLoadingFreeTrial && user?.subscriptionTier === 'starter' && !user?.isPlatformAdmin && (
          <CardDescription className="text-green-600 font-medium mt-1">
            Try with 1 free CV evaluation before subscribing
          </CardDescription>
        )}
        {usageLimit && !isLoadingUsage && (
          <CardDescription>
            {usageLimit.currentUsage} of {usageLimit.limit} uploads used this month
            <Progress
              value={(usageLimit.currentUsage / usageLimit.limit) * 100}
              className="h-1.5 mt-1"
              indicatorClassName={
                usageLimit.hasReachedLimit
                  ? 'bg-red-500'
                  : usageLimit.currentUsage > usageLimit.limit * 0.8
                    ? 'bg-amber-500'
                    : 'bg-green-500'
              }
            />
          </CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Show warning if approaching limit */}
        {usageLimit && !isLoadingUsage && usageLimit.currentUsage > usageLimit.limit * 0.8 && !usageLimit.hasReachedLimit && (
          <Alert variant="warning" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              You're approaching your monthly CV upload limit. {usageLimit.limit - usageLimit.currentUsage} uploads remaining.
            </AlertDescription>
          </Alert>
        )}

        {/* Show free trial used message */}
        {hasUsedFreeTrial && usageLimit && usageLimit.currentUsage === 0 && user?.subscriptionTier === 'starter' && !user?.isPlatformAdmin && (
          <Alert className="bg-amber-50 border-amber-200 mb-4">
            <AlertCircle className="h-4 w-4 text-amber-800" />
            <AlertDescription className="text-amber-800">
              You've used your free CV evaluation. Subscribe to a plan to upload more CVs.
            </AlertDescription>
          </Alert>
        )}

        {/* Show error if limit reached */}
        {usageLimit && usageLimit.hasReachedLimit && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4 mr-2" />
            <AlertDescription>
              You've reached your monthly CV upload limit. Please upgrade your plan to upload more CVs.
            </AlertDescription>
          </Alert>
        )}
        {/* Drag and drop area */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center ${
            isDragging ? 'border-primary bg-primary/5' : 'border-gray-300'
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-800 mb-2">
            Drag and drop your CV {enableBulkUpload ? 'files' : 'file'} here
          </h3>
          <p className="text-gray-500 mb-4">
            Supports PDF, DOC, DOCX (Max 10MB per file)
            {enableBulkUpload && ` • Upload up to ${maxFiles} files at once`}
          </p>
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileSelect}
            className="hidden"
            multiple
            accept=".pdf,.doc,.docx,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
          />
          <Button
            variant="outline"
            className="border-gray-300 text-gray-700 hover:bg-gray-100"
            onClick={() => fileInputRef.current?.click()}
          >
            <Upload className="mr-2 h-4 w-4" /> Browse Files
          </Button>
        </div>

        {/* Uploaded files list */}
        {uploadedFiles.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-gray-800 font-medium">Uploaded Files ({uploadedFiles.length})</h3>
            <div className="space-y-3">
              {uploadedFiles.map(file => (
                <div
                  key={file.id}
                  className="bg-gray-50 border border-gray-200 rounded-lg p-4 flex items-center"
                >
                  <div className="mr-3">
                    <File className="h-8 w-8 text-gray-400" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between">
                      <p className="text-gray-800 font-medium truncate">{file.name}</p>
                      <span className="text-gray-500 text-sm">{formatFileSize(file.size)}</span>
                    </div>
                    <div className="mt-1">
                      <Progress
                        value={file.progress}
                        className="h-1.5"
                        indicatorClassName={
                          file.status === 'error'
                            ? 'bg-red-500'
                            : file.progress === 100
                              ? 'bg-green-500'
                              : 'bg-recruiter-blue'
                        }
                      />
                    </div>
                    <div className="flex justify-between mt-1">
                      <span className="text-xs text-gray-500">
                        {file.status === 'uploading' && `Uploading... ${file.progress}%`}
                        {file.status === 'success' && 'Upload complete'}
                        {file.status === 'error' && file.error}
                      </span>
                      {file.status === 'success' && (
                        <span className="text-xs text-green-500 flex items-center">
                          <Check className="h-3 w-3 mr-1" /> Done
                        </span>
                      )}
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="ml-2 text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                    onClick={() => removeFile(file.id)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CVUploadCard;
