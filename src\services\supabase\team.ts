import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { safeDbOperation } from '@/lib/errorHandling';
import { sendTeamInvitation } from '../email/teamNotifications';
import { signUpFromInvitation, getInvitationDetails } from './invitationAuth';

export type TeamMember = Database['public']['Tables']['team_members']['Row'];
export type TeamMemberInsert = Database['public']['Tables']['team_members']['Insert'];
export type TeamMemberUpdate = Database['public']['Tables']['team_members']['Update'];

// Enhanced team member type with profile information
export interface EnhancedTeamMember extends TeamMember {
  profiles?: {
    full_name: string;
    avatar_url: string | null;
    last_sign_in_at: string | null;
    email?: string;
  } | null;
  metadata?: {
    name?: string;
    email?: string;
  } | null;
}

/**
 * Get all team members for a company
 */
export const getTeamMembers = async (companyId: string): Promise<EnhancedTeamMember[]> => {
  return safeDbOperation(
    async () => {
      try {
        // Try to fetch with metadata column and explicit join
        const { data, error } = await supabase
          .from('team_members')
          .select(`
            *,
            metadata
          `)
          .eq('company_id', companyId)
          .order('created_at', { ascending: false });

        // If successful, manually fetch profile data for each team member
        if (!error && data) {
          // Get all user_ids that are not null
          const userIds = data
            .filter(member => member.user_id)
            .map(member => member.user_id);

          // If there are any user_ids, fetch their profiles
          if (userIds.length > 0) {
            const { data: profilesData, error: profilesError } = await supabase
              .from('profiles')
              .select('user_id, full_name, avatar_url, last_sign_in_at, email')
              .in('user_id', userIds);

            if (!profilesError && profilesData) {
              // Create a map of user_id to profile data
              const profilesMap = profilesData.reduce((map: Record<string, any>, profile) => {
                map[profile.user_id] = profile;
                return map;
              }, {});

              // Add profile data to each team member
              data.forEach(member => {
                if (member.user_id && profilesMap[member.user_id]) {
                  member.profiles = profilesMap[member.user_id];
                }
              });
            }
          }
        }

        if (error) throw error;
        return data || [];
      } catch (err) {
        // If that fails, try without metadata column
        console.warn('Falling back to query without metadata column:', err);
        const { data, error } = await supabase
          .from('team_members')
          .select('*')
          .eq('company_id', companyId)
          .order('created_at', { ascending: false });

        // If successful, manually fetch profile data for each team member
        if (!error && data) {
          // Get all user_ids that are not null
          const userIds = data
            .filter(member => member.user_id)
            .map(member => member.user_id);

          // If there are any user_ids, fetch their profiles
          if (userIds.length > 0) {
            const { data: profilesData, error: profilesError } = await supabase
              .from('profiles')
              .select('user_id, full_name, avatar_url, last_sign_in_at, email')
              .in('user_id', userIds);

            if (!profilesError && profilesData) {
              // Create a map of user_id to profile data
              const profilesMap = profilesData.reduce((map: Record<string, any>, profile) => {
                map[profile.user_id] = profile;
                return map;
              }, {});

              // Add profile data to each team member
              data.forEach(member => {
                if (member.user_id && profilesMap[member.user_id]) {
                  member.profiles = profilesMap[member.user_id];
                }
              });
            }
          }
        }

        if (error) throw error;
        return data || [];
      }
    },
    `Failed to fetch team members for company ${companyId}`,
    [] // Return empty array as fallback
  );
};

/**
 * Get a team member by ID
 */
export const getTeamMember = async (id: string): Promise<EnhancedTeamMember | null> => {
  return safeDbOperation(
    async () => {
      try {
        // Try to fetch with metadata column
        const { data, error } = await supabase
          .from('team_members')
          .select(`
            *,
            metadata
          `)
          .eq('id', id)
          .single();

        // If successful and user_id is not null, fetch profile data
        if (!error && data && data.user_id) {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('user_id, full_name, avatar_url, last_sign_in_at, email')
            .eq('user_id', data.user_id)
            .single();

          if (!profileError && profileData) {
            data.profiles = profileData;
          }
        }

        if (error) throw error;
        return data;
      } catch (err) {
        // If that fails, try without metadata column
        console.warn('Falling back to query without metadata column:', err);
        const { data, error } = await supabase
          .from('team_members')
          .select('*')
          .eq('id', id)
          .single();

        // If successful and user_id is not null, fetch profile data
        if (!error && data && data.user_id) {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('user_id, full_name, avatar_url, last_sign_in_at, email')
            .eq('user_id', data.user_id)
            .single();

          if (!profileError && profileData) {
            data.profiles = profileData;
          }
        }

        if (error) throw error;
        return data;
      }
    },
    `Failed to fetch team member with ID ${id}`,
    null // Return null as fallback
  );
};

/**
 * Invite a new team member with usage limit checking
 */
export const inviteTeamMember = async (
  teamMember: TeamMemberInsert,
  userId: string,
  checkUsageLimit = true,
  metadata?: { name?: string, email?: string }
): Promise<EnhancedTeamMember> => {
  return safeDbOperation(
    async () => {
      // If usage limit checking is enabled
      if (checkUsageLimit) {
        // Get user's subscription tier
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('subscription_tier, subscription_status')
          .eq('user_id', userId)
          .single();

        if (profileError) throw profileError;

        // Check if subscription is active
        if (profile.subscription_status !== 'active') {
          throw new Error('Your subscription is not active. Please renew your subscription to invite team members.');
        }

        // Get current team members count
        const { data: teamMembers, error: teamMembersError } = await supabase
          .from('team_members')
          .select('id')
          .eq('company_id', teamMember.company_id);

        if (teamMembersError) throw teamMembersError;

        // Get subscription limits
        const { data: limits, error: limitsError } = await supabase
          .from('subscription_limits')
          .select('team_member_limit')
          .eq('tier', profile.subscription_tier)
          .single();

        if (limitsError) throw limitsError;

        // Check if user has reached their limit
        const teamMembersCount = teamMembers?.length || 0;
        if (teamMembersCount >= limits.team_member_limit) {
          throw new Error(`You have reached your team member limit (${limits.team_member_limit}). Please upgrade your plan to invite more team members.`);
        }
      }

      // Generate a random invitation token
      const token = Math.random().toString(36).substring(2, 15);

      // Create the team member with metadata if provided
      const insertData: any = {
        ...teamMember,
        invitation_token: token
      };

      // Make sure user_id is null for pending invitations if not provided
      if (!insertData.user_id) {
        insertData.user_id = null;
      }

      // Try to add metadata if provided
      let data;
      let error;

      try {
        if (metadata && (metadata.name || metadata.email)) {
          // Store metadata in a separate variable to use later
          const metadataValue = metadata;

          // Try to create the team member with metadata
          try {
            insertData.metadata = metadataValue;

            const result = await supabase
              .from('team_members')
              .insert(insertData)
              .select()
              .single();

            data = result.data;
            error = result.error;

            // If successful, manually add the metadata to the returned data
            // since it might not be included in the response
            if (data && !data.metadata) {
              data.metadata = metadataValue;
            }
          } catch (metadataErr) {
            console.warn('Failed to insert with metadata, trying without:', metadataErr);

            // If that fails, try without metadata
            const insertDataWithoutMetadata = { ...insertData };
            delete insertDataWithoutMetadata.metadata;

            const result = await supabase
              .from('team_members')
              .insert(insertDataWithoutMetadata)
              .select()
              .single();

            data = result.data;
            error = result.error;

            // Manually add the metadata to the returned data
            if (data) {
              data.metadata = metadataValue;
            }
          }
        } else {
          // No metadata provided, just insert normally
          const result = await supabase
            .from('team_members')
            .insert(insertData)
            .select()
            .single();

          data = result.data;
          error = result.error;
        }
      } catch (err) {
        console.error('Failed to insert team member:', err);

        // Last resort, try without any extras
        const basicData = {
          user_id: null,
          company_id: teamMember.company_id,
          role: teamMember.role || 'viewer',
          status: 'pending',
          invited_by: userId,
          invitation_token: token
        };

        const result = await supabase
          .from('team_members')
          .insert(basicData)
          .select()
          .single();

        data = result.data;
        error = result.error;

        // Manually add the metadata to the returned data
        if (data && metadata) {
          data.metadata = metadata;
        }
      }

      if (error) throw error;

      // If usage limit checking is enabled, increment the usage counter
      if (checkUsageLimit) {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

        try {
          // Increment the team members count
          const { error } = await supabase.rpc('increment_usage_counter', {
            p_user_id: userId,
            p_month_year: currentMonth,
            p_counter_name: 'team_members_count',
            p_increment_by: 1
          });

          if (error) {
            console.error('Failed to increment usage counter:', error);
            // Don't throw here, we still want to return the team member even if tracking fails
          }
        } catch (error) {
          console.error('Failed to increment usage counter:', error);
          // Don't throw here, we still want to return the team member even if tracking fails
        }
      }

      // In a real application, you would send an email with the invitation link
      // containing the token to the invited user's email address

      // Send invitation email if metadata contains email and name
      if (metadata?.email && metadata?.name) {
        try {
          // Get inviter's profile for the email
          const { data: inviterProfile } = await supabase
            .from('profiles')
            .select('full_name')
            .eq('user_id', userId)
            .single();

          // Get company name
          const { data: company } = await supabase
            .from('companies')
            .select('name')
            .eq('id', teamMember.company_id)
            .single();

          const inviterName = inviterProfile?.full_name || 'Team Admin';
          const companyName = company?.name || 'Your Company';

          await sendTeamInvitation(
            metadata.email,
            metadata.name,
            inviterName,
            companyName,
            teamMember.role || 'team member',
            token
          );
        } catch (emailError) {
          console.error('Failed to send invitation email:', emailError);
          // Don't throw here - we still want to return the team member even if email fails
        }
      }

      return data;
    },
    `Failed to invite team member to company ${teamMember.company_id}`
  );
};

/**
 * Update a team member
 */
export const updateTeamMember = async (id: string, teamMember: TeamMemberUpdate): Promise<EnhancedTeamMember> => {
  return safeDbOperation(
    async () => {
      try {
        // Try to update with metadata in the select
        const { data, error } = await supabase
          .from('team_members')
          .update(teamMember)
          .eq('id', id)
          .select(`
            *,
            metadata
          `)
          .single();

        // If successful and user_id is not null, fetch profile data
        if (!error && data && data.user_id) {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('user_id, full_name, avatar_url, last_sign_in_at, email')
            .eq('user_id', data.user_id)
            .single();

          if (!profileError && profileData) {
            data.profiles = profileData;
          }
        }

        if (error) throw error;
        return data;
      } catch (err) {
        // If that fails, try without metadata
        console.warn('Falling back to update without metadata in select:', err);
        const { data, error } = await supabase
          .from('team_members')
          .update(teamMember)
          .eq('id', id)
          .select('*')
          .single();

        // If successful and user_id is not null, fetch profile data
        if (!error && data && data.user_id) {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('user_id, full_name, avatar_url, last_sign_in_at, email')
            .eq('user_id', data.user_id)
            .single();

          if (!profileError && profileData) {
            data.profiles = profileData;
          }
        }

        if (error) throw error;
        return data;
      }
    },
    `Failed to update team member with ID ${id}`
  );
};

/**
 * Delete a team member
 */
export const deleteTeamMember = async (id: string, userId?: string, checkUsageLimit = true): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      // Get the team member to check company_id
      const { data: teamMember, error: fetchError } = await supabase
        .from('team_members')
        .select('*')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      // Delete the team member
      const { error } = await supabase
        .from('team_members')
        .delete()
        .eq('id', id);

      if (error) throw error;

      // If usage limit checking is enabled and userId is provided, decrement the usage counter
      if (checkUsageLimit && userId) {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

        try {
          // Decrement the team members count
          const { error } = await supabase.rpc('increment_usage_counter', {
            p_user_id: userId,
            p_month_year: currentMonth,
            p_counter_name: 'team_members_count',
            p_increment_by: -1
          });

          if (error) {
            console.error('Failed to decrement usage counter:', error);
            // Don't throw here, we still want to return success even if tracking fails
          }
        } catch (error) {
          console.error('Failed to decrement usage counter:', error);
          // Don't throw here, we still want to return success even if tracking fails
        }
      }

      return true;
    },
    `Failed to delete team member with ID ${id}`,
    false // Return false as fallback
  );
};

/**
 * Accept an invitation with auto-signup if user doesn't exist
 */
export const acceptInvitationWithAutoSignup = async (token: string, userId?: string): Promise<EnhancedTeamMember> => {
  return safeDbOperation(
    async () => {
      // First, get invitation details
      const invitationDetails = await getInvitationDetails(token);
      
      if (!invitationDetails) {
        throw new Error('Invalid or expired invitation token');
      }

      let finalUserId = userId;
      let needsPasswordSetup = false;

      // If no user is logged in, create account from invitation metadata
      if (!finalUserId && invitationDetails.metadata?.email && invitationDetails.metadata?.name) {
        try {
          const signupResult = await signUpFromInvitation({
            email: invitationDetails.metadata.email,
            fullName: invitationDetails.metadata.name,
            invitationToken: token
          });

          if (signupResult.user) {
            finalUserId = signupResult.user.id;
            needsPasswordSetup = true;
            
            // If manual confirmation was needed, try signing in again
            if (signupResult.needsManualConfirmation) {
              // Wait a moment for the confirmation to process
              await new Promise(resolve => setTimeout(resolve, 2000));
              
              // Try signing in again
              try {
                const { data: retrySignIn, error: retryError } = await supabase.auth.signInWithPassword({
                  email: invitationDetails.metadata.email,
                  password: signupResult.tempPassword,
                });
                
                if (retryError) {
                  console.error('Retry sign-in failed:', retryError);
                }
              } catch (retryErr) {
                console.error('Retry sign-in error:', retryErr);
              }
            }
            
            // Verify the user is signed in
            const { data: session } = await supabase.auth.getSession();
            if (!session.session) {
              console.warn('User not properly signed in after signup');
            }
          }
        } catch (signupError) {
          console.error('Auto-signup failed:', signupError);
          throw new Error('Failed to create account from invitation');
        }
      }

      if (!finalUserId) {
        throw new Error('Unable to determine user for invitation acceptance');
      }

      // Now accept the invitation with the user ID
      try {
        const { data, error } = await supabase
          .from('team_members')
          .update({
            user_id: finalUserId,
            status: 'active',
            invitation_token: null
          })
          .eq('invitation_token', token)
          .select(`
            *,
            metadata
          `)
          .single();

        if (error) throw error;

        // Fetch profile data
        if (data && data.user_id) {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('user_id, full_name, avatar_url, last_sign_in_at, email')
            .eq('user_id', data.user_id)
            .single();

          if (!profileError && profileData) {
            data.profiles = profileData;
          }
        }

        // Add flag to indicate if password setup is needed
        data.needsPasswordSetup = needsPasswordSetup;

        return data;
      } catch (err) {
        console.error('Failed to accept invitation:', err);
        throw new Error('Failed to accept invitation');
      }
    },
    `Failed to accept invitation with token ${token}`
  );
};


