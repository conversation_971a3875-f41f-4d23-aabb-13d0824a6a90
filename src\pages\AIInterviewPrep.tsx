import React from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BrainCircuit, Lock } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

const AIInterviewPrep = () => {
  const navigate = useNavigate();

  return (
    <div className="space-y-8">
      <Card className="border-2 border-dashed border-gray-300 bg-gray-50">
        <CardHeader className="text-center">
          <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-primary/10 mb-4">
            <BrainCircuit className="h-10 w-10 text-primary" />
          </div>
          <CardTitle className="text-2xl">AI Interview Preparation</CardTitle>
          <CardDescription className="text-lg">
            This feature is coming soon to Pro plan subscribers
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center space-y-6">
          <div className="space-y-2">
            <p className="text-gray-600">
              The AI Interview Preparation feature will allow you to:
            </p>
            <ul className="text-left mx-auto max-w-md space-y-2 text-gray-600">
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Generate tailored interview questions based on job requirements</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Create structured interview guides for different positions</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Get AI-powered suggestions for evaluating candidate responses</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Access a library of best-practice interview techniques</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="text-primary">•</span>
                <span>Standardize your interview process across your organization</span>
              </li>
            </ul>
          </div>

          <div className="flex justify-center gap-4">
            <Button
              variant="outline"
              onClick={() => navigate('/dashboard/pricing')}
              className="gap-2"
            >
              <Lock className="h-4 w-4" />
              Upgrade to Pro
            </Button>
            <Button
              variant="default"
              onClick={() => navigate('/dashboard')}
            >
              Return to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Wrap the component with the DashboardLayout
const AIInterviewPrepWithLayout = () => (
  <DashboardLayout>
    <AIInterviewPrep />
  </DashboardLayout>
);

export default AIInterviewPrepWithLayout;
