/**
 * Data validation and sanitization utilities
 */

import { z } from 'zod';

/**
 * Sanitize a string by removing HTML tags and trimming whitespace
 */
export function sanitizeString(input: string): string {
  if (!input) return '';

  // Remove HTML tags
  const withoutTags = input.replace(/<[^>]*>/g, '');

  // Trim whitespace
  return withoutTags.trim();
}

/**
 * Sanitize an object by applying sanitizeString to all string properties
 */
export function sanitizeObject<T extends Record<string, any>>(obj: T): T {
  if (!obj) return obj;

  const result = { ...obj };

  for (const key in result) {
    if (typeof result[key] === 'string') {
      result[key] = sanitizeString(result[key]);
    } else if (typeof result[key] === 'object' && result[key] !== null) {
      result[key] = sanitizeObject(result[key]);
    }
  }

  return result;
}

/**
 * Validate data against a schema and sanitize it
 */
export function validateAndSanitize<T>(schema: z.ZodType<T>, data: unknown): {
  success: boolean;
  data?: T;
  errors?: z.ZodError
} {
  try {
    // Parse and validate with Zod
    const validData = schema.parse(data);

    // Sanitize the validated data
    const sanitizedData = sanitizeObject(validData);

    return { success: true, data: sanitizedData };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { success: false, errors: error };
    }

    throw error;
  }
}

/**
 * Common validation schemas
 */

// Email validation schema
export const emailSchema = z.string().email('Invalid email address');

// URL validation schema
export const urlSchema = z.string().url('Invalid URL');

// Phone validation schema (basic)
export const phoneSchema = z.string().regex(/^\+?[0-9\s\-()]{7,20}$/, 'Invalid phone number');

// UUID validation schema
export const uuidSchema = z.string().uuid('Invalid UUID');

// Profile validation schema
export const profileSchema = z.object({
  user_id: uuidSchema,
  full_name: z.string().min(1, 'Name is required').max(100),
  avatar_url: z.string().optional().nullable(), // Allow any string format for avatar URLs
  company_name: z.string().max(100).optional().nullable(),
  job_title: z.string().max(100).optional().nullable(),
  email: emailSchema.optional().nullable(), // Make email optional for profile updates
  phone: z.string().optional().nullable(), // Make phone less strict
  subscription_tier: z.enum(['starter', 'growth', 'pro']).default('starter'),
  subscription_status: z.enum(['active', 'inactive', 'cancelled', 'expired']).default('inactive'),
  subscription_end_date: z.string().optional().nullable(),
  platform_admin: z.boolean().default(false),
  metadata: z.any().optional() // Allow any metadata
});

// Company validation schema
export const companySchema = z.object({
  user_id: uuidSchema,
  name: z.string().min(1, 'Company name is required').max(100),
  logo_url: urlSchema.optional().nullable(),
  website: urlSchema.optional().nullable(),
  industry: z.string().max(100).optional().nullable(),
  size: z.string().max(50).optional().nullable(),
  description: z.string().max(1000).optional().nullable(),
  location: z.string().max(100).optional().nullable()
});

// Job validation schema
export const jobSchema = z.object({
  user_id: uuidSchema,
  company_id: uuidSchema.optional().nullable(),
  title: z.string().min(1, 'Job title is required').max(100),
  description: z.string().min(1, 'Job description is required').max(5000),
  requirements: z.string().max(2000).optional().nullable(),
  location: z.string().max(100).optional().nullable(),
  salary_range: z.string().max(100).optional().nullable(),
  job_type: z.string().max(50).optional().nullable(),
  experience_level: z.string().max(50).optional().nullable(),
  status: z.enum(['draft', 'active', 'closed']).default('draft')
});

// Candidate validation schema
export const candidateSchema = z.object({
  user_id: uuidSchema,
  job_id: uuidSchema.optional().nullable(),
  first_name: z.string().min(1, 'First name is required').max(50),
  last_name: z.string().min(1, 'Last name is required').max(50),
  email: emailSchema,
  phone: phoneSchema.optional().nullable(),
  cv_url: urlSchema.optional().nullable(),
  linkedin_url: urlSchema.optional().nullable(),
  status: z.enum(['new', 'reviewing', 'screening', 'interviewed', 'offer', 'hired', 'rejected']).default('new'),
  score: z.number().min(0).max(100).optional().nullable(),
  notes: z.string().max(1000).optional().nullable()
});

// Team member validation schema
export const teamMemberSchema = z.object({
  company_id: uuidSchema,
  user_id: uuidSchema.optional().nullable(),
  email: emailSchema,
  role: z.enum(['owner', 'admin', 'member']),
  status: z.enum(['pending', 'active', 'inactive']).default('pending'),
  invitation_token: z.string().optional().nullable()
});

// Notification validation schema
export const notificationSchema = z.object({
  user_id: uuidSchema,
  title: z.string().min(1, 'Title is required').max(100),
  message: z.string().min(1, 'Message is required').max(500),
  type: z.string().max(50).optional().nullable(),
  read: z.boolean().default(false)
});

// Subscription validation schema
export const subscriptionSchema = z.object({
  user_id: uuidSchema,
  tier: z.enum(['starter', 'growth', 'pro']),
  status: z.enum(['active', 'inactive', 'cancelled', 'expired']),
  start_date: z.string(),
  end_date: z.string().optional().nullable(),
  payment_provider: z.string().optional().nullable(),
  payment_id: z.string().optional().nullable()
});
