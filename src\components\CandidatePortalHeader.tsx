import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { 
  Search, 
  User, 
  LogOut, 
  Briefcase, 
  Home,
  ChevronDown,
  Menu,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useState } from 'react';

export const CandidatePortalHeader = () => {
  const { user, isAuthenticated, logout } = useAuth();
  const location = useLocation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase();
  };

  const handleLogout = () => {
    logout();
    setMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <header className="bg-gradient-to-r from-gray-900 to-black shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <img src="/logo.png" alt="Sourcio.ai Logo" className="h-8 w-auto" />
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              to="/jobs"
              className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                isActive('/jobs')
                  ? 'bg-gray-700 text-white'
                  : 'text-gray-300 hover:bg-gray-700 hover:text-white'
              }`}
            >
              <Search className="h-4 w-4 mr-2" />
              Browse Jobs
            </Link>

            {isAuthenticated && user?.userType === 'candidate' && (
              <Link
                to="/candidate-dashboard"
                className={`flex items-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  isActive('/candidate-dashboard')
                    ? 'bg-gray-700 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
              >
                <Briefcase className="h-4 w-4 mr-2" />
                My Applications
              </Link>
            )}
          </nav>

          {/* Desktop Auth Section */}
          <div className="hidden md:flex items-center space-x-4">
            {isAuthenticated ? (
              user?.userType === 'candidate' ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="relative h-10 text-white hover:bg-gray-700 rounded-full border border-gray-600 px-3 flex items-center">
                      <Avatar className="h-8 w-8 mr-2">
                        <AvatarImage
                          src={user?.avatarUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.name || 'User')}&background=374151&color=fff&size=150`}
                          alt={user?.name || 'User'}
                        />
                        <AvatarFallback className="bg-gray-600 text-white">
                          {user?.name ? getInitials(user.name) : 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <span className="hidden sm:block">{user?.name}</span>
                      <ChevronDown className="h-4 w-4 ml-1" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-56" align="end">
                    <DropdownMenuItem asChild>
                      <Link to="/candidate-dashboard" className="flex items-center">
                        <User className="mr-2 h-4 w-4" />
                        Dashboard
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      Sign Out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <div className="flex items-center space-x-3">
                  <span className="text-gray-300 text-sm">Logged in as Recruiter</span>
                  <Button variant="outline" size="sm" onClick={handleLogout} className="border-gray-600 text-gray-300 hover:bg-gray-700">
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
              )
            ) : (
              <div className="flex items-center space-x-3">
                <Link to="/candidate-signup">
                  <Button variant="secondary" size="sm" className="bg-white text-gray-900 hover:bg-gray-100">
                    Join as Candidate
                  </Button>
                </Link>
                <Link to="/login">
                  <Button variant="secondary" size="sm" className="bg-white text-gray-900 hover:bg-gray-100">
                    Sign In
                  </Button>
                </Link>
              </div>
            )}
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="icon"
              onClick={toggleMobileMenu}
              className="text-gray-300 hover:bg-gray-700"
            >
              {mobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-gray-800 rounded-lg mt-2 mb-4">
              <Link
                to="/jobs"
                className={`flex items-center px-3 py-2 rounded-md text-base font-medium ${
                  isActive('/jobs')
                    ? 'bg-gray-700 text-white'
                    : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                }`}
                onClick={() => setMobileMenuOpen(false)}
              >
                <Search className="h-5 w-5 mr-3" />
                Browse Jobs
              </Link>

              {isAuthenticated && user?.userType === 'candidate' && (
                <Link
                  to="/candidate-dashboard"
                  className={`flex items-center px-3 py-2 rounded-md text-base font-medium ${
                    isActive('/candidate-dashboard')
                      ? 'bg-gray-700 text-white'
                      : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                  }`}
                  onClick={() => setMobileMenuOpen(false)}
                >
                  <Briefcase className="h-5 w-5 mr-3" />
                  My Applications
                </Link>
              )}

              <div className="border-t border-gray-600 pt-4 mt-4">
                {isAuthenticated ? (
                  user?.userType === 'candidate' ? (
                    <div className="space-y-1">
                      <div className="flex items-center px-3 py-2 text-gray-300">
                        <Avatar className="h-8 w-8 mr-3">
                          <AvatarImage
                            src={user?.avatarUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(user?.name || 'User')}&background=374151&color=fff&size=150`}
                            alt={user?.name || 'User'}
                          />
                          <AvatarFallback className="bg-gray-600 text-white">
                            {user?.name ? getInitials(user.name) : 'U'}
                          </AvatarFallback>
                        </Avatar>
                        <span>{user?.name}</span>
                      </div>
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-gray-300 hover:bg-gray-700"
                        onClick={handleLogout}
                      >
                        <LogOut className="h-5 w-5 mr-3" />
                        Sign Out
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-2">
                      <div className="px-3 py-2 text-gray-300 text-sm">Logged in as Recruiter</div>
                      <Button
                        variant="ghost"
                        className="w-full justify-start text-gray-300 hover:bg-gray-700"
                        onClick={handleLogout}
                      >
                        <LogOut className="h-5 w-5 mr-3" />
                        Sign Out
                      </Button>
                    </div>
                  )
                ) : (
                  <div className="space-y-2">
                    <Link to="/candidate-signup" onClick={() => setMobileMenuOpen(false)}>
                      <Button variant="secondary" className="w-full bg-white text-gray-900 hover:bg-gray-100">
                        Join as Candidate
                      </Button>
                    </Link>
                    <Link to="/login" onClick={() => setMobileMenuOpen(false)}>
                      <Button variant="secondary" className="w-full bg-white text-gray-900 hover:bg-gray-100">
                        Sign In
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};


