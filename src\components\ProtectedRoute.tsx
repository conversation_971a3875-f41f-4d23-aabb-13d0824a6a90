import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { ResourceType, ActionType, RoleType } from '@/contexts/PermissionsContext';
import PermissionGuard from './PermissionGuard';

interface ProtectedRouteProps {
  children: React.ReactNode;
  resource?: ResourceType;
  action?: ActionType;
  requiredRoles?: RoleType[];
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  resource,
  action,
  requiredRoles
}) => {
  // Use PermissionGuard for both authentication and permission checks
  return (
    <PermissionGuard
      resource={resource}
      action={action}
      requiredRoles={requiredRoles}
    >
      {children}
    </PermissionGuard>
  );
};

export default ProtectedRoute;
