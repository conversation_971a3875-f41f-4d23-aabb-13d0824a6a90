import { Share2, <PERSON>ed<PERSON>, Twitter, Facebook, Link } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";

interface JobSocialShareProps {
  jobId: string;
  jobTitle: string;
  companyName: string;
  location?: string;
  jobType?: string;
  experienceLevel?: string;
  salaryMin?: number;
  salaryMax?: number;
  salaryCurrency?: string;
  department?: string;
  description?: string;
}

export const JobSocialShare = ({ 
  jobId, 
  jobTitle, 
  companyName,
  location,
  jobType,
  experienceLevel,
  salaryMin,
  salaryMax,
  salaryCurrency = 'USD',
  department,
  description
}: JobSocialShareProps) => {
  const { toast } = useToast();
  const jobUrl = `${window.location.origin}/jobs/${jobId}`;
  const safeCompanyName = companyName || 'Company';
  
  // Format salary
  const formatSalary = () => {
    if (salaryMin && salaryMax) {
      return `${salaryCurrency} ${salaryMin.toLocaleString()} - ${salaryMax.toLocaleString()}`;
    } else if (salaryMin) {
      return `${salaryCurrency} ${salaryMin.toLocaleString()}+`;
    } else if (salaryMax) {
      return `Up to ${salaryCurrency} ${salaryMax.toLocaleString()}`;
    }
    return null;
  };

  const salary = formatSalary();
  
  // Create comprehensive LinkedIn share text
  const createLinkedInText = () => {
    let text = `🚀 EXCITING JOB OPPORTUNITY!\n\n`;
    text += `📋 Position: ${jobTitle}\n`;
    text += `🏢 Company: ${safeCompanyName}\n`;
    
    if (department) text += `🏛️ Department: ${department}\n`;
    if (location) text += `📍 Location: ${location}\n`;
    if (jobType) text += `⏰ Type: ${jobType}\n`;
    if (experienceLevel) text += `🎓 Experience: ${experienceLevel}\n`;
    if (salary) text += `💰 Salary: ${salary}\n`;
    
    text += `\n`;
    
    if (description) {
      const shortDesc = description.length > 200 ? description.substring(0, 200) + '...' : description;
      text += `📝 About the role:\n${shortDesc}\n\n`;
    }
    
    text += `🔗 Apply here: ${jobUrl}\n\n`;
    text += `Don't miss out on this amazing opportunity! 🌟\n\n`;
    text += `#JobOpportunity #Hiring #Career #${safeCompanyName.replace(/\s+/g, '')} #Jobs`;
    
    if (department) text += ` #${department.replace(/\s+/g, '')}`;
    if (jobType) text += ` #${jobType.replace(/\s+/g, '').replace('-', '')}`;
    
    return text;
  };

  const shareOnLinkedIn = () => {
    const linkedInText = createLinkedInText();
    const linkedInUrl = `https://www.linkedin.com/feed/?shareActive=true&text=${encodeURIComponent(linkedInText)}`;
    window.open(linkedInUrl, '_blank');
  };

  const shareOnTwitter = () => {
    // Twitter has character limits, so keep it shorter
    let twitterText = `🚀 ${jobTitle} at ${safeCompanyName}\n`;
    if (location) twitterText += `📍 ${location}\n`;
    if (salary) twitterText += `💰 ${salary}\n`;
    twitterText += `\n${jobUrl}\n\n#JobOpportunity #Hiring #Career`;
    
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(twitterText)}`;
    window.open(twitterUrl, '_blank');
  };

  const copyLink = async () => {
    await navigator.clipboard.writeText(jobUrl);
    toast({ title: "Link copied to clipboard!" });
  };

  return (
    <div className="flex gap-2">
      <Button variant="outline" size="sm" onClick={shareOnLinkedIn}>
        <Linkedin className="mr-2 h-4 w-4" /> LinkedIn
      </Button>
      <Button variant="outline" size="sm" onClick={copyLink}>
        <Link className="mr-2 h-4 w-4" /> Copy Link
      </Button>
    </div>
  );
};






