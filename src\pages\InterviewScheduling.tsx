import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { CalendarClock, Plus, ArrowLeft, UserRound } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useCompanyContext } from '@/contexts/CompanyContext';
import { useInterviews, useGenerateDummyInterviews } from '@/hooks/use-interviews';
import { Interview } from '@/services/supabase/interviews';

import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import PlanGuard from '@/components/guards/PlanGuard';
import InterviewCalendar from '@/components/interviews/InterviewCalendar';
import InterviewList from '@/components/interviews/InterviewList';
import InterviewDetails from '@/components/interviews/InterviewDetails';
import ScheduleInterviewForm from '@/components/interviews/ScheduleInterviewForm';
import CandidateInterviewView from '@/components/interviews/CandidateInterviewView';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';

const InterviewScheduling = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { activeCompany } = useCompanyContext();
  const { data: interviews, isLoading, refetch } = useInterviews();
  const { mutate: generateDummyData, isPending: isGenerating } = useGenerateDummyInterviews();

  const [activeTab, setActiveTab] = useState('calendar');
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [selectedInterview, setSelectedInterview] = useState<Interview | null>(null);
  const [isScheduling, setIsScheduling] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [hasDummyData, setHasDummyData] = useState(false);

  // Check if there's any interview data
  useEffect(() => {
    if (interviews && interviews.length > 0) {
      setHasDummyData(true);
    }
  }, [interviews]);

  // Handle date selection
  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
  };

  // Handle interview selection
  const handleInterviewSelect = (interview: Interview) => {
    navigate(`/dashboard/interviews/${interview.id}`);
  };

  // Handle scheduling new interview
  const handleScheduleNew = () => {
    setSelectedInterview(null);
    setIsScheduling(true);
    setIsEditing(false);
  };

  // Handle edit interview
  const handleEditInterview = () => {
    setIsEditing(true);
  };

  // Handle delete interview
  const handleDeleteInterview = () => {
    setSelectedInterview(null);
    refetch();
  };

  // Handle back button
  const handleBack = () => {
    if (isScheduling || isEditing) {
      setIsScheduling(false);
      setIsEditing(false);
    } else if (selectedInterview) {
      setSelectedInterview(null);
    }
  };

  // Handle generate dummy data
  const handleGenerateDummyData = () => {
    if (activeCompany) {
      generateDummyData(activeCompany.id, {
        onSuccess: () => {
          setHasDummyData(true);
          refetch();
        }
      });
    }
  };

  // Determine what to render in the main content area
  const renderMainContent = () => {
    if (isScheduling) {
      return (
        <div className="space-y-4">
          <Button variant="ghost" onClick={handleBack} className="mb-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <ScheduleInterviewForm
            onSuccess={() => {
              setIsScheduling(false);
              refetch();
            }}
          />
        </div>
      );
    }

    if (isEditing && selectedInterview) {
      return (
        <div className="space-y-4">
          <Button variant="ghost" onClick={handleBack} className="mb-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <ScheduleInterviewForm
            onSuccess={() => {
              setIsEditing(false);
              refetch();
            }}
            preselectedCandidateId={selectedInterview.candidate_id}
            preselectedJobId={selectedInterview.job_id}
          />
        </div>
      );
    }

    if (selectedInterview) {
      return (
        <div className="space-y-4">
          <Button variant="ghost" onClick={handleBack} className="mb-2">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <InterviewDetails
            interviewId={selectedInterview.id}
            onEdit={handleEditInterview}
            onDelete={handleDeleteInterview}
          />
        </div>
      );
    }

    // Remove the hasDummyData logic and always show the tabs
    return (
      <Tabs value={activeTab} onValueChange={setActiveTab} defaultValue="calendar">
        <TabsList className="mb-4">
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="list">List View</TabsTrigger>
        </TabsList>
        <TabsContent value="calendar">
          <InterviewCalendar
            interviews={interviews || []}
            onDateSelect={handleDateSelect}
            onInterviewSelect={handleInterviewSelect}
          />
        </TabsContent>
        <TabsContent value="list">
          <InterviewList
            interviews={interviews || []}
            isLoading={isLoading}
            onInterviewSelect={handleInterviewSelect}
            onScheduleNew={handleScheduleNew}
          />
        </TabsContent>
      </Tabs>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Interview Scheduling</h1>
          <p className="text-gray-500">Schedule and manage candidate interviews</p>
        </div>
        {!isScheduling && !isEditing && !selectedInterview && hasDummyData && (
          <Button onClick={handleScheduleNew}>
            <CalendarClock className="h-4 w-4 mr-2" />
            Schedule New
          </Button>
        )}
      </div>

      <Separator />

      {renderMainContent()}
    </div>
  );
};

// Wrap the component with the DashboardLayout and PlanGuard
const InterviewSchedulingWithLayout = () => (
  <DashboardLayout>
    <PlanGuard feature="INTERVIEW_SCHEDULING" featureName="Interview Scheduling">
      <InterviewScheduling />
    </PlanGuard>
  </DashboardLayout>
);

export default InterviewSchedulingWithLayout;
