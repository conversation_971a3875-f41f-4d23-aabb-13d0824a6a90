import React, { createContext, useContext, useEffect, useState } from 'react';
import { PAYPAL_SDK_URL } from '@/config/paypal';

interface PayPalScriptContextType {
  isLoaded: boolean;
  isLoading: boolean;
  failedToLoad: boolean;
  reload: () => void;
}

const PayPalScriptContext = createContext<PayPalScriptContextType>({
  isLoaded: false,
  isLoading: false,
  failedToLoad: false,
  reload: () => {},
});

export const usePayPalScriptContext = () => useContext(PayPalScriptContext);

interface PayPalScriptProviderProps {
  children: React.ReactNode;
  options?: {
    'data-client-token'?: string;
    'data-page-type'?: string;
    'data-user-id-token'?: string;
  };
}

export const PayPalScriptProvider: React.FC<PayPalScriptProviderProps> = ({ 
  children,
  options = {}
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [failedToLoad, setFailedToLoad] = useState(false);
  const [scriptId] = useState(`paypal-script-${Math.random().toString(36).substring(7)}`);

  const loadScript = () => {
    // Skip if already loading or loaded
    if (isLoading || isLoaded) return;

    // Check if script already exists
    const existingScript = document.getElementById(scriptId);
    if (existingScript) {
      setIsLoaded(true);
      return;
    }

    setIsLoading(true);
    setFailedToLoad(false);

    // Create script element
    const script = document.createElement('script');
    script.id = scriptId;
    script.src = PAYPAL_SDK_URL;
    script.async = true;
    
    // Add custom data attributes
    Object.entries(options).forEach(([key, value]) => {
      if (value) script.setAttribute(key, value);
    });

    // Handle script load events
    script.onload = () => {
      console.log('PayPal SDK loaded successfully');
      setIsLoaded(true);
      setIsLoading(false);
    };

    script.onerror = () => {
      console.error('Failed to load PayPal SDK');
      setFailedToLoad(true);
      setIsLoading(false);
      
      // Remove the failed script
      document.body.removeChild(script);
    };

    // Add script to document
    document.body.appendChild(script);
  };

  // Load script on mount
  useEffect(() => {
    loadScript();

    // Cleanup on unmount
    return () => {
      const script = document.getElementById(scriptId);
      if (script) {
        document.body.removeChild(script);
      }
    };
  }, []);

  // Reload function for retrying if load fails
  const reload = () => {
    if (isLoaded) {
      const script = document.getElementById(scriptId);
      if (script) {
        document.body.removeChild(script);
      }
      setIsLoaded(false);
    }
    
    setIsLoading(false);
    setFailedToLoad(false);
    loadScript();
  };

  const contextValue = {
    isLoaded,
    isLoading,
    failedToLoad,
    reload,
  };

  return (
    <PayPalScriptContext.Provider value={contextValue}>
      {children}
    </PayPalScriptContext.Provider>
  );
};

export default PayPalScriptProvider;
