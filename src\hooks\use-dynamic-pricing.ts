import { useQuery } from '@tanstack/react-query';
import { getPlanFeaturesConfig } from '@/services/supabase/planFeatures';
import { PRICING, FEATURES } from '@/config/paypal';

export interface DynamicPricingFeature {
  text: string;
  highlighted?: boolean;
  enabled: boolean;
  featureKey?: string;
}

export interface DynamicPricingTier {
  title: string;
  price: number;
  currency: string;
  period: string;
  description: string;
  features: DynamicPricingFeature[];
  enabledFeatures: Record<string, boolean>;
  planKey: 'STARTER' | 'GROWTH' | 'PRO';
}

/**
 * Hook to get dynamic pricing information with features from database
 */
export const useDynamicPricing = () => {
  // Fetch plan features from database
  const { data: dbPlanFeatures, isLoading } = useQuery({
    queryKey: ['plan-features'],
    queryFn: getPlanFeaturesConfig,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Feature display mapping - maps feature keys to user-friendly descriptions
  const featureDisplayMap: Record<string, { text: string; highlighted?: boolean }> = {
    [FEATURES.CV_UPLOAD]: { text: "CV Upload & Processing", highlighted: true },
    [FEATURES.JOB_POSTING]: { text: "Job Posting & Management" },
    [FEATURES.COMPANY_MANAGEMENT]: { text: "Company Profile Management" },
    [FEATURES.CV_EVALUATION]: { text: "Automatic CV Evaluation", highlighted: true },
    [FEATURES.REPORTS]: { text: "Basic Reports & Analytics" },
    [FEATURES.INTERVIEW_SCHEDULING]: { text: "Interview Scheduling", highlighted: true },
    [FEATURES.TEAM_MANAGEMENT]: { text: "Team Member Management" },
    [FEATURES.CUSTOM_SCORING]: { text: "Custom Scoring Rules", highlighted: true },
    [FEATURES.ADVANCED_ANALYTICS]: { text: "Advanced Analytics & Insights" },
    [FEATURES.API_ACCESS]: { text: "API Access", highlighted: true },
    [FEATURES.WHITE_LABEL]: { text: "White-label Solution" },
    [FEATURES.PRIORITY_SUPPORT]: { text: "Priority Support" }
  };

  // Plan-specific additional features (usage limits, etc.)
  const planSpecificFeatures: Record<string, DynamicPricingFeature[]> = {
    STARTER: [
      { text: "Upload up to 50 CVs per month", highlighted: true, enabled: true },
      { text: "Post 1 job at a time", enabled: true },
      { text: "1 company profile", enabled: true },
    ],
    GROWTH: [
      { text: "Upload up to 500 CVs per month", highlighted: true, enabled: true },
      { text: "Post up to 5 jobs at a time", highlighted: true, enabled: true },
      { text: "Manage up to 5 company profiles", enabled: true },
    ],
    PRO: [
      { text: "Unlimited CV uploads", highlighted: true, enabled: true },
      { text: "Unlimited job postings", highlighted: true, enabled: true },
      { text: "Unlimited company profiles", enabled: true },
    ]
  };

  const getDynamicPricingTiers = (): DynamicPricingTier[] => {
    const planFeatures = dbPlanFeatures || {
      STARTER: {},
      GROWTH: {},
      PRO: {}
    };

    return [
      {
        title: "Starter Plan",
        price: PRICING.STARTER.price,
        currency: PRICING.STARTER.currency,
        period: PRICING.STARTER.period,
        description: "Perfect for small recruitment agencies getting started",
        planKey: 'STARTER',
        enabledFeatures: planFeatures.STARTER || {},
        features: [
          // Plan-specific features first
          ...planSpecificFeatures.STARTER,
          // Then dynamic features from database
          ...Object.entries(featureDisplayMap).map(([featureKey, display]) => ({
            ...display,
            enabled: planFeatures.STARTER?.[featureKey] || false,
            featureKey
          }))
        ]
      },
      {
        title: "Growth Plan",
        price: PRICING.GROWTH.price,
        currency: PRICING.GROWTH.currency,
        period: PRICING.GROWTH.period,
        description: "Ideal for growing agencies with multiple clients",
        planKey: 'GROWTH',
        enabledFeatures: planFeatures.GROWTH || {},
        features: [
          // Plan-specific features first
          ...planSpecificFeatures.GROWTH,
          // Then dynamic features from database
          ...Object.entries(featureDisplayMap).map(([featureKey, display]) => ({
            ...display,
            enabled: planFeatures.GROWTH?.[featureKey] || false,
            featureKey
          }))
        ]
      },
      {
        title: "Pro Plan",
        price: PRICING.PRO.price,
        currency: PRICING.PRO.currency,
        period: PRICING.PRO.period,
        description: "Complete solution for enterprise recruitment teams",
        planKey: 'PRO',
        enabledFeatures: planFeatures.PRO || {},
        features: [
          // Plan-specific features first
          ...planSpecificFeatures.PRO,
          // Then dynamic features from database
          ...Object.entries(featureDisplayMap).map(([featureKey, display]) => ({
            ...display,
            enabled: planFeatures.PRO?.[featureKey] || false,
            featureKey
          }))
        ]
      }
    ];
  };

  return {
    pricingTiers: getDynamicPricingTiers(),
    isLoading,
    hasDbFeatures: !!dbPlanFeatures
  };
};

/**
 * Get feature comparison for all plans - only show features available on at least one plan
 */
export const useFeatureComparison = () => {
  const { pricingTiers, isLoading } = useDynamicPricing();

  const getFeatureComparison = () => {
    if (isLoading || !pricingTiers.length) return [];

    // Get all unique features across all plans
    const allFeatureKeys = new Set<string>();
    pricingTiers.forEach(tier => {
      tier.features.forEach(feature => {
        if (feature.featureKey) {
          allFeatureKeys.add(feature.featureKey);
        }
      });
    });

    // Create comparison matrix, but filter out features disabled on ALL plans
    return Array.from(allFeatureKeys)
      .map(featureKey => {
        const featureDisplay = pricingTiers[0].features.find(f => f.featureKey === featureKey);
        const availability = {
          STARTER: pricingTiers[0].enabledFeatures[featureKey] || false,
          GROWTH: pricingTiers[1].enabledFeatures[featureKey] || false,
          PRO: pricingTiers[2].enabledFeatures[featureKey] || false,
        };

        return {
          featureKey,
          name: featureDisplay?.text || featureKey,
          highlighted: featureDisplay?.highlighted || false,
          availability,
          // Check if feature is enabled on at least one plan
          hasAnyEnabled: Object.values(availability).some(enabled => enabled)
        };
      })
      // Filter out features that are disabled on ALL plans
      .filter(feature => feature.hasAnyEnabled)
      .map(({ hasAnyEnabled, ...feature }) => feature); // Remove the helper property
  };

  return {
    featureComparison: getFeatureComparison(),
    isLoading
  };
};
