# Plan-Based Feature System

This document explains how to use the plan-based feature restriction system in the Sourcio.ai application.

## Overview

The plan-based feature system allows you to control which features are available to users based on their subscription plan (Starter, Growth, Pro). This enables you to create tiered pricing with different feature sets for each plan.

## Key Components

### 1. Feature Configuration (`src/config/paypal.ts`)

The feature configuration defines:
- Available features (`FEATURES` constant)
- Which features are enabled for each plan (`PLAN_FEATURES` constant)

```typescript
export const FEATURES = {
  CV_UPLOAD: 'cv_upload',
  JOB_POSTING: 'job_posting',
  INTERVIEW_SCHEDULING: 'interview_scheduling',
  TEAM_MANAGEMENT: 'team_management',
  // ... more features
} as const;

export const PLAN_FEATURES = {
  STARTER: {
    [FEATURES.CV_UPLOAD]: true,
    [FEATURES.INTERVIEW_SCHEDULING]: false, // Disabled for starter
    // ... other features
  },
  GROWTH: {
    [FEATURES.CV_UPLOAD]: true,
    [FEATURES.INTERVIEW_SCHEDULING]: true, // Enabled for growth+
    // ... other features
  },
  // ... other plans
};
```

### 2. PlanGuard Component (`src/components/guards/PlanGuard.tsx`)

Use this component to restrict access to features based on the user's plan:

```tsx
import PlanGuard from '@/components/guards/PlanGuard';

// Basic usage
<PlanGuard feature="INTERVIEW_SCHEDULING" featureName="Interview Scheduling">
  <InterviewSchedulingComponent />
</PlanGuard>

// Compact version (shows inline message instead of full card)
<PlanGuard feature="TEAM_MANAGEMENT" compact>
  <TeamManagementButton />
</PlanGuard>

// Custom fallback
<PlanGuard 
  feature="CUSTOM_SCORING" 
  fallback={<div>Custom scoring not available</div>}
>
  <CustomScoringComponent />
</PlanGuard>
```

### 3. usePlanFeatures Hook (`src/hooks/use-plan-features.ts`)

Use this hook to check feature availability in your components:

```tsx
import { usePlanFeatures } from '@/hooks/use-plan-features';

function MyComponent() {
  const { hasFeature, currentPlan, getMinimumPlan } = usePlanFeatures();
  
  const canScheduleInterviews = hasFeature('INTERVIEW_SCHEDULING');
  const requiredPlan = getMinimumPlan('TEAM_MANAGEMENT');
  
  return (
    <div>
      {canScheduleInterviews && (
        <button>Schedule Interview</button>
      )}
      <p>Team management requires {requiredPlan} plan</p>
    </div>
  );
}
```

## How to Add a New Feature

1. **Add the feature to the FEATURES constant:**
```typescript
export const FEATURES = {
  // ... existing features
  MY_NEW_FEATURE: 'my_new_feature',
} as const;
```

2. **Configure which plans have access:**
```typescript
export const PLAN_FEATURES = {
  STARTER: {
    // ... existing features
    [FEATURES.MY_NEW_FEATURE]: false, // Not available
  },
  GROWTH: {
    // ... existing features
    [FEATURES.MY_NEW_FEATURE]: false, // Not available
  },
  PRO: {
    // ... existing features
    [FEATURES.MY_NEW_FEATURE]: true, // Available
  }
};
```

3. **Protect the feature in your component:**
```tsx
<PlanGuard feature="MY_NEW_FEATURE" featureName="My New Feature">
  <MyNewFeatureComponent />
</PlanGuard>
```

## Current Feature Configuration

### Starter Plan ($49/month)
- ✅ CV Upload (up to 50/month)
- ✅ Job Posting (1 at a time)
- ✅ Company Management (1 profile)
- ✅ CV Evaluation
- ✅ Basic Reports
- ❌ Interview Scheduling
- ❌ Team Management
- ❌ Custom Scoring
- ❌ Advanced Analytics

### Growth Plan ($99/month)
- ✅ CV Upload (up to 500/month)
- ✅ Job Posting (up to 5 at a time)
- ✅ Company Management (up to 5 profiles)
- ✅ CV Evaluation
- ✅ Reports
- ✅ Interview Scheduling
- ✅ Team Management
- ❌ Custom Scoring
- ✅ Advanced Analytics

### Pro Plan ($199/month)
- ✅ All features enabled
- ✅ Unlimited usage limits
- ✅ API Access
- ✅ White-label solution
- ✅ Priority Support

## Examples

### Conditional Navigation
The sidebar automatically shows/hides navigation items based on plan features:

```tsx
// In DashboardSidebar.tsx
{hasInterviewScheduling && (
  <SidebarItem
    icon={CalendarClock}
    label="Interview Scheduling"
    href="/dashboard/interviews"
    badge={currentPlan === 'starter' ? 'Growth+' : undefined}
  />
)}
```

### Feature Showcase
View the plan features showcase in Settings > Plan Features to see all available features and their plan requirements.

### Page-Level Protection
Protect entire pages by wrapping them with PlanGuard:

```tsx
// In InterviewScheduling.tsx
const InterviewSchedulingWithLayout = () => (
  <DashboardLayout>
    <PlanGuard feature="INTERVIEW_SCHEDULING" featureName="Interview Scheduling">
      <InterviewScheduling />
    </PlanGuard>
  </DashboardLayout>
);
```

## Best Practices

1. **Use descriptive feature names** in the FEATURES constant
2. **Always provide a featureName** when using PlanGuard for better UX
3. **Use compact mode** for inline restrictions (buttons, small components)
4. **Check features early** in components to avoid unnecessary rendering
5. **Update pricing page** when adding new features to plans
6. **Test with different plan levels** to ensure restrictions work correctly

## Testing

To test the feature system:

1. Change your user's subscription tier in the database
2. Use the debug information in the PlanFeatureShowcase component (development mode)
3. Check the browser console for feature availability logs
4. Navigate to Settings > Plan Features to see current feature status

## Migration from ProPlanGuard

If you have existing ProPlanGuard components, replace them with PlanGuard:

```tsx
// Old
<ProPlanGuard featureName="Interview Scheduling">
  <Component />
</ProPlanGuard>

// New
<PlanGuard feature="INTERVIEW_SCHEDULING" featureName="Interview Scheduling">
  <Component />
</PlanGuard>
```

This new system is more flexible and allows for granular control over which plans have access to which features.
