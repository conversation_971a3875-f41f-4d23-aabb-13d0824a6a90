import { supabase } from '@/lib/supabase';
import { safeDbOperation } from '@/lib/errorHandling';
import { createNotification } from '@/services/supabase/notifications';
import {
  sendCandidateStatusUpdateEmail,
  sendRejectionEmail,
  sendOfferEmail
} from '@/services/email/candidateNotifications';

/**
 * Status management interfaces
 */
export interface StatusTransition {
  from: string;
  to: string;
  candidateId: string;
  assignmentId?: string;
  reason?: string;
  notes?: string;
}

export interface StatusRule {
  trigger: 'assignment' | 'interview_scheduled' | 'interview_completed' | 'manual';
  fromStatus: string[];
  toStatus: string;
  autoApply: boolean;
  requiresApproval?: boolean;
}

/**
 * Default status transition rules
 */
const DEFAULT_STATUS_RULES: StatusRule[] = [
  {
    trigger: 'assignment',
    fromStatus: ['new'],
    toStatus: 'screening',
    autoApply: true
  },
  {
    trigger: 'interview_scheduled',
    fromStatus: ['screening'],
    toStatus: 'interview',
    autoApply: true
  },
  {
    trigger: 'interview_completed',
    fromStatus: ['interview'],
    toStatus: 'offer',
    autoApply: false,
    requiresApproval: true
  }
];

/**
 * Auto-status manager class
 */
export class StatusManager {
  private rules: StatusRule[];

  constructor(customRules?: StatusRule[]) {
    this.rules = customRules || DEFAULT_STATUS_RULES;
  }

  /**
   * Apply automatic status transitions based on triggers
   */
  async applyAutoStatus(
    trigger: StatusRule['trigger'],
    candidateId: string,
    assignmentId?: string,
    context?: any
  ): Promise<boolean> {
    return safeDbOperation(
      async () => {
        // Get current candidate status
        const { data: candidate, error: candidateError } = await supabase
          .from('candidates')
          .select('status, name')
          .eq('id', candidateId)
          .single();

        if (candidateError || !candidate) {
          console.error('Failed to fetch candidate:', candidateError);
          return false;
        }

        // Find applicable rules
        const applicableRules = this.rules.filter(rule => 
          rule.trigger === trigger && 
          rule.fromStatus.includes(candidate.status) &&
          rule.autoApply
        );

        if (applicableRules.length === 0) {
          console.log(`No applicable auto-status rules for trigger: ${trigger}, current status: ${candidate.status}`);
          return false;
        }

        // Apply the first applicable rule
        const rule = applicableRules[0];
        
        // Update candidate status
        const { error: updateError } = await supabase
          .from('candidates')
          .update({ 
            status: rule.toStatus,
            updated_at: new Date().toISOString()
          })
          .eq('id', candidateId);

        if (updateError) {
          console.error('Failed to update candidate status:', updateError);
          return false;
        }

        // Update assignment status if provided
        if (assignmentId) {
          await supabase
            .from('candidate_assignments')
            .update({ 
              status: rule.toStatus,
              updated_at: new Date().toISOString()
            })
            .eq('id', assignmentId);
        }

        // Create status transition log
        await this.logStatusTransition({
          from: candidate.status,
          to: rule.toStatus,
          candidateId,
          assignmentId,
          reason: `Auto-transition triggered by: ${trigger}`
        });

        // Send notifications
        await this.sendStatusChangeNotifications(candidateId, candidate.name, candidate.status, rule.toStatus, context);

        console.log(`Auto-status applied: ${candidate.status} → ${rule.toStatus} for candidate ${candidateId}`);
        return true;
      },
      'Failed to apply auto-status',
      false
    );
  }

  /**
   * Manual status transition with validation
   */
  async transitionStatus(
    candidateId: string,
    newStatus: string,
    userId: string,
    notes?: string,
    assignmentId?: string
  ): Promise<boolean> {
    return safeDbOperation(
      async () => {
        // Get current candidate status
        const { data: candidate, error: candidateError } = await supabase
          .from('candidates')
          .select('status, name')
          .eq('id', candidateId)
          .single();

        if (candidateError || !candidate) {
          throw new Error('Candidate not found');
        }

        // Validate transition
        if (!this.isValidTransition(candidate.status, newStatus)) {
          throw new Error(`Invalid status transition: ${candidate.status} → ${newStatus}`);
        }

        // Update candidate status
        const { error: updateError } = await supabase
          .from('candidates')
          .update({ 
            status: newStatus,
            updated_at: new Date().toISOString()
          })
          .eq('id', candidateId);

        if (updateError) throw updateError;

        // Update assignment status if provided
        if (assignmentId) {
          await supabase
            .from('candidate_assignments')
            .update({ 
              status: newStatus,
              notes: notes || null,
              updated_at: new Date().toISOString()
            })
            .eq('id', assignmentId);
        }

        // Log the transition
        await this.logStatusTransition({
          from: candidate.status,
          to: newStatus,
          candidateId,
          assignmentId,
          reason: 'Manual transition',
          notes
        });

        // Send notifications
        await this.sendStatusChangeNotifications(candidateId, candidate.name, candidate.status, newStatus);

        return true;
      },
      'Failed to transition status',
      false
    );
  }

  /**
   * Validate if a status transition is allowed
   */
  private isValidTransition(fromStatus: string, toStatus: string): boolean {
    const validTransitions: Record<string, string[]> = {
      'new': ['screening', 'rejected'],
      'screening': ['interview', 'rejected'],
      'interview': ['offer', 'rejected'],
      'offer': ['hired', 'rejected'],
      'hired': [], // Final state
      'rejected': [] // Final state
    };

    return validTransitions[fromStatus]?.includes(toStatus) || false;
  }

  /**
   * Log status transitions for audit trail
   */
  private async logStatusTransition(transition: StatusTransition): Promise<void> {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      
      await supabase
        .from('status_transitions')
        .insert({
          candidate_id: transition.candidateId,
          assignment_id: transition.assignmentId,
          from_status: transition.from,
          to_status: transition.to,
          reason: transition.reason,
          notes: transition.notes,
          changed_by: user?.id,
          changed_at: new Date().toISOString()
        });
    } catch (error) {
      console.error('Failed to log status transition:', error);
      // Don't throw - logging failure shouldn't break the main operation
    }
  }

  /**
   * Send notifications for status changes
   */
  private async sendStatusChangeNotifications(
    candidateId: string,
    candidateName: string,
    fromStatus: string,
    toStatus: string,
    context?: any
  ): Promise<void> {
    try {
      // Get candidate and assignment details for notification context
      const { data: candidate } = await supabase
        .from('candidates')
        .select('email, name')
        .eq('id', candidateId)
        .single();

      const { data: assignments } = await supabase
        .from('candidate_assignments')
        .select(`
          *,
          jobs:job_id (title),
          companies:company_id (name, user_id)
        `)
        .eq('candidate_id', candidateId);

      if (!assignments || assignments.length === 0) return;

      // Notify company owners about status changes
      for (const assignment of assignments) {
        const companyUserId = assignment.companies?.user_id;
        if (companyUserId) {
          await createNotification({
            user_id: companyUserId,
            title: 'Candidate Status Updated',
            message: `${candidateName} status changed from ${fromStatus} to ${toStatus} for ${assignment.jobs?.title}`,
            type: 'info',
            link: `/dashboard/cvs/${candidateId}`
          });
        }
      }

      // Send email notifications to candidate
      if (candidate?.email && assignments.length > 0) {
        const assignment = assignments[0]; // Use first assignment for email context
        const jobTitle = assignment.jobs?.title || 'Unknown Position';
        const companyName = assignment.companies?.name || 'Unknown Company';

        const notificationData = {
          candidateEmail: candidate.email,
          candidateName: candidate.name || candidateName,
          jobTitle,
          companyName,
          oldStatus: fromStatus,
          newStatus: toStatus,
          message: context?.message,
          nextSteps: context?.nextSteps
        };

        // Send appropriate email based on status
        if (toStatus === 'rejected') {
          await sendRejectionEmail({
            ...notificationData,
            message: context?.message
          });
        } else if (toStatus === 'offer') {
          await sendOfferEmail({
            ...notificationData,
            message: context?.message,
            nextSteps: context?.nextSteps
          });
        } else {
          await sendCandidateStatusUpdateEmail(notificationData);
        }
      }

    } catch (error) {
      console.error('Failed to send status change notifications:', error);
      // Don't throw - notification failure shouldn't break the main operation
    }
  }

  /**
   * Get status transition history for a candidate
   */
  async getStatusHistory(candidateId: string): Promise<any[]> {
    return safeDbOperation(
      async () => {
        const { data, error } = await supabase
          .from('status_transitions')
          .select(`
            *,
            changed_by_user:changed_by (
              email,
              user_metadata
            )
          `)
          .eq('candidate_id', candidateId)
          .order('changed_at', { ascending: false });

        if (error) throw error;
        return data || [];
      },
      'Failed to fetch status history',
      []
    );
  }

  /**
   * Bulk status update for multiple candidates
   */
  async bulkStatusUpdate(
    candidateIds: string[],
    newStatus: string,
    userId: string,
    reason?: string
  ): Promise<{ successful: number; failed: number }> {
    let successful = 0;
    let failed = 0;

    for (const candidateId of candidateIds) {
      try {
        const success = await this.transitionStatus(candidateId, newStatus, userId, reason);
        if (success) {
          successful++;
        } else {
          failed++;
        }
      } catch (error) {
        console.error(`Failed to update status for candidate ${candidateId}:`, error);
        failed++;
      }
    }

    return { successful, failed };
  }
}

// Export singleton instance
export const statusManager = new StatusManager();

// Export helper functions
export const applyAutoStatus = statusManager.applyAutoStatus.bind(statusManager);
export const transitionStatus = statusManager.transitionStatus.bind(statusManager);
export const getStatusHistory = statusManager.getStatusHistory.bind(statusManager);
export const bulkStatusUpdate = statusManager.bulkStatusUpdate.bind(statusManager);
