import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import * as interviewService from '@/services/supabase/interviews';
import { Interview, InterviewParticipant, InterviewFeedback } from '@/services/supabase/interviews';
import { toast } from '@/components/ui/use-toast';
import { createInterviewWithNotifications } from '@/services/supabase/interviews';
import { createInterviewFeedback } from '@/services/supabase/interview-feedback';
import { InterviewFeedback } from '@/services/supabase/interview-feedback';

// Query keys
const queryKeys = {
  interviews: 'interviews',
  interview: (id: string) => ['interview', id],
  participants: (interviewId: string) => ['interview-participants', interviewId],
  feedback: (interviewId: string) => ['interview-feedback', interviewId],
  upcomingInterviews: 'upcoming-interviews'
};

/**
 * Hook for fetching interviews
 */
export const useInterviews = (filters: {
  candidateId?: string;
  jobId?: string;
  companyId?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
} = {}) => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: [queryKeys.interviews, filters],
    queryFn: () => interviewService.getInterviews({
      userId: user?.id,
      ...filters
    }),
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

/**
 * Hook for fetching a single interview
 */
export const useInterview = (id: string) => {
  return useQuery({
    queryKey: queryKeys.interview(id),
    queryFn: () => interviewService.getInterviewById(id),
    enabled: !!id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

/**
 * Hook for creating an interview
 */
export const useCreateInterview = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (interview: Omit<Interview, 'id' | 'created_at' | 'updated_at'>) => 
      interviewService.createInterview(interview),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.interviews] });
      queryClient.invalidateQueries({ queryKey: [queryKeys.upcomingInterviews] });
      toast({
        title: 'Interview scheduled',
        description: 'The interview has been scheduled successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to schedule interview',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for updating an interview
 */
export const useUpdateInterview = (id: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (updates: Partial<Omit<Interview, 'id' | 'created_at' | 'updated_at'>>) => 
      interviewService.updateInterview(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.interviews] });
      queryClient.invalidateQueries({ queryKey: queryKeys.interview(id) });
      queryClient.invalidateQueries({ queryKey: [queryKeys.upcomingInterviews] });
      toast({
        title: 'Interview updated',
        description: 'The interview has been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to update interview',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for deleting an interview
 */
export const useDeleteInterview = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => interviewService.deleteInterview(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.interviews] });
      queryClient.invalidateQueries({ queryKey: [queryKeys.upcomingInterviews] });
      toast({
        title: 'Interview deleted',
        description: 'The interview has been deleted successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to delete interview',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for fetching interview participants
 */
export const useInterviewParticipants = (interviewId: string) => {
  return useQuery({
    queryKey: queryKeys.participants(interviewId),
    queryFn: () => interviewService.getInterviewParticipants(interviewId),
    enabled: !!interviewId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

/**
 * Hook for adding an interview participant
 */
export const useAddInterviewParticipant = (interviewId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (participant: Omit<InterviewParticipant, 'id' | 'created_at' | 'interview_id'>) => 
      interviewService.addInterviewParticipant({
        ...participant,
        interview_id: interviewId
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.participants(interviewId) });
      toast({
        title: 'Participant added',
        description: 'The participant has been added to the interview.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to add participant',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for updating an interview participant
 */
export const useUpdateInterviewParticipant = (interviewId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<InterviewParticipant, 'id' | 'created_at'>> }) => 
      interviewService.updateInterviewParticipant(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.participants(interviewId) });
      toast({
        title: 'Participant updated',
        description: 'The participant has been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to update participant',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for removing an interview participant
 */
export const useRemoveInterviewParticipant = (interviewId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => interviewService.removeInterviewParticipant(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.participants(interviewId) });
      toast({
        title: 'Participant removed',
        description: 'The participant has been removed from the interview.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to remove participant',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for fetching interview feedback
 */
export const useInterviewFeedback = (interviewId: string) => {
  return useQuery({
    queryKey: queryKeys.feedback(interviewId),
    queryFn: () => interviewService.getInterviewFeedback(interviewId),
    enabled: !!interviewId,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

/**
 * Hook for adding interview feedback
 */
export const useAddInterviewFeedback = (interviewId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (feedback: Omit<InterviewFeedback, 'id' | 'created_at' | 'updated_at' | 'interview_id'>) => 
      interviewService.addInterviewFeedback({
        ...feedback,
        interview_id: interviewId
      }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.feedback(interviewId) });
      toast({
        title: 'Feedback submitted',
        description: 'Your feedback has been submitted successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to submit feedback',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for updating interview feedback
 */
export const useUpdateInterviewFeedback = (interviewId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, updates }: { id: string, updates: Partial<Omit<InterviewFeedback, 'id' | 'created_at' | 'updated_at'>> }) => 
      interviewService.updateInterviewFeedback(id, updates),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.feedback(interviewId) });
      toast({
        title: 'Feedback updated',
        description: 'Your feedback has been updated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to update feedback',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for deleting interview feedback
 */
export const useDeleteInterviewFeedback = (interviewId: string) => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: (id: string) => interviewService.deleteInterviewFeedback(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: queryKeys.feedback(interviewId) });
      toast({
        title: 'Feedback deleted',
        description: 'The feedback has been deleted successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to delete feedback',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook for fetching upcoming interviews for dashboard
 */
export const useUpcomingInterviews = (limit: number = 5) => {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: [queryKeys.upcomingInterviews, limit],
    queryFn: () => interviewService.getUpcomingInterviews(user?.id || '', limit),
    enabled: !!user?.id,
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
};

/**
 * Hook for generating dummy interview data
 */
export const useGenerateDummyInterviews = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  return useMutation({
    mutationFn: (companyId: string) => 
      interviewService.generateDummyInterviews(user?.id || '', companyId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [queryKeys.interviews] });
      queryClient.invalidateQueries({ queryKey: [queryKeys.upcomingInterviews] });
      toast({
        title: 'Dummy data generated',
        description: 'Sample interview data has been generated successfully.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Failed to generate dummy data',
        description: error instanceof Error ? error.message : 'An unknown error occurred',
        variant: 'destructive',
      });
    },
  });
};

/**
 * Hook to create interview with notifications
 */
export const useCreateInterviewWithNotifications = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      interview, 
      interviewerIds 
    }: { 
      interview: Omit<Interview, 'id' | 'created_at' | 'updated_at'>;
      interviewerIds: string[];
    }) => {
      return await createInterviewWithNotifications(interview, interviewerIds);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['interviews'] });
      queryClient.invalidateQueries({ queryKey: ['interview-participants'] });
    }
  });
};

/**
 * Hook to create interview feedback
 */
export const useCreateInterviewFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (
      feedback: Omit<InterviewFeedback, 'id' | 'created_at' | 'updated_at' | 'user_name' | 'user_email'>
    ) => {
      return await createInterviewFeedback(feedback);
    },
    onSuccess: (data) => {
      if (data) {
        queryClient.invalidateQueries({ queryKey: ['interview-feedback', data.interview_id] });
        queryClient.invalidateQueries({ queryKey: ['interviews'] });
      }
    }
  });
};

