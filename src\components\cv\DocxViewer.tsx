import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON>, { DocViewerRenderers } from '@cyntler/react-doc-viewer';
import '@cyntler/react-doc-viewer/dist/index.css';
import { Loader2, AlertCircle } from 'lucide-react';

interface DocxViewerProps {
  url: string;
  fileName: string;
}

const DocxViewer: React.FC<DocxViewerProps> = ({ url, fileName }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Effect to fix iframe height
  useEffect(() => {
    // Add a style to target the iframe after component mounts
    const style = document.createElement('style');
    style.innerHTML = `
      #msdoc-iframe {
        height: 45em !important;
        min-height: 45em !important;
      }
      .react-pdf__Document, .react-pdf__Page {
        height: 45em !important;
        min-height: 45em !important;
      }
    `;
    document.head.appendChild(style);

    return () => {
      // Clean up when component unmounts
      if (document.head.contains(style)) {
        document.head.removeChild(style);
      }
    };
  }, []);

  // Effect to check URL validity
  useEffect(() => {
    // Log the URL for debugging
    console.log('DocxViewer received URL:', url);

    // Check if the URL is valid
    if (!url) {
      setError('No valid document URL available');
      setIsLoading(false);
      return;
    }

    // Create a test fetch to check if the URL is accessible
    const testFetch = async () => {
      try {
        const response = await fetch(url, { method: 'HEAD' });
        if (!response.ok) {
          setError(`Unable to access document (${response.status}: ${response.statusText})`);
        }
      } catch (err) {
        setError(`Error accessing document: ${err instanceof Error ? err.message : String(err)}`);
      } finally {
        setIsLoading(false);
      }
    };

    testFetch();
  }, [url]);

  // Handle loading state
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
        <p>Loading document...</p>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-4 text-center">
        <AlertCircle className="h-12 w-12 text-red-500 mb-2" />
        <p className="text-red-500 mb-2">{error}</p>
        <p className="mb-4">The document might be inaccessible due to CORS restrictions or storage permissions.</p>
        <a
          href={url}
          target="_blank"
          rel="noopener noreferrer"
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Open Document in New Tab
        </a>
      </div>
    );
  }

  return (
    <div className="h-full w-full overflow-auto" style={{ minHeight: '700px' }}>
      <DocViewer
        documents={[{ uri: url, fileName: fileName }]}
        pluginRenderers={DocViewerRenderers}
        style={{
          height: '45em', // Set a fixed height that works
          width: '100%',
          minHeight: '45em',
          display: 'flex',
          flexDirection: 'column'
        }}
        config={{
          header: {
            disableHeader: true, // We'll use our own header
          },
          pdfVerticalScrollByDefault: true, // Enable vertical scrolling
        }}
      />
    </div>
  );
};

export default DocxViewer;
