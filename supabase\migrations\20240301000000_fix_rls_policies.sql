-- Fix RLS policies to avoid infinite recursion and implement proper multi-tenant access

-- First, drop all existing policies that might be causing issues
DROP POLICY IF EXISTS "Users can view own team_members" ON team_members;
DROP POLICY IF EXISTS "Users can insert own team_members" ON team_members;
DROP POLICY IF EXISTS "Users can update own team_members" ON team_members;
DROP POLICY IF EXISTS "Users can delete own team_members" ON team_members;
DROP POLICY IF EXISTS "Admin bypass for team_members" ON team_members;

DROP POLICY IF EXISTS "Users can view own candidates" ON candidates;
DROP POLICY IF EXISTS "Users can insert own candidates" ON candidates;
DROP POLICY IF EXISTS "Users can update own candidates" ON candidates;
DROP POLICY IF EXISTS "Users can delete own candidates" ON candidates;
DROP POLICY IF EXISTS "Admin bypass for candidates" ON candidates;

DROP POLICY IF EXISTS "Users can view own jobs" ON jobs;
DROP POLICY IF EXISTS "Users can insert own jobs" ON jobs;
DROP POLICY IF EXISTS "Users can update own jobs" ON jobs;
DROP POLICY IF EXISTS "Users can delete own jobs" ON jobs;
DROP POLICY IF EXISTS "Admin bypass for jobs" ON jobs;

-- Make sure RLS is enabled for all tables
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE candidates ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE companies ENABLE ROW LEVEL SECURITY;

-- Create simple, non-recursive policies for team_members
CREATE POLICY "Users can view own team_members"
ON team_members FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own team_members"
ON team_members FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own team_members"
ON team_members FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own team_members"
ON team_members FOR DELETE
TO authenticated
USING (auth.uid() = user_id);

-- Create policies for candidates
CREATE POLICY "Users can view own candidates"
ON candidates FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can insert own candidates"
ON candidates FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own candidates"
ON candidates FOR UPDATE
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can delete own candidates"
ON candidates FOR DELETE
TO authenticated
USING (user_id = auth.uid());

-- Create policies for jobs
CREATE POLICY "Users can view own jobs"
ON jobs FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can insert own jobs"
ON jobs FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own jobs"
ON jobs FOR UPDATE
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can delete own jobs"
ON jobs FOR DELETE
TO authenticated
USING (user_id = auth.uid());

-- Create policies for companies
CREATE POLICY "Users can view own companies"
ON companies FOR SELECT
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can insert own companies"
ON companies FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update own companies"
ON companies FOR UPDATE
TO authenticated
USING (user_id = auth.uid());

CREATE POLICY "Users can delete own companies"
ON companies FOR DELETE
TO authenticated
USING (user_id = auth.uid());

-- Add a special policy for platform admins to view all data
-- This uses a direct check on the user ID to avoid recursion
CREATE POLICY "Platform admins can view all data"
ON candidates FOR SELECT
TO authenticated
USING (
  auth.uid() IN (
    '778d128d-606f-484e-b257-257390743df4'  -- Your admin ID
    -- Add more admin IDs here if needed, separated by commas
  )
);

CREATE POLICY "Platform admins can view all data"
ON jobs FOR SELECT
TO authenticated
USING (
  auth.uid() IN (
    '778d128d-606f-484e-b257-257390743df4'  -- Your admin ID
    -- Add more admin IDs here if needed, separated by commas
  )
);

CREATE POLICY "Platform admins can view all data"
ON companies FOR SELECT
TO authenticated
USING (
  auth.uid() IN (
    '778d128d-606f-484e-b257-257390743df4'  -- Your admin ID
    -- Add more admin IDs here if needed, separated by commas
  )
);

CREATE POLICY "Platform admins can view all data"
ON team_members FOR SELECT
TO authenticated
USING (
  auth.uid() IN (
    '778d128d-606f-484e-b257-257390743df4'  -- Your admin ID
    -- Add more admin IDs here if needed, separated by commas
  )
);

-- Update the seed data to associate with the current user
-- This ensures that when you log in, you'll see the seed data
UPDATE candidates SET user_id = auth.uid() WHERE user_id IS NULL;
UPDATE jobs SET user_id = auth.uid() WHERE user_id IS NULL;
UPDATE companies SET user_id = auth.uid() WHERE user_id IS NULL;
