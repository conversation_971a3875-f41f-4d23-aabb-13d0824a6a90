import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Building, Plus, Check, ChevronDown } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useCompanies } from '@/hooks/use-companies';
import { useToast } from '@/hooks/use-toast';
import { Skeleton } from '@/components/ui/skeleton';
import { useCompanyContext } from '@/contexts/CompanyContext';

/**
 * Company Avatar Component
 */
const CompanyAvatar = ({ company, size = 'h-6 w-6' }) => (
  <Avatar className={size}>
    {company.logo_url ? (
      <AvatarImage src={company.logo_url} alt={company.name} />
    ) : (
      <AvatarFallback className="bg-primary-gradient text-white text-xs">
        {company.name.charAt(0)}
      </AvatarFallback>
    )}
  </Avatar>
);

/**
 * Company Switcher Component
 */
const CompanySwitcher = () => {
  // Hooks - always called in the same order
  const navigate = useNavigate();
  const { toast } = useToast();
  const { data: companies = [], isLoading } = useCompanies();
  const [isOpen, setIsOpen] = useState(false);
  const { activeCompanyId, activeCompany, setActiveCompanyId } = useCompanyContext();

  // Set active company if there's only one
  useEffect(() => {
    if (companies.length === 1 && activeCompanyId !== companies[0].id) {
      setActiveCompanyId(companies[0].id);
    }
  }, [companies, activeCompanyId, setActiveCompanyId]);

  // Event handlers
  const handleSetActiveCompany = (id) => {
    setActiveCompanyId(id);
    const company = companies.find(c => c.id === id);
    toast({
      title: 'Company switched',
      description: `Now working with ${company?.name}`,
    });
    setIsOpen(false);
  };

  const handleCreateCompany = () => {
    navigate('/dashboard/companies');
    setIsOpen(false);
  };

  // Render loading state
  if (isLoading) {
    return (
      <div className="flex items-center space-x-2">
        <Skeleton className="h-8 w-8 rounded-full" />
        <Skeleton className="h-4 w-32" />
      </div>
    );
  }

  // Render create company button if no companies
  if (companies.length === 0) {
    return (
      <Button
        variant="outline"
        size="sm"
        className="text-gray-700 bg-white border-gray-200 hover:bg-gray-50"
        onClick={handleCreateCompany}
      >
        <Plus className="mr-2 h-4 w-4" />
        Create Company
      </Button>
    );
  }

  // Render single company view (no dropdown)
  if (companies.length === 1) {
    const company = companies[0];
    return (
      <div className="flex items-center space-x-2 px-3 py-1.5 text-sm text-gray-700 bg-white border border-gray-200 rounded-md">
        <CompanyAvatar company={company} />
        <span className="font-medium truncate max-w-[150px]">{company.name}</span>
      </div>
    );
  }

  // Render dropdown for multiple companies
  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="text-gray-700 bg-white border-gray-200 hover:bg-gray-50"
        >
          {activeCompany ? (
            <>
              <CompanyAvatar company={activeCompany} size="h-6 w-6 mr-2" />
              <span className="font-medium truncate max-w-[120px]">{activeCompany.name}</span>
            </>
          ) : (
            <>
              <Building className="mr-2 h-4 w-4" />
              <span>Select Company</span>
            </>
          )}
          <ChevronDown className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-56 bg-white border-gray-200 text-foreground shadow-lg rounded-lg animate-scale-in z-50">
        <DropdownMenuLabel className="text-gray-500 font-medium">
          Switch Company
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gray-200" />

        {companies.map((company) => (
          <DropdownMenuItem
            key={company.id}
            className="cursor-pointer"
            onClick={() => handleSetActiveCompany(company.id)}
          >
            <div className="flex items-center w-full">
              <CompanyAvatar company={company} size="h-6 w-6 mr-2" />
              <span className="flex-1 truncate">{company.name}</span>
              {company.id === activeCompanyId && (
                <Check className="h-4 w-4 text-green-500 ml-2" />
              )}
            </div>
          </DropdownMenuItem>
        ))}

        <DropdownMenuSeparator className="bg-gray-200" />
        <DropdownMenuItem className="cursor-pointer" onClick={handleCreateCompany}>
          <Plus className="mr-2 h-4 w-4" />
          <span>Create New Company</span>
        </DropdownMenuItem>
        <DropdownMenuItem className="cursor-pointer" onClick={() => navigate('/dashboard/companies')}>
          <Building className="mr-2 h-4 w-4" />
          <span>Manage Companies</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default CompanySwitcher;
