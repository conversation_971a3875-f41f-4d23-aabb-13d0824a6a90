import React, { useEffect, useState } from 'react';
import { usePara<PERSON>, <PERSON> } from "react-router-dom";
import { usePublicJob } from "@/hooks/use-jobs";
import { useAuth } from "@/contexts/AuthContext";
import { useHasAppliedToJob } from "@/hooks/use-candidate-portal";
import { JobSocialShare } from "@/components/JobSocialShare";
import { JobApplicationModal } from "@/components/JobApplicationModal";
import { CandidatePortalHeader } from "@/components/CandidatePortalHeader";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  MapPin,
  Clock,
  Building,
  DollarSign,
  Calendar,
  Users,
  Briefcase,
  GraduationCap,
  Star,
  Gift,
  CheckCircle,
  AlertCircle,
  Send,
  LogIn
} from "lucide-react";

export const PublicJobView = () => {
  const { jobId } = useParams();
  const { data: job, isLoading, error } = usePublicJob(jobId!);
  const { user, isAuthenticated } = useAuth();
  const { data: hasApplied = false } = useHasAppliedToJob(jobId!);
  const [showApplicationModal, setShowApplicationModal] = useState(false);

  // Update document title and meta tags
  useEffect(() => {
    if (job) {
      document.title = `${job.title} - ${job.company_name || 'Company'}`;
      
      // Clear existing meta tags
      const existingMetas = document.querySelectorAll('meta[property^="og:"], meta[name="description"]');
      existingMetas.forEach(meta => meta.remove());
      
      // Create rich description
      const salary = formatSalary();
      const location = job.location || 'Remote';
      const jobType = job.job_type || 'Full-time';
      const experience = job.experience_level || 'All levels';
      
      const richDescription = `${job.title} at ${job.company_name || 'Company'} | ${location} | ${jobType} | ${experience}${salary ? ` | ${salary}` : ''}${job.department ? ` | ${job.department}` : ''}

${job.description?.substring(0, 200) || 'Great job opportunity'}...

Apply now!`;
      
      // Add comprehensive Open Graph meta tags
      const metaTags = [
        { property: 'og:title', content: `${job.title} at ${job.company_name || 'Company'}` },
        { property: 'og:description', content: richDescription },
        { property: 'og:url', content: `${window.location.origin}/jobs/${job.id}` },
        { property: 'og:type', content: 'website' },
        { property: 'og:site_name', content: 'Sourcio.ai' },
        { property: 'og:image', content: job.companies?.logo_url || `${window.location.origin}/og-image.jpg` },
        { name: 'description', content: richDescription }
      ];

      metaTags.forEach(({ property, name, content }) => {
        const meta = document.createElement('meta');
        if (property) meta.setAttribute('property', property);
        if (name) meta.setAttribute('name', name);
        meta.setAttribute('content', content);
        document.head.appendChild(meta);
      });
    }
  }, [job]);

  // Helper function to format salary
  const formatSalary = () => {
    if (!job?.salary_min && !job?.salary_max) return null;
    
    const currency = job.salary_currency || 'USD';
    const period = job.salary_period || 'yearly';
    
    if (job.salary_min && job.salary_max) {
      return `${currency} ${job.salary_min.toLocaleString()} - ${job.salary_max.toLocaleString()} ${period}`;
    } else if (job.salary_min) {
      return `${currency} ${job.salary_min.toLocaleString()}+ ${period}`;
    } else if (job.salary_max) {
      return `Up to ${currency} ${job.salary_max.toLocaleString()} ${period}`;
    }
    return null;
  };

  // Helper function to parse array fields
  const parseArrayField = (field: string | string[] | null | undefined) => {
    if (!field) return [];
    if (Array.isArray(field)) return field;
    if (typeof field === 'string') {
      // Try to parse as JSON first, then split by newlines
      try {
        const parsed = JSON.parse(field);
        return Array.isArray(parsed) ? parsed : [field];
      } catch {
        return field.split('\n').filter(item => item.trim());
      }
    }
    return [];
  };

  // Helper function to get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 border-green-200';
      case 'closed': return 'bg-red-100 text-red-800 border-red-200';
      case 'draft': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading job details...</p>
        </div>
      </div>
    );
  }

  if (error || !job) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Job Not Found</h1>
          <p className="text-gray-600">The job you're looking for doesn't exist or has been removed.</p>
        </div>
      </div>
    );
  }

  const requirements = parseArrayField(job.requirements);
  const responsibilities = parseArrayField(job.responsibilities);
  const benefits = parseArrayField(job.benefits);
  const salary = formatSalary();

  return (
    <div className="min-h-screen bg-gray-50">
      <CandidatePortalHeader />
      <div className="max-w-6xl mx-auto px-6 py-8">
        {/* Header Section */}
        <Card className="mb-8 shadow-lg">
          <CardContent className="p-8">
            <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-6">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-4">
                  <h1 className="text-4xl font-bold text-gray-900">{job.title}</h1>
                  <Badge className={getStatusColor(job.status)}>
                    {job.status?.charAt(0).toUpperCase() + job.status?.slice(1)}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-2 mb-6">
                  <Building className="h-5 w-5 text-blue-600" />
                  <span className="text-xl font-semibold text-gray-800">
                    {job.company_name || job.companies?.name || 'Company'}
                  </span>
                </div>

                {/* Key Details Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                  {job.location && (
                    <div className="flex items-center gap-2 text-gray-600">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{job.location}</span>
                    </div>
                  )}
                  
                  {job.job_type && (
                    <div className="flex items-center gap-2 text-gray-600">
                      <Clock className="h-4 w-4 text-gray-500" />
                      <span>{job.job_type}</span>
                    </div>
                  )}
                  
                  {job.experience_level && (
                    <div className="flex items-center gap-2 text-gray-600">
                      <GraduationCap className="h-4 w-4 text-gray-500" />
                      <span>{job.experience_level}</span>
                    </div>
                  )}
                  
                  {salary && (
                    <div className="flex items-center gap-2 text-gray-600">
                      <DollarSign className="h-4 w-4 text-gray-500" />
                      <span>{salary}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-2 text-gray-600">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span>Posted: {new Date(job.created_at).toLocaleDateString()}</span>
                  </div>
                  
                  {job.closing_date && (
                    <div className="flex items-center gap-2 text-gray-600">
                      <AlertCircle className="h-4 w-4 text-gray-500" />
                      <span>Deadline: {new Date(job.closing_date).toLocaleDateString()}</span>
                    </div>
                  )}
                </div>

                {/* Department and Additional Info */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {job.department && (
                    <Badge variant="secondary" className="text-sm">
                      <Briefcase className="mr-1 h-3 w-3" />
                      {job.department}
                    </Badge>
                  )}
                  {job.location_type && (
                    <Badge variant="outline" className="text-sm">
                      {job.location_type}
                    </Badge>
                  )}
                  {job.salary_range && (
                    <Badge variant="outline" className="text-sm">
                      {job.salary_range}
                    </Badge>
                  )}
                </div>

                {/* Apply Button Section */}
                <div className="flex flex-col sm:flex-row gap-3 mb-6">
                  {isAuthenticated ? (
                    user?.userType === 'candidate' ? (
                      hasApplied ? (
                        <Button disabled className="bg-green-100 text-green-800 border-green-200">
                          <CheckCircle className="mr-2 h-4 w-4" />
                          Application Submitted
                        </Button>
                      ) : (
                        <Button
                          onClick={() => setShowApplicationModal(true)}
                          className="bg-blue-600 hover:bg-blue-700"
                        >
                          <Send className="mr-2 h-4 w-4" />
                          Apply Now
                        </Button>
                      )
                    ) : (
                      <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                        <p>You're logged in as a recruiter. Switch to a candidate account to apply for jobs.</p>
                      </div>
                    )
                  ) : (
                    <Link to="/candidate-signup">
                      <Button className="bg-blue-600 hover:bg-blue-700">
                        <Send className="mr-2 h-4 w-4" />
                        Apply Now
                      </Button>
                    </Link>
                  )}
                </div>
              </div>

              {/* Share Section */}
              <div className="lg:ml-6 flex-shrink-0">
                <JobSocialShare 
                  jobId={job.id}
                  jobTitle={job.title}
                  companyName={job.company_name || job.companies?.name || 'Company'}
                  location={job.location}
                  jobType={job.job_type}
                  experienceLevel={job.experience_level}
                  salaryMin={job.salary_min}
                  salaryMax={job.salary_max}
                  salaryCurrency={job.salary_currency || 'USD'}
                  department={job.department}
                  description={job.description}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Job Description */}
            <Card className="shadow-md">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Star className="h-5 w-5 text-blue-600" />
                  Job Description
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="prose max-w-none text-gray-700 whitespace-pre-wrap">
                  {job.description || 'No description available.'}
                </div>
              </CardContent>
            </Card>

            {/* Requirements */}
            {requirements.length > 0 && (
              <Card className="shadow-md">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    Requirements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {requirements.map((requirement, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{requirement}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Responsibilities */}
            {responsibilities.length > 0 && (
              <Card className="shadow-md">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <Briefcase className="h-5 w-5 text-purple-600" />
                    Responsibilities
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {responsibilities.map((responsibility, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="h-2 w-2 bg-purple-500 rounded-full mt-2 flex-shrink-0"></div>
                        <span className="text-gray-700">{responsibility}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Benefits */}
            {benefits.length > 0 && (
              <Card className="shadow-md">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-xl">
                    <Gift className="h-5 w-5 text-orange-600" />
                    Benefits & Perks
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {benefits.map((benefit, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <Gift className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{benefit}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Facts */}
            <Card className="shadow-md">
              <CardHeader>
                <CardTitle className="text-lg">Quick Facts</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {job.job_type && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Employment Type</p>
                    <p className="text-gray-900">{job.job_type}</p>
                  </div>
                )}
                
                {job.experience_level && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Experience Level</p>
                    <p className="text-gray-900">{job.experience_level}</p>
                  </div>
                )}
                
                {job.department && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Department</p>
                    <p className="text-gray-900">{job.department}</p>
                  </div>
                )}
                
                {salary && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Salary Range</p>
                    <p className="text-gray-900">{salary}</p>
                  </div>
                )}
                
                <Separator />
                
                <div>
                  <p className="text-sm font-medium text-gray-500">Posted Date</p>
                  <p className="text-gray-900">{new Date(job.created_at).toLocaleDateString()}</p>
                </div>
                
                {job.updated_at && job.updated_at !== job.created_at && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Last Updated</p>
                    <p className="text-gray-900">{new Date(job.updated_at).toLocaleDateString()}</p>
                  </div>
                )}
                
                {job.closing_date && (
                  <div>
                    <p className="text-sm font-medium text-gray-500">Application Deadline</p>
                    <p className="text-gray-900">{new Date(job.closing_date).toLocaleDateString()}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Company Info */}
            {(job.company_name || job.companies) && (
              <Card className="shadow-md">
                <CardHeader>
                  <CardTitle className="text-lg">About the Company</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-3 mb-3">
                    {job.companies?.logo_url && (
                      <img 
                        src={job.companies.logo_url} 
                        alt={`${job.company_name} logo`}
                        className="h-12 w-12 rounded-lg object-cover"
                      />
                    )}
                    <div>
                      <h3 className="font-semibold text-gray-900">
                        {job.company_name || job.companies?.name}
                      </h3>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Apply Section */}
            <Card className="shadow-md bg-blue-50 border-blue-200">
              <CardContent className="p-6 text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  Ready to Apply?
                </h3>
                <p className="text-gray-600 mb-4">
                  Don't miss out on this opportunity!
                </p>
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors">
                  Apply Now
                </button>
                <p className="text-xs text-gray-500 mt-2">
                  Click to start your application
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Application Modal */}
      {showApplicationModal && job && (
        <JobApplicationModal
          isOpen={showApplicationModal}
          onClose={() => setShowApplicationModal(false)}
          job={{
            id: job.id,
            title: job.title,
            company_name: job.company_name || job.companies?.name
          }}
        />
      )}
    </div>
  );
};
