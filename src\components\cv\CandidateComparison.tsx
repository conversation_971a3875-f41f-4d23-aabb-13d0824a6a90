import React, { useState } from 'react';
import {
  BarChart3,
  Award,
  Briefcase,
  GraduationCap,
  Star,
  ChevronDown,
  ChevronUp,
  Check,
  X
} from 'lucide-react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface Candidate {
  id: string;
  name: string;
  avatar?: string;
  overallMatch: number;
  skills: {
    name: string;
    match: number;
    required: boolean;
  }[];
  experience: {
    years: number;
    relevance: number;
  };
  education: {
    level: string;
    relevance: number;
  };
  strengths: string[];
  weaknesses: string[];
}

interface CandidateComparisonProps {
  candidates: Candidate[];
  position: string;
}

const CandidateComparison: React.FC<CandidateComparisonProps> = ({
  candidates,
  position
}) => {
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    skills: true,
    experience: false,
    education: false,
    strengths: false,
    weaknesses: false
  });

  // Toggle section expansion
  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Get color based on match score
  const getMatchColor = (score: number) => {
    if (score >= 90) return 'text-green-500';
    if (score >= 75) return 'text-blue-500';
    if (score >= 60) return 'text-amber-500';
    return 'text-red-500';
  };

  // Get progress bar color based on match score
  const getProgressColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 75) return 'bg-blue-500';
    if (score >= 60) return 'bg-amber-500';
    return 'bg-red-500';
  };

  // Sort candidates by overall match score
  const sortedCandidates = [...candidates].sort((a, b) => b.overallMatch - a.overallMatch);

  return (
    <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
      <CardHeader>
        <CardTitle className="text-gray-800 text-lg">Candidate Comparison for {position}</CardTitle>
      </CardHeader>
      <CardContent>
        {/* Candidate headers */}
        <div className="grid grid-cols-[200px_repeat(auto-fill,minmax(150px,1fr))] gap-4 mb-6">
          <div className="text-gray-500">Criteria</div>
          {sortedCandidates.map(candidate => (
            <div key={candidate.id} className="text-center">
              <Avatar className="h-16 w-16 mx-auto mb-2">
                <AvatarImage src="https://i0.wp.com/www.lifewaycenters.com/wp-content/uploads/2016/06/placeholder-150x150-1.png" alt={candidate.name} />
                <AvatarFallback className="bg-recruiter-blue text-white text-xl">
                  {candidate.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <h3 className="text-gray-800 font-medium">{candidate.name}</h3>
              <div className="flex items-center justify-center mt-1">
                <span className={`text-lg font-bold ${getMatchColor(candidate.overallMatch)}`}>
                  {candidate.overallMatch}%
                </span>
                <Star className={`h-4 w-4 ml-1 ${getMatchColor(candidate.overallMatch)}`} fill="currentColor" />
              </div>
            </div>
          ))}
        </div>

        {/* Skills comparison */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <Button
            variant="ghost"
            className="w-full flex justify-between items-center text-gray-800 hover:bg-gray-100 mb-2"
            onClick={() => toggleSection('skills')}
          >
            <div className="flex items-center">
              <Award className="mr-2 h-5 w-5 text-gray-500" />
              <span className="font-medium">Skills</span>
            </div>
            {expandedSections.skills ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>

          {expandedSections.skills && (
            <div className="space-y-4 mt-4">
              {/* Get all unique skills across candidates */}
              {Array.from(new Set(candidates.flatMap(c => c.skills.map(s => s.name)))).map(skillName => (
                <div key={skillName} className="grid grid-cols-[200px_repeat(auto-fill,minmax(150px,1fr))] gap-4 items-center">
                  <div className="text-gray-700 flex items-center">
                    {skillName}
                    {candidates.some(c => c.skills.find(s => s.name === skillName)?.required) && (
                      <Badge className="ml-2 bg-blue-500/20 text-blue-500 hover:bg-blue-500/30">
                        Required
                      </Badge>
                    )}
                  </div>

                  {sortedCandidates.map(candidate => {
                    const skill = candidate.skills.find(s => s.name === skillName);
                    return (
                      <div key={`${candidate.id}-${skillName}`} className="flex flex-col items-center">
                        {skill ? (
                          <>
                            <span className={`text-sm ${getMatchColor(skill.match)}`}>{skill.match}%</span>
                            <Progress
                              value={skill.match}
                              className="h-1.5 w-full mt-1"
                              indicatorClassName={getProgressColor(skill.match)}
                            />
                          </>
                        ) : (
                          <span className="text-gray-500">N/A</span>
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Experience comparison */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <Button
            variant="ghost"
            className="w-full flex justify-between items-center text-gray-800 hover:bg-gray-100 mb-2"
            onClick={() => toggleSection('experience')}
          >
            <div className="flex items-center">
              <Briefcase className="mr-2 h-5 w-5 text-gray-500" />
              <span className="font-medium">Experience</span>
            </div>
            {expandedSections.experience ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>

          {expandedSections.experience && (
            <div className="space-y-4 mt-4">
              {/* Years of experience */}
              <div className="grid grid-cols-[200px_repeat(auto-fill,minmax(150px,1fr))] gap-4 items-center">
                <div className="text-gray-700">Years of Experience</div>
                {sortedCandidates.map(candidate => (
                  <div key={`${candidate.id}-years`} className="text-center">
                    <span className="text-gray-800 font-medium">{candidate.experience.years} years</span>
                  </div>
                ))}
              </div>

              {/* Experience relevance */}
              <div className="grid grid-cols-[200px_repeat(auto-fill,minmax(150px,1fr))] gap-4 items-center">
                <div className="text-gray-700">Relevance</div>
                {sortedCandidates.map(candidate => (
                  <div key={`${candidate.id}-exp-relevance`} className="flex flex-col items-center">
                    <span className={`text-sm ${getMatchColor(candidate.experience.relevance)}`}>
                      {candidate.experience.relevance}%
                    </span>
                    <Progress
                      value={candidate.experience.relevance}
                      className="h-1.5 w-full mt-1"
                      indicatorClassName={getProgressColor(candidate.experience.relevance)}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Education comparison */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <Button
            variant="ghost"
            className="w-full flex justify-between items-center text-gray-800 hover:bg-gray-100 mb-2"
            onClick={() => toggleSection('education')}
          >
            <div className="flex items-center">
              <GraduationCap className="mr-2 h-5 w-5 text-gray-500" />
              <span className="font-medium">Education</span>
            </div>
            {expandedSections.education ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>

          {expandedSections.education && (
            <div className="space-y-4 mt-4">
              {/* Education level */}
              <div className="grid grid-cols-[200px_repeat(auto-fill,minmax(150px,1fr))] gap-4 items-center">
                <div className="text-gray-700">Education Level</div>
                {sortedCandidates.map(candidate => (
                  <div key={`${candidate.id}-edu-level`} className="text-center">
                    <span className="text-gray-800">{candidate.education.level}</span>
                  </div>
                ))}
              </div>

              {/* Education relevance */}
              <div className="grid grid-cols-[200px_repeat(auto-fill,minmax(150px,1fr))] gap-4 items-center">
                <div className="text-gray-700">Relevance</div>
                {sortedCandidates.map(candidate => (
                  <div key={`${candidate.id}-edu-relevance`} className="flex flex-col items-center">
                    <span className={`text-sm ${getMatchColor(candidate.education.relevance)}`}>
                      {candidate.education.relevance}%
                    </span>
                    <Progress
                      value={candidate.education.relevance}
                      className="h-1.5 w-full mt-1"
                      indicatorClassName={getProgressColor(candidate.education.relevance)}
                    />
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        {/* Strengths comparison */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <Button
            variant="ghost"
            className="w-full flex justify-between items-center text-gray-800 hover:bg-gray-100 mb-2"
            onClick={() => toggleSection('strengths')}
          >
            <div className="flex items-center">
              <Check className="mr-2 h-5 w-5 text-green-500" />
              <span className="font-medium">Strengths</span>
            </div>
            {expandedSections.strengths ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>

          {expandedSections.strengths && (
            <div className="grid grid-cols-[200px_repeat(auto-fill,minmax(150px,1fr))] gap-4 mt-4">
              <div className="text-gray-700">Key Strengths</div>
              {sortedCandidates.map(candidate => (
                <div key={`${candidate.id}-strengths`} className="space-y-2">
                  {candidate.strengths.map((strength, index) => (
                    <div key={index} className="flex items-start">
                      <Check className="h-4 w-4 text-green-500 mr-1 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{strength}</span>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Weaknesses comparison */}
        <div className="mb-6 border-b border-gray-200 pb-4">
          <Button
            variant="ghost"
            className="w-full flex justify-between items-center text-gray-800 hover:bg-gray-100 mb-2"
            onClick={() => toggleSection('weaknesses')}
          >
            <div className="flex items-center">
              <X className="mr-2 h-5 w-5 text-red-500" />
              <span className="font-medium">Areas for Improvement</span>
            </div>
            {expandedSections.weaknesses ? <ChevronUp className="h-5 w-5" /> : <ChevronDown className="h-5 w-5" />}
          </Button>

          {expandedSections.weaknesses && (
            <div className="grid grid-cols-[200px_repeat(auto-fill,minmax(150px,1fr))] gap-4 mt-4">
              <div className="text-gray-700">Improvement Areas</div>
              {sortedCandidates.map(candidate => (
                <div key={`${candidate.id}-weaknesses`} className="space-y-2">
                  {candidate.weaknesses.map((weakness, index) => (
                    <div key={index} className="flex items-start">
                      <X className="h-4 w-4 text-red-500 mr-1 mt-0.5 flex-shrink-0" />
                      <span className="text-gray-700 text-sm">{weakness}</span>
                    </div>
                  ))}
                </div>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CandidateComparison;

