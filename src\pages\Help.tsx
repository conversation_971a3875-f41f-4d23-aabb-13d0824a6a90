import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Search,
  FileText,
  Users,
  Building,
  BarChart3,
  Settings,
  CreditCard,
  Mail,
  MessageSquare,
  Video,
  BookOpen,
  Lightbulb,
  HelpCircle,
  Briefcase
} from 'lucide-react';

const Help = () => {
  const [searchQuery, setSearchQuery] = useState('');

  // Filter FAQs based on search query
  const filterFAQs = (faqs: { question: string; answer: string }[], query: string) => {
    if (!query) return faqs;

    const lowerCaseQuery = query.toLowerCase();
    return faqs.filter(
      faq =>
        faq.question.toLowerCase().includes(lowerCaseQuery) ||
        faq.answer.toLowerCase().includes(lowerCaseQuery)
    );
  };

  // FAQ data by category
  const faqData = {
    general: [
      {
        question: "What is Expert-Recruiter?",
        answer: "Expert-Recruiter is an AI-powered CV evaluation platform designed specifically for recruitment agencies. It helps you automatically extract information from CVs, evaluate candidates against job requirements, and manage your recruitment process more efficiently."
      },
      {
        question: "How do I get started with Expert-Recruiter?",
        answer: "To get started, create your first company profile, add job listings, and then upload candidate CVs. Our platform will automatically evaluate candidates against your job requirements and provide detailed reports."
      },
      {
        question: "What subscription plans are available?",
        answer: "We offer three subscription tiers: Starter ($49/month), Growth ($149/month), and Pro ($299/month). Each plan offers different features and limits for CV uploads, job postings, and company profiles. Visit the Subscription page for more details."
      },
      {
        question: "Is there a free trial available?",
        answer: "Yes, new users on the Starter plan can upload and evaluate 1 CV for free to test the platform's capabilities before committing to a subscription."
      }
    ],
    cvs: [
      {
        question: "How do I upload candidate CVs?",
        answer: "Navigate to the CVs section in the dashboard, click on 'Upload CV', and select the CV files you want to upload. You can upload multiple CVs at once and optionally associate them with specific jobs."
      },
      {
        question: "What file formats are supported for CV uploads?",
        answer: "Our platform supports PDF, DOCX, and DOC file formats for CV uploads. For best results, we recommend using PDF files as they maintain consistent formatting."
      },
      {
        question: "How does the CV evaluation work?",
        answer: "Our AI analyzes the content of each CV, extracting key information such as skills, experience, education, and other relevant details. It then compares this information against job requirements to provide a match score and detailed evaluation."
      },
      {
        question: "Can I evaluate a CV against multiple jobs?",
        answer: "Yes, you can evaluate a single CV against multiple jobs. Simply select the CV from your candidate list and click 'Evaluate' to choose which job or company to evaluate against."
      }
    ],
    jobs: [
      {
        question: "How do I create a new job listing?",
        answer: "Go to the Jobs section in the dashboard and click 'Add New Job'. Fill in the job details including title, description, requirements, and other relevant information. The more detailed your job description, the better our AI can match candidates."
      },
      {
        question: "Can I edit a job after creating it?",
        answer: "Yes, you can edit job listings at any time. Navigate to the specific job and click the 'Edit' button to modify any details."
      },
      {
        question: "How many jobs can I create?",
        answer: "The number of jobs you can create depends on your subscription plan: Starter (1 job), Growth (5 jobs), and Pro (unlimited jobs)."
      },
      {
        question: "Can I duplicate an existing job?",
        answer: "Yes, you can duplicate an existing job to create a similar listing quickly. Navigate to the job you want to duplicate, click the options menu, and select 'Duplicate'."
      }
    ],
    companies: [
      {
        question: "How do I add a new company?",
        answer: "Go to the Companies section in the dashboard and click 'Add New Company'. Fill in the company details including name, description, industry, and upload a company logo if available."
      },
      {
        question: "How many companies can I manage?",
        answer: "The number of company profiles you can manage depends on your subscription plan: Starter (1 company), Growth (5 companies), and Pro (unlimited companies)."
      },
      {
        question: "How do I switch between companies?",
        answer: "Users on the Growth or Pro plans can switch between companies using the company switcher in the header/sidebar. This will dynamically load the selected company's data and job listings."
      },
      {
        question: "Can I upload a company logo?",
        answer: "Yes, you can upload a company logo when creating or editing a company profile. We recommend using a square image with dimensions of at least 200x200 pixels for best results."
      }
    ],
    account: [
      {
        question: "How do I update my profile information?",
        answer: "Navigate to the Profile section in the dashboard where you can update your personal information, change your password, and manage your account settings."
      },
      {
        question: "How do I change my password?",
        answer: "Go to the Profile page and scroll down to the Account Settings section. Enter your current password and your new password, then click 'Update Password'."
      },
      {
        question: "How do I manage my subscription?",
        answer: "Visit the Subscription page in the dashboard to view your current plan, upgrade or downgrade your subscription, and manage your billing information."
      },
      {
        question: "How do I cancel my subscription?",
        answer: "To cancel your subscription, go to the Subscription page and click 'Cancel Subscription'. Your access will continue until the end of your current billing period."
      }
    ],
    billing: [
      {
        question: "What payment methods are accepted?",
        answer: "We accept all major credit cards including Visa, Mastercard, American Express, and Discover."
      },
      {
        question: "How often will I be billed?",
        answer: "Subscriptions are billed monthly or annually, depending on the plan you choose. Annual plans offer a discount compared to monthly billing."
      },
      {
        question: "How do I update my billing information?",
        answer: "Go to the Billing page in the dashboard where you can update your payment method, billing address, and other payment details."
      },
      {
        question: "How do I get a receipt for my payment?",
        answer: "Receipts are automatically sent to your email address after each payment. You can also view and download past invoices from the Billing page."
      }
    ]
  };

  // Filter FAQs based on search query
  const filteredFAQs = {
    general: filterFAQs(faqData.general, searchQuery),
    cvs: filterFAQs(faqData.cvs, searchQuery),
    jobs: filterFAQs(faqData.jobs, searchQuery),
    companies: filterFAQs(faqData.companies, searchQuery),
    account: filterFAQs(faqData.account, searchQuery),
    billing: filterFAQs(faqData.billing, searchQuery)
  };

  // Check if any FAQs match the search query
  const hasResults = Object.values(filteredFAQs).some(category => category.length > 0);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <h1 className="text-2xl font-bold text-gray-800">Help & Support</h1>
        </div>

        {/* Search Bar */}
        <div className="relative max-w-2xl mx-auto mb-8">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            type="text"
            placeholder="Search for help topics..."
            className="pl-10 bg-white border-gray-200 text-gray-800"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 px-2 text-gray-400 hover:text-gray-600"
              onClick={() => setSearchQuery('')}
            >
              Clear
            </Button>
          )}
        </div>

        {searchQuery && !hasResults && (
          <div className="text-center py-8">
            <HelpCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">No results found</h3>
            <p className="text-gray-500 max-w-md mx-auto">
              We couldn't find any help articles matching "{searchQuery}". Try using different keywords or contact our support team.
            </p>
          </div>
        )}

        {/* Quick Links */}
        {!searchQuery && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardContent className="p-6 text-center">
                <Video className="h-10 w-10 mx-auto mb-4 text-primary" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">Video Tutorials</h3>
                <p className="text-gray-500 mb-4">Watch step-by-step guides on how to use Expert-Recruiter</p>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="bg-primary hover:bg-primary/90">
                      Watch Tutorials
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Video Tutorials</DialogTitle>
                      <DialogDescription>
                        Our video tutorials are coming soon! We're currently working on creating comprehensive video guides to help you get the most out of Expert-Recruiter.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-center items-center py-6">
                      <Video className="h-16 w-16 text-primary opacity-70" />
                    </div>
                    <div className="text-center text-gray-600 mb-4">
                      Check back soon for step-by-step video tutorials on all platform features.
                    </div>
                    <div className="flex justify-end">
                      <DialogClose asChild>
                        <Button type="button" variant="secondary">Close</Button>
                      </DialogClose>
                    </div>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>

            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardContent className="p-6 text-center">
                <BookOpen className="h-10 w-10 mx-auto mb-4 text-primary" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">Documentation</h3>
                <p className="text-gray-500 mb-4">Browse our comprehensive documentation and guides</p>
                <Dialog>
                  <DialogTrigger asChild>
                    <Button className="bg-primary hover:bg-primary/90">
                      Read Docs
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Documentation</DialogTitle>
                      <DialogDescription>
                        Our documentation is coming soon! We're currently working on creating detailed guides and documentation for all Expert-Recruiter features.
                      </DialogDescription>
                    </DialogHeader>
                    <div className="flex justify-center items-center py-6">
                      <BookOpen className="h-16 w-16 text-primary opacity-70" />
                    </div>
                    <div className="text-center text-gray-600 mb-4">
                      Check back soon for comprehensive documentation on how to use the platform effectively.
                    </div>
                    <div className="flex justify-end">
                      <DialogClose asChild>
                        <Button type="button" variant="secondary">Close</Button>
                      </DialogClose>
                    </div>
                  </DialogContent>
                </Dialog>
              </CardContent>
            </Card>

            <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
              <CardContent className="p-6 text-center">
                <Mail className="h-10 w-10 mx-auto mb-4 text-primary" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">Contact Support</h3>
                <p className="text-gray-500 mb-4">Get help from our support team</p>
                <Button
                  className="bg-primary hover:bg-primary/90"
                  onClick={() => window.location.href = 'mailto:<EMAIL>'}
                >
                  Contact Us
                </Button>
              </CardContent>
            </Card>
          </div>
        )}

        {/* FAQ Tabs */}
        <Tabs defaultValue="general" className="w-full">
          <TabsList className="bg-white border-b border-gray-200 w-full justify-start mb-6 overflow-x-auto flex-nowrap">
            <TabsTrigger value="general" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              General
            </TabsTrigger>
            <TabsTrigger value="cvs" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              CV Management
            </TabsTrigger>
            <TabsTrigger value="jobs" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              Jobs
            </TabsTrigger>
            <TabsTrigger value="companies" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              Companies
            </TabsTrigger>
            <TabsTrigger value="account" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              Account
            </TabsTrigger>
            <TabsTrigger value="billing" className="data-[state=active]:bg-primary-gradient data-[state=active]:text-white">
              Billing
            </TabsTrigger>
          </TabsList>

          {/* General Tab */}
          <TabsContent value="general">
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center">
                  <Lightbulb className="mr-2 h-5 w-5 text-primary" />
                  General Questions
                </CardTitle>
              </CardHeader>
              <CardContent>
                {filteredFAQs.general.length > 0 ? (
                  <Accordion type="single" collapsible className="w-full">
                    {filteredFAQs.general.map((faq, index) => (
                      <AccordionItem key={index} value={`general-item-${index}`}>
                        <AccordionTrigger className="text-left font-medium text-gray-800 hover:text-primary">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-gray-600">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : (
                  <p className="text-gray-500 text-center py-4">No matching questions found in this category.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* CV Management Tab */}
          <TabsContent value="cvs">
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-primary" />
                  CV Management
                </CardTitle>
              </CardHeader>
              <CardContent>
                {filteredFAQs.cvs.length > 0 ? (
                  <Accordion type="single" collapsible className="w-full">
                    {filteredFAQs.cvs.map((faq, index) => (
                      <AccordionItem key={index} value={`cvs-item-${index}`}>
                        <AccordionTrigger className="text-left font-medium text-gray-800 hover:text-primary">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-gray-600">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : (
                  <p className="text-gray-500 text-center py-4">No matching questions found in this category.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Jobs Tab */}
          <TabsContent value="jobs">
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center">
                  <Briefcase className="mr-2 h-5 w-5 text-primary" />
                  Jobs
                </CardTitle>
              </CardHeader>
              <CardContent>
                {filteredFAQs.jobs.length > 0 ? (
                  <Accordion type="single" collapsible className="w-full">
                    {filteredFAQs.jobs.map((faq, index) => (
                      <AccordionItem key={index} value={`jobs-item-${index}`}>
                        <AccordionTrigger className="text-left font-medium text-gray-800 hover:text-primary">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-gray-600">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : (
                  <p className="text-gray-500 text-center py-4">No matching questions found in this category.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Companies Tab */}
          <TabsContent value="companies">
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center">
                  <Building className="mr-2 h-5 w-5 text-primary" />
                  Companies
                </CardTitle>
              </CardHeader>
              <CardContent>
                {filteredFAQs.companies.length > 0 ? (
                  <Accordion type="single" collapsible className="w-full">
                    {filteredFAQs.companies.map((faq, index) => (
                      <AccordionItem key={index} value={`companies-item-${index}`}>
                        <AccordionTrigger className="text-left font-medium text-gray-800 hover:text-primary">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-gray-600">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : (
                  <p className="text-gray-500 text-center py-4">No matching questions found in this category.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Account Tab */}
          <TabsContent value="account">
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center">
                  <Users className="mr-2 h-5 w-5 text-primary" />
                  Account
                </CardTitle>
              </CardHeader>
              <CardContent>
                {filteredFAQs.account.length > 0 ? (
                  <Accordion type="single" collapsible className="w-full">
                    {filteredFAQs.account.map((faq, index) => (
                      <AccordionItem key={index} value={`account-item-${index}`}>
                        <AccordionTrigger className="text-left font-medium text-gray-800 hover:text-primary">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-gray-600">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : (
                  <p className="text-gray-500 text-center py-4">No matching questions found in this category.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Billing Tab */}
          <TabsContent value="billing">
            <Card className="bg-white border-gray-200 shadow-sm">
              <CardHeader>
                <CardTitle className="text-gray-800 flex items-center">
                  <CreditCard className="mr-2 h-5 w-5 text-primary" />
                  Billing
                </CardTitle>
              </CardHeader>
              <CardContent>
                {filteredFAQs.billing.length > 0 ? (
                  <Accordion type="single" collapsible className="w-full">
                    {filteredFAQs.billing.map((faq, index) => (
                      <AccordionItem key={index} value={`billing-item-${index}`}>
                        <AccordionTrigger className="text-left font-medium text-gray-800 hover:text-primary">
                          {faq.question}
                        </AccordionTrigger>
                        <AccordionContent className="text-gray-600">
                          {faq.answer}
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                ) : (
                  <p className="text-gray-500 text-center py-4">No matching questions found in this category.</p>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Contact Support */}
        <Card className="bg-white border-gray-200 shadow-sm mt-8">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex items-center">
                <MessageSquare className="h-10 w-10 text-primary mr-4" />
                <div>
                  <h3 className="text-lg font-medium text-gray-800">Still need help?</h3>
                  <p className="text-gray-500">Contact our support team at <a href="mailto:<EMAIL>" className="text-primary hover:underline"><EMAIL></a></p>
                </div>
              </div>
              <Button
                className="bg-primary hover:bg-primary/90 whitespace-nowrap"
                onClick={() => window.location.href = 'mailto:<EMAIL>'}
              >
                Contact Support
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
};

export default Help;
