import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { showRateLimitToast, isRateLimitError, getErrorMessage } from '@/components/ui/rate-limit-toast';
import { getCompanies } from '@/services/supabase/companies';
import { getJobs } from '@/services/supabase/jobs';
import { matchCandidate, matchCandidateToCompany, matchCandidateToAllCompanies, bulkEvaluateSelectedCandidates } from '@/services/cv-processing/jobMatcher';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import CoverLetterInput from './CoverLetterInput';

interface CoverLetterData {
  content?: string;
  file?: File;
  url?: string;
}

interface EvaluateModalProps {
  isOpen: boolean;
  onClose: () => void;
  candidateId?: string;
  candidateIds?: string[]; // For bulk evaluation
  candidateName: string;
  onSuccess?: (score: number) => void;
  onEvaluate?: (mode: string, selectedId?: string, coverLetter?: CoverLetterData) => void;
  isBulkMode?: boolean; // Flag to indicate bulk evaluation
}

interface Company {
  id: string;
  name: string;
}

interface Job {
  id: string;
  title: string;
  company_id: string;
}

// Evaluation modes
type EvaluationMode = 'specific-job' | 'all-company-jobs' | 'all-companies';

const EvaluateModal: React.FC<EvaluateModalProps> = ({
  isOpen,
  onClose,
  candidateId,
  candidateIds,
  candidateName,
  onSuccess,
  onEvaluate,
  isBulkMode = false
}) => {
  const { toast } = useToast();
  const navigate = useNavigate();

  const [companies, setCompanies] = useState<Company[]>([]);
  const [jobs, setJobs] = useState<Job[]>([]);
  const [filteredJobs, setFilteredJobs] = useState<Job[]>([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState<string>('');
  const [selectedJobId, setSelectedJobId] = useState<string>('');
  const [evaluationMode, setEvaluationMode] = useState<EvaluationMode>('specific-job');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isEvaluating, setIsEvaluating] = useState<boolean>(false);
  const [includeCoverLetter, setIncludeCoverLetter] = useState<boolean>(false);
  const [coverLetterData, setCoverLetterData] = useState<CoverLetterData | null>(null);

  // Fetch companies and jobs on mount
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch companies
        const companiesData = await getCompanies();
        setCompanies(companiesData);

        // Fetch jobs
        const jobsData = await getJobs();
        setJobs(jobsData);
      } catch (error) {
        console.error('Error fetching data:', error);
        toast({
          title: 'Error',
          description: 'Failed to load companies and jobs.',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (isOpen) {
      fetchData();
    }
  }, [isOpen, toast]);

  // Filter jobs when company is selected
  useEffect(() => {
    if (selectedCompanyId) {
      const jobsForCompany = jobs.filter(job => job.company_id === selectedCompanyId);
      setFilteredJobs(jobsForCompany);
      // Reset selected job when company changes
      setSelectedJobId('');
    } else {
      setFilteredJobs([]);
    }
  }, [selectedCompanyId, jobs]);

  const handleCompanyChange = (value: string) => {
    setSelectedCompanyId(value);
  };

  const handleJobChange = (value: string) => {
    setSelectedJobId(value);
  };

  const handleEvaluate = async () => {
    // Validate selections based on evaluation mode
    if (evaluationMode === 'specific-job' && !selectedJobId) {
      toast({
        title: 'Selection Required',
        description: 'Please select a job to evaluate the candidate against.',
        variant: 'destructive'
      });
      return;
    }

    if (evaluationMode === 'all-company-jobs' && !selectedCompanyId) {
      toast({
        title: 'Selection Required',
        description: 'Please select a company to evaluate the candidate against all its jobs.',
        variant: 'destructive'
      });
      return;
    }

    setIsEvaluating(true);
    try {
      // If onEvaluate callback is provided, use it (for CV upload flow)
      if (onEvaluate) {
        const selectedId = evaluationMode === 'specific-job' ? selectedJobId :
                          evaluationMode === 'all-company-jobs' ? selectedCompanyId :
                          undefined;

        await onEvaluate(evaluationMode, selectedId, includeCoverLetter ? coverLetterData || undefined : undefined);
        onClose();
        return;
      }

      // Handle bulk evaluation
      if (isBulkMode && candidateIds && candidateIds.length > 0) {
        const targetId = evaluationMode === 'specific-job' ? selectedJobId :
                        evaluationMode === 'all-company-jobs' ? selectedCompanyId :
                        undefined;

        const results = await bulkEvaluateSelectedCandidates(candidateIds, evaluationMode, targetId);

        // Calculate overall success rate
        const successfulEvaluations = results.filter(r => r.evaluatedCandidates > 0).length;
        const totalCandidates = candidateIds.length;

        toast({
          title: 'Bulk Evaluation Complete',
          description: `Successfully evaluated ${successfulEvaluations} out of ${totalCandidates} candidates.`,
        });

        // Close the modal and navigate to results (you might want to show results in a different way)
        onClose();
        return;
      }

      // Otherwise, use the traditional flow (for single candidate evaluation)
      if (!candidateId) {
        throw new Error('No candidate ID provided');
      }

      let result;

      // Get cover letter content if checkbox is checked (only for single candidate)
      const coverLetterContent = includeCoverLetter && !isBulkMode ? coverLetterData?.content || null : null;

      // Call the appropriate evaluation function based on the selected mode
      switch (evaluationMode) {
        case 'specific-job':
          result = await matchCandidate(candidateId, selectedJobId, coverLetterContent);
          break;
        case 'all-company-jobs':
          result = await matchCandidateToCompany(candidateId, selectedCompanyId, coverLetterContent);
          break;
        case 'all-companies':
          result = await matchCandidateToAllCompanies(candidateId, coverLetterContent);
          break;
      }

      // Count the number of evaluations performed (number of jobs evaluated)
      let evaluationCount = 1; // Default for specific-job mode

      if (evaluationMode === 'all-company-jobs' && result.jobSpecificMatches) {
        evaluationCount = result.jobSpecificMatches.length;
      } else if (evaluationMode === 'all-companies' && result.jobSpecificMatches) {
        evaluationCount = result.jobSpecificMatches.length;
      }

      // Show success message with the number of evaluations performed
      toast({
        title: 'Evaluation Complete',
        description: `${candidateName} has been evaluated against ${evaluationCount} ${evaluationCount === 1 ? 'job' : 'jobs'} with an overall match score of ${result.overallScore}%.`,
      });

      // Call onSuccess callback if provided
      if (onSuccess) {
        onSuccess(result.overallScore);
      }

      // Close the modal
      onClose();

      // Navigate to the candidate details page with the Evaluation tab active
      navigate(`/dashboard/cvs/${candidateId}?tab=evaluation`);
    } catch (error) {
      console.error('Error evaluating candidate:', error);

      // Check if it's a rate limiting error
      const rateLimit = isRateLimitError(error);
      const errorMessage = getErrorMessage(error);

      if (rateLimit) {
        showRateLimitToast({
          message: errorMessage,
          onRetry: () => handleEvaluate()
        });

        // Keep the modal open if it's a rate limit error so the user can retry
      } else {
        toast({
          title: 'Evaluation Failed',
          description: errorMessage,
          variant: 'destructive'
        });

        // Close the modal for non-rate-limit errors
        onClose();
      }
    } finally {
      setIsEvaluating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`${includeCoverLetter && !isBulkMode ? 'sm:max-w-[900px]' : 'sm:max-w-[500px]'} max-h-[90vh] overflow-y-auto`}>
        <DialogHeader>
          <DialogTitle>
            {isBulkMode ? 'Evaluate Multiple Candidates' : 'Evaluate Candidate'}
          </DialogTitle>
          <DialogDescription>
            {isBulkMode
              ? `Choose an evaluation mode and select the necessary options to evaluate ${candidateIds?.length || 0} selected candidates.`
              : `Choose an evaluation mode and select the necessary options to evaluate ${candidateName}.`
            }
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {isLoading ? (
            <div className="flex justify-center items-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-primary" />
              <span className="ml-2">Loading...</span>
            </div>
          ) : (
            <div className={`grid gap-6 ${includeCoverLetter && !isBulkMode ? 'grid-cols-2' : 'grid-cols-1'}`}>
              {/* Left Column - Evaluation Settings */}
              <div className="space-y-6">
                <div>
                  <h3 className="text-sm font-medium mb-3">Evaluation Mode</h3>
                  <RadioGroup
                    value={evaluationMode}
                    onValueChange={(value) => setEvaluationMode(value as EvaluationMode)}
                    className="space-y-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="specific-job" id="specific-job" />
                      <Label htmlFor="specific-job">Specific Job</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="all-company-jobs" id="all-company-jobs" />
                      <Label htmlFor="all-company-jobs">All Jobs in a Company</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="all-companies" id="all-companies" />
                      <Label htmlFor="all-companies">All Companies</Label>
                    </div>
                  </RadioGroup>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="company" className="text-right text-sm font-medium col-span-1">
                      Company
                    </label>
                    <Select
                      value={selectedCompanyId}
                      onValueChange={handleCompanyChange}
                      disabled={evaluationMode === 'all-companies'}
                    >
                      <SelectTrigger id="company" className="col-span-3">
                        <SelectValue placeholder="Select company" />
                      </SelectTrigger>
                      <SelectContent>
                        {companies.map((company) => (
                          <SelectItem key={company.id} value={company.id}>
                            {company.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="grid grid-cols-4 items-center gap-4">
                    <label htmlFor="job" className="text-right text-sm font-medium col-span-1">
                      Job
                    </label>
                    <Select
                      value={selectedJobId}
                      onValueChange={handleJobChange}
                      disabled={
                        evaluationMode !== 'specific-job' ||
                        !selectedCompanyId ||
                        filteredJobs.length === 0
                      }
                    >
                      <SelectTrigger id="job" className="col-span-3">
                        <SelectValue placeholder={
                          evaluationMode !== 'specific-job'
                            ? "Not required for this mode"
                            : !selectedCompanyId
                              ? "Select a company first"
                              : filteredJobs.length === 0
                                ? "No jobs available"
                                : "Select job"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {filteredJobs.map((job) => (
                          <SelectItem key={job.id} value={job.id}>
                            {job.title}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Cover Letter Checkbox - Only for single candidate evaluation */}
                {!isBulkMode && (
                  <div className="border-t pt-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="include-cover-letter"
                        checked={includeCoverLetter}
                        onCheckedChange={(checked) => {
                          setIncludeCoverLetter(checked as boolean);
                          if (!checked) {
                            setCoverLetterData(null);
                          }
                        }}
                      />
                      <Label htmlFor="include-cover-letter" className="text-sm font-medium">
                        Include cover letter in evaluation
                      </Label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1 ml-6">
                      Add a cover letter to provide additional context for the evaluation
                    </p>
                  </div>
                )}

                {/* Bulk Mode Notice */}
                {isBulkMode && (
                  <div className="border-t pt-4">
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-sm text-blue-800">
                        <strong>Bulk Evaluation Mode:</strong> Cover letters are not available for bulk evaluations.
                        Each candidate will be evaluated based on their CV content only.
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Right Column - Cover Letter Input (only when checkbox is checked and not bulk mode) */}
              {includeCoverLetter && !isBulkMode && (
                <div className="border-l pl-6">
                  <CoverLetterInput
                    onCoverLetterChange={setCoverLetterData}
                    disabled={isEvaluating}
                  />
                </div>
              )}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isEvaluating}>
            Cancel
          </Button>
          <Button
            onClick={handleEvaluate}
            disabled={
              (evaluationMode === 'specific-job' && !selectedJobId) ||
              (evaluationMode === 'all-company-jobs' && !selectedCompanyId) ||
              isEvaluating
            }
            className="bg-primary-gradient text-white hover:text-white"
          >
            {isEvaluating && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEvaluating ? 'Evaluating...' : 'Evaluate'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EvaluateModal;
