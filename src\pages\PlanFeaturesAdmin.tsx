import React from 'react';
import { DashboardLayout } from '@/components/layouts/DashboardLayout';
import PlanFeatureManager from '@/components/admin/PlanFeatureManager';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

/**
 * Admin page for managing plan features
 * Only accessible to platform administrators
 */
const PlanFeaturesAdmin = () => {
  const navigate = useNavigate();

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate('/dashboard/admin')}
            className="gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Admin
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Plan Features Management</h1>
            <p className="text-gray-600">Configure which features are available for each subscription plan</p>
          </div>
        </div>

        {/* Plan Feature Manager */}
        <PlanFeatureManager />
      </div>
    </DashboardLayout>
  );
};

export default PlanFeaturesAdmin;
