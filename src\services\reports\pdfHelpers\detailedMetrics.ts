import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { format } from 'date-fns';
import { ReportData, ReportType } from '../types';

/**
 * Helper function to add detailed metrics based on report type
 * @param doc The PDF document
 * @param reportData The report data
 * @param reportType The type of report
 * @param startY The Y position to start drawing
 * @returns The new Y position after drawing
 */
export const addDetailedMetrics = (doc: jsPDF, reportData: ReportData, reportType: ReportType, startY: number): number => {
  const pageWidth = doc.internal.pageSize.width;
  const pageHeight = doc.internal.pageSize.height;

  // Check if we need to add a page break
  if (startY > pageHeight - 100) {
    doc.addPage();
    startY = 20;
  }

  // Add section title
  doc.setFontSize(16);
  doc.setTextColor(41, 128, 185);
  doc.text('Detailed Metrics', 14, startY);
  startY += 10;

  // Add metrics based on report type
  switch (reportType) {
    case 'recruitment_funnel':
      if (reportData.data && reportData.data.length > 0) {
        // Add conversion rates between stages
        doc.setFontSize(12);
        doc.setTextColor(70);
        doc.text('Conversion Rates Between Stages:', 14, startY);
        startY += 8;

        doc.setFontSize(10);
        for (let i = 0; i < reportData.data.length - 1; i++) {
          const fromStage = reportData.data[i];
          const toStage = reportData.data[i + 1];
          const fromCount = fromStage.count || 0;
          const toCount = toStage.count || 0;
          const rate = fromCount > 0 ? (toCount / fromCount * 100).toFixed(1) : '0.0';

          doc.text(`${fromStage.stage} → ${toStage.stage}: ${rate}%`, 20, startY);
          startY += 6;
        }

        startY += 10;

        // Add overall conversion rate
        const firstStage = reportData.data[0];
        const lastStage = reportData.data[reportData.data.length - 1];
        const firstCount = firstStage.count || 0;
        const lastCount = lastStage.count || 0;
        const overallRate = firstCount > 0 ? (lastCount / firstCount * 100).toFixed(1) : '0.0';

        doc.setFontSize(12);
        doc.text('Overall Conversion Rate:', 14, startY);
        startY += 8;

        doc.setFontSize(10);
        doc.text(`${firstStage.stage} → ${lastStage.stage}: ${overallRate}%`, 20, startY);
        startY += 10;

        // Add time period analysis if available
        if (reportData.filters?.timePeriod) {
          startY += 10;
          doc.setFontSize(12);
          doc.text('Time Period Analysis:', 14, startY);
          startY += 8;

          doc.setFontSize(10);
          doc.text(`Report covers: ${reportData.filters.timePeriod}`, 20, startY);
          startY += 6;
        }
      }
      break;

    case 'time_to_hire':
      if (reportData.data && reportData.data.length > 0) {
        // Calculate average time to hire
        const totalDays = reportData.data.reduce((sum, item) => sum + (item.days || 0), 0);
        const avgDays = Math.round(totalDays / reportData.data.length);

        // Find fastest and slowest positions
        let fastestPosition = { position: '', days: Number.MAX_SAFE_INTEGER };
        let slowestPosition = { position: '', days: 0 };

        reportData.data.forEach(item => {
          const days = item.days || 0;
          if (days < fastestPosition.days) {
            fastestPosition = { position: item.position, days };
          }
          if (days > slowestPosition.days) {
            slowestPosition = { position: item.position, days };
          }
        });

        // Add average time to hire
        doc.setFontSize(12);
        doc.setTextColor(70);
        doc.text('Average Time to Hire:', 14, startY);
        startY += 8;

        doc.setFontSize(10);
        doc.text(`${avgDays} days across all positions`, 20, startY);
        startY += 10;

        // Add fastest and slowest positions
        doc.setFontSize(12);
        doc.text('Fastest Position to Fill:', 14, startY);
        startY += 8;

        doc.setFontSize(10);
        if (fastestPosition.position) {
          doc.text(`${fastestPosition.position}: ${fastestPosition.days} days`, 20, startY);
        } else {
          doc.text('No data available', 20, startY);
        }
        startY += 10;

        doc.setFontSize(12);
        doc.text('Slowest Position to Fill:', 14, startY);
        startY += 8;

        doc.setFontSize(10);
        if (slowestPosition.position) {
          doc.text(`${slowestPosition.position}: ${slowestPosition.days} days`, 20, startY);
        } else {
          doc.text('No data available', 20, startY);
        }
        startY += 10;

        // Add industry benchmark comparison
        doc.setFontSize(12);
        doc.text('Industry Benchmark Comparison:', 14, startY);
        startY += 8;

        doc.setFontSize(10);
        const benchmarkDays = 30; // Industry average
        const comparisonText = avgDays <= benchmarkDays
          ? `Your average time to hire (${avgDays} days) is better than the industry benchmark (${benchmarkDays} days).`
          : `Your average time to hire (${avgDays} days) is higher than the industry benchmark (${benchmarkDays} days).`;

        const comparisonLines = doc.splitTextToSize(comparisonText, pageWidth - 40);
        doc.text(comparisonLines, 20, startY);
        startY += comparisonLines.length * 6 + 10;
      }
      break;

    case 'source_effectiveness':
      if (reportData.data && reportData.data.length > 0) {
        // Calculate total applicants and hires
        let totalApplicants = 0;
        let totalHires = 0;

        reportData.data.forEach(item => {
          totalApplicants += item.applicants || 0;
          totalHires += item.hires || 0;
        });

        // Calculate overall conversion rate
        const overallRate = totalApplicants > 0 ? (totalHires / totalApplicants * 100).toFixed(1) : '0.0';

        // Add overall metrics
        doc.setFontSize(12);
        doc.setTextColor(70);
        doc.text('Overall Recruitment Metrics:', 14, startY);
        startY += 8;

        doc.setFontSize(10);
        doc.text(`Total Applicants: ${totalApplicants}`, 20, startY);
        startY += 6;
        doc.text(`Total Hires: ${totalHires}`, 20, startY);
        startY += 6;
        doc.text(`Overall Conversion Rate: ${overallRate}%`, 20, startY);
        startY += 10;

        // Add source effectiveness ranking
        doc.setFontSize(12);
        doc.text('Source Effectiveness Ranking:', 14, startY);
        startY += 8;

        // Sort sources by conversion rate
        const sortedSources = [...reportData.data].sort((a, b) => {
          const aConversion = a.applicants > 0 ? (a.hires / a.applicants) : 0;
          const bConversion = b.applicants > 0 ? (b.hires / b.applicants) : 0;
          return bConversion - aConversion;
        });

        // Create a table for the ranking
        const tableData = sortedSources.map(item => {
          const applicants = item.applicants || 0;
          const hires = item.hires || 0;
          const conversion = applicants > 0 ? (hires / applicants * 100).toFixed(1) : '0.0';
          const quality = item.quality || 0;

          return [
            item.source,
            applicants.toString(),
            hires.toString(),
            `${conversion}%`,
            `${quality}%`
          ];
        });

        // Add the table
        autoTable(doc, {
          head: [['Source', 'Applicants', 'Hires', 'Conversion', 'Quality']],
          body: tableData,
          startY,
          theme: 'grid',
          styles: { fontSize: 8, cellPadding: 3 },
          headStyles: { fillColor: [41, 128, 185], textColor: 255 },
        });

        startY = (doc as any).lastAutoTable.finalY + 10;
      }
      break;

    default:
      // Generic metrics
      if (reportData.data && reportData.data.length > 0) {
        // Add basic statistics
        doc.setFontSize(12);
        doc.setTextColor(70);
        doc.text('Basic Statistics:', 14, startY);
        startY += 8;

        doc.setFontSize(10);
        doc.text(`Total Records: ${reportData.data.length}`, 20, startY);
        startY += 6;

        // Find numeric properties for statistics
        const numericKeys = Object.keys(reportData.data[0]).filter(key =>
          typeof reportData.data[0][key] === 'number' && key !== 'id'
        );

        numericKeys.forEach(key => {
          const values = reportData.data.map(item => item[key] || 0);
          const sum = values.reduce((a, b) => a + b, 0);
          const avg = (sum / values.length).toFixed(2);
          const max = Math.max(...values);
          const min = Math.min(...values);

          doc.setFontSize(12);
          doc.text(`${key.charAt(0).toUpperCase() + key.slice(1)} Statistics:`, 14, startY);
          startY += 8;

          doc.setFontSize(10);
          doc.text(`Average: ${avg}`, 20, startY);
          startY += 6;
          doc.text(`Maximum: ${max}`, 20, startY);
          startY += 6;
          doc.text(`Minimum: ${min}`, 20, startY);
          startY += 10;
        });
      }
      break;
  }

  return startY;
};
