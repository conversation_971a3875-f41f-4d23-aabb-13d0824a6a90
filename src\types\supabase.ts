export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      email_templates: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          name: string
          display_name: string
          description: string | null
          subject: string
          content: string
          template_type: string
          variables: any
          is_active: boolean
          created_by: string | null
          updated_by: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          updated_at?: string
          name: string
          display_name: string
          description?: string | null
          subject: string
          content: string
          template_type: string
          variables?: any
          is_active?: boolean
          created_by?: string | null
          updated_by?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          name?: string
          display_name?: string
          description?: string | null
          subject?: string
          content?: string
          template_type?: string
          variables?: any
          is_active?: boolean
          created_by?: string | null
          updated_by?: string | null
        }
      }
      companies: {
        Row: {
          id: string
          created_at: string
          name: string
          description: string | null
          logo_url: string | null
          website: string | null
          industry: string | null
          size: string | null
          location: string | null
          user_id: string
        }
        Insert: {
          id?: string
          created_at?: string
          name: string
          description?: string | null
          logo_url?: string | null
          website?: string | null
          industry?: string | null
          size?: string | null
          location?: string | null
          user_id: string
        }
        Update: {
          id?: string
          created_at?: string
          name?: string
          description?: string | null
          logo_url?: string | null
          website?: string | null
          industry?: string | null
          size?: string | null
          location?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "companies_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      evaluations: {
        Row: {
          id: string
          created_at: string
          candidate_id: string
          job_id: string | null
          company_id: string | null
          evaluation_mode: string
          evaluation_score: number | null
          evaluation_summary: string | null
          user_id: string
        }
        Insert: {
          id?: string
          created_at?: string
          candidate_id: string
          job_id?: string | null
          company_id?: string | null
          evaluation_mode: string
          evaluation_score?: number | null
          evaluation_summary?: string | null
          user_id: string
        }
        Update: {
          id?: string
          created_at?: string
          candidate_id?: string
          job_id?: string | null
          company_id?: string | null
          evaluation_mode?: string
          evaluation_score?: number | null
          evaluation_summary?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "evaluations_candidate_id_fkey"
            columns: ["candidate_id"]
            referencedRelation: "candidates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "evaluations_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "evaluations_job_id_fkey"
            columns: ["job_id"]
            referencedRelation: "jobs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "evaluations_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      jobs: {
        Row: {
          id: string
          created_at: string
          title: string
          description: string
          requirements: string
          location: string
          salary_min: number | null
          salary_max: number | null
          job_type: string
          experience_level: string
          status: string
          company_id: string
          user_id: string
        }
        Insert: {
          id?: string
          created_at?: string
          title: string
          description: string
          requirements: string
          location: string
          salary_min?: number | null
          salary_max?: number | null
          job_type: string
          experience_level: string
          status?: string
          company_id: string
          user_id: string
        }
        Update: {
          id?: string
          created_at?: string
          title?: string
          description?: string
          requirements?: string
          location?: string
          salary_min?: number | null
          salary_max?: number | null
          job_type?: string
          experience_level?: string
          status?: string
          company_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "jobs_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "jobs_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      applications: {
        Row: {
          id: string
          created_at: string
          candidate_id: string
          job_id: string
          status: string
          applied_at: string
          user_id: string
          notes: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          candidate_id: string
          job_id: string
          status?: string
          applied_at?: string
          user_id: string
          notes?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          candidate_id?: string
          job_id?: string
          status?: string
          applied_at?: string
          user_id?: string
          notes?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "applications_candidate_id_fkey"
            columns: ["candidate_id"]
            referencedRelation: "candidates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "applications_job_id_fkey"
            columns: ["job_id"]
            referencedRelation: "jobs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "applications_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      candidates: {
        Row: {
          id: string
          created_at: string
          name: string
          email: string
          phone: string | null
          cv_url: string
          status: string
          job_id: string | null
          company_id: string | null
          user_id: string
          evaluation_score: number | null
          evaluation_summary: string | null
          notes: string | null
          cover_letter_content: string | null
          cover_letter_url: string | null
          source: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          name: string
          email: string
          phone?: string | null
          cv_url: string
          status?: string
          job_id?: string | null
          company_id?: string | null
          user_id: string
          evaluation_score?: number | null
          evaluation_summary?: string | null
          notes?: string | null
          cover_letter_content?: string | null
          cover_letter_url?: string | null
          source?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          name?: string
          email?: string
          phone?: string | null
          cv_url?: string
          status?: string
          job_id?: string | null
          company_id?: string | null
          user_id?: string
          evaluation_score?: number | null
          evaluation_summary?: string | null
          notes?: string | null
          cover_letter_content?: string | null
          cover_letter_url?: string | null
          source?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "candidates_job_id_fkey"
            columns: ["job_id"]
            referencedRelation: "jobs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "candidates_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "candidates_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          id: string
          created_at: string
          user_id: string
          full_name: string
          avatar_url: string | null
          job_title: string | null
          company_id: string | null
          subscription_tier: string
          subscription_status: string
          subscription_end_date: string | null
          platform_admin: boolean | null
          banned: boolean | null
          email: string | null
          last_sign_in_at: string | null
          metadata: Json | null
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          full_name: string
          avatar_url?: string | null
          job_title?: string | null
          company_id?: string | null
          subscription_tier?: string
          subscription_status?: string
          subscription_end_date?: string | null
          platform_admin?: boolean | null
          banned?: boolean | null
          email?: string | null
          last_sign_in_at?: string | null
          metadata?: Json | null
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          full_name?: string
          avatar_url?: string | null
          job_title?: string | null
          company_id?: string | null
          subscription_tier?: string
          subscription_status?: string
          subscription_end_date?: string | null
          platform_admin?: boolean | null
          banned?: boolean | null
          email?: string | null
          last_sign_in_at?: string | null
          metadata?: Json | null
        }
        Relationships: [
          {
            foreignKeyName: "profiles_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      team_members: {
        Row: {
          id: string
          created_at: string
          user_id: string | null
          company_id: string
          role: string
          status: string
          invited_by: string
          invitation_token: string | null
          metadata?: { [key: string]: any } | null
        }
        Insert: {
          id?: string
          created_at?: string
          user_id?: string | null
          company_id: string
          role: string
          status?: string
          invited_by: string
          invitation_token?: string | null
          metadata?: { [key: string]: any } | null
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string | null
          company_id?: string
          role?: string
          status?: string
          invited_by?: string
          invitation_token?: string | null
          metadata?: { [key: string]: any } | null
        }
        Relationships: [
          {
            foreignKeyName: "team_members_company_id_fkey"
            columns: ["company_id"]
            referencedRelation: "companies"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_invited_by_fkey"
            columns: ["invited_by"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "team_members_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
      notifications: {
        Row: {
          id: string
          created_at: string
          user_id: string
          title: string
          message: string
          type: string
          read: boolean
          link: string | null
        }
        Insert: {
          id?: string
          created_at?: string
          user_id: string
          title: string
          message: string
          type: string
          read?: boolean
          link?: string | null
        }
        Update: {
          id?: string
          created_at?: string
          user_id?: string
          title?: string
          message?: string
          type?: string
          read?: boolean
          link?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_user_id_fkey"
            columns: ["user_id"]
            referencedRelation: "users"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
