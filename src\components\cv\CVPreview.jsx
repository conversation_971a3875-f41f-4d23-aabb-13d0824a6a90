import React, { useState } from 'react';
import {
  Download,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  Maximize,
  Minimize,
  Printer,
  RotateCw
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import DocumentViewer from './DocumentViewer';

const CVPreview = ({
  url,
  cvUrl,
  fileName,
  candidateName,
  isLoading = false
}) => {
  // Use cvUrl if provided, otherwise fall back to url or default
  const documentUrl = cvUrl || url || '/mock-cv-preview.pdf';

  // Determine file extension based on URL
  const getFileExtension = (url) => {
    const extension = url.split('.').pop()?.toLowerCase();
    return extension === 'pdf' ? 'pdf' :
           extension === 'docx' ? 'docx' :
           extension === 'doc' ? 'doc' : 'pdf'; // Default to PDF if unknown
  };

  // Use candidateName to generate fileName if not provided
  const fileExtension = getFileExtension(documentUrl);
  const documentFileName = fileName || (candidateName ? `${candidateName.replace(/\s+/g, '_')}_CV.${fileExtension}` : `resume.${fileExtension}`);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0); // Will be updated by the PDF viewer
  const [zoom, setZoom] = useState(100);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showPagination, setShowPagination] = useState(fileExtension === 'pdf');

  // Handle page navigation
  const goToPreviousPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(currentPage + 1);
    }
  };

  // Handle zoom
  const zoomIn = () => {
    if (zoom < 200) {
      setZoom(zoom + 25);
    }
  };

  const zoomOut = () => {
    if (zoom > 50) {
      setZoom(zoom - 25);
    }
  };

  // Handle fullscreen
  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  // Handle download
  const handleDownload = () => {
    if (documentUrl && documentUrl !== '/mock-cv-preview.pdf') {
      // Create a temporary anchor element to trigger the download
      const link = document.createElement('a');
      link.href = documentUrl;
      link.download = documentFileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      console.log('No real CV URL available for download');
      // Show a message to the user that there's no CV to download
      alert('No CV file available for download');
    }
  };

  // Handle print
  const handlePrint = () => {
    if (documentUrl && documentUrl !== '/mock-cv-preview.pdf') {
      // Open the document in a new window and trigger print
      const printWindow = window.open(documentUrl, '_blank');
      if (printWindow) {
        printWindow.addEventListener('load', () => {
          printWindow.print();
        });
      } else {
        // If popup is blocked, provide a message
        alert('Please allow popups to print the CV');
      }
    } else {
      console.log('No real CV URL available for printing');
      // Show a message to the user that there's no CV to print
      alert('No CV file available for printing');
    }
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 ${isFullscreen ? 'fixed inset-0 z-50 rounded-none' : ''}`}>
      <div className="flex flex-row items-center justify-between p-6 pb-2 border-b border-gray-200">
        <h3 className="text-gray-800 text-lg font-semibold">CV Preview: {candidateName || documentFileName}</h3>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="icon"
            className="border-gray-200 text-gray-700 hover:bg-gray-100"
            onClick={handleDownload}
          >
            <Download className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="border-gray-200 text-gray-700 hover:bg-gray-100"
            onClick={handlePrint}
          >
            <Printer className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            size="icon"
            className="border-gray-200 text-gray-700 hover:bg-gray-100"
            onClick={toggleFullscreen}
          >
            {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
          </Button>
        </div>
      </div>
      <div className="p-6">
        <div className="bg-gray-50 border border-gray-200 rounded-lg overflow-hidden">
          <div className="flex items-center justify-between p-2 border-b border-gray-200">
            {showPagination && totalPages > 0 ? (
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                  onClick={goToPreviousPage}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-gray-600 text-sm">
                  Page {currentPage} of {totalPages}
                </span>
                <Button
                  variant="ghost"
                  size="icon"
                  className="text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                  onClick={goToNextPage}
                  disabled={currentPage === totalPages}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            ) : (
              <div className="flex-1">{/* Empty div to maintain layout */}</div>
            )}
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                onClick={zoomOut}
                disabled={zoom <= 50}
              >
                <ZoomOut className="h-4 w-4" />
              </Button>
              <span className="text-gray-600 text-sm">{zoom}%</span>
              <Button
                variant="ghost"
                size="icon"
                className="text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                onClick={zoomIn}
                disabled={zoom >= 200}
              >
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="text-gray-500 hover:text-gray-800 hover:bg-gray-100"
                onClick={() => setZoom(100)}
              >
                <RotateCw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div
            className="p-4 flex justify-center overflow-auto"
            style={{
              minHeight: isFullscreen ? 'calc(100vh - 150px)' : '45em',
              maxHeight: isFullscreen ? 'calc(100vh - 150px)' : 'auto',
              height: '100%'
            }}
          >
            {isLoading ? (
              <div className="w-full max-w-[595px] aspect-[1/1.414] bg-white rounded">
                <Skeleton className="w-full h-full" />
              </div>
            ) : (
              <div
                className="bg-white rounded shadow-lg overflow-hidden"
                style={{
                  width: fileExtension === 'pdf' ? '100%' : '100%',
                  height: fileExtension === 'pdf' ? '100%' : 'auto',
                  transform: fileExtension === 'pdf' ? `scale(${zoom / 100})` : 'none',
                  transformOrigin: 'top center',
                  maxWidth: '100%',
                  minHeight: fileExtension === 'pdf' ? '700px' : 'auto'
                }}
              >
                {documentUrl && documentUrl !== '/mock-cv-preview.pdf' ? (
                  <div className="w-full h-full">
                    <DocumentViewer
                      url={documentUrl}
                      fileName={documentFileName}
                      onPageCountChange={(count) => {
                        setTotalPages(count);
                        setShowPagination(fileExtension === 'pdf' && count > 0);
                      }}
                    />
                  </div>
                ) : (
                  <div className="p-8">
                    <h1 className="text-2xl font-bold mb-4">John Doe</h1>
                    <p className="text-gray-600 mb-4">Frontend Developer</p>

                    <div className="mb-6">
                      <h2 className="text-lg font-semibold border-b border-gray-300 pb-1 mb-2">Contact</h2>
                      <p>Email: <EMAIL></p>
                      <p>Phone: (*************</p>
                      <p>Location: New York, NY</p>
                    </div>

                    <div className="mb-6">
                      <h2 className="text-lg font-semibold border-b border-gray-300 pb-1 mb-2">Skills</h2>
                      <ul className="list-disc list-inside">
                        <li>JavaScript / TypeScript</li>
                        <li>React.js</li>
                        <li>HTML / CSS</li>
                        <li>Node.js</li>
                        <li>Git</li>
                      </ul>
                    </div>

                    <div className="mb-6">
                      <h2 className="text-lg font-semibold border-b border-gray-300 pb-1 mb-2">Experience</h2>
                      <div className="mb-3">
                        <h3 className="font-medium">Senior Frontend Developer - ABC Company</h3>
                        <p className="text-sm text-gray-600">Jan 2020 - Present</p>
                        <p>Developed and maintained web applications using React.js and TypeScript.</p>
                      </div>
                      <div>
                        <h3 className="font-medium">Frontend Developer - XYZ Inc.</h3>
                        <p className="text-sm text-gray-600">Jun 2017 - Dec 2019</p>
                        <p>Built responsive user interfaces and implemented new features.</p>
                      </div>
                    </div>

                    <div>
                      <h2 className="text-lg font-semibold border-b border-gray-300 pb-1 mb-2">Education</h2>
                      <h3 className="font-medium">Bachelor of Science in Computer Science</h3>
                      <p className="text-sm text-gray-600">University of Technology, 2017</p>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CVPreview;
