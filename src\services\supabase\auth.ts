import { supabase } from '@/lib/supabase';
import { User } from '@supabase/supabase-js';

export interface SignUpCredentials {
  email: string;
  password: string;
  fullName: string;
  userType?: 'recruiter' | 'candidate';
}

export interface SignInCredentials {
  email: string;
  password: string;
}

export interface ResetPasswordCredentials {
  email: string;
}

export interface UpdatePasswordCredentials {
  password: string;
}

/**
 * Sign up a new user
 */
export const signUp = async ({ email, password, fullName, userType = 'recruiter' }: SignUpCredentials) => {
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
        user_type: userType,
      },
    },
  });

  if (error) {
    throw error;
  }

  return data;
};

/**
 * Sign in a user
 */
export const signIn = async ({ email, password }: SignInCredentials) => {
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  if (error) {
    throw error;
  }

  return data;
};

/**
 * Sign out the current user
 */
export const signOut = async () => {
  const { error } = await supabase.auth.signOut();

  if (error) {
    throw error;
  }

  return true;
};

/**
 * Get the current user
 */
export const getCurrentUser = async (): Promise<User | null> => {
  const { data } = await supabase.auth.getUser();
  return data.user;
};

/**
 * Get the current session
 */
export const getCurrentSession = async () => {
  const { data, error } = await supabase.auth.getSession();

  if (error) {
    throw error;
  }

  return data.session;
};

/**
 * Send a password reset email
 */
export const resetPassword = async ({ email }: ResetPasswordCredentials) => {
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${window.location.origin}/reset-password`,
  });

  if (error) {
    throw error;
  }

  return true;
};

/**
 * Update the user's password
 */
export const updatePassword = async ({ password }: UpdatePasswordCredentials) => {
  const { error } = await supabase.auth.updateUser({
    password,
  });

  if (error) {
    throw error;
  }

  return true;
};

/**
 * Update the user's profile
 */
export const updateProfile = async (profile: { fullName?: string; avatarUrl?: string }) => {
  const { error } = await supabase.auth.updateUser({
    data: {
      full_name: profile.fullName,
      avatar_url: profile.avatarUrl,
    },
  });

  if (error) {
    throw error;
  }

  return true;
};

/**
 * Resend email verification
 * @param email The email address to resend verification to
 * @returns A promise that resolves when the email is sent
 */
export const resendEmailVerification = async (email: string) => {
  const { error } = await supabase.auth.resend({
    type: 'signup',
    email,
    options: {
      emailRedirectTo: `${window.location.origin}/login`,
    },
  });

  if (error) {
    throw error;
  }

  return true;
};

/**
 * Check if a user's email is verified
 * @param user The user to check
 * @returns True if the email is verified, false otherwise
 */
export const isEmailVerified = (user: User | null): boolean => {
  if (!user) return false;
  return !!user.email_confirmed_at;
};
