/**
 * React Query hooks for team data
 */

import { useAuth } from '@/contexts/AuthContext';
import { useEnhancedQuery, useEnhancedMutation, queryKeys } from '@/lib/queryUtils';
import * as teamService from '@/services/supabase/team';
import { TeamMember, TeamMemberInsert, TeamMemberUpdate, EnhancedTeamMember } from '@/services/supabase/team';
import { useUsageLimit } from './use-usage';
import { useQueryClient } from '@tanstack/react-query';

/**
 * Hook to fetch all team members for a company
 */
export function useTeamMembers(companyId: string) {
  return useEnhancedQuery<EnhancedTeamMember[]>(
    queryKeys.team.byCompany(companyId),
    () => teamService.getTeamMembers(companyId),
    {
      enabled: !!companyId,
      fallbackData: [],
      errorMessage: `Failed to load team members for company ${companyId}`,
      refetchOnWindowFocus: true,
      refetchOnMount: true,
    }
  );
}

/**
 * Hook to fetch a specific team member
 */
export function useTeamMember(id: string) {
  return useEnhancedQuery<EnhancedTeamMember | null>(
    queryKeys.team.detail(id),
    () => teamService.getTeamMember(id),
    {
      enabled: !!id,
      fallbackData: null,
      errorMessage: `Failed to load team member with ID ${id}`,
      refetchOnWindowFocus: true,
      refetchOnMount: true,
    }
  );
}

/**
 * Hook to invite a new team member with usage limit checking
 */
export function useInviteTeamMember() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useEnhancedMutation<EnhancedTeamMember, TeamMemberInsert & { name?: string, email?: string }>(
    (teamMember) => {
      const { name, email, ...rest } = teamMember;
      return teamService.inviteTeamMember(rest, user?.id || '', true, { name, email });
    },
    {
      errorMessage: 'Failed to invite team member',
      successMessage: 'Team member invited successfully',
      onSuccess: (data, variables) => {
        // Invalidate the team members query for the specific company
        queryClient.invalidateQueries({ queryKey: queryKeys.team.byCompany(variables.company_id) });

        // Also invalidate usage queries
        queryClient.invalidateQueries({ queryKey: queryKeys.usage.current(user?.id) });
        queryClient.invalidateQueries({ queryKey: queryKeys.usage.limit(user?.id, 'team_members') });
      },
    }
  );
}

/**
 * Hook to update a team member
 */
export function useUpdateTeamMember() {
  const queryClient = useQueryClient();

  return useEnhancedMutation<EnhancedTeamMember, { id: string; teamMember: TeamMemberUpdate }>(
    ({ id, teamMember }) => teamService.updateTeamMember(id, teamMember),
    {
      errorMessage: 'Failed to update team member',
      successMessage: 'Team member updated successfully',
      onSuccess: (data) => {
        // Invalidate the team members query for the specific company
        queryClient.invalidateQueries({ queryKey: queryKeys.team.byCompany(data.company_id) });
        queryClient.invalidateQueries({ queryKey: ['teamMember', data.id] });
      },
    }
  );
}

/**
 * Hook to delete a team member
 */
export function useDeleteTeamMember() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useEnhancedMutation<boolean, { id: string; companyId: string }>(
    ({ id }) => teamService.deleteTeamMember(id, user?.id, true),
    {
      errorMessage: 'Failed to delete team member',
      successMessage: 'Team member deleted successfully',
      onSuccess: (_, variables) => {
        // Invalidate the team members query for the specific company
        queryClient.invalidateQueries({ queryKey: queryKeys.team.byCompany(variables.companyId) });

        // Also invalidate usage queries
        queryClient.invalidateQueries({ queryKey: queryKeys.usage.current(user?.id) });
        queryClient.invalidateQueries({ queryKey: queryKeys.usage.limit(user?.id, 'team_members') });
      },
    }
  );
}

/**
 * Hook to accept an invitation
 */
export function useAcceptInvitation() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useEnhancedMutation<EnhancedTeamMember, string>(
    (token) => teamService.acceptInvitation(token, user?.id || ''),
    {
      errorMessage: 'Failed to accept invitation',
      successMessage: 'Invitation accepted successfully',
      onSuccess: (data) => {
        // Invalidate the team members query for the specific company
        queryClient.invalidateQueries({ queryKey: queryKeys.team.byCompany(data.company_id) });
      },
    }
  );
}

/**
 * Hook to accept an invitation with auto-signup
 */
export function useAcceptInvitationWithAutoSignup() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useEnhancedMutation<EnhancedTeamMember, string>(
    (token) => teamService.acceptInvitationWithAutoSignup(token, user?.id),
    {
      errorMessage: 'Failed to accept invitation',
      successMessage: 'Invitation accepted successfully',
      onSuccess: (data) => {
        queryClient.invalidateQueries({ queryKey: queryKeys.team.byCompany(data.company_id) });
      },
    }
  );
}

/**
 * Hook to check team member limit
 */
export function useTeamMemberLimit() {
  return useUsageLimit('team_members');
}

