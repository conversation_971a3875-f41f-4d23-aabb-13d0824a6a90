import React from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from '@/components/ui/dialog';
import ScheduleInterviewForm from './ScheduleInterviewForm';

interface ScheduleInterviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  preselectedCandidateId?: string;
  preselectedJobId?: string;
  candidateName?: string;
}

const ScheduleInterviewModal: React.FC<ScheduleInterviewModalProps> = ({
  isOpen,
  onClose,
  preselectedCandidateId,
  preselectedJobId,
  candidateName
}) => {
  const handleSuccess = () => {
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Schedule Interview</DialogTitle>
          <DialogDescription>
            {candidateName 
              ? `Schedule an interview with ${candidateName}`
              : 'Schedule and manage candidate interviews'
            }
          </DialogDescription>
        </DialogHeader>
        
        <div className="mt-4">
          <ScheduleInterviewForm
            onSuccess={handleSuccess}
            preselectedCandidateId={preselectedCandidateId}
            preselectedJobId={preselectedJobId}
            isModal={true}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ScheduleInterviewModal;
