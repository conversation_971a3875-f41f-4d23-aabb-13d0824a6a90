import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { ReportData, ReportType } from '../types';

/**
 * Generate an Excel report
 */
export const generateExcelReport = (reportData: ReportData, reportType: ReportType): XLSX.WorkBook => {
  // Create a new workbook
  const wb = XLSX.utils.book_new();

  // Create headers row
  const headers = reportData.columns.map(col => col.header);

  // Create data rows
  const data = reportData.data.map((item: any) =>
    reportData.columns.map(col => item[col.dataKey])
  );

  // Combine headers and data
  const wsData = [headers, ...data];

  // Create worksheet
  const ws = XLSX.utils.aoa_to_sheet(wsData);

  // Add the worksheet to the workbook
  XLSX.utils.book_append_sheet(wb, ws, reportData.title.substring(0, 31)); // Excel sheet names limited to 31 chars

  // Add summary sheet if available
  if (reportData.summary && Object.keys(reportData.summary).length > 0) {
    const summaryData = Object.entries(reportData.summary).map(([key, value]) => [key, value]);
    const summaryWs = XLSX.utils.aoa_to_sheet([['Key', 'Value'], ...summaryData]);
    XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');
  }

  // Add metadata sheet
  const metaData = [
    ['Report Title', reportData.title],
    ['Generated Date', format(reportData.date, 'PPP')],
  ];

  if (reportData.companyName) {
    metaData.push(['Company', reportData.companyName]);
  }

  if (reportData.description) {
    metaData.push(['Description', reportData.description]);
  }

  if (reportData.filters && Object.keys(reportData.filters).length > 0) {
    metaData.push(['Filters', '']);
    Object.entries(reportData.filters).forEach(([key, value]) => {
      let displayValue = value;
      if (value instanceof Date) {
        displayValue = format(value, 'PPP');
      }
      metaData.push([`  ${key}`, displayValue]);
    });
  }

  const metaWs = XLSX.utils.aoa_to_sheet(metaData);
  XLSX.utils.book_append_sheet(wb, metaWs, 'Metadata');

  return wb;
};

/**
 * Download an Excel report
 */
export const downloadExcelReport = (reportData: ReportData, reportType: ReportType): void => {
  const wb = generateExcelReport(reportData, reportType);
  const fileName = `${reportData.title.replace(/\s+/g, '_')}_${format(reportData.date, 'yyyy-MM-dd')}.xlsx`;
  XLSX.writeFile(wb, fileName);
};
