
import { ArrowRight, LogIn } from "lucide-react";
import { Rating } from "./Rating";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "@/contexts/AuthContext";

export const HeroSection = () => {
  const { isAuthenticated } = useAuth();

  return (
    <div className="container mx-auto px-6 py-16 text-center relative">
      <div
        className="absolute inset-0 m-auto max-w-xs h-[357px] blur-[118px] sm:max-w-md md:max-w-lg"
        style={{
          background: "linear-gradient(106.89deg, rgba(192, 132, 252, 0.11) 15.73%, rgba(14, 165, 233, 0.41) 15.74%, rgba(232, 121, 249, 0.26) 56.49%, rgba(79, 70, 229, 0.4) 115.91%)"
        }}
      />
      <h1 className="text-5xl font-bold text-white mb-6">
        AI-Powered CV Evaluation<br />Platform for Agencies
      </h1>
      <p className="text-gray-300 mb-8 max-w-3xl mx-auto">
        Upload CVs, match against company requirements, and deliver better candidates to your clients. Our intelligent system helps agencies evaluate talent more effectively.
      </p>
      <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-8">
        {isAuthenticated ? (
          <Link
            to="/dashboard"
            className="bg-recruiter-lightblue hover:bg-blue-500 text-white py-3 px-6 rounded-lg flex items-center transition-colors w-full sm:w-auto justify-center"
          >
            Go to Dashboard <ArrowRight size={16} className="ml-2" />
          </Link>
        ) : (
          <>
            <Link
              to="/signup"
              className="bg-recruiter-lightblue hover:bg-blue-500 text-white py-3 px-6 rounded-lg flex items-center transition-colors w-full sm:w-auto justify-center"
            >
              Start Evaluating Candidates <ArrowRight size={16} className="ml-2" />
            </Link>
            <Link
              to="/login"
              className="bg-[#2a2f3d] border border-gray-700 hover:bg-[#3a3f4d] text-white py-3 px-6 rounded-lg flex items-center transition-colors w-full sm:w-auto justify-center"
            >
              <LogIn size={16} className="mr-2" /> Sign In
            </Link>
          </>
        )}
      </div>
      <div className="flex justify-center items-center">
        <Rating />
        <span className="text-white ml-2">5.0 by over 1,200 companies</span>
      </div>
    </div>
  );
};


