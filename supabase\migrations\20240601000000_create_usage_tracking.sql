-- Create usage tracking table
CREATE TABLE IF NOT EXISTS public.usage_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    month_year TEXT NOT NULL, -- Format: YYYY-MM
    cv_uploads_count INTEGER NOT NULL DEFAULT 0,
    active_jobs_count INTEGER NOT NULL DEFAULT 0,
    company_profiles_count INTEGER NOT NULL DEFAULT 0,
    team_members_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    UNIQUE(user_id, month_year)
);

-- Add indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_usage_tracking_user_id ON public.usage_tracking(user_id);
CREATE INDEX IF NOT EXISTS idx_usage_tracking_month_year ON public.usage_tracking(month_year);

-- Add RLS policies
ALTER TABLE public.usage_tracking ENABLE ROW LEVEL SECURITY;

-- Users can view their own usage
CREATE POLICY "Users can view their own usage"
ON public.usage_tracking
FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Users can update their own usage
CREATE POLICY "Users can update their own usage"
ON public.usage_tracking
FOR UPDATE
TO authenticated
USING (auth.uid() = user_id);

-- Users can insert their own usage
CREATE POLICY "Users can insert their own usage"
ON public.usage_tracking
FOR INSERT
TO authenticated
WITH CHECK (auth.uid() = user_id);

-- Platform admins can view all usage data
CREATE POLICY "Platform admins can view all usage data"
ON public.usage_tracking
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.user_id = auth.uid()
    AND profiles.platform_admin = true
  )
);

-- Add comments for documentation
COMMENT ON TABLE public.usage_tracking IS 'Tracks usage metrics for subscription limits';
COMMENT ON COLUMN public.usage_tracking.month_year IS 'Month and year in YYYY-MM format for monthly tracking';
COMMENT ON COLUMN public.usage_tracking.cv_uploads_count IS 'Number of CVs uploaded in the month';
COMMENT ON COLUMN public.usage_tracking.active_jobs_count IS 'Number of active job postings';
COMMENT ON COLUMN public.usage_tracking.company_profiles_count IS 'Number of company profiles created';
COMMENT ON COLUMN public.usage_tracking.team_members_count IS 'Number of team members invited';

-- Create subscription limits table
CREATE TABLE IF NOT EXISTS public.subscription_limits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tier TEXT NOT NULL UNIQUE CHECK (tier IN ('starter', 'growth', 'pro')),
    cv_upload_limit INTEGER NOT NULL,
    job_posting_limit INTEGER NOT NULL,
    company_profile_limit INTEGER NOT NULL,
    team_member_limit INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT now() NOT NULL
);

-- Insert default subscription limits
INSERT INTO public.subscription_limits (tier, cv_upload_limit, job_posting_limit, company_profile_limit, team_member_limit)
VALUES
    ('starter', 50, 1, 1, 1),
    ('growth', 500, 5, 5, 5),
    ('pro', 999999, 999999, 999999, 999999) -- Using a large number for "unlimited"
ON CONFLICT (tier) DO UPDATE SET
    cv_upload_limit = EXCLUDED.cv_upload_limit,
    job_posting_limit = EXCLUDED.job_posting_limit,
    company_profile_limit = EXCLUDED.company_profile_limit,
    team_member_limit = EXCLUDED.team_member_limit,
    updated_at = now();

-- Add RLS policies for subscription limits
ALTER TABLE public.subscription_limits ENABLE ROW LEVEL SECURITY;

-- Everyone can view subscription limits
CREATE POLICY "Everyone can view subscription limits"
ON public.subscription_limits
FOR SELECT
TO authenticated
USING (true);

-- Only platform admins can modify subscription limits
CREATE POLICY "Only platform admins can modify subscription limits"
ON public.subscription_limits
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1 FROM public.profiles
    WHERE profiles.user_id = auth.uid()
    AND profiles.platform_admin = true
  )
);

-- Add comments for documentation
COMMENT ON TABLE public.subscription_limits IS 'Defines limits for each subscription tier';
COMMENT ON COLUMN public.subscription_limits.tier IS 'Subscription tier (starter, growth, pro)';
COMMENT ON COLUMN public.subscription_limits.cv_upload_limit IS 'Maximum number of CV uploads per month';
COMMENT ON COLUMN public.subscription_limits.job_posting_limit IS 'Maximum number of active job postings';
COMMENT ON COLUMN public.subscription_limits.company_profile_limit IS 'Maximum number of company profiles';
COMMENT ON COLUMN public.subscription_limits.team_member_limit IS 'Maximum number of team members';

-- Create function to reset monthly usage
CREATE OR REPLACE FUNCTION public.reset_monthly_usage()
RETURNS void AS $$
DECLARE
    current_month TEXT;
BEGIN
    -- Get current month in YYYY-MM format
    current_month := to_char(now(), 'YYYY-MM');

    -- Insert new records for the current month for all users
    -- This will reset their usage to 0 for the new month
    INSERT INTO public.usage_tracking (user_id, month_year, cv_uploads_count, active_jobs_count, company_profiles_count, team_members_count)
    SELECT
        profiles.user_id,
        current_month,
        0, -- Reset CV uploads count
        0, -- Reset active jobs count
        0, -- Reset company profiles count
        0  -- Reset team members count
    FROM
        public.profiles
    ON CONFLICT (user_id, month_year) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Add comment for documentation
COMMENT ON FUNCTION public.reset_monthly_usage() IS 'Resets monthly usage counters for all users';
