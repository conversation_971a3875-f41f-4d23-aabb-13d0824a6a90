import React from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Linkedin,
  Globe,
  Briefcase,
  GraduationCap,
  Award,
  Code,
  MessageSquare,
  Languages,
  Calendar,
  Building,
  FileText,
  Star,
  ExternalLink,
  FolderOpen
} from 'lucide-react';
import { ParsedCV } from '@/services/cv-processing/cvParser';

interface CVStructuredDetailsTabProps {
  candidate: any;
  parsedCV: ParsedCV | null;
}

const CVStructuredDetailsTab: React.FC<CVStructuredDetailsTabProps> = ({ candidate, parsedCV }) => {
  if (!parsedCV) {
    return (
      <div className="space-y-6">
        <Card className="bg-white border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center">
              <FileText className="mr-2 h-5 w-5 text-gray-500" />
              Structured CV Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800">No Structured CV Data Available</h3>
              <p className="text-gray-500 mt-1">
                The parsed CV data is not available for this candidate.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const formatDate = (date: string) => {
    if (!date) return 'N/A';
    if (date.toLowerCase() === 'present') return 'Present';
    return date;
  };

  return (
    <div className="space-y-6">
      {/* Personal Details */}
      <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
        <CardHeader>
          <CardTitle className="text-gray-800 text-lg flex items-center">
            <User className="mr-2 h-5 w-5 text-blue-500" />
            Personal Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center">
              <User className="h-4 w-4 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600 mr-2">Name:</span>
              <span className="font-medium">{parsedCV.personalDetails.name}</span>
            </div>
            <div className="flex items-center">
              <Mail className="h-4 w-4 text-gray-500 mr-2" />
              <span className="text-sm text-gray-600 mr-2">Email:</span>
              <span className="font-medium">{parsedCV.personalDetails.email}</span>
            </div>
            {parsedCV.personalDetails.phone && (
              <div className="flex items-center">
                <Phone className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm text-gray-600 mr-2">Phone:</span>
                <span className="font-medium">{parsedCV.personalDetails.phone}</span>
              </div>
            )}
            {parsedCV.personalDetails.location && (
              <div className="flex items-center">
                <MapPin className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm text-gray-600 mr-2">Location:</span>
                <span className="font-medium">{parsedCV.personalDetails.location}</span>
              </div>
            )}
            {parsedCV.personalDetails.linkedIn && (
              <div className="flex items-center">
                <Linkedin className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm text-gray-600 mr-2">LinkedIn:</span>
                <a
                  href={parsedCV.personalDetails.linkedIn}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-medium text-blue-600 hover:text-blue-800 flex items-center"
                >
                  Profile <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </div>
            )}
            {parsedCV.personalDetails.website && (
              <div className="flex items-center">
                <Globe className="h-4 w-4 text-gray-500 mr-2" />
                <span className="text-sm text-gray-600 mr-2">Website:</span>
                <a
                  href={parsedCV.personalDetails.website}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="font-medium text-blue-600 hover:text-blue-800 flex items-center"
                >
                  Visit <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Professional Summary */}
      {parsedCV.summary && (
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center">
              <FileText className="mr-2 h-5 w-5 text-green-500" />
              Professional Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-700 leading-relaxed">{parsedCV.summary}</p>
          </CardContent>
        </Card>
      )}

      {/* Work Experience */}
      {parsedCV.workExperience && parsedCV.workExperience.length > 0 && (
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center">
              <Briefcase className="mr-2 h-5 w-5 text-purple-500" />
              Work Experience
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {parsedCV.workExperience.map((exp, index) => (
                <div key={index} className={index > 0 ? 'border-t pt-6' : ''}>
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="font-semibold text-gray-800">{exp.title}</h3>
                      <div className="flex items-center text-gray-600 mt-1">
                        <Building className="h-4 w-4 mr-1" />
                        <span>{exp.company}</span>
                      </div>
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span>{formatDate(exp.startDate)} - {formatDate(exp.endDate || 'Present')}</span>
                    </div>
                  </div>

                  {exp.responsibilities && exp.responsibilities.length > 0 && (
                    <div className="mb-3">
                      <h4 className="font-medium text-gray-700 mb-2">Responsibilities:</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {exp.responsibilities.map((resp, respIndex) => (
                          <li key={respIndex} className="text-gray-600 text-sm">{resp}</li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {exp.achievements && exp.achievements.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Achievements:</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {exp.achievements.map((achievement, achIndex) => (
                          <li key={achIndex} className="text-gray-600 text-sm">{achievement}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Education */}
      {parsedCV.education && parsedCV.education.length > 0 && (
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center">
              <GraduationCap className="mr-2 h-5 w-5 text-blue-500" />
              Education
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {parsedCV.education.map((edu, index) => (
                <div key={index} className={index > 0 ? 'border-t pt-4' : ''}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-800">{edu.degree}</h3>
                      {edu.field && (
                        <p className="text-gray-600 text-sm">Field: {edu.field}</p>
                      )}
                      <div className="flex items-center text-gray-600 mt-1">
                        <Building className="h-4 w-4 mr-1" />
                        <span>{edu.institution}</span>
                      </div>
                      {edu.gpa && (
                        <p className="text-gray-600 text-sm mt-1">GPA: {edu.gpa}</p>
                      )}
                    </div>
                    <div className="flex items-center text-sm text-gray-500">
                      <Calendar className="h-4 w-4 mr-1" />
                      <span>{formatDate(edu.startDate)} - {formatDate(edu.endDate || 'Present')}</span>
                    </div>
                  </div>

                  {edu.achievements && edu.achievements.length > 0 && (
                    <div className="mt-3">
                      <h4 className="font-medium text-gray-700 mb-2">Achievements:</h4>
                      <ul className="list-disc list-inside space-y-1">
                        {edu.achievements.map((achievement, achIndex) => (
                          <li key={achIndex} className="text-gray-600 text-sm">{achievement}</li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Skills */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Technical Skills */}
        {parsedCV.skills?.technical && parsedCV.skills.technical.length > 0 && (
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg flex items-center">
                <Code className="mr-2 h-5 w-5 text-orange-500" />
                Technical Skills
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {parsedCV.skills.technical.map((skill, index) => (
                  <Badge key={index} variant="secondary" className="bg-orange-100 text-orange-800">
                    {skill}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Soft Skills */}
        {parsedCV.skills?.soft && parsedCV.skills.soft.length > 0 && (
          <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
            <CardHeader>
              <CardTitle className="text-gray-800 text-lg flex items-center">
                <MessageSquare className="mr-2 h-5 w-5 text-green-500" />
                Soft Skills
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {parsedCV.skills.soft.map((skill, index) => (
                  <Badge key={index} variant="secondary" className="bg-green-100 text-green-800">
                    {skill}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Certifications */}
      {parsedCV.certifications && parsedCV.certifications.length > 0 && (
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center">
              <Award className="mr-2 h-5 w-5 text-yellow-500" />
              Certifications
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {parsedCV.certifications.map((cert, index) => (
                <div key={index} className={index > 0 ? 'border-t pt-4' : ''}>
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-semibold text-gray-800">{cert.name}</h3>
                      {cert.issuer && (
                        <p className="text-gray-600 text-sm">Issued by: {cert.issuer}</p>
                      )}
                    </div>
                    <div className="text-sm text-gray-500">
                      {cert.date && (
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          <span>{formatDate(cert.date)}</span>
                        </div>
                      )}
                      {cert.expiryDate && (
                        <div className="flex items-center mt-1">
                          <span className="text-xs">Expires: {formatDate(cert.expiryDate)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Projects */}
      {parsedCV.projects && parsedCV.projects.length > 0 && (
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center">
              <FolderOpen className="mr-2 h-5 w-5 text-indigo-500" />
              Projects
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {parsedCV.projects.map((project, index) => (
                <div key={index} className={index > 0 ? 'border-t pt-6' : ''}>
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h3 className="font-semibold text-gray-800">{project.name}</h3>
                      {project.url && (
                        <a
                          href={project.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:text-blue-800 text-sm flex items-center mt-1"
                        >
                          View Project <ExternalLink className="h-3 w-3 ml-1" />
                        </a>
                      )}
                    </div>
                    {(project.startDate || project.endDate) && (
                      <div className="flex items-center text-sm text-gray-500">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>
                          {formatDate(project.startDate || '')} - {formatDate(project.endDate || 'Present')}
                        </span>
                      </div>
                    )}
                  </div>

                  {project.description && (
                    <p className="text-gray-700 text-sm mb-3">{project.description}</p>
                  )}

                  {project.technologies && project.technologies.length > 0 && (
                    <div>
                      <h4 className="font-medium text-gray-700 mb-2">Technologies:</h4>
                      <div className="flex flex-wrap gap-2">
                        {project.technologies.map((tech, techIndex) => (
                          <Badge key={techIndex} variant="outline" className="text-xs">
                            {tech}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Languages */}
      {parsedCV.languages && parsedCV.languages.length > 0 && (
        <Card className="bg-white border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-gray-800 text-lg flex items-center">
              <Languages className="mr-2 h-5 w-5 text-pink-500" />
              Languages
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {parsedCV.languages.map((lang, index) => (
                <div key={index} className="flex justify-between items-center">
                  <span className="font-medium text-gray-800">{lang.name}</span>
                  {lang.proficiency && (
                    <Badge variant="outline" className="text-xs">
                      {lang.proficiency}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default CVStructuredDetailsTab;
