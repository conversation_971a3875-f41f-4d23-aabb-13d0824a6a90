import React from 'react';
import { motion } from 'framer-motion';
import {
  Upload,
  Briefcase,
  CheckCircle,
  BarChart2,
  ArrowRight,
  FileText,
  Users,
  Award,
  TrendingUp
} from 'lucide-react';

export const HowItWorks = () => {
  const features = [
    {
      id: 'upload-cvs',
      title: 'Upload CVs',
      icon: <Upload className="h-8 w-8 text-white" />,
      color: 'bg-gradient-to-r from-blue-500 to-blue-600',
      description: 'Easily upload candidate CVs in multiple formats including PDF, DOCX, and TXT. Our system automatically parses and extracts key information for your agency.',
      steps: [
        'Drag and drop multiple files or use the file browser',
        'Automatic parsing of contact details, skills, experience, and education',
        'Organize candidates into talent pools for better management',
        'Bulk upload CVs to process multiple candidates at once'
      ],
      image: '/images/upload-cvs-demo.png',
      altImage: 'https://images.unsplash.com/photo-1586281380349-632531db7ed4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'
    },
    {
      id: 'manage-companies',
      title: 'Manage Companies',
      icon: <Briefcase className="h-8 w-8 text-white" />,
      color: 'bg-gradient-to-r from-purple-500 to-purple-600',
      description: 'Create and manage company profiles with detailed requirements, skills, and qualifications needed for their open positions.',
      steps: [
        'Add multiple company profiles to your agency account',
        'Define required skills, experience levels, and qualifications for each company',
        'Upload company logos and customize company details',
        'Manage multiple companies from a single dashboard'
      ],
      image: '/images/post-jobs-demo.png',
      altImage: 'https://images.unsplash.com/photo-*************-868a62a9f521?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1932&q=80'
    },
    {
      id: 'automatic-evaluation',
      title: 'Automatic Evaluation',
      icon: <CheckCircle className="h-8 w-8 text-white" />,
      color: 'bg-gradient-to-r from-green-500 to-green-600',
      description: 'Our AI-powered system automatically evaluates candidates against company requirements, helping agencies match the right talent to the right opportunities.',
      steps: [
        'AI analyzes CV content against company job requirements',
        'Skills matching with percentage-based scoring for each company',
        'Experience evaluation based on relevance to company needs',
        'Evaluate candidates against multiple companies simultaneously'
      ],
      image: '/images/evaluation-demo.png',
      altImage: 'https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'
    },
    {
      id: 'view-reports',
      title: 'View Reports',
      icon: <BarChart2 className="h-8 w-8 text-white" />,
      color: 'bg-gradient-to-r from-orange-500 to-orange-600',
      description: 'Generate comprehensive reports on your CV evaluation process, match quality, and candidate skills to optimize your agency operations.',
      steps: [
        'Visual dashboards showing candidate evaluation results',
        'Match quality metrics across companies and positions',
        'Skill distribution analysis in your candidate pool',
        'Custom reports for clients and agency management'
      ],
      image: '/images/reports-demo.png',
      altImage: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80'
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section className="py-20 bg-recruiter-darknavy" id="how-it-works">
      <div className="container mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">How Our CV Evaluation Process Works</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our AI-powered platform helps recruitment agencies evaluate CVs against company requirements to find the best matches.
          </p>
        </div>

        <motion.div
          className="space-y-24"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, amount: 0.2 }}
        >
          {features.map((feature, index) => (
            <motion.div
              key={feature.id}
              className={`flex flex-col ${index % 2 === 1 ? 'lg:flex-row-reverse' : 'lg:flex-row'} items-center gap-12`}
              variants={itemVariants}
            >
              {/* Content */}
              <div className="lg:w-1/2 space-y-6">
                <div className={`w-16 h-16 rounded-2xl ${feature.color} flex items-center justify-center mb-6`}>
                  {feature.icon}
                </div>
                <h3 className="text-3xl font-bold text-white">{feature.title}</h3>
                <p className="text-xl text-gray-300">{feature.description}</p>

                <div className="space-y-4 mt-8">
                  <h4 className="text-xl font-semibold text-white">How it works:</h4>
                  <ul className="space-y-3">
                    {feature.steps.map((step, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <div className="mt-1 flex-shrink-0">
                          <div className="w-6 h-6 rounded-full bg-blue-500 flex items-center justify-center text-white text-sm font-medium">
                            {idx + 1}
                          </div>
                        </div>
                        <span className="text-gray-300">{step}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              </div>

              {/* Image */}
              <div className="lg:w-1/2 rounded-xl overflow-hidden shadow-2xl transform hover:scale-105 transition-transform duration-300">
                <img
                  src={feature.altImage}
                  alt={feature.title}
                  className="w-full h-[300px] object-cover"
                />
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Additional Benefits */}
        <div className="mt-32">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Boost Your Agency's Productivity by Over 70%</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Recruitment agencies using our platform report significant improvements in their CV evaluation and candidate matching metrics.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <FileText className="h-8 w-8 text-blue-400" />,
                title: "CV Processing",
                description: "Process up to 1,000 CVs per day with our automated system",
                stat: "10x faster"
              },
              {
                icon: <Users className="h-8 w-8 text-purple-400" />,
                title: "Candidate Matching",
                description: "Match candidates to companies with 95% accuracy using AI",
                stat: "95% accuracy"
              },
              {
                icon: <Award className="h-8 w-8 text-green-400" />,
                title: "Client Satisfaction",
                description: "Improve client satisfaction with better candidate matches",
                stat: "40% better"
              },
              {
                icon: <TrendingUp className="h-8 w-8 text-orange-400" />,
                title: "Agency Efficiency",
                description: "Reduce evaluation time by eliminating manual screening",
                stat: "70% faster"
              }
            ].map((benefit, index) => (
              <motion.div
                key={index}
                className="bg-card-gradient rounded-xl p-8 border border-gray-800 hover:border-recruiter-blue transition-colors duration-300"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
              >
                <div className="w-14 h-14 rounded-full bg-gradient-to-br from-recruiter-blue/20 to-recruiter-lightblue/20 flex items-center justify-center mb-6">
                  {benefit.icon}
                </div>
                <h3 className="text-xl font-bold text-white mb-2">{benefit.title}</h3>
                <p className="text-gray-300 mb-4">{benefit.description}</p>
                <div className="text-2xl font-bold bg-gradient-to-r from-recruiter-blue to-recruiter-lightblue text-transparent bg-clip-text">{benefit.stat}</div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="mt-24 text-center">
          <a
            href="/signup"
            className="inline-flex items-center bg-recruiter-lightblue hover:bg-blue-500 text-white py-3 px-8 rounded-lg text-lg font-medium transition-colors duration-300"
          >
            Start Evaluating Candidates Today <ArrowRight className="ml-2 h-5 w-5" />
          </a>
        </div>
      </div>
    </section>
  );
};
