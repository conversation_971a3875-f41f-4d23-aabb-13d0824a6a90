import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useAuth } from './AuthContext';
import * as usageService from '@/services/supabase/usage';
import * as profileService from '@/services/supabase/profiles';
import { UsageType } from '@/services/supabase/usage';

// Define the context type
interface UsageContextType {
  usageLimits: Record<UsageType, { hasReachedLimit: boolean; currentUsage: number; limit: number }>;
  isLoading: boolean;
  error: Error | null;
  refreshUsage: () => Promise<void>;
}

// Create the context
const UsageContext = createContext<UsageContextType | undefined>(undefined);

// Create a provider component
export const UsageProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [usageLimits, setUsageLimits] = useState<Record<UsageType, { hasReachedLimit: boolean; currentUsage: number; limit: number }>>({
    cv_uploads: { hasReachedLimit: false, currentUsage: 0, limit: 0 },
    active_jobs: { hasReachedLimit: false, currentUsage: 0, limit: 0 },
    company_profiles: { hasReachedLimit: false, currentUsage: 0, limit: 0 },
    team_members: { hasReachedLimit: false, currentUsage: 0, limit: 0 },
  });

  // Function to fetch all usage limits at once
  const fetchUsageLimits = useCallback(async () => {
    if (!user) {
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Get user's subscription tier
      const profile = await profileService.getProfile(user.id);
      if (!profile) {
        throw new Error('Profile not found');
      }

      const tier = profile.subscription_tier || 'starter';

      // Get usage limits for all types at once
      const usageTypes: UsageType[] = ['cv_uploads', 'active_jobs', 'company_profiles', 'team_members'];
      
      const results = await Promise.all(
        usageTypes.map(async (type) => {
          const result = await usageService.checkUsageLimit(user.id, type, tier as 'starter' | 'growth' | 'pro');
          return { type, result };
        })
      );

      // Update state with all results
      const newUsageLimits = { ...usageLimits };
      results.forEach(({ type, result }) => {
        newUsageLimits[type] = result;
      });

      setUsageLimits(newUsageLimits);
    } catch (err) {
      console.error('Error fetching usage limits:', err);
      setError(err instanceof Error ? err : new Error('Unknown error fetching usage limits'));
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  // Fetch usage limits on mount and when user changes
  useEffect(() => {
    fetchUsageLimits();
  }, [fetchUsageLimits]);

  // Provide a function to manually refresh usage data
  const refreshUsage = useCallback(async () => {
    await fetchUsageLimits();
  }, [fetchUsageLimits]);

  return (
    <UsageContext.Provider
      value={{
        usageLimits,
        isLoading,
        error,
        refreshUsage,
      }}
    >
      {children}
    </UsageContext.Provider>
  );
};

// Create a hook to use the context
export const useUsage = () => {
  const context = useContext(UsageContext);
  if (context === undefined) {
    throw new Error('useUsage must be used within a UsageProvider');
  }
  return context;
};

// Create a hook to check a specific usage limit
export const useUsageLimit = (usageType: UsageType) => {
  const { usageLimits, isLoading, error, refreshUsage } = useUsage();
  return {
    data: usageLimits[usageType],
    isLoading,
    error,
    refresh: refreshUsage,
  };
};
