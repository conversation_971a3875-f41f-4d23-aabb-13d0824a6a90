import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>R<PERSON>, <PERSON> } from 'lucide-react';
import { Link } from 'react-router-dom';

export const SimpleHero: React.FC = () => {
  const [userEmail, setUserEmail] = useState('');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (userEmail) {
      // In a real implementation, this would show the upload modal
      // For now, we'll just redirect to signup
      window.location.href = '/signup';
    }
  };
  
  return (
    <section className="relative">
      <div className="relative z-10 max-w-screen-xl mx-auto px-4 py-28 md:px-8">
        <div className="space-y-5 max-w-4xl mx-auto text-center">
          <motion.h2 
            className="text-4xl text-white font-extrabold mx-auto md:text-5xl"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
          >
            AI-Powered CV Evaluation Platform for Agencies
          </motion.h2>
          
          <motion.p 
            className="max-w-2xl mx-auto text-gray-400"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            Upload CVs, match against company requirements, and deliver better candidates to your clients. 
            Our intelligent system helps agencies evaluate talent more effectively.
          </motion.p>
          
          <motion.form 
            onSubmit={handleSubmit}
            className="justify-center items-center gap-x-3 sm:flex"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
          >
            <input 
              type="email" 
              value={userEmail}
              onChange={(e) => setUserEmail(e.target.value)}
              placeholder="Enter your email" 
              className="w-full px-3 py-2.5 text-gray-400 bg-gray-700 focus:bg-gray-900 duration-150 outline-none rounded-lg shadow sm:max-w-sm sm:w-auto" 
              required 
            />
            <motion.button 
              type="submit" 
              className="flex items-center justify-center gap-x-2 py-2.5 px-4 mt-3 w-full text-sm text-white font-medium bg-sky-500 hover:bg-sky-400 active:bg-sky-600 duration-150 rounded-lg sm:mt-0 sm:w-auto"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Apply Now
              <ArrowRight className="w-5 h-5" />
            </motion.button>
          </motion.form>
          
          <motion.p 
            className="text-gray-500 text-xs mt-2"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            (Demo Version - Limited Features Available)
          </motion.p>
          
          <motion.div 
            className="flex justify-center items-center gap-x-4 text-gray-400 text-sm"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star key={i} className="w-5 h-5 text-yellow-400" fill="currentColor" />
              ))}
            </div>
            <p><span className="text-gray-100">5.0</span> by over 65 companies</p>
          </motion.div>
        </div>
      </div>
      
      {/* The blurred gradient background */}
      <div 
        className="absolute inset-0 m-auto max-w-xs h-[357px] blur-[118px] sm:max-w-md md:max-w-lg" 
        style={{ 
          background: 'linear-gradient(106.89deg, rgba(192, 132, 252, 0.11) 15.73%, rgba(14, 165, 233, 0.41) 15.74%, rgba(232, 121, 249, 0.26) 56.49%, rgba(79, 70, 229, 0.4) 115.91%)'
        }}
      ></div>
    </section>
  );
};
