import React, { useEffect, useRef, useState } from 'react';
import { usePayPalScriptContext } from './PayPalScriptProvider';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

interface PayPalPaymentButtonProps {
  amount: number;
  currency?: string;
  description?: string;
  onSuccess?: (details: any) => void;
  onError?: (error: any) => void;
  onCancel?: () => void;
  className?: string;
}

declare global {
  interface Window {
    paypal?: any;
  }
}

const PayPalPaymentButton: React.FC<PayPalPaymentButtonProps> = ({
  amount,
  currency = 'USD',
  description = 'Payment for services',
  onSuccess,
  onError,
  onCancel,
  className = '',
}) => {
  const { isLoaded, failedToLoad, reload } = usePayPalScriptContext();
  const [buttonRendered, setButtonRendered] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const buttonContainerRef = useRef<HTMLDivElement>(null);
  const { toast } = useToast();

  // Render PayPal button when SDK is loaded
  useEffect(() => {
    // Skip if SDK not loaded, button already rendered, or no container ref
    if (!isLoaded || buttonRendered || !buttonContainerRef.current) return;

    // Clear any existing content
    if (buttonContainerRef.current.firstChild) {
      buttonContainerRef.current.innerHTML = '';
    }

    try {
      // Create payment button
      window.paypal?.Buttons({
        style: {
          layout: 'vertical',
          color: 'blue',
          shape: 'rect',
          label: 'pay',
        },
        createOrder: (data: any, actions: any) => {
          setIsProcessing(true);
          return actions.order.create({
            purchase_units: [
              {
                description,
                amount: {
                  currency_code: currency,
                  value: amount.toString(),
                },
              },
            ],
            application_context: {
              shipping_preference: 'NO_SHIPPING',
            },
          });
        },
        onApprove: async (data: any, actions: any) => {
          try {
            // Capture the funds from the transaction
            const details = await actions.order.capture();
            console.log('Payment successful:', details);
            
            // Call onSuccess callback with payment details
            if (onSuccess) {
              onSuccess({
                orderID: data.orderID,
                payerID: data.payerID,
                paymentID: details.id,
                status: details.status,
                amount: {
                  value: amount,
                  currency,
                },
              });
            }

            toast({
              title: 'Payment successful',
              description: 'Your payment has been processed successfully.',
            });
          } catch (error) {
            console.error('Error processing payment:', error);
            if (onError) onError(error);
            
            toast({
              title: 'Payment error',
              description: 'There was a problem processing your payment. Please try again.',
              variant: 'destructive',
            });
          } finally {
            setIsProcessing(false);
          }
        },
        onCancel: () => {
          console.log('Payment canceled by user');
          setIsProcessing(false);
          if (onCancel) onCancel();
          
          toast({
            title: 'Payment canceled',
            description: 'You canceled the payment process.',
          });
        },
        onError: (err: any) => {
          console.error('PayPal payment error:', err);
          setIsProcessing(false);
          if (onError) onError(err);
          
          toast({
            title: 'Payment error',
            description: 'There was a problem with PayPal. Please try again later.',
            variant: 'destructive',
          });
        },
      }).render(buttonContainerRef.current);

      setButtonRendered(true);
    } catch (error) {
      console.error('Error rendering PayPal button:', error);
      setButtonRendered(false);
      
      toast({
        title: 'PayPal error',
        description: 'Failed to load PayPal payment options. Please try again.',
        variant: 'destructive',
      });
    }
  }, [isLoaded, buttonRendered, amount, currency, description, onSuccess, onError, onCancel]);

  // Reset button when amount changes
  useEffect(() => {
    setButtonRendered(false);
  }, [amount, currency]);

  // Show loading state
  if (isProcessing) {
    return (
      <div className={`flex items-center justify-center p-4 ${className}`}>
        <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
        <span>Processing payment...</span>
      </div>
    );
  }

  // Show error with retry button
  if (failedToLoad) {
    return (
      <div className={`text-center ${className}`}>
        <p className="text-red-500 mb-2">Failed to load PayPal</p>
        <button
          onClick={reload}
          className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90"
        >
          Retry
        </button>
      </div>
    );
  }

  // Show loading or button container
  return (
    <div className={className}>
      {!isLoaded && (
        <div className="flex items-center justify-center p-4">
          <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
          <span>Loading PayPal...</span>
        </div>
      )}
      <div
        ref={buttonContainerRef}
        className={!isLoaded ? 'hidden' : ''}
        style={{ minHeight: '45px' }}
      />
    </div>
  );
};

export default PayPalPaymentButton;
