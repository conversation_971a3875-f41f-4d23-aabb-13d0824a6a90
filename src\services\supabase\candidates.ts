import { supabase } from '@/lib/supabase';
import { Database } from '@/types/supabase';
import { safeDbOperation } from '@/lib/errorHandling';
import { RealtimeChannel } from '@supabase/supabase-js';
import { sendApplicationReceivedEmail } from '@/services/email/candidateNotifications';
import { candidateNotificationOrchestrator } from '@/services/notifications/candidateNotificationOrchestrator';

// Candidate portal specific types
export interface CandidateApplication {
  id: string;
  created_at: string;
  applied_at: string;
  status: string;
  notes: string | null;
  job_title: string;
  job_location: string;
  job_type: string;
  company_name: string;
  company_logo_url: string | null;
}

export interface ApplyToJobPayload {
  jobId: string;
  candidateName: string;
  candidateEmail: string;
  cvFileUrl: string;
  coverLetterContent?: string;
}

export type Candidate = Database['public']['Tables']['candidates']['Row'];
export type CandidateInsert = Database['public']['Tables']['candidates']['Insert'];
export type CandidateUpdate = Database['public']['Tables']['candidates']['Update'];

// Define event types for real-time updates
export type CandidateRealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE';
export type CandidateRealtimeCallback = (event: CandidateRealtimeEvent, candidate: Candidate) => void;

/**
 * Get all candidates for the current user (owned + candidates who applied to their jobs)
 */
export const getCandidates = async (jobId?: string, companyId?: string): Promise<Candidate[]> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Build the query - RLS policies will handle access control
      // This will include:
      // 1. Candidates created by the user (user_id = userId)
      // 2. Candidates who applied to jobs owned by the user (via RLS policy)
      let query = supabase
        .from('candidates')
        .select(`
          *,
          jobs(
            id,
            title,
            company_id,
            user_id,
            companies(
              id,
              name
            )
          ),
          companies(
            id,
            name
          )
        `)
        .order('created_at', { ascending: false });

      // Filter by specific job if provided
      if (jobId) {
        query = query.eq('job_id', jobId);
      }

      // Filter by company if provided
      if (companyId) {
        // Filter by company_id directly (for candidate pool) or through job relationship
        query = query.or(`company_id.eq.${companyId},jobs.company_id.eq.${companyId}`);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching candidates:', error);
        throw error;
      }

      return data || [];
    },
    'Failed to fetch candidates',
    []
  );
};

/**
 * Get a candidate by ID
 */
export const getCandidate = async (id: string): Promise<Candidate | null> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // Let RLS policies handle access control - this will include:
      // 1. Candidates created by the user (user_id = userId)
      // 2. Candidates who applied to jobs owned by the user
      // 3. Candidates for jobs from companies the user is a team member of
      const { data, error } = await supabase
        .from('candidates')
        .select('*')
        .eq('id', id)
        .single();

      if (error) {
        // If no rows found, return null instead of throwing an error
        if (error.code === 'PGRST116') {
          return null;
        }
        throw error;
      }

      return data;
    },
    `Failed to fetch candidate with ID ${id}`,
    null // Return null as fallback
  );
};

/**
 * Create a new candidate
 */
export const createCandidate = async (candidate: CandidateInsert): Promise<Candidate> => {
  return safeDbOperation(
    async () => {
      const { data, error } = await supabase
        .from('candidates')
        .insert(candidate)
        .select()
        .single();

      if (error) throw error;

      return data;
    },
    'Failed to create candidate'
  );
};

/**
 * Update a candidate
 */
export const updateCandidate = async (id: string, candidate: CandidateUpdate): Promise<Candidate> => {
  return safeDbOperation(
    async () => {
      // Get the current candidate data to check for status changes
      const { data: currentCandidate, error: fetchError } = await supabase
        .from('candidates')
        .select('status, name, email, job_id')
        .eq('id', id)
        .single();

      if (fetchError) throw fetchError;

      const { data, error } = await supabase
        .from('candidates')
        .update(candidate)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      // Check if status changed and trigger notification
      if (candidate.status && currentCandidate.status !== candidate.status) {
        try {
          console.log(`Status changed for candidate ${id}: ${currentCandidate.status} → ${candidate.status}`);

          // Trigger notification asynchronously (don't wait for it to complete)
          candidateNotificationOrchestrator.handleStatusChange(
            id,
            currentCandidate.status || 'unknown',
            candidate.status,
            {
              jobId: currentCandidate.job_id
            }
          ).catch(error => {
            console.error('Failed to send status change notification:', error);
          });
        } catch (notificationError) {
          console.error('Error triggering status change notification:', notificationError);
          // Don't throw - notification failure shouldn't break the update
        }
      }

      return data;
    },
    `Failed to update candidate with ID ${id}`
  );
};

/**
 * Delete a candidate
 */
export const deleteCandidate = async (id: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase
        .from('candidates')
        .delete()
        .eq('id', id);

      if (error) throw error;

      return true;
    },
    `Failed to delete candidate with ID ${id}`,
    false // Return false as fallback
  );
};

/**
 * Upload a CV file
 */
export const uploadCV = async (file: File, checkUsageLimit = true): Promise<string> => {
  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      // If usage limit checking is enabled
      if (checkUsageLimit) {
        // Get user's subscription tier and free trial status
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('subscription_tier, subscription_status, free_trial_used')
          .eq('user_id', userId)
          .single();

        if (profileError) throw profileError;

        // Get current month's usage
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format
        const { data: usage, error: usageError } = await supabase
          .from('usage_tracking')
          .select('cv_uploads_count')
          .eq('user_id', userId)
          .eq('month_year', currentMonth)
          .single();

        // If no usage record exists, create one
        if (usageError && usageError.code === 'PGRST116') {
          await supabase
            .from('usage_tracking')
            .insert({
              user_id: userId,
              month_year: currentMonth,
              cv_uploads_count: 0,
              active_jobs_count: 0,
              company_profiles_count: 0,
              team_members_count: 0,
            });
        } else if (usageError) {
          throw usageError;
        }

        // Get subscription limits
        const { data: limits, error: limitsError } = await supabase
          .from('subscription_limits')
          .select('cv_upload_limit')
          .eq('tier', profile.subscription_tier)
          .single();

        if (limitsError) throw limitsError;

        // Check if user has reached their limit
        const currentUsage = usage?.cv_uploads_count || 0;

        // If this is their first CV upload (free trial) and they haven't used it yet, allow it
        const isFirstUpload = currentUsage === 0;
        const freeTrialUsed = profile.free_trial_used || false;

        // If subscription is not active, only allow the free trial
        if (profile.subscription_status !== 'active') {
          if (isFirstUpload && !freeTrialUsed) {
            // Allow the free trial upload
            console.log('Allowing free trial CV upload');
          } else {
            throw new Error('Your subscription is not active. Please subscribe to upload more CVs.');
          }
        }
        // If subscription is active but they've reached their limit
        else if (currentUsage >= limits.cv_upload_limit) {
          throw new Error(`You have reached your monthly CV upload limit (${limits.cv_upload_limit}). Please upgrade your plan to upload more CVs.`);
        }
      }

      // Upload the file
      const fileExt = file.name.split('.').pop();
      const fileName = `${Math.random()}.${fileExt}`;
      const filePath = `cvs/${fileName}`;

      const { error } = await supabase.storage
        .from('cvs')
        .upload(filePath, file);

      if (error) throw error;

      const { data } = supabase.storage.from('cvs').getPublicUrl(filePath);

      // If usage limit checking is enabled, increment the usage counter
      if (checkUsageLimit && userId) {
        const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

        try {
          // Get current usage to check if this is the first upload
          const { data: currentUsage } = await supabase
            .from('usage_tracking')
            .select('cv_uploads_count')
            .eq('user_id', userId)
            .eq('month_year', currentMonth)
            .single();

          const isFirstUpload = !currentUsage || currentUsage.cv_uploads_count === 0;

          // Increment the CV uploads count
          const { error } = await supabase.rpc('increment_usage_counter', {
            p_user_id: userId,
            p_month_year: currentMonth,
            p_counter_name: 'cv_uploads_count',
            p_increment_by: 1
          });

          if (error) {
            console.error('Failed to increment usage counter:', error);
            // Don't throw here, we still want to return the URL even if tracking fails
          }

          // If this is the first upload, mark the free trial as used
          if (isFirstUpload) {
            const { error: updateError } = await supabase
              .from('profiles')
              .update({ free_trial_used: true })
              .eq('user_id', userId);

            if (updateError) {
              console.error('Failed to mark free trial as used:', updateError);
              // Don't throw here, we still want to return the URL even if tracking fails
            }
          }
        } catch (error) {
          console.error('Failed to increment usage counter:', error);
          // Don't throw here, we still want to return the URL even if tracking fails
        }
      }

      return data.publicUrl;
    },
    `Failed to upload CV file: ${file.name}`
  );
};

/**
 * Get candidate statistics
 *
 * Since RLS is temporarily disabled, this function manually filters candidates
 * to only show statistics for those belonging to the current user.
 */
export const getCandidateStatistics = async (jobId?: string) => {
  const defaultStats = {
    total: 0,
    new: 0,
    reviewing: 0,
    interviewed: 0,
    hired: 0,
    rejected: 0
  };

  return safeDbOperation(
    async () => {
      // Get the current user's ID
      const { data: { user } } = await supabase.auth.getUser();
      const userId = user?.id;

      if (!userId) {
        throw new Error('User not authenticated');
      }

      let query = supabase
        .from('candidates')
        .select('id, status, user_id');

      if (jobId) {
        query = query.eq('job_id', jobId);
      }

      // Let RLS policies handle access control - this will include:
      // 1. Candidates created by the user (user_id = userId)
      // 2. Candidates who applied to jobs owned by the user
      // 3. Candidates for jobs from companies the user is a team member of
      const { data, error } = await query;

      if (error) throw error;

      const total = data.length;
      const new_candidates = data.filter(candidate => candidate.status === 'new').length;
      const reviewing = data.filter(candidate => candidate.status === 'reviewing' || candidate.status === 'screening').length;
      const interviewed = data.filter(candidate => candidate.status === 'interviewed' || candidate.status === 'interview').length;
      const hired = data.filter(candidate => candidate.status === 'hired' || candidate.status === 'offer').length;
      const rejected = data.filter(candidate => candidate.status === 'rejected').length;

      return {
        total,
        new: new_candidates,
        reviewing,
        interviewed,
        hired,
        rejected
      };
    },
    `Failed to fetch candidate statistics${jobId ? ` for job ${jobId}` : ''}`,
    defaultStats // Return default statistics as fallback
  );
};

/**
 * Subscribe to real-time candidate updates
 *
 * @param userId User ID to filter candidates by
 * @param jobId Optional job ID to filter candidates by
 * @param callback Function to call when a candidate is updated
 * @returns Supabase subscription that can be unsubscribed from
 */
export const subscribeToCandidates = (
  userId: string,
  jobId?: string,
  callback?: CandidateRealtimeCallback
): RealtimeChannel => {
  // Create a channel for candidate updates
  const channel = supabase.channel(`candidates-${userId}-${jobId || 'all'}`);

  // Set up filters for the subscription
  const insertFilter: Record<string, string> = { user_id: `eq.${userId}` };
  const updateFilter: Record<string, string> = { user_id: `eq.${userId}` };
  const deleteFilter: Record<string, string> = { user_id: `eq.${userId}` };

  // Add job filter if provided
  if (jobId) {
    insertFilter.job_id = `eq.${jobId}`;
    updateFilter.job_id = `eq.${jobId}`;
    deleteFilter.job_id = `eq.${jobId}`;
  }

  // Subscribe to INSERT events
  channel.on(
    'postgres_changes',
    {
      event: 'INSERT',
      schema: 'public',
      table: 'candidates',
      filter: insertFilter
    },
    (payload) => {
      if (callback) {
        callback('INSERT', payload.new as Candidate);
      }
    }
  );

  // Subscribe to UPDATE events
  channel.on(
    'postgres_changes',
    {
      event: 'UPDATE',
      schema: 'public',
      table: 'candidates',
      filter: updateFilter
    },
    (payload) => {
      if (callback) {
        callback('UPDATE', payload.new as Candidate);
      }
    }
  );

  // Subscribe to DELETE events
  channel.on(
    'postgres_changes',
    {
      event: 'DELETE',
      schema: 'public',
      table: 'candidates',
      filter: deleteFilter
    },
    (payload) => {
      if (callback) {
        callback('DELETE', payload.old as Candidate);
      }
    }
  );

  // Start the subscription
  return channel.subscribe();
}

// ===== CANDIDATE PORTAL FUNCTIONS =====

/**
 * Apply to a job as a candidate
 */
export const applyToJob = async (payload: ApplyToJobPayload): Promise<string> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data: applicationId, error } = await supabase.rpc('apply_to_job', {
        job_uuid: payload.jobId,
        candidate_name: payload.candidateName,
        candidate_email: payload.candidateEmail,
        cv_file_url: payload.cvFileUrl,
        cover_letter_content: payload.coverLetterContent || null
      });

      if (error) {
        console.error('Error applying to job:', error);
        throw error;
      }

      // Send application received email notification
      try {
        // Get job and company details for the email
        const { data: jobData, error: jobError } = await supabase
          .from('jobs')
          .select(`
            title,
            companies:company_id (
              name
            )
          `)
          .eq('id', payload.jobId)
          .single();

        if (!jobError && jobData) {
          await sendApplicationReceivedEmail({
            candidateEmail: payload.candidateEmail,
            candidateName: payload.candidateName,
            jobTitle: jobData.title,
            companyName: jobData.companies?.name || 'Unknown Company',
            applicationId: applicationId,
            jobId: payload.jobId
          });
        }
      } catch (emailError) {
        console.error('Failed to send application received email:', emailError);
        // Don't throw - email failure shouldn't break the application process
      }

      return applicationId;
    },
    'Failed to apply to job'
  );
};

/**
 * Get candidate's applications
 */
export const getMyCandidateApplications = async (): Promise<CandidateApplication[]> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase.rpc('get_my_applications');

      if (error) {
        console.error('Error fetching candidate applications:', error);
        throw error;
      }

      return data || [];
    },
    'Failed to fetch your applications'
  );
};

/**
 * Check if user has already applied to a job
 */
export const hasAppliedToJob = async (jobId: string): Promise<boolean> => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        return false;
      }

      const { data, error } = await supabase
        .from('applications')
        .select('id')
        .eq('job_id', jobId)
        .eq('user_id', user.id)
        .limit(1);

      if (error) {
        console.error('Error checking application status:', error);
        return false;
      }

      return (data || []).length > 0;
    },
    'Failed to check application status',
    false
  );
};

/**
 * Get candidate profile information
 */
export const getCandidateProfile = async () => {
  return safeDbOperation(
    async () => {
      const { data: { user } } = await supabase.auth.getUser();

      if (!user) {
        throw new Error('User not authenticated');
      }

      // Return basic profile from user metadata
      return {
        id: user.id,
        email: user.email || '',
        name: user.user_metadata?.full_name || '',
        avatarUrl: user.user_metadata?.avatar_url,
        userType: user.user_metadata?.user_type || 'candidate'
      };
    },
    'Failed to fetch candidate profile'
  );
};

/**
 * Update candidate profile
 */
export const updateCandidateProfile = async (updates: {
  fullName?: string;
  avatarUrl?: string;
}): Promise<void> => {
  return safeDbOperation(
    async () => {
      const { error } = await supabase.auth.updateUser({
        data: {
          full_name: updates.fullName,
          avatar_url: updates.avatarUrl
        }
      });

      if (error) {
        throw error;
      }
    },
    'Failed to update profile'
  );
};