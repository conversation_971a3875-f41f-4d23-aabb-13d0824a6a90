
import { Link } from "react-router-dom";
import { ArrowRight } from "lucide-react";
import { JobSocialShare } from "@/components/JobSocialShare";

interface JobCardProps {
  title: string;
  company: string;
  description: string;
  isNew: boolean;
  applicants: number;
}

export const JobCard = ({ title, company, description, isNew, applicants }: JobCardProps) => {
  return (
    <div className="bg-card-gradient rounded-lg p-6 flex flex-col h-full">
      <div className="flex justify-between mb-4">
        <h3 className="text-white text-xl font-semibold">{title}</h3>
        <div className="flex space-x-2">
          {isNew && (
            <span className="bg-recruiter-lightblue text-white text-xs font-medium px-2 py-1 rounded">New</span>
          )}
          <span className="bg-[#222a45] text-white text-xs font-medium px-2 py-1 rounded">
            {applicants} Applicants
          </span>
        </div>
      </div>
      <p className="text-gray-400 mb-2">{company}</p>
      <p className="text-gray-300 mb-6 flex-grow">{description}</p>
      <div className="flex gap-2 mt-4">
        <Link 
          to="#" 
          className="flex-1 bg-recruiter-lightblue hover:bg-blue-500 text-white py-3 rounded text-center transition-colors flex items-center justify-center"
        >
          Apply Now
        </Link>
        <JobSocialShare 
          jobId={job.id}
          jobTitle={title}
          companyName={company}
        />
      </div>
    </div>
  );
};

