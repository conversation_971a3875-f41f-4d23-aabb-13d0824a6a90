import React, { Suspense } from 'react';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Float, Sphere, MeshDistortMaterial } from '@react-three/drei';
import * as THREE from 'three';

// Simple fallback component while loading
const Fallback = () => {
  return (
    <mesh>
      <sphereGeometry args={[1, 16, 16]} />
      <meshBasicMaterial color="#4F46E5" wireframe />
    </mesh>
  );
};

// 3D Scene Component
const Scene = () => {
  return (
    <>
      <ambientLight intensity={0.5} />
      <directionalLight position={[10, 10, 5]} intensity={1} />
      <directionalLight position={[-10, -10, -5]} intensity={0.5} color="#8B5CF6" />

      <Suspense fallback={<Fallback />}>
        <Float
          speed={4}
          rotationIntensity={0.5}
          floatIntensity={1}
          position={[0, 0, 0]}
        >
          <Sphere args={[1.2, 64, 64]} position={[0, 0, 0]}>
            <MeshDistortMaterial
              color={new THREE.Color("#4F46E5")}
              distort={0.4}
              speed={2}
              roughness={0.2}
              metalness={0.8}
            />
          </Sphere>
        </Float>

        <Float
          speed={2}
          rotationIntensity={0.2}
          floatIntensity={0.5}
          position={[2.5, 0, 0]}
        >
          <mesh position={[0, 0, 0]} scale={0.5}>
            <boxGeometry args={[1, 1.5, 0.05]} />
            <meshStandardMaterial color={new THREE.Color("#0EA5E9")} />
          </mesh>
        </Float>

        <Float
          speed={2}
          rotationIntensity={0.2}
          floatIntensity={0.5}
          position={[-2.5, 0, 0]}
        >
          <mesh position={[0, 0, 0]} scale={0.5}>
            <boxGeometry args={[1, 1.5, 0.05]} />
            <meshStandardMaterial color={new THREE.Color("#EC4899")} />
          </mesh>
        </Float>
      </Suspense>

      <OrbitControls enableZoom={false} enablePan={false} autoRotate autoRotateSpeed={0.5} />
    </>
  );
};

export const HeroScene3D: React.FC = () => {
  return (
    <div className="absolute inset-0 z-0">
      <Canvas
        camera={{ position: [0, 0, 5], fov: 45 }}
        gl={{
          antialias: true,
          alpha: true,
          powerPreference: 'high-performance'
        }}
      >
        <Scene />
      </Canvas>
    </div>
  );
};
