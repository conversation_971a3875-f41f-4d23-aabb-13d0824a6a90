import React, { useRef } from 'react';
import { useGSAP } from '@gsap/react';
import gsap from 'gsap';

// Floating CV Component
export const FloatingCV: React.FC<{ delay?: number; className?: string }> = ({ 
  delay = 0,
  className = ""
}) => {
  const cvRef = useRef<HTMLDivElement>(null);
  
  useGSAP(() => {
    if (cvRef.current) {
      gsap.fromTo(
        cvRef.current,
        { 
          y: '+=20',
          opacity: 0,
        },
        { 
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );
      
      // Floating animation
      gsap.to(cvRef.current, {
        y: '+=10',
        duration: 2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
      });
    }
  }, [delay]);
  
  return (
    <div 
      ref={cvRef} 
      className={`w-48 h-64 bg-white rounded-lg shadow-xl p-4 flex flex-col ${className}`}
      style={{ opacity: 0 }}
    >
      <div className="w-full h-4 bg-blue-500 rounded mb-3"></div>
      <div className="w-3/4 h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-1/2 h-3 bg-gray-200 rounded mb-4"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded mb-3"></div>
      <div className="w-1/2 h-6 bg-indigo-500 rounded mb-3"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-2/3 h-2 bg-gray-200 rounded"></div>
      <div className="mt-auto flex justify-between">
        <div className="w-8 h-8 rounded-full bg-green-500"></div>
        <div className="w-12 h-4 bg-blue-500 rounded"></div>
      </div>
    </div>
  );
};

// Floating Company Profile Component
export const FloatingCompany: React.FC<{ delay?: number; className?: string }> = ({ 
  delay = 0,
  className = ""
}) => {
  const companyRef = useRef<HTMLDivElement>(null);
  
  useGSAP(() => {
    if (companyRef.current) {
      gsap.fromTo(
        companyRef.current,
        { 
          y: '+=20',
          opacity: 0,
        },
        { 
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );
      
      // Floating animation
      gsap.to(companyRef.current, {
        y: '+=10',
        duration: 2.5,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: 0.5,
      });
    }
  }, [delay]);
  
  return (
    <div 
      ref={companyRef} 
      className={`w-56 h-72 bg-white rounded-lg shadow-xl p-4 flex flex-col ${className}`}
      style={{ opacity: 0 }}
    >
      <div className="flex items-center mb-4">
        <div className="w-12 h-12 rounded-full bg-purple-500 mr-3"></div>
        <div>
          <div className="w-32 h-4 bg-gray-800 rounded mb-1"></div>
          <div className="w-24 h-3 bg-gray-400 rounded"></div>
        </div>
      </div>
      <div className="w-full h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-full h-3 bg-gray-200 rounded mb-2"></div>
      <div className="w-3/4 h-3 bg-gray-200 rounded mb-4"></div>
      
      <div className="grid grid-cols-2 gap-2 mb-4">
        <div className="h-8 bg-blue-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-blue-500 rounded"></div>
        </div>
        <div className="h-8 bg-green-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-green-500 rounded"></div>
        </div>
        <div className="h-8 bg-purple-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-purple-500 rounded"></div>
        </div>
        <div className="h-8 bg-yellow-100 rounded flex items-center justify-center">
          <div className="w-16 h-3 bg-yellow-500 rounded"></div>
        </div>
      </div>
      
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded"></div>
      
      <div className="mt-auto flex justify-end">
        <div className="w-20 h-6 bg-indigo-500 rounded"></div>
      </div>
    </div>
  );
};

// Floating Match Result Component
export const FloatingMatch: React.FC<{ delay?: number; className?: string }> = ({ 
  delay = 0,
  className = ""
}) => {
  const matchRef = useRef<HTMLDivElement>(null);
  
  useGSAP(() => {
    if (matchRef.current) {
      gsap.fromTo(
        matchRef.current,
        { 
          y: '+=20',
          opacity: 0,
        },
        { 
          y: 0,
          opacity: 1,
          duration: 1,
          delay: delay,
          ease: 'power3.out',
        }
      );
      
      // Floating animation
      gsap.to(matchRef.current, {
        y: '+=10',
        duration: 2.2,
        repeat: -1,
        yoyo: true,
        ease: 'sine.inOut',
        delay: 0.3,
      });
      
      // Animate the progress bar
      gsap.fromTo(
        matchRef.current.querySelector('.progress-fill'),
        { width: '0%' },
        { 
          width: '85%',
          duration: 1.5,
          delay: delay + 0.5,
          ease: 'power2.out',
        }
      );
    }
  }, [delay]);
  
  return (
    <div 
      ref={matchRef} 
      className={`w-64 h-48 bg-white rounded-lg shadow-xl p-4 flex flex-col ${className}`}
      style={{ opacity: 0 }}
    >
      <div className="flex justify-between items-center mb-4">
        <div className="w-32 h-5 bg-gray-800 rounded"></div>
        <div className="w-12 h-12 rounded-full bg-indigo-500 flex items-center justify-center text-white font-bold">
          85%
        </div>
      </div>
      
      <div className="w-full h-4 bg-gray-200 rounded-full mb-4 overflow-hidden">
        <div className="progress-fill h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full" style={{ width: '0%' }}></div>
      </div>
      
      <div className="grid grid-cols-2 gap-2 mb-3">
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
        <div className="flex items-center">
          <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
          <div className="w-20 h-3 bg-gray-200 rounded"></div>
        </div>
      </div>
      
      <div className="w-full h-2 bg-gray-200 rounded mb-1"></div>
      <div className="w-3/4 h-2 bg-gray-200 rounded"></div>
      
      <div className="mt-auto flex justify-end">
        <div className="w-24 h-6 bg-green-500 rounded"></div>
      </div>
    </div>
  );
};
