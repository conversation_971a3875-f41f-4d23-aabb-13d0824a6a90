import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Mail, 
  Play, 
  Square, 
  RefreshCw, 
  Send, 
  CheckCircle, 
  XCircle, 
  Clock,
  AlertCircle
} from 'lucide-react';
import { useNotificationProcessor, useTestNotifications } from '@/hooks/use-notification-processor';
import { env } from '@/lib/env';

export const EmailNotificationSettings: React.FC = () => {
  const {
    isRunning,
    stats,
    start,
    stop,
    triggerProcessing,
    refreshStats,
    isEnabled
  } = useNotificationProcessor();

  const {
    testNotifications,
    isTesting,
    testResult,
    clearResult
  } = useTestNotifications();

  const [testEmail, setTestEmail] = useState('');

  const handleTestNotification = async () => {
    if (!testEmail) return;
    await testNotifications(testEmail);
  };

  if (!isEnabled) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Notifications
          </CardTitle>
          <CardDescription>
            Automated email notifications to candidates during the hiring process
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Email notifications are currently disabled. To enable them, set ENABLE_EMAIL_NOTIFICATIONS=true in your environment configuration.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Main Settings Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Notifications
          </CardTitle>
          <CardDescription>
            Automated email notifications to candidates during the hiring process
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Status Section */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-base font-medium">Notification Processor</Label>
              <p className="text-sm text-muted-foreground">
                Automatically processes and sends email notifications to candidates
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isRunning ? "default" : "secondary"}>
                {isRunning ? "Running" : "Stopped"}
              </Badge>
              {isRunning ? (
                <Button onClick={stop} variant="outline" size="sm">
                  <Square className="h-4 w-4 mr-2" />
                  Stop
                </Button>
              ) : (
                <Button onClick={start} size="sm">
                  <Play className="h-4 w-4 mr-2" />
                  Start
                </Button>
              )}
            </div>
          </div>

          <Separator />

          {/* Statistics Section */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Processing Statistics</Label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
                <div className="text-sm text-muted-foreground">Total Events</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-green-600">{stats.processed}</div>
                <div className="text-sm text-muted-foreground">Processed</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
                <div className="text-sm text-muted-foreground">Pending</div>
              </div>
              <div className="text-center p-3 bg-muted rounded-lg">
                <div className="text-2xl font-bold text-red-600">{stats.failed}</div>
                <div className="text-sm text-muted-foreground">Failed</div>
              </div>
            </div>
            <div className="flex gap-2">
              <Button onClick={refreshStats} variant="outline" size="sm">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Stats
              </Button>
              <Button onClick={triggerProcessing} variant="outline" size="sm">
                <Clock className="h-4 w-4 mr-2" />
                Process Now
              </Button>
            </div>
          </div>

          <Separator />

          {/* Test Section */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Test Notifications</Label>
            <p className="text-sm text-muted-foreground">
              Send a test email notification to verify the system is working correctly
            </p>
            <div className="flex gap-2">
              <Input
                type="email"
                placeholder="Enter test email address"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                className="flex-1"
              />
              <Button 
                onClick={handleTestNotification}
                disabled={!testEmail || isTesting}
                size="sm"
              >
                {isTesting ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Send className="h-4 w-4 mr-2" />
                )}
                Send Test
              </Button>
            </div>
            
            {testResult && (
              <Alert className={testResult.success ? "border-green-200 bg-green-50" : "border-red-200 bg-red-50"}>
                {testResult.success ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <AlertDescription className={testResult.success ? "text-green-800" : "text-red-800"}>
                  {testResult.message}
                </AlertDescription>
              </Alert>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Notification Types Card */}
      <Card>
        <CardHeader>
          <CardTitle>Automated Notification Types</CardTitle>
          <CardDescription>
            The following email notifications are automatically sent to candidates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Application Received</h4>
                <p className="text-sm text-muted-foreground">
                  Sent immediately when a candidate applies to a job position
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Interview Scheduled</h4>
                <p className="text-sm text-muted-foreground">
                  Sent when an interview is scheduled with calendar invite attachment
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Status Updates</h4>
                <p className="text-sm text-muted-foreground">
                  Sent when candidate status changes (screening, interview, offer, etc.)
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Interview Reminders</h4>
                <p className="text-sm text-muted-foreground">
                  Sent 24 hours and 1 hour before scheduled interviews
                </p>
              </div>
            </div>
            
            <div className="flex items-start gap-3 p-3 border rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <h4 className="font-medium">Offer & Rejection Notifications</h4>
                <p className="text-sm text-muted-foreground">
                  Specialized emails for job offers and application rejections
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
