import React, { useState, useEffect } from 'react';
import { FEATURES, PLAN_FEATURES } from '@/config/paypal';
import { getFeatureAccessSummary, getAllFeaturesSummary, validatePlanConfiguration } from '@/utils/planConfigHelper';
import { getPlanFeaturesConfig, updatePlanFeatures } from '@/services/supabase/planFeatures';
import { useAuth } from '@/contexts/AuthContext';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Info,
  Save,
  RotateCcw
} from 'lucide-react';

/**
 * Admin component for managing plan features
 * Only accessible to platform administrators
 */
const PlanFeatureManager: React.FC = () => {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [localConfig, setLocalConfig] = useState(PLAN_FEATURES);
  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Fetch current plan features from database
  const { data: dbPlanFeatures, isLoading } = useQuery({
    queryKey: ['plan-features'],
    queryFn: getPlanFeaturesConfig,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Update local config when database data loads
  useEffect(() => {
    if (dbPlanFeatures) {
      setLocalConfig(dbPlanFeatures);
      setHasChanges(false);
    }
  }, [dbPlanFeatures]);

  const allFeatures = getAllFeaturesSummary();
  const validation = validatePlanConfiguration(localConfig);

  const handleFeatureToggle = (
    plan: keyof typeof PLAN_FEATURES,
    feature: string,
    enabled: boolean
  ) => {
    setLocalConfig(prev => ({
      ...prev,
      [plan]: {
        ...prev[plan],
        [feature]: enabled
      }
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    if (!user) {
      toast({
        title: 'Error',
        description: 'User not authenticated',
        variant: 'destructive',
      });
      return;
    }

    setIsSaving(true);
    try {
      await updatePlanFeatures(localConfig, user.id);

      // Invalidate and refetch plan features
      await queryClient.invalidateQueries({ queryKey: ['plan-features'] });

      setHasChanges(false);
      toast({
        title: 'Success',
        description: 'Plan features updated successfully. Changes will be reflected for all users.',
      });
    } catch (error) {
      console.error('Error saving plan features:', error);
      toast({
        title: 'Error',
        description: 'Failed to save plan features. Please try again.',
        variant: 'destructive',
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleReset = () => {
    if (dbPlanFeatures) {
      setLocalConfig(dbPlanFeatures);
    } else {
      setLocalConfig(PLAN_FEATURES);
    }
    setHasChanges(false);
  };

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case 'STARTER': return 'bg-blue-100 text-blue-800';
      case 'GROWTH': return 'bg-green-100 text-green-800';
      case 'PRO': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-pulse text-gray-500">Loading plan features...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Plan Feature Manager</h2>
          <p className="text-gray-600">Configure which features are available for each subscription plan</p>
        </div>
        <div className="flex gap-2">
          {hasChanges && (
            <Button variant="outline" onClick={handleReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          )}
          <Button
            onClick={handleSave}
            disabled={!hasChanges || !validation.isValid || isSaving}
            className="bg-primary text-white"
          >
            <Save className="h-4 w-4 mr-2" />
            {isSaving ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>

      {/* Validation Alerts */}
      {validation.issues.length > 0 && (
        <Alert className="border-red-200 bg-red-50">
          <AlertTriangle className="h-4 w-4 text-red-600" />
          <AlertDescription className="text-red-800">
            <strong>Configuration Issues:</strong>
            <ul className="mt-1 list-disc list-inside">
              {validation.issues.map((issue, index) => (
                <li key={index}>{issue}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {validation.warnings.length > 0 && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <Info className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <strong>Warnings:</strong>
            <ul className="mt-1 list-disc list-inside">
              {validation.warnings.map((warning, index) => (
                <li key={index}>{warning}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="by-feature">
        <TabsList>
          <TabsTrigger value="by-feature">By Feature</TabsTrigger>
          <TabsTrigger value="by-plan">By Plan</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
        </TabsList>

        {/* By Feature View */}
        <TabsContent value="by-feature" className="space-y-4">
          {allFeatures.map((featureSummary) => (
            <Card key={featureSummary.feature}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg capitalize">
                      {featureSummary.feature.toLowerCase().replace(/_/g, ' ')}
                    </CardTitle>
                    <CardDescription>
                      Available on: {featureSummary.allowedPlans.join(', ') || 'No plans'}
                    </CardDescription>
                  </div>
                  <div className="flex gap-2">
                    {Object.entries(localConfig).map(([plan, features]) => (
                      <div key={plan} className="flex items-center gap-2">
                        <Badge className={getPlanBadgeColor(plan)}>
                          {plan}
                        </Badge>
                        <Switch
                          checked={features[featureSummary.featureKey] || false}
                          onCheckedChange={(enabled) => 
                            handleFeatureToggle(plan as keyof typeof PLAN_FEATURES, featureSummary.featureKey, enabled)
                          }
                        />
                      </div>
                    ))}
                  </div>
                </div>
              </CardHeader>
            </Card>
          ))}
        </TabsContent>

        {/* By Plan View */}
        <TabsContent value="by-plan" className="space-y-4">
          {Object.entries(localConfig).map(([plan, features]) => (
            <Card key={plan}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Badge className={getPlanBadgeColor(plan)}>{plan}</Badge>
                  <span>Plan Features</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {Object.entries(features).map(([featureKey, enabled]) => {
                    const featureName = Object.entries(FEATURES).find(
                      ([_, value]) => value === featureKey
                    )?.[0] || featureKey;
                    
                    return (
                      <div key={featureKey} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-2">
                          {enabled ? (
                            <CheckCircle className="h-4 w-4 text-green-600" />
                          ) : (
                            <XCircle className="h-4 w-4 text-gray-400" />
                          )}
                          <span className="text-sm capitalize">
                            {featureName.toLowerCase().replace(/_/g, ' ')}
                          </span>
                        </div>
                        <Switch
                          checked={enabled}
                          onCheckedChange={(newEnabled) => 
                            handleFeatureToggle(plan as keyof typeof PLAN_FEATURES, featureKey, newEnabled)
                          }
                        />
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Overview */}
        <TabsContent value="overview">
          <Card>
            <CardHeader>
              <CardTitle>Feature Overview</CardTitle>
              <CardDescription>
                Summary of all features and their plan availability
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full border-collapse">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left p-2">Feature</th>
                      <th className="text-center p-2">Starter</th>
                      <th className="text-center p-2">Growth</th>
                      <th className="text-center p-2">Pro</th>
                    </tr>
                  </thead>
                  <tbody>
                    {allFeatures.map((feature) => (
                      <tr key={feature.feature} className="border-b">
                        <td className="p-2 font-medium capitalize">
                          {feature.feature.toLowerCase().replace(/_/g, ' ')}
                        </td>
                        <td className="text-center p-2">
                          {localConfig.STARTER[feature.featureKey] ? (
                            <CheckCircle className="h-4 w-4 text-green-600 mx-auto" />
                          ) : (
                            <XCircle className="h-4 w-4 text-gray-400 mx-auto" />
                          )}
                        </td>
                        <td className="text-center p-2">
                          {localConfig.GROWTH[feature.featureKey] ? (
                            <CheckCircle className="h-4 w-4 text-green-600 mx-auto" />
                          ) : (
                            <XCircle className="h-4 w-4 text-gray-400 mx-auto" />
                          )}
                        </td>
                        <td className="text-center p-2">
                          {localConfig.PRO[feature.featureKey] ? (
                            <CheckCircle className="h-4 w-4 text-green-600 mx-auto" />
                          ) : (
                            <XCircle className="h-4 w-4 text-gray-400 mx-auto" />
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default PlanFeatureManager;
